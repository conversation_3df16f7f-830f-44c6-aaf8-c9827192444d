"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const cache_1 = require("../../utils/cache");
describe('Cache', () => {
    let cache;
    let logger;
    const mockLogger = {
        error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn(),
        debug: jest.fn()
    };
    beforeEach(() => {
        logger = mockLogger;
        cache = new cache_1.Cache(logger);
    });
    describe('set', () => {
        it('should set cache with default options', async () => {
            const key = 'test-key';
            const value = { data: 'test-value' };
            await cache.set(key, value);
            const result = await cache.get(key);
            expect(result).toEqual(value);
        });
        it('should set cache with custom TTL', async () => {
            const key = 'test-key-ttl';
            const value = { data: 'test-value' };
            const ttl = 1000;
            await cache.set(key, value, ttl);
            const result = await cache.get(key);
            expect(result).toEqual(value);
            await new Promise(resolve => setTimeout(resolve, ttl + 100));
            const expiredResult = await cache.get(key);
            expect(expiredResult).toBeNull();
        });
        it('should handle set error', async () => {
            const key = 'test-key-error';
            const value = { data: 'test-value' };
            jest.spyOn(cache['redis'], 'set').mockRejectedValue(new Error('Redis error'));
            await expect(cache.set(key, value)).rejects.toThrow('Redis error');
            expect(logger.error).toHaveBeenCalled();
        });
    });
    describe('get', () => {
        it('should get cached value', async () => {
            const key = 'test-key-get';
            const value = { data: 'test-value' };
            await cache.set(key, value);
            const result = await cache.get(key);
            expect(result).toEqual(value);
        });
        it('should return null for non-existent key', async () => {
            const key = 'non-existent-key';
            const result = await cache.get(key);
            expect(result).toBeNull();
        });
        it('should handle get error', async () => {
            const key = 'test-key-get-error';
            jest.spyOn(cache['redis'], 'get').mockRejectedValue(new Error('Redis error'));
            await expect(cache.get(key)).rejects.toThrow('Redis error');
            expect(logger.error).toHaveBeenCalled();
        });
    });
    describe('delete', () => {
        it('should delete cached value', async () => {
            const key = 'test-key-delete';
            const value = { data: 'test-value' };
            await cache.set(key, value);
            await cache.delete(key);
            const result = await cache.get(key);
            expect(result).toBeNull();
        });
        it('should handle delete error', async () => {
            const key = 'test-key-delete-error';
            jest.spyOn(cache['redis'], 'del').mockRejectedValue(new Error('Redis error'));
            await expect(cache.delete(key)).rejects.toThrow('Redis error');
            expect(logger.error).toHaveBeenCalled();
        });
    });
    describe('clear', () => {
        it('should clear all cached values', async () => {
            const key1 = 'test-key-clear-1';
            const key2 = 'test-key-clear-2';
            const value = { data: 'test-value' };
            await cache.set(key1, value);
            await cache.set(key2, value);
            await cache.clear();
            const result1 = await cache.get(key1);
            const result2 = await cache.get(key2);
            expect(result1).toBeNull();
            expect(result2).toBeNull();
        });
        it('should handle clear error', async () => {
            jest.spyOn(cache['redis'], 'flushall').mockRejectedValue(new Error('Redis error'));
            await expect(cache.clear()).rejects.toThrow('Redis error');
            expect(logger.error).toHaveBeenCalled();
        });
    });
    describe('getOrSet', () => {
        it('should get cached value if exists', async () => {
            const key = 'test-key-get-or-set';
            const value = { data: 'test-value' };
            await cache.set(key, value);
            const result = await cache.getOrSet(key, () => Promise.resolve({ data: 'new-value' }));
            expect(result).toEqual(value);
        });
        it('should set and return new value if cache miss', async () => {
            const key = 'test-key-get-or-set-miss';
            const value = { data: 'new-value' };
            const result = await cache.getOrSet(key, () => Promise.resolve(value));
            expect(result).toEqual(value);
            const cachedValue = await cache.get(key);
            expect(cachedValue).toEqual(value);
        });
        it('should handle getOrSet error', async () => {
            const key = 'test-key-get-or-set-error';
            jest.spyOn(cache['redis'], 'get').mockRejectedValue(new Error('Redis error'));
            await expect(cache.getOrSet(key, () => Promise.resolve({ data: 'test-value' })))
                .rejects.toThrow('Redis error');
            expect(logger.error).toHaveBeenCalled();
        });
    });
    describe('mget', () => {
        it('should get multiple cached values', async () => {
            const key1 = 'test-key-mget-1';
            const key2 = 'test-key-mget-2';
            const value1 = { data: 'value-1' };
            const value2 = { data: 'value-2' };
            await cache.set(key1, value1);
            await cache.set(key2, value2);
            const result = await cache.mget([key1, key2]);
            expect(result).toEqual([value1, value2]);
        });
        it('should handle non-existent keys', async () => {
            const key1 = 'test-key-mget-exist';
            const key2 = 'test-key-mget-non-exist';
            const value = { data: 'test-value' };
            await cache.set(key1, value);
            const result = await cache.mget([key1, key2]);
            expect(result).toEqual([value, null]);
        });
        it('should handle mget error', async () => {
            jest.spyOn(cache['redis'], 'mget').mockRejectedValue(new Error('Redis error'));
            await expect(cache.mget(['key1', 'key2'])).rejects.toThrow('Redis error');
            expect(logger.error).toHaveBeenCalled();
        });
    });
    describe('mset', () => {
        it('should set multiple cache values', async () => {
            const key1 = 'test-key-mset-1';
            const key2 = 'test-key-mset-2';
            const value1 = { data: 'value-1' };
            const value2 = { data: 'value-2' };
            await cache.mset([
                { key: key1, value: value1 },
                { key: key2, value: value2 }
            ]);
            const result1 = await cache.get(key1);
            const result2 = await cache.get(key2);
            expect(result1).toEqual(value1);
            expect(result2).toEqual(value2);
        });
        it('should handle mset error', async () => {
            jest.spyOn(cache['redis'], 'mset').mockRejectedValue(new Error('Redis error'));
            await expect(cache.mset([
                { key: 'key1', value: 'value1' },
                { key: 'key2', value: 'value2' }
            ])).rejects.toThrow('Redis error');
            expect(logger.error).toHaveBeenCalled();
        });
    });
});
//# sourceMappingURL=cache.spec.js.map