import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// 使用CPU版本的TensorFlow，因为GPU版本需要额外配置
import * as tf from '@tensorflow/tfjs-node';

@Injectable()
export class RiskModelService {
  private model: tf.LayersModel;
  private readonly gpuEnabled: boolean;

  constructor(private configService: ConfigService) {
    this.gpuEnabled = this.configService.get('USE_GPU') === 'true';
    this.initializeModel();
  }

  private async initializeModel() {
    try {
      // 加载预训练模型
      this.model = await tf.loadLayersModel('file://models/risk_assessment_model/model.json');
      console.log('风控模型加载成功');
    } catch (error) {
      console.error('模型加载失败:', error);
      // 如果加载失败，创建新模型
      this.model = this.createModel();
      await this.trainModel();
    }
  }

  private createModel(): tf.Sequential {
    const model = tf.sequential();

    // 添加输入层和隐藏层
    model.add(tf.layers.dense({
      units: 64,
      activation: 'relu',
      inputShape: [20], // 20个特征输入
    }));

    model.add(tf.layers.dropout({ rate: 0.2 }));

    model.add(tf.layers.dense({
      units: 32,
      activation: 'relu',
    }));

    model.add(tf.layers.dropout({ rate: 0.2 }));

    // 输出层
    model.add(tf.layers.dense({
      units: 1,
      activation: 'sigmoid',
    }));

    // 编译模型
    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'binaryCrossentropy',
      metrics: ['accuracy'],
    });

    return model;
  }

  private async trainModel() {
    // 这里应该加载实际的训练数据
    const trainingData = await this.loadTrainingData();
    const { features, labels } = trainingData;

    await this.model.fit(features, labels, {
      epochs: 50,
      batchSize: 32,
      validationSplit: 0.2,      callbacks: {
        onEpochEnd: (epoch: number, logs: any) => {
          console.log(`Epoch ${epoch}: loss = ${logs?.loss || 'N/A'}`);
        },
      },
    });

    // 保存训练后的模型
    await this.model.save('file://models/risk_assessment_model');
  }

  private async loadTrainingData() {
    // 这里应该实现实际的数据加载逻辑
    // 示例数据
    const features = tf.randomNormal([1000, 20]);
    const labels = tf.randomUniform([1000, 1]);
    return { features, labels };
  }

  async assessRisk(data: any): Promise<number> {
    try {
      // 预处理数据
      const features = this.preprocessData(data);
      
      // 使用模型预测
      const prediction = this.model.predict(features) as tf.Tensor;
      const riskScore = (await prediction.data())[0] * 100;
      
      // 清理内存
      tf.dispose([features, prediction]);
      
      return Math.round(riskScore * 100) / 100;    } catch (error: any) {
      console.error('风险评估失败:', error);
      throw new Error(`风险评估失败: ${error.message}`);
    }
  }

  private preprocessData(data: any): tf.Tensor2D {
    // 特征提取和标准化
    const features = [
      data.credit_score / 850, // 信用分数标准化
      data.debt_ratio, // 负债率
      data.income_stability === 'HIGH' ? 1 : 0,
      data.loan_amount / 1000000, // 贷款金额标准化
      // ... 其他特征
    ];

    // 填充到20个特征
    while (features.length < 20) {
      features.push(0);
    }

    return tf.tensor2d([features]);
  }
}
