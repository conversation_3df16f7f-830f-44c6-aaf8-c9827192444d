import { Repository } from 'typeorm';
import { Product } from '../entities/product.entity';
import { User } from '../../user/entities/user.entity';
import { CacheService } from '../../../cache/cache.service';
import { LoggerService } from '../../../logger/logger.service';
export declare class ProductRecommendationService {
    private readonly productRepository;
    private readonly cacheService;
    private readonly logger;
    constructor(productRepository: Repository<Product>, cacheService: CacheService, logger: LoggerService);
    getRecommendedProducts(user: User): Promise<Product[]>;
    private getUserFeatures;
    private calculateProductScore;
    recommendProductsByUserFeatures(userId: string): Promise<Product[]>;
    getSimilarProducts(productId: number): Promise<Product[]>;
}
