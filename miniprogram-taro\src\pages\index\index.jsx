/**
 * Taro 3.x 小程序主页面
 * 基于React技术栈的跨端解决方案
 * 支持微信小程序、支付宝小程序、百度小程序
 */

import React, { useState, useEffect } from 'react';
import { View, Text, Button, Image, ScrollView } from '@tarojs/components';
import { useLoad, useDidShow } from '@tarojs/taro';
import Taro from '@tarojs/taro';
import './index.scss';

// 自定义Hooks - 产品数据管理
function useProductData() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await Taro.request({
        url: 'http://localhost:3001/api/products',
        method: 'GET'
      });
      
      if (response.data.success) {
        setProducts(response.data.data);
      }
    } catch (error) {
      console.error('获取产品数据失败:', error);
      Taro.showToast({
        title: '网络错误',
        icon: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  return { products, loading, fetchProducts };
}

// 智能匹配组件
function SmartMatch() {
  const [matchResult, setMatchResult] = useState(null);
  const [matching, setMatching] = useState(false);

  const handleSmartMatch = async () => {
    setMatching(true);
    
    try {
      const response = await Taro.request({
        url: 'http://localhost:3001/api/products/match/smart',
        method: 'POST',
        data: {
          amount: 100000,
          term: 24,
          purpose: '个人消费'
        }
      });

      if (response.data.success) {
        setMatchResult(response.data.data);
        Taro.showToast({
          title: '匹配成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('智能匹配失败:', error);
      Taro.showToast({
        title: '匹配失败',
        icon: 'error'
      });
    } finally {
      setMatching(false);
    }
  };

  return (
    <View className="smart-match-container">
      <View className="match-header">
        <Text className="match-title">🎯 智能产品匹配</Text>
        <Text className="match-subtitle">AI为您推荐最适合的金融产品</Text>
      </View>
      
      <Button 
        className="match-button"
        onClick={handleSmartMatch}
        loading={matching}
      >
        {matching ? '匹配中...' : '开始智能匹配'}
      </Button>

      {matchResult && (
        <View className="match-results">
          {matchResult.slice(0, 3).map((item, index) => (
            <View key={index} className="product-card">
              <View className="product-header">
                <Text className="product-name">{item.product.name}</Text>
                <Text className="match-score">{(item.match_score * 100).toFixed(1)}%</Text>
              </View>
              <View className="product-details">
                <Text className="product-provider">{item.product.provider}</Text>
                <Text className="product-rate">利率: {item.product.interest_rate}%</Text>
                <Text className="product-amount">最高: ¥{item.product.amount_max.toLocaleString()}</Text>
              </View>
              <Text className="ai-reasoning">{item.ai_reasoning}</Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
}

// OCR识别组件
function OCRRecognition() {
  const [ocrResult, setOcrResult] = useState(null);
  const [processing, setProcessing] = useState(false);

  const handleChooseImage = () => {
    Taro.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        const tempFilePath = res.tempFilePaths[0];
        await processOCR(tempFilePath);
      }
    });
  };

  const processOCR = async (imagePath) => {
    setProcessing(true);
    
    try {
      // 模拟OCR处理
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const response = await Taro.request({
        url: 'http://localhost:3001/api/ocr/recognize',
        method: 'POST',
        data: {
          documentType: 'identity_card',
          imageData: imagePath
        }
      });

      if (response.data.success) {
        setOcrResult(response.data.data);
        Taro.showToast({
          title: '识别成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('OCR识别失败:', error);
      Taro.showToast({
        title: '识别失败',
        icon: 'error'
      });
    } finally {
      setProcessing(false);
    }
  };

  return (
    <View className="ocr-container">
      <View className="ocr-header">
        <Text className="ocr-title">📷 证件识别</Text>
        <Text className="ocr-subtitle">支持15类证件智能识别</Text>
      </View>

      <View className="upload-area" onClick={handleChooseImage}>
        <Text className="upload-icon">📄</Text>
        <Text className="upload-text">点击上传证件照片</Text>
        <Text className="upload-hint">支持身份证、营业执照等</Text>
      </View>

      {processing && (
        <View className="processing-indicator">
          <Text>🔄 AI识别中...</Text>
        </View>
      )}

      {ocrResult && (
        <View className="ocr-result">
          <View className="result-header">
            <Text className="result-title">✅ 识别成功</Text>
            <Text className="confidence">置信度: {(ocrResult.confidence * 100).toFixed(1)}%</Text>
          </View>
          <View className="extracted-data">
            <Text className="data-item">姓名: {ocrResult.extractedData.name}</Text>
            <Text className="data-item">身份证: {ocrResult.extractedData.idNumber}</Text>
            <Text className="data-item">地址: {ocrResult.extractedData.address}</Text>
          </View>
        </View>
      )}
    </View>
  );
}

// 贷款计算器组件
function LoanCalculator() {
  const [amount, setAmount] = useState('100');
  const [term, setTerm] = useState('240');
  const [result, setResult] = useState(null);

  const calculateLoan = async () => {
    try {
      const response = await Taro.request({
        url: 'http://localhost:3001/api/loan/calculator',
        method: 'POST',
        data: {
          loanType: '商业贷款',
          totalAmount: parseFloat(amount) * 10000,
          loanTerm: parseInt(term),
          repaymentMethod: '等额本息'
        }
      });

      if (response.data.success) {
        setResult(response.data.data);
      }
    } catch (error) {
      console.error('计算失败:', error);
      Taro.showToast({
        title: '计算失败',
        icon: 'error'
      });
    }
  };

  return (
    <View className="calculator-container">
      <View className="calculator-header">
        <Text className="calculator-title">💰 贷款计算器</Text>
      </View>

      <View className="input-group">
        <Text className="input-label">贷款金额 (万元)</Text>
        <input 
          className="input-field"
          type="number"
          value={amount}
          onInput={(e) => setAmount(e.detail.value)}
          placeholder="请输入贷款金额"
        />
      </View>

      <View className="input-group">
        <Text className="input-label">贷款期限</Text>
        <picker 
          mode="selector"
          range={['20年', '30年']}
          value={term === '240' ? 0 : 1}
          onChange={(e) => setTerm(e.detail.value === 0 ? '240' : '360')}
        >
          <View className="picker-view">
            {term === '240' ? '20年' : '30年'}
          </View>
        </picker>
      </View>

      <Button className="calculate-button" onClick={calculateLoan}>
        计算月供
      </Button>

      {result && (
        <View className="calculation-result">
          <View className="result-item">
            <Text className="result-label">月供金额:</Text>
            <Text className="result-value">¥{result.monthlyPaymentAmount.toLocaleString()}</Text>
          </View>
          <View className="result-item">
            <Text className="result-label">总利息:</Text>
            <Text className="result-value">¥{result.totalInterest.toLocaleString()}</Text>
          </View>
          <View className="result-item">
            <Text className="result-label">还款总额:</Text>
            <Text className="result-value">¥{result.totalPayment.toLocaleString()}</Text>
          </View>
        </View>
      )}
    </View>
  );
}

// 主页面组件
export default function Index() {
  const { products, loading, fetchProducts } = useProductData();
  const [activeTab, setActiveTab] = useState(0);

  useLoad(() => {
    console.log('SmartLoan 小程序页面加载');
    fetchProducts();
  });

  useDidShow(() => {
    console.log('SmartLoan 小程序页面显示');
  });

  const tabs = [
    { title: '智能匹配', component: <SmartMatch /> },
    { title: '证件识别', component: <OCRRecognition /> },
    { title: '贷款计算', component: <LoanCalculator /> }
  ];

  return (
    <View className="index-container">
      {/* 头部 */}
      <View className="header">
        <Text className="app-title">🚀 SmartLoan</Text>
        <Text className="app-subtitle">智能金融服务小程序</Text>
      </View>

      {/* 标签栏 */}
      <View className="tab-bar">
        {tabs.map((tab, index) => (
          <View 
            key={index}
            className={`tab-item ${activeTab === index ? 'active' : ''}`}
            onClick={() => setActiveTab(index)}
          >
            <Text className="tab-text">{tab.title}</Text>
          </View>
        ))}
      </View>

      {/* 内容区域 */}
      <ScrollView className="content-area" scrollY>
        {tabs[activeTab].component}
      </ScrollView>

      {/* 底部信息 */}
      <View className="footer">
        <Text className="footer-text">
          🏆 2025年最新AI技术 | 支持数字人民币 | 30秒审批
        </Text>
      </View>
    </View>
  );
}
