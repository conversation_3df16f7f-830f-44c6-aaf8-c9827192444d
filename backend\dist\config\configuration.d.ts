declare const _default: () => {
    port: number;
    database: {
        host: string;
        port: number;
        username: string;
        password: string;
        database: string;
    };
    jwt: {
        secret: string;
        expiresIn: string;
    };
    upload: {
        dir: string;
        maxSize: number;
        allowedTypes: string[];
    };
    cache: {
        ttl: number;
    };
    monitoring: {
        enabled: boolean;
        metrics: {
            enabled: boolean;
            interval: number;
        };
        alerts: {
            enabled: boolean;
            threshold: {
                cpu: number;
                memory: number;
                disk: number;
            };
        };
        email: {
            enabled: boolean;
            from: string;
            to: string;
            smtp: {
                host: string;
                port: number;
                secure: boolean;
                auth: {
                    user: string;
                    pass: string;
                };
            };
        };
        slack: {
            enabled: boolean;
            webhookUrl: string;
        };
        thresholds: {
            responseTime: number;
            errorRate: number;
            cpuUsage: number;
            memoryUsage: number;
            diskUsage: number;
        };
    };
    logging: {
        level: string;
        format: string;
        file: string;
    };
    cors: {
        origin: string[];
        methods: string[];
        allowedHeaders: string[];
    };
};
export default _default;
