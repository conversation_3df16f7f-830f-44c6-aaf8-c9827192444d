/**
 * SmartLoan 2025 简化测试服务器
 * 使用CommonJS语法，确保兼容性
 */

const express = require('express');
const path = require('path');

const app = express();
const PORT = 3001;

// 中间件
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});
app.use(express.json());
app.use(express.static('.'));

// 请求日志
app.use((req, res, next) => {
  console.log(`📝 ${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

console.log('🚀 SmartLoan 2025 启动中...');

// 健康检查
app.get('/api/health', (req, res) => {
  console.log('✅ 健康检查请求');
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      database: 'UP',
      redis: 'UP',
      metax_gpu: 'UP',
      gitee_ai: 'UP'
    }
  });
});

// 获取产品列表
app.get('/api/products', (req, res) => {
  console.log('🏦 获取产品列表');
  res.json({
    success: true,
    data: [
      {
        id: 'icbc_2025_001',
        name: '工商银行融e借2025版',
        institution: '中国工商银行',
        rate_min: 3.85,
        rate_max: 4.20,
        amount_max: 800000,
        features: ['数字人民币支持', '30秒审批', 'AI风控']
      },
      {
        id: 'ccb_2025_001',
        name: '建设银行快贷Pro2025',
        institution: '中国建设银行',
        rate_min: 3.95,
        rate_max: 4.35,
        amount_max: 500000,
        features: ['线上申请', '秒级放款', '智能定价']
      }
    ]
  });
});

// 智能产品匹配
app.post('/api/products/match/smart', (req, res) => {
  console.log('🎯 智能产品匹配:', req.body);
  
  setTimeout(() => {
    res.json({
      success: true,
      data: [
        {
          product: {
            id: 'icbc_2025_001',
            name: '工商银行融e借2025版',
            institution: '中国工商银行',
            rate_min: 3.85,
            rate_max: 4.20
          },
          match_score: 0.95,
          ai_reasoning: '该产品利率优惠，支持数字人民币，审批速度快'
        }
      ]
    });
  }, 500);
});

// OCR识别
app.post('/api/ai/ocr', (req, res) => {
  console.log('📷 OCR识别');
  
  setTimeout(() => {
    res.json({
      success: true,
      data: {
        document_type: 'identity_card',
        extracted_data: {
          name: '张三',
          idNumber: '110101199001011234',
          address: '北京市朝阳区'
        },
        confidence: 0.96,
        processing_time: '800ms'
      }
    });
  }, 800);
});

// 活体检测
app.post('/api/ai/liveness', (req, res) => {
  console.log('👤 活体检测');
  
  setTimeout(() => {
    res.json({
      success: true,
      data: {
        is_live: true,
        confidence: 0.98,
        processing_time: '1200ms'
      }
    });
  }, 1200);
});

// 风险评估
app.post('/api/ai/risk-assessment', (req, res) => {
  console.log('📊 风险评估');
  
  setTimeout(() => {
    res.json({
      success: true,
      data: {
        risk_score: 750,
        risk_level: 'LOW',
        processing_time: '1500ms'
      }
    });
  }, 1500);
});

// AI顾问对话
app.post('/api/ai/advisor/chat', (req, res) => {
  console.log('🤖 AI顾问:', req.body.message);
  
  setTimeout(() => {
    res.json({
      success: true,
      data: {
        response: '基于您的需求，我推荐工商银行融e借2025版，支持数字人民币，30秒审批。',
        model: 'Fin-R1-2025'
      }
    });
  }, 1000);
});

// 贷款计算器
app.post('/api/loan/calculator', (req, res) => {
  console.log('💰 贷款计算器:', req.body);
  
  const { totalAmount, loanTerm } = req.body;
  const monthlyPayment = Math.round(totalAmount * 0.006);
  
  res.json({
    success: true,
    data: {
      monthlyPaymentAmount: monthlyPayment,
      totalPayment: monthlyPayment * loanTerm,
      totalInterest: monthlyPayment * loanTerm - totalAmount
    }
  });
});

// 静态页面路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'smartloan-api-demo.html'));
});

app.get('/complete', (req, res) => {
  res.sendFile(path.join(__dirname, 'smartloan-complete.html'));
});

app.get('/demo', (req, res) => {
  res.sendFile(path.join(__dirname, 'demo.html'));
});

app.get('/qualification', (req, res) => {
  res.sendFile(path.join(__dirname, 'qualification-review.html'));
});

app.get('/risk', (req, res) => {
  res.sendFile(path.join(__dirname, 'risk-dashboard.html'));
});

app.get('/miniprogram', (req, res) => {
  res.sendFile(path.join(__dirname, 'miniprogram-demo.html'));
});

app.get('/app', (req, res) => {
  res.sendFile(path.join(__dirname, 'app-demo.html'));
});

app.get('/monitor', (req, res) => {
  res.sendFile(path.join(__dirname, 'performance-monitor.html'));
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'API接口未找到',
    available_endpoints: [
      'GET /api/health - 健康检查',
      'POST /api/products/match/smart - 智能产品匹配',
      'POST /api/ai/risk-assessment - AI风险评估',
      'POST /api/ai/advisor/chat - AI顾问对话',
      'POST /api/ai/ocr - OCR识别',
      'POST /api/ai/liveness - 活体检测',
      'GET /api/products - 获取产品列表'
    ]
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`
🚀 SmartLoan 2025 测试服务器启动成功!
📍 服务地址: http://localhost:${PORT}
🎯 可用页面:
   主页面: http://localhost:${PORT}/
   演示页: http://localhost:${PORT}/demo
   资质审核: http://localhost:${PORT}/qualification
   风控看板: http://localhost:${PORT}/risk
   小程序版: http://localhost:${PORT}/miniprogram
   APP版: http://localhost:${PORT}/app
   性能监控: http://localhost:${PORT}/monitor

🎮 沐曦MetaX GPU服务已模拟启用
📡 Gitee AI平台已模拟连接
🏦 支持500+金融机构产品
⚡ 系统就绪，等待请求...
  `);
});

// 错误处理
process.on('uncaughtException', (err) => {
  console.error('❌ 未捕获的异常:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
});
