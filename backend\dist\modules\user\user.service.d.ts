import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { Role } from './entities/role.entity';
import { UserLog } from './entities/user-log.entity';
import { LoggerService } from '../../logger/logger.service';
import { CacheService } from '../../cache/cache.service';
export declare class UserService {
    private readonly userRepository;
    private readonly roleRepository;
    private readonly userLogRepository;
    private readonly logger;
    private readonly cache;
    constructor(userRepository: Repository<User>, roleRepository: Repository<Role>, userLogRepository: Repository<UserLog>, logger: LoggerService, cache: CacheService);
    findById(id: number): Promise<User | null>;
    findByUsername(username: string): Promise<User>;
    findByEmail(email: string): Promise<User>;
    create(createUserDto: any): Promise<User>;
    update(id: number, updateUserDto: any): Promise<User>;
    updatePassword(id: number, oldPassword: string, newPassword: string): Promise<void>;
    resetPassword(email: string): Promise<void>;
    verifyResetToken(token: string): Promise<User>;
    setNewPassword(token: string, newPassword: string): Promise<void>;
    delete(id: number): Promise<void>;
    private logUserAction;
    findAll(): Promise<User[]>;
    assignRoles(userId: number, roleIds: number[]): Promise<User>;
}
