import { RepaymentPlan } from './repayment-plan.entity';
export declare class Contract {
    id: string;
    contractNumber: string;
    applicationNumber: string;
    loanType: string;
    loanAmount: number;
    loanTerm: number;
    interestRate: number;
    repaymentMethod: string;
    guaranteeMethod: string;
    disbursementDate: string;
    maturityDate: string;
    loanPurpose: string;
    repaymentDay: number;
    guarantor: string;
    status: string;
    signature: string;
    signedAt: Date;
    repaymentPlan: RepaymentPlan[];
    createdAt: Date;
    updatedAt: Date;
}
