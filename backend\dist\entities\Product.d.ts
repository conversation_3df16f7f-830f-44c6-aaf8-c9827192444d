export interface Product {
    id: string;
    name: string;
    institutionId: string;
    institutionName: string;
    type: ProductType;
    category: ProductCategory;
    description: string;
    features: string[];
    advantages: string[];
    loanAmount: {
        min: number;
        max: number;
        unit: 'yuan' | 'wan';
    };
    interestRate: {
        min: number;
        max: number;
        type: 'fixed' | 'floating';
        basis: 'LPR' | 'base_rate';
    };
    loanTerm: {
        min: number;
        max: number;
        unit: 'month' | 'year';
    };
    requirements: {
        age: {
            min: number;
            max: number;
        };
        income: {
            min: number;
            unit: 'monthly' | 'annual';
        };
        creditScore: {
            min: number;
        };
        workYears: {
            min: number;
        };
        location: string[];
        documents: string[];
    };
    specialFeatures: {
        hasInstantApproval: boolean;
        hasOnlineProcess: boolean;
        hasCollateralFree: boolean;
        hasEarlyRepayment: boolean;
        processingTime: number;
    };
    fees: {
        processingFee: {
            rate: number;
            cap?: number;
        };
        managementFee: {
            rate: number;
            frequency: 'monthly' | 'annual';
        };
        earlyRepaymentFee: {
            rate: number;
        };
        otherFees: Array<{
            name: string;
            amount: number;
            type: 'fixed' | 'rate';
        }>;
    };
    riskLevel: 'low' | 'medium' | 'high';
    riskFactors: string[];
    matchingWeights: {
        amountWeight: number;
        rateWeight: number;
        termWeight: number;
        requirementWeight: number;
        featureWeight: number;
    };
    status: 'active' | 'inactive' | 'suspended';
    popularity: number;
    approvalRate: number;
    averageProcessingDays: number;
    createdAt: Date;
    updatedAt: Date;
    version: number;
}
export declare enum ProductType {
    PERSONAL_LOAN = "personal_loan",
    MORTGAGE = "mortgage",
    BUSINESS_LOAN = "business_loan",
    CAR_LOAN = "car_loan",
    EDUCATION_LOAN = "education_loan",
    CREDIT_CARD = "credit_card",
    LINE_OF_CREDIT = "line_of_credit"
}
export declare enum ProductCategory {
    SECURED = "secured",
    UNSECURED = "unsecured",
    GUARANTEED = "guaranteed",
    GOVERNMENT = "government"
}
export interface FinancialInstitution {
    id: string;
    name: string;
    type: InstitutionType;
    code: string;
    fullName: string;
    englishName?: string;
    logo: string;
    website: string;
    description: string;
    businessScope: string[];
    serviceAreas: string[];
    establishedYear: number;
    contact: {
        phone: string;
        email: string;
        address: string;
        customerService: string;
    };
    licenses: Array<{
        type: string;
        number: string;
        issuer: string;
        validUntil: Date;
    }>;
    rating: {
        overall: number;
        service: number;
        reliability: number;
        innovation: number;
    };
    stats: {
        totalProducts: number;
        activeProducts: number;
        totalUsers: number;
        averageApprovalTime: number;
        customerSatisfaction: number;
    };
    status: 'active' | 'inactive' | 'pending';
    isPartner: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum InstitutionType {
    COMMERCIAL_BANK = "commercial_bank",
    CITY_BANK = "city_bank",
    RURAL_BANK = "rural_bank",
    CREDIT_UNION = "credit_union",
    ONLINE_LENDER = "online_lender",
    P2P_PLATFORM = "p2p_platform",
    MICROFINANCE = "microfinance",
    GOVERNMENT = "government"
}
export interface UserMatchingRequest {
    userId: string;
    loanPurpose: string;
    desiredAmount: number;
    preferredTerm: number;
    maxAcceptableRate: number;
    userProfile: {
        age: number;
        monthlyIncome: number;
        annualIncome: number;
        creditScore?: number;
        workYears: number;
        location: string;
        occupation: string;
        employmentType: 'employed' | 'self_employed' | 'freelance';
    };
    preferences: {
        institutionTypes: InstitutionType[];
        maxProcessingTime: number;
        preferOnlineProcess: boolean;
        priorityFactors: Array<'rate' | 'amount' | 'speed' | 'service'>;
    };
    constraints: {
        mustHaveFeatures: string[];
        blacklistInstitutions: string[];
        maxTotalFees: number;
    };
}
export interface ProductMatchResult {
    product: Product;
    institution: FinancialInstitution;
    matchScore: number;
    scoreBreakdown: {
        amountMatch: number;
        rateMatch: number;
        termMatch: number;
        requirementMatch: number;
        featureMatch: number;
        locationMatch: number;
    };
    reasons: string[];
    warnings: string[];
    estimatedApproval: {
        probability: number;
        processingTime: number;
        requiredDocuments: string[];
    };
    costAnalysis: {
        totalInterest: number;
        totalFees: number;
        monthlyPayment: number;
        apr: number;
    };
}
