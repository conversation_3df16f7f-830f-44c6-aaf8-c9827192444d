import { Injectable } from '@nestjs/common';
import { validate, ValidationError } from 'class-validator';
import { Transform } from 'class-transformer';

export interface ValidationOptions {
  skipMissingProperties?: boolean;
  strictGroups?: boolean;
  groups?: string[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

@Injectable()
export class Validator {
  
  async validateObject(obj: any, options?: ValidationOptions): Promise<ValidationResult> {
    try {
      const errors = await validate(obj, {
        skipMissingProperties: options?.skipMissingProperties || false,
        strictGroups: options?.strictGroups || false,
        groups: options?.groups,
      });

      if (errors.length === 0) {
        return { isValid: true, errors: [] };
      }

      const errorMessages = this.formatValidationErrors(errors);
      return { isValid: false, errors: errorMessages };
    } catch (error) {
      return { 
        isValid: false, 
        errors: [`Validation failed: ${error.message}`] 
      };
    }
  }

  validateEmail(email: string): ValidationResult {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(email);
    
    return {
      isValid,
      errors: isValid ? [] : ['Invalid email format']
    };
  }

  validatePhone(phone: string): ValidationResult {
    // Support multiple phone formats
    const phoneRegex = /^(\+?86)?1[3-9]\d{9}$/;
    const isValid = phoneRegex.test(phone.replace(/[\s-]/g, ''));
    
    return {
      isValid,
      errors: isValid ? [] : ['Invalid phone number format']
    };
  }

  validateIdCard(idCard: string): ValidationResult {
    // 18位身份证号验证
    const idCardRegex = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    
    if (!idCardRegex.test(idCard)) {
      return { isValid: false, errors: ['Invalid ID card format'] };
    }

    // 验证校验码
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    
    const sum = idCard.slice(0, 17)
      .split('')
      .reduce((acc, char, index) => acc + parseInt(char) * weights[index], 0);
    
    const checkCode = checkCodes[sum % 11];
    const lastChar = idCard.slice(-1).toUpperCase();
    
    const isValid = checkCode === lastChar;
    return {
      isValid,
      errors: isValid ? [] : ['Invalid ID card check digit']
    };
  }

  validateBankCard(cardNumber: string): ValidationResult {
    // Luhn算法验证银行卡号
    const cleanNumber = cardNumber.replace(/\s/g, '');
    
    if (!/^\d{16,19}$/.test(cleanNumber)) {
      return { isValid: false, errors: ['Bank card number must be 16-19 digits'] };
    }

    let sum = 0;
    let alternate = false;
    
    for (let i = cleanNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cleanNumber.charAt(i));
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    const isValid = sum % 10 === 0;
    return {
      isValid,
      errors: isValid ? [] : ['Invalid bank card number']
    };
  }

  validateAmount(amount: number, min?: number, max?: number): ValidationResult {
    const errors: string[] = [];
    
    if (isNaN(amount) || amount < 0) {
      errors.push('Amount must be a positive number');
    }
    
    if (min !== undefined && amount < min) {
      errors.push(`Amount must be at least ${min}`);
    }
    
    if (max !== undefined && amount > max) {
      errors.push(`Amount must not exceed ${max}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  validateDateRange(startDate: Date, endDate: Date): ValidationResult {
    const errors: string[] = [];
    
    if (!(startDate instanceof Date) || isNaN(startDate.getTime())) {
      errors.push('Invalid start date');
    }
    
    if (!(endDate instanceof Date) || isNaN(endDate.getTime())) {
      errors.push('Invalid end date');
    }
    
    if (errors.length === 0 && startDate >= endDate) {
      errors.push('Start date must be before end date');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // 批量验证
  async validateBatch<T>(objects: T[], options?: ValidationOptions): Promise<ValidationResult[]> {
    return Promise.all(
      objects.map(obj => this.validateObject(obj, options))
    );
  }

  // 条件验证
  validateConditional(
    value: any, 
    condition: boolean, 
    validator: (val: any) => ValidationResult
  ): ValidationResult {
    if (!condition) {
      return { isValid: true, errors: [] };
    }
    return validator(value);
  }

  private formatValidationErrors(errors: ValidationError[]): string[] {
    const messages: string[] = [];
    
    for (const error of errors) {
      if (error.constraints) {
        messages.push(...Object.values(error.constraints));
      }
      
      if (error.children && error.children.length > 0) {
        messages.push(...this.formatValidationErrors(error.children));
      }
    }
    
    return messages;
  }

  // 自定义验证规则
  addCustomValidator(
    name: string, 
    validator: (value: any, args?: any) => boolean,
    message?: string
  ): void {
    // 这里可以扩展自定义验证器的逻辑
    // 由于class-validator的限制，这里主要提供一个接口
  }
}
