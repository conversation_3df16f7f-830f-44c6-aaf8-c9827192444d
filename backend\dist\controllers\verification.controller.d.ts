import { VerificationService } from '../services/verification.service';
import { Request } from 'express';
export declare class VerificationController {
    private readonly verificationService;
    constructor(verificationService: VerificationService);
    verifyIdentity(body: {
        imageData: string;
    }, req: Request): Promise<{
        success: boolean;
        message: string;
    }>;
    verifyDocuments(body: {
        documents: any[];
    }, req: Request): Promise<{
        success: boolean;
        results: ({
            documentId: any;
            isValid: boolean;
            details: {
                name?: string;
                idNumber?: string;
                validityPeriod?: string;
                error?: string;
            };
            matchScore?: undefined;
        } | {
            documentId: any;
            isValid: any;
            matchScore: any;
            details?: undefined;
        })[];
    }>;
    performLivenessDetection(body: {
        videoData: string;
    }, req: Request): Promise<{
        success: boolean;
        message: string;
    }>;
}
