/// <reference types="node" />
/// <reference types="node" />
import { ConfigService } from '@nestjs/config';
import { LoggerService } from './logger.service';
import { <PERSON>rrorHand<PERSON> } from '../utils/error-handler';
import { MonitorService } from './monitor.service';
import { CacheService } from './cache.service';
export declare class OcrService {
    private readonly configService;
    private readonly logger;
    private readonly errorHandler;
    private readonly monitorService;
    private readonly cacheService;
    private readonly supportedDocuments;
    constructor(configService: ConfigService, logger: LoggerService, errorHandler: ErrorHandler, monitorService: MonitorService, cacheService: CacheService);
    processDocument(documentType: string, imageBuffer: Buffer): Promise<any>;
    private preprocessImage;
    private enhanceImage;
    private removeNoise;
    private binarize;
    private detectAndCorrectDocument;
    recognizeText(imageBuffer: Buffer): Promise<{
        text: string;
    }>;
    private extractInformation;
    private validateInformation;
    private extractIdCardInfo;
    private validateIdCardInfo;
    recognizeIdentityCard(imageBuffer: Buffer): Promise<{
        success: boolean;
        data: {
            name: any;
            idNumber: any;
            gender: any;
            birthDate: any;
            address: any;
            issuingAuthority: any;
            validPeriod: any;
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data?: undefined;
    }>;
    recognizeBankCard(imageBuffer: Buffer): Promise<{
        success: boolean;
        data: {
            cardNumber: any;
            bankName: any;
            cardType: any;
            validThru: any;
            holderName: any;
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data?: undefined;
    }>;
    recognizeIncomeProof(imageBuffer: Buffer): Promise<{
        success: boolean;
        data: {
            employerName: any;
            employeeName: any;
            position: any;
            monthlyIncome: any;
            workYears: any;
            issueDate: any;
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data?: undefined;
    }>;
    private extractName;
    private extractIdNumber;
    private extractPassportInfo;
    private extractDriverLicenseInfo;
    private extractBusinessLicenseInfo;
    private extractGenericInfo;
    private validatePassportInfo;
    private validateDriverLicenseInfo;
    private validateBusinessLicenseInfo;
    private validateGenericInfo;
    private extractGender;
    private extractNationality;
    private extractBirthDate;
    private extractAddress;
    private validateName;
    private validateIdNumber;
    private validateGender;
    private validateNationality;
    private validateBirthDate;
    private validateAddress;
    private validateIssueDate;
    private calculateConfidence;
}
