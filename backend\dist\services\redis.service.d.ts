import { On<PERSON><PERSON><PERSON><PERSON><PERSON>roy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import { LoggerService } from './logger.service';
export declare class RedisService implements OnModuleDestroy {
    private readonly configService;
    private readonly redisClient;
    private readonly logger;
    constructor(configService: ConfigService, loggerService?: LoggerService);
    onModuleDestroy(): Promise<void>;
    getClient(): Redis;
    get(key: string): Promise<string | null>;
    set(key: string, value: string, ttl?: number): Promise<void>;
    del(key: string): Promise<void>;
    exists(key: string): Promise<boolean>;
    incr(key: string): Promise<number>;
    decr(key: string): Promise<number>;
    expire(key: string, seconds: number): Promise<void>;
    ttl(key: string): Promise<number>;
    keys(pattern: string): Promise<string[]>;
    flushAll(): Promise<void>;
}
