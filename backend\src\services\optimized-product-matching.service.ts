import { Injectable, Logger, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Product } from '../entities/product.entity';
import { RiskAssessment } from '../entities/risk-assessment.entity';
import { User } from '../entities/user.entity';
import { MonitoringService } from '../services/monitoring.service';
import { GpuService } from '../services/gpu.service';
import {
  FeatureWeights,
  MatchScoreDetails,
  MatchedProduct,
  ProductMatchCriteria,
  LocationMatchRule,
  UserLocation
} from '../interfaces/matching.interface';

@Injectable()
export class OptimizedProductMatchingService {
  private readonly logger = new Logger(OptimizedProductMatchingService.name);
  private readonly BATCH_SIZE = 100;
  private readonly CACHE_TTL = 3600; // 1小时缓存
  
  private readonly MATCH_WEIGHTS = {
    amount: 0.30,    // 金额匹配权重
    term: 0.20,     // 期限匹配权重
    rate: 0.25,     // 利率匹配权重  
    features: 0.15,  // 特征匹配权重
    location: 0.10   // 位置匹配权重
  };

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(RiskAssessment)  
    private readonly riskRepository: Repository<RiskAssessment>,
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
    private readonly gpuService: GpuService,
    private readonly monitoringService: MonitoringService
  ) {}

  /**
   * 智能产品匹配主入口
   */
  async matchProducts(user: User, criteria: ProductMatchCriteria): Promise<MatchedProduct[]> {
    const startTime = Date.now();
    
    try {
      // 检查用户参数
      if (!user || !user.id) {
        throw new Error('用户信息无效');
      }
      
      // 检查缓存
      const cacheKey = this.generateCacheKey(user.id, criteria);
      const cached = await this.cacheManager.get<MatchedProduct[]>(cacheKey);
      if (cached) {
        return cached;
      }

      // 获取候选产品
      const products = await this.getEligibleProducts(criteria);
      if (!products.length) {
        return [];
      }

      // 获取用户风险评估
      const riskAssessment = await this.getRiskAssessment(user);

      let matchedProducts: MatchedProduct[];

      // 根据产品数量选择处理策略
      if (products.length >= this.BATCH_SIZE && this.gpuService) {
        try {
          matchedProducts = await this.performGpuMatching(products, criteria);
        } catch (error: any) {
          this.logger.warn('GPU匹配失败，回退到CPU处理', error.message);
          matchedProducts = await this.cpuMatchProducts(products, criteria);
        }
      } else {
        matchedProducts = await this.cpuMatchProducts(products, criteria);
      }

      // 后处理和排序
      const finalResults = this.postProcessResults(
        matchedProducts,
        riskAssessment,
        criteria
      );

      // 缓存结果
      await this.cacheManager.set(cacheKey, finalResults, this.CACHE_TTL);

      this.monitoringService.recordMetric('product_matching_duration', Date.now() - startTime);
      return finalResults;

    } catch (error: any) {
      this.logger.error('产品匹配失败', error);
      this.monitoringService.recordError('product_matching', error);
      throw error;
    }
  }

  private async getEligibleProducts(criteria: ProductMatchCriteria): Promise<Product[]> {
    const query = this.productRepository.createQueryBuilder('product')
      .where('product.isActive = :isActive', { isActive: true });

    if (criteria.amount) {
      query.andWhere('product.minAmount <= :amount AND product.maxAmount >= :amount', 
        { amount: criteria.amount });
    }

    if (criteria.term) {
      query.andWhere('product.minTerm <= :term AND product.maxTerm >= :term', 
        { term: criteria.term });
    }

    return query.getMany();
  }

  private async performGpuMatching(
    products: Product[],
    criteria: ProductMatchCriteria
  ): Promise<MatchedProduct[]> {
    this.monitoringService.recordMetric('gpu_matching_started', 1);
    const startTime = Date.now();
    
    try {
      // GPU批处理计算
      const scores = await this.gpuService.processMatchingBatch(
        products.map(p => ({ product: p, criteria: criteria }))
      );

      // 合并结果
      const results = products.map((product, index) => ({
        product,
        score: scores[index]?.matchScore || 0,
        details: this.calculateDetailScores(product, criteria)
      }));

      const processingTime = Date.now() - startTime;
      this.monitoringService.recordMetric('gpu_processing_time', processingTime);
      return results.sort((a, b) => b.score - a.score);

    } catch (error: any) {
      this.logger.error('GPU处理失败,回退到CPU', error);
      this.monitoringService.recordMetric('gpu_processing_error', 1);
      throw error;
    }
  }

  private async cpuMatchProducts(
    products: Product[],
    criteria: ProductMatchCriteria
  ): Promise<MatchedProduct[]> {
    const results: MatchedProduct[] = [];

    for (const product of products) {
      const details = this.calculateDetailScores(product, criteria);
      const score = this.calculateOverallScore(details);
      
      results.push({
        product,
        score,
        details
      });
    }

    return results.sort((a, b) => b.score - a.score);
  }

  private calculateDetailScores(
    product: Product,
    criteria: ProductMatchCriteria
  ): MatchScoreDetails {
    return {
      amountScore: this.calculateAmountScore(product, criteria.amount),
      termScore: this.calculateTermScore(product, criteria.term),
      rateScore: this.calculateRateScore(product),
      featureScore: this.calculateFeatureScore(product, criteria),
      locationScore: this.calculateLocationScore(product, criteria.location)
    };
  }

  private calculateOverallScore(details: MatchScoreDetails): number {
    return (
      details.amountScore * this.MATCH_WEIGHTS.amount +
      details.termScore * this.MATCH_WEIGHTS.term +
      details.rateScore * this.MATCH_WEIGHTS.rate +
      details.featureScore * this.MATCH_WEIGHTS.features +
      details.locationScore * this.MATCH_WEIGHTS.location
    );
  }
  private calculateAmountScore(product: Product, amount?: number): number {
    if (!amount || !product.minAmount || !product.maxAmount) {
      return 0.5;
    }

    if (amount < product.minAmount || amount > product.maxAmount) {
      return 0;
    }

    const range = product.maxAmount - product.minAmount;
    const position = (amount - product.minAmount) / range;
    return 1 - Math.abs(0.5 - position);
  }

  private calculateTermScore(product: Product, term?: number): number {
    if (!term || !product.minTerm || !product.maxTerm) {
      return 0.5;
    }

    if (term < product.minTerm || term > product.maxTerm) {
      return 0;
    }

    const range = product.maxTerm - product.minTerm;
    const position = (term - product.minTerm) / range;
    return 1 - Math.abs(0.5 - position);
  }

  private calculateRateScore(product: Product): number {
    if (typeof product.interestRate !== 'number') {
      return 0.5;
    }

    const marketAverageRate = 8.5; // 市场平均利率
    const rateDiff = marketAverageRate - product.interestRate;
    return Math.min(1, Math.max(0, 0.5 + rateDiff * 0.1));
  }

  private calculateFeatureScore(product: Product, criteria: ProductMatchCriteria): number {
    if (!product.features?.length) {
      return 0.5;
    }

    try {
      this.monitoringService.recordMetric('feature_matching_started', 1);
      
      const features = product.features;
      let score = 0.5;

      if (criteria.features?.length) {
        const hasRequiredFeatures = criteria.features.every(f =>
          features.some(pf => pf.name === f)
        );
        
        if (hasRequiredFeatures) {
          score += 0.3;
        }

        const extraFeatures = features.filter(f => !criteria.features?.includes(f.name));
        score += extraFeatures.length * 0.05;
      }

      this.monitoringService.recordMetric(
        'feature_match_calculation_success', 1
      );
      
      return Math.min(1, score);
      
    } catch (error: any) {
      this.logger.error('特征匹配计算错误', error);
      this.monitoringService.recordMetric('feature_matching_error', 1);
      return 0.5;
    }
  }

  private calculateLocationScore(product: Product, location?: string): number {
    if (!product.requirements || !location) {
      return 0.8;
    }

    try {
      this.monitoringService.recordMetric('location_matching_started', 1);
      
      const locationRules = this.parseLocationRules(product.requirements);
      const userLocation = this.parseUserLocation(location);
      
      let maxScore = 0;
      for (const rule of locationRules) {
        if (this.matchLocationRule(userLocation, rule)) {
          maxScore = Math.max(maxScore, rule.score);
        }
      }

      this.monitoringService.recordMetric(
        'location_match_calculation_success', 1
      );
      this.monitoringService.recordMetric('location_match_score', maxScore);
      
      return maxScore;
      
    } catch (error: any) {
      this.logger.error('位置匹配计算错误', error);
      this.monitoringService.recordMetric('location_matching_error', 1);
      return 0.8;
    }
  }

  private parseLocationRules(requirements: any): LocationMatchRule[] {
    const rules: LocationMatchRule[] = [];
    
    if (requirements.location?.exact?.length) {
      rules.push({
        type: 'exact',
        locations: requirements.location.exact,
        score: 1.0
      });
    }
    
    if (requirements.location?.city?.length) {
      rules.push({
        type: 'city',
        locations: requirements.location.city,
        score: 0.8
      });
    }
    
    if (requirements.location?.province?.length) {
      rules.push({
        type: 'province',
        locations: requirements.location.province,
        score: 0.6
      });
    }
    
    return rules;
  }

  private parseUserLocation(location: string): UserLocation {
    const [province, city, district] = location.split('-');
    return { province, city, district };
  }

  private matchLocationRule(
    userLocation: UserLocation,
    rule: LocationMatchRule
  ): boolean {
    switch (rule.type) {
      case 'exact':
        return rule.locations.includes(
          `${userLocation.province}-${userLocation.city}-${userLocation.district}`
        );
      case 'city':
        return rule.locations.includes(
          `${userLocation.province}-${userLocation.city}`
        );
      case 'province':
        return rule.locations.includes(userLocation.province);
      default:
        return false;
    }
  }

  private postProcessResults(
    matchedProducts: MatchedProduct[],
    riskAssessment: RiskAssessment | null,
    criteria: ProductMatchCriteria
  ): MatchedProduct[] {
    // 基于风险评估过滤产品
    const filteredProducts = matchedProducts.filter(match => {
      const product = match.product;
      return this.isProductSuitableForRisk(product, riskAssessment);
    });

    // 调整分数
    return filteredProducts.map(match => ({
      ...match,
      score: this.calculateFinalScore(match, riskAssessment)
    })).sort((a, b) => b.score - a.score);
  }

  private calculateFinalScore(
    match: MatchedProduct,
    riskAssessment: RiskAssessment | null
  ): number {
    const baseScore = match.score;
    const product = match.product;

    const riskPenalty = this.calculateRiskPenalty(product, riskAssessment);
    return Math.max(0, baseScore - riskPenalty);
  }

  private calculateRiskPenalty(
    product: Product,
    riskAssessment: RiskAssessment | null
  ): number {
    if (!riskAssessment) return 0;

    // 简化的风险惩罚计算
    const riskScore = riskAssessment.riskScore || 0;
    const penalty = riskScore > 50 ? 0.2 : 0;
    return penalty;
  }

  private isProductSuitableForRisk(
    product: Product,
    riskAssessment: RiskAssessment | null
  ): boolean {
    if (!riskAssessment) return true;

    // 简化的风险适应性检查
    const riskScore = riskAssessment.riskScore || 0;
    return riskScore <= 80; // 风险分数低于80的可以通过
  }

  private generateCacheKey(userId: string, criteria: ProductMatchCriteria): string {
    const criteriaHash = JSON.stringify(criteria);
    return `product_match:${userId}:${Buffer.from(criteriaHash).toString('base64')}`;
  }

  private async getRiskAssessment(user: User): Promise<RiskAssessment | null> {
    try {
      const cacheKey = `risk:${user.id}`;
      const cachedAssessment = await this.cacheManager.get<RiskAssessment>(cacheKey);
      if (cachedAssessment) {
        return cachedAssessment;
      }

      const assessment = await this.riskRepository.createQueryBuilder('risk')
        .where('risk.applicationId = :userId', { userId: user.id })
        .orderBy('risk.createdAt', 'DESC')
        .getOne();

      if (assessment) {
        await this.cacheManager.set(cacheKey, assessment, 3600);
      }

      return assessment;
    } catch (error: any) {
      this.logger.error('获取风险评估失败', error);
      return null;
    }
  }
}
    this.monitoringService?.recordMetric('product_matching_started', 1);

    try {
      // 1. 从缓存获取匹配结果
      if (!user || !user.id) {
        throw new Error('用户信息无效');
      }
      const cacheKey = this.generateCacheKey(user.id, criteria);
      const cachedResult = await this.cacheManager.get<MatchedProduct[]>(cacheKey);
      
      if (cachedResult) {
        this.logger.log('从缓存获取匹配结果');
        return cachedResult;
      }

      // 2. 预筛选符合条件的产品
      const products = await this.loadAndPrefilterProducts(criteria);
      
      // 3. 获取用户风险评估
      const riskAssessment = await this.getRiskAssessment(user);

      // 4. 计算匹配度
      let matchedProducts: MatchedProduct[];
      
      if (products.length > this.BATCH_SIZE) {
        // 使用GPU批量计算
        matchedProducts = await this.gpuMatchProducts(user, products, criteria);
      } else {
        // 使用CPU计算
        matchedProducts = await this.cpuMatchProducts(user, products, criteria);
      }

      // 5. 进行后处理
      const finalResults = this.postProcessResults(
        matchedProducts,
        riskAssessment,
        criteria
      );

      // 6. 缓存结果
      await this.cacheManager.set(cacheKey, finalResults, this.CACHE_TTL);

      // 7. 记录性能指标
      const processingTime = Date.now() - startTime;
      this.monitoringService.recordMetric('product_matching_time', processingTime);
      this.monitoringService.recordMetric('matched_products_count', finalResults.length);

      return finalResults;
    } catch (error) {
      this.logger.error('产品匹配失败', error);
      this.monitoringService.recordMetric('product_matching_error', 1);
      throw error;
    }
  }

  /**
   * 预筛选产品
   */
  private async loadAndPrefilterProducts(criteria: ProductMatchCriteria): Promise<Product[]> {
    const startTime = Date.now();
    
    const queryBuilder = this.productRepository.createQueryBuilder('product')
      .where('product.status = :status', { status: 'active' })
      .andWhere('product.minAmount <= :amount', { amount: criteria.amount })
      .andWhere('product.maxAmount >= :amount', { amount: criteria.amount })
      .andWhere('product.minTerm <= :term', { term: criteria.term })
      .andWhere('product.maxTerm >= :term', { term: criteria.term });

    // 添加位置过滤
    if (criteria.location) {
      queryBuilder.andWhere(
        'product.requirements @> :location',
        { location: { location: criteria.location } }
      );
    }

    // 使用索引优化的排序
    queryBuilder
      .orderBy('product.popularityScore', 'DESC')
      .addOrderBy('product.interestRate', 'ASC')
      .take(1000); // 限制最大结果数

    const products = await queryBuilder.getMany();
    
    const processingTime = Date.now() - startTime;
    this.monitoringService.recordMetric('product_prefilter_time', processingTime);
    this.monitoringService.recordMetric('prefiltered_products_count', products.length);

    return products;
  }

  /**
   * CPU匹配产品
   */
  private async cpuMatchProducts(
    user: User,
    products: Product[],
    criteria: ProductMatchCriteria
  ): Promise<MatchedProduct[]> {
    this.monitoringService.recordMetric('cpu_matching_started', 1);
    const startTime = Date.now();

    // 并行处理产品批次
    const batches = this.chunkArray(products, this.BATCH_SIZE);
    const scoredProducts = await Promise.all(
      batches.map(async batch => {
        return Promise.all(
          batch.map(async product => {
            const amountScore = this.calculateAmountScore(product, criteria.amount);
            const termScore = this.calculateTermScore(product, criteria.term);
            const rateScore = this.calculateRateScore(product);
            const featureScore = this.calculateFeatureScore(product, criteria);
            const locationScore = this.calculateLocationScore(product, criteria.location);

            const totalScore = 
              amountScore * this.MATCH_WEIGHTS.amount +
              termScore * this.MATCH_WEIGHTS.term +
              rateScore * this.MATCH_WEIGHTS.rate +
              featureScore * this.MATCH_WEIGHTS.features +
              locationScore * this.MATCH_WEIGHTS.location;

            return {
              product,
              score: totalScore,
              details: {
                amountScore,
                termScore,
                rateScore,
                featureScore,
                locationScore
              }
            };
          })
        );
      })
    );

    const processingTime = Date.now() - startTime;
    this.monitoringService.recordMetric('cpu_processing_time', processingTime);
    
    return scoredProducts.flat().sort((a, b) => b.score - a.score);
  }

  /**
   * GPU匹配产品
   */
  private async gpuMatchProducts(
    user: User,
      private async performGpuMatching(
    products: Product[],
    criteria: ProductMatchCriteria
  ): Promise<MatchedProduct[]> {
    this.monitoringService.recordMetric('gpu_matching_started', 1);
    const startTime = Date.now();
    
    try {
      // 准备数据为GPU友好格式
      const productTensor = await this.gpuService.prepareProductTensor(products);
      const criteriaTensor = await this.gpuService.prepareCriteriaTensor(criteria);

      // GPU批处理计算
      const scores = await this.gpuService.processMatchingBatch(
        products.map(p => ({ product: p, criteria: criteria }))
      );

      // 合并结果
      const results = products.map((product, index) => ({
        product,
        score: scores[index]?.matchScore || 0,
        details: this.calculateDetailScores(product, criteria)
      }));

      const processingTime = Date.now() - startTime;
      this.monitoringService.recordMetric('gpu_processing_time', processingTime);
      
      return results.sort((a, b) => b.score - a.score);
    } catch (error) {
      this.logger.error('GPU处理失败,回退到CPU', error);
      this.monitoringService.recordMetric('gpu_processing_error', 1);
      return this.cpuMatchProducts(user, products, criteria);
    }
  }

  /**
   * 后处理匹配结果
   */
  private postProcessResults(
    matchedProducts: MatchedProduct[],
    riskAssessment: RiskAssessment,
    criteria: ProductMatchCriteria
  ): MatchedProduct[] {
    // 根据用户风险等级筛选
    const filteredProducts = matchedProducts.filter(match => {
      const product = match.product;
      return this.isProductSuitableForRisk(product, riskAssessment);
    });

    // 对结果重新排序,考虑转化率和下款率
    return filteredProducts
      .map(match => ({
        ...match,
        score: this.calculateFinalScore(match, riskAssessment)
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 10); // 只返回前10个最佳匹配
  }

  /**
   * 计算最终分数
   */
  private calculateFinalScore(
    match: MatchedProduct,
    riskAssessment: RiskAssessment
  ): number {
    const baseScore = match.score;
    const product = match.product;
    
    // 考虑历史转化率
    const conversionBonus = product.popularityScore * 0.1;
    
    // 考虑风险匹配度
    const riskPenalty = this.calculateRiskPenalty(product, riskAssessment);
    
    return Math.min(1, baseScore + conversionBonus - riskPenalty);
  }

  /**
   * 计算风险惩罚因子
   */
  private calculateRiskPenalty(
    product: Product,
    riskAssessment: RiskAssessment
  ): number {
    // TODO: 基于风险评估和产品风险等级计算惩罚因子
    return 0;
  }

  /**
   * 检查产品是否符合风险评级
   */
  private isProductSuitableForRisk(
    product: Product,
    riskAssessment: RiskAssessment
  ): boolean {
    // TODO: 实现风险匹配逻辑
    return true;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(userId: string, criteria: ProductMatchCriteria): string {
    const criteriaHash = JSON.stringify(criteria);
    return `product_match:${userId}:${criteriaHash}`;
  }

  /**
   * 辅助方法:数组分块
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 计算贷款金额匹配得分
   */
  private calculateAmountScore(product: Product, amount: number): number {
    if (!product.minAmount || !product.maxAmount) {
      return 0;
    }

    const range = product.maxAmount - product.minAmount;
    if (range <= 0) {
      return amount === product.minAmount ? 1 : 0;
    }

    // 判断金额是否在范围内
    if (amount < product.minAmount || amount > product.maxAmount) {
      return 0;
    }

    // 计算相对位置得分(距离中间值越近得分越高)
    const position = (amount - product.minAmount) / range;
    return 1 - Math.abs(position - 0.5) * 2;
  }

  /**
   * 计算贷款期限匹配得分
   */
  private calculateTermScore(product: Product, term: number): number {
    if (!product.minTerm || !product.maxTerm) {
      return 0;
    }

    const range = product.maxTerm - product.minTerm;
    if (range <= 0) {
      return term === product.minTerm ? 1 : 0;
    }

    // 判断期限是否在范围内
    if (term < product.minTerm || term > product.maxTerm) {
      return 0;
    }

    // 计算相对位置得分
    const position = (term - product.minTerm) / range;
    return 1 - Math.abs(position - 0.5) * 2;
  }

  /**
   * 计算利率匹配得分
   */
  private calculateRateScore(product: Product): number {
    if (typeof product.interestRate !== 'number') {
      return 0;
    }

    // 假设市场平均利率为5%
    const marketAverageRate = 5;
    const rateDiff = marketAverageRate - product.interestRate;

    // 利率越低得分越高,与市场平均利率的偏差越小得分越高
    return Math.max(0, 1 - Math.abs(rateDiff) / marketAverageRate);
  }

  /**
   * 计算特征匹配得分
   */
  private calculateFeatureScore(product: Product, criteria: ProductMatchCriteria): number {
    if (!product.features?.length) {
      return 0;
    }

    let score = 0;
    const features = product.features;

    // 特征匹配评分
    this.monitoringService.recordMetric('feature_matching_started', 1);
    const startTime = Date.now();

    try {
      // 必要特征匹配
      const hasRequiredFeatures = criteria.features?.every(f =>
        features.some(pf => pf.name === f)
      );
      if (hasRequiredFeatures) {
        score += 0.6; // 基础分60%
      }

      // 额外特征加分
      const extraFeatures = features.filter(f => !criteria.features?.includes(f.name));
      score += Math.min(0.4, extraFeatures.length * 0.1); // 每个额外特征加10%,最多40%

      this.monitoringService.recordMetric(
        'feature_matching_time',
        Date.now() - startTime
      );

      return score;
    } catch (error) {
      this.logger.error('特征匹配计算错误', error);
      this.monitoringService.recordMetric('feature_matching_error', 1);
      return 0;
    }
  }

  /**
   * 计算位置匹配得分
   */
  private calculateLocationScore(product: Product, location?: string): number {
    if (!product.requirements || !location) {
      return 0;
    }

    try {
      this.monitoringService.recordMetric('location_matching_started', 1);
      const startTime = Date.now();

      // 从缓存获取位置规则
      const cacheKey = `location_rules:${product.id}`;
      const locationRules = this.parseLocationRules(product.requirements);
      
      const userLocation = this.parseUserLocation(location);
      let maxScore = 0;

      // 应用位置匹配规则
      for (const rule of locationRules) {
        if (this.matchLocationRule(userLocation, rule)) {
          maxScore = Math.max(maxScore, rule.score);
        }
      }

      // 记录性能指标
      this.monitoringService.recordMetric(
        'location_matching_time',
        Date.now() - startTime
      );
      this.monitoringService.recordMetric('location_match_score', maxScore);

      return maxScore;
    } catch (error) {
      this.logger.error('位置匹配计算错误', error);
      this.monitoringService.recordMetric('location_matching_error', 1);
      return 0;
    }
  }

  /**
   * 解析位置匹配规则
   */
  private parseLocationRules(requirements: any): LocationMatchRule[] {
    const rules: LocationMatchRule[] = [];

    // 完全匹配规则(区县级)
    if (requirements.location?.exact?.length) {
      rules.push({
        type: 'exact',
        locations: requirements.location.exact,
        score: 1.0
      });
    }

    // 城市级匹配规则 
    if (requirements.location?.city?.length) {
      rules.push({
        type: 'city',
        locations: requirements.location.city,
        score: 0.8
      });
    }

    // 省级匹配规则
    if (requirements.location?.province?.length) {
      rules.push({
        type: 'province',
        locations: requirements.location.province,
        score: 0.6
      });
    }

    return rules;
  }

  /**
   * 解析用户位置信息
   */
  private parseUserLocation(location: string): UserLocation {
    const [province, city, district] = location.split('-');
    return {
      province,
      city: city || '',
      district: district || ''
    };
  }

  /**
   * 匹配位置规则
   */
  private matchLocationRule(
    userLocation: UserLocation,
    rule: LocationMatchRule
  ): boolean {
    switch (rule.type) {
      case 'exact':
        return rule.locations.includes(
          `${userLocation.province}-${userLocation.city}-${userLocation.district}`
        );
      case 'city':
        return rule.locations.includes(
          `${userLocation.province}-${userLocation.city}`
        );
      case 'province':
        return rule.locations.includes(userLocation.province);
      default:
        return false;
    }
  }

  /**
   * 计算得分明细
   */
  private calculateDetailScores(
    product: Product,
    criteria: ProductMatchCriteria
  ): MatchScoreDetails {
    return {
      amountScore: this.calculateAmountScore(product, criteria.amount),
      termScore: this.calculateTermScore(product, criteria.term),
      rateScore: this.calculateRateScore(product),
      featureScore: this.calculateFeatureScore(product, criteria),
      locationScore: this.calculateLocationScore(product, criteria.location)
    };
  }

  /**
   * 获取用户风险评估
   */
  private async getRiskAssessment(user: User): Promise<RiskAssessment | null> {
    const cacheKey = `risk:${user.id}`;
    const cachedAssessment = await this.cacheManager.get<RiskAssessment>(cacheKey);
    
    if (cachedAssessment) {
      return cachedAssessment;
    }    const assessment = await this.riskRepository.createQueryBuilder('risk')
      .where('risk.applicationId = :userId', { userId: user.id })
      .orderBy('risk.createdAt', 'DESC')
      .getOne();

    if (assessment) {
      await this.cacheManager.set(cacheKey, assessment, 3600);
    }

    return assessment;
  }
