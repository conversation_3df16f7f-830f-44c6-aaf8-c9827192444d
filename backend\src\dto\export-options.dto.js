"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportColumnDto = exports.ExportOptionsDto = exports.ExportFormat = void 0;
var class_validator_1 = require("class-validator");
var class_transformer_1 = require("class-transformer");
var ExportFormat;
(function (ExportFormat) {
    ExportFormat["EXCEL"] = "excel";
    ExportFormat["PDF"] = "pdf";
    ExportFormat["CSV"] = "csv";
    ExportFormat["JSON"] = "json";
    ExportFormat["XML"] = "xml";
    ExportFormat["HTML"] = "html";
})(ExportFormat || (exports.ExportFormat = ExportFormat = {}));
var ExportOptionsDto = function () {
    var _a;
    var _format_decorators;
    var _format_initializers = [];
    var _format_extraInitializers = [];
    var _template_decorators;
    var _template_initializers = [];
    var _template_extraInitializers = [];
    var _filename_decorators;
    var _filename_initializers = [];
    var _filename_extraInitializers = [];
    var _fields_decorators;
    var _fields_initializers = [];
    var _fields_extraInitializers = [];
    var _filters_decorators;
    var _filters_initializers = [];
    var _filters_extraInitializers = [];
    var _includeHeaders_decorators;
    var _includeHeaders_initializers = [];
    var _includeHeaders_extraInitializers = [];
    var _includeMetadata_decorators;
    var _includeMetadata_initializers = [];
    var _includeMetadata_extraInitializers = [];
    var _pageSize_decorators;
    var _pageSize_initializers = [];
    var _pageSize_extraInitializers = [];
    var _page_decorators;
    var _page_initializers = [];
    var _page_extraInitializers = [];
    var _sortBy_decorators;
    var _sortBy_initializers = [];
    var _sortBy_extraInitializers = [];
    var _sortOrder_decorators;
    var _sortOrder_initializers = [];
    var _sortOrder_extraInitializers = [];
    var _encrypt_decorators;
    var _encrypt_initializers = [];
    var _encrypt_extraInitializers = [];
    var _password_decorators;
    var _password_initializers = [];
    var _password_extraInitializers = [];
    var _compress_decorators;
    var _compress_initializers = [];
    var _compress_extraInitializers = [];
    var _language_decorators;
    var _language_initializers = [];
    var _language_extraInitializers = [];
    var _styling_decorators;
    var _styling_initializers = [];
    var _styling_extraInitializers = [];
    var _layout_decorators;
    var _layout_initializers = [];
    var _layout_extraInitializers = [];
    var _columns_decorators;
    var _columns_initializers = [];
    var _columns_extraInitializers = [];
    return _a = /** @class */ (function () {
            function ExportOptionsDto() {
                this.format = __runInitializers(this, _format_initializers, void 0);
                this.template = (__runInitializers(this, _format_extraInitializers), __runInitializers(this, _template_initializers, void 0));
                this.filename = (__runInitializers(this, _template_extraInitializers), __runInitializers(this, _filename_initializers, void 0));
                this.fields = (__runInitializers(this, _filename_extraInitializers), __runInitializers(this, _fields_initializers, void 0));
                this.filters = (__runInitializers(this, _fields_extraInitializers), __runInitializers(this, _filters_initializers, void 0));
                this.includeHeaders = (__runInitializers(this, _filters_extraInitializers), __runInitializers(this, _includeHeaders_initializers, void 0));
                this.includeMetadata = (__runInitializers(this, _includeHeaders_extraInitializers), __runInitializers(this, _includeMetadata_initializers, void 0));
                this.pageSize = (__runInitializers(this, _includeMetadata_extraInitializers), __runInitializers(this, _pageSize_initializers, void 0));
                this.page = (__runInitializers(this, _pageSize_extraInitializers), __runInitializers(this, _page_initializers, void 0));
                this.sortBy = (__runInitializers(this, _page_extraInitializers), __runInitializers(this, _sortBy_initializers, void 0));
                this.sortOrder = (__runInitializers(this, _sortBy_extraInitializers), __runInitializers(this, _sortOrder_initializers, void 0));
                this.encrypt = (__runInitializers(this, _sortOrder_extraInitializers), __runInitializers(this, _encrypt_initializers, void 0));
                this.password = (__runInitializers(this, _encrypt_extraInitializers), __runInitializers(this, _password_initializers, void 0));
                this.compress = (__runInitializers(this, _password_extraInitializers), __runInitializers(this, _compress_initializers, void 0));
                this.language = (__runInitializers(this, _compress_extraInitializers), __runInitializers(this, _language_initializers, void 0));
                this.styling = (__runInitializers(this, _language_extraInitializers), __runInitializers(this, _styling_initializers, void 0));
                this.layout = (__runInitializers(this, _styling_extraInitializers), __runInitializers(this, _layout_initializers, void 0));
                this.columns = (__runInitializers(this, _layout_extraInitializers), __runInitializers(this, _columns_initializers, void 0));
                __runInitializers(this, _columns_extraInitializers);
            }
            return ExportOptionsDto;
        }()),
        (function () {
            var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            _format_decorators = [(0, class_validator_1.IsEnum)(ExportFormat)];
            _template_decorators = [(0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _filename_decorators = [(0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _fields_decorators = [(0, class_validator_1.IsArray)(), (0, class_validator_1.IsOptional)()];
            _filters_decorators = [(0, class_validator_1.IsObject)(), (0, class_validator_1.IsOptional)()];
            _includeHeaders_decorators = [(0, class_validator_1.IsBoolean)(), (0, class_validator_1.IsOptional)()];
            _includeMetadata_decorators = [(0, class_validator_1.IsBoolean)(), (0, class_validator_1.IsOptional)()];
            _pageSize_decorators = [(0, class_validator_1.IsNumber)(), (0, class_validator_1.IsOptional)(), (0, class_validator_1.Min)(1), (0, class_validator_1.Max)(10000)];
            _page_decorators = [(0, class_validator_1.IsNumber)(), (0, class_validator_1.IsOptional)(), (0, class_validator_1.Min)(1)];
            _sortBy_decorators = [(0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _sortOrder_decorators = [(0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _encrypt_decorators = [(0, class_validator_1.IsBoolean)(), (0, class_validator_1.IsOptional)()];
            _password_decorators = [(0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _compress_decorators = [(0, class_validator_1.IsBoolean)(), (0, class_validator_1.IsOptional)()];
            _language_decorators = [(0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _styling_decorators = [(0, class_validator_1.IsObject)(), (0, class_validator_1.IsOptional)()];
            _layout_decorators = [(0, class_validator_1.IsObject)(), (0, class_validator_1.IsOptional)()];
            _columns_decorators = [(0, class_validator_1.IsArray)(), (0, class_validator_1.IsOptional)(), (0, class_validator_1.ValidateNested)({ each: true }), (0, class_transformer_1.Type)(function () { return ExportColumnDto; })];
            __esDecorate(null, null, _format_decorators, { kind: "field", name: "format", static: false, private: false, access: { has: function (obj) { return "format" in obj; }, get: function (obj) { return obj.format; }, set: function (obj, value) { obj.format = value; } }, metadata: _metadata }, _format_initializers, _format_extraInitializers);
            __esDecorate(null, null, _template_decorators, { kind: "field", name: "template", static: false, private: false, access: { has: function (obj) { return "template" in obj; }, get: function (obj) { return obj.template; }, set: function (obj, value) { obj.template = value; } }, metadata: _metadata }, _template_initializers, _template_extraInitializers);
            __esDecorate(null, null, _filename_decorators, { kind: "field", name: "filename", static: false, private: false, access: { has: function (obj) { return "filename" in obj; }, get: function (obj) { return obj.filename; }, set: function (obj, value) { obj.filename = value; } }, metadata: _metadata }, _filename_initializers, _filename_extraInitializers);
            __esDecorate(null, null, _fields_decorators, { kind: "field", name: "fields", static: false, private: false, access: { has: function (obj) { return "fields" in obj; }, get: function (obj) { return obj.fields; }, set: function (obj, value) { obj.fields = value; } }, metadata: _metadata }, _fields_initializers, _fields_extraInitializers);
            __esDecorate(null, null, _filters_decorators, { kind: "field", name: "filters", static: false, private: false, access: { has: function (obj) { return "filters" in obj; }, get: function (obj) { return obj.filters; }, set: function (obj, value) { obj.filters = value; } }, metadata: _metadata }, _filters_initializers, _filters_extraInitializers);
            __esDecorate(null, null, _includeHeaders_decorators, { kind: "field", name: "includeHeaders", static: false, private: false, access: { has: function (obj) { return "includeHeaders" in obj; }, get: function (obj) { return obj.includeHeaders; }, set: function (obj, value) { obj.includeHeaders = value; } }, metadata: _metadata }, _includeHeaders_initializers, _includeHeaders_extraInitializers);
            __esDecorate(null, null, _includeMetadata_decorators, { kind: "field", name: "includeMetadata", static: false, private: false, access: { has: function (obj) { return "includeMetadata" in obj; }, get: function (obj) { return obj.includeMetadata; }, set: function (obj, value) { obj.includeMetadata = value; } }, metadata: _metadata }, _includeMetadata_initializers, _includeMetadata_extraInitializers);
            __esDecorate(null, null, _pageSize_decorators, { kind: "field", name: "pageSize", static: false, private: false, access: { has: function (obj) { return "pageSize" in obj; }, get: function (obj) { return obj.pageSize; }, set: function (obj, value) { obj.pageSize = value; } }, metadata: _metadata }, _pageSize_initializers, _pageSize_extraInitializers);
            __esDecorate(null, null, _page_decorators, { kind: "field", name: "page", static: false, private: false, access: { has: function (obj) { return "page" in obj; }, get: function (obj) { return obj.page; }, set: function (obj, value) { obj.page = value; } }, metadata: _metadata }, _page_initializers, _page_extraInitializers);
            __esDecorate(null, null, _sortBy_decorators, { kind: "field", name: "sortBy", static: false, private: false, access: { has: function (obj) { return "sortBy" in obj; }, get: function (obj) { return obj.sortBy; }, set: function (obj, value) { obj.sortBy = value; } }, metadata: _metadata }, _sortBy_initializers, _sortBy_extraInitializers);
            __esDecorate(null, null, _sortOrder_decorators, { kind: "field", name: "sortOrder", static: false, private: false, access: { has: function (obj) { return "sortOrder" in obj; }, get: function (obj) { return obj.sortOrder; }, set: function (obj, value) { obj.sortOrder = value; } }, metadata: _metadata }, _sortOrder_initializers, _sortOrder_extraInitializers);
            __esDecorate(null, null, _encrypt_decorators, { kind: "field", name: "encrypt", static: false, private: false, access: { has: function (obj) { return "encrypt" in obj; }, get: function (obj) { return obj.encrypt; }, set: function (obj, value) { obj.encrypt = value; } }, metadata: _metadata }, _encrypt_initializers, _encrypt_extraInitializers);
            __esDecorate(null, null, _password_decorators, { kind: "field", name: "password", static: false, private: false, access: { has: function (obj) { return "password" in obj; }, get: function (obj) { return obj.password; }, set: function (obj, value) { obj.password = value; } }, metadata: _metadata }, _password_initializers, _password_extraInitializers);
            __esDecorate(null, null, _compress_decorators, { kind: "field", name: "compress", static: false, private: false, access: { has: function (obj) { return "compress" in obj; }, get: function (obj) { return obj.compress; }, set: function (obj, value) { obj.compress = value; } }, metadata: _metadata }, _compress_initializers, _compress_extraInitializers);
            __esDecorate(null, null, _language_decorators, { kind: "field", name: "language", static: false, private: false, access: { has: function (obj) { return "language" in obj; }, get: function (obj) { return obj.language; }, set: function (obj, value) { obj.language = value; } }, metadata: _metadata }, _language_initializers, _language_extraInitializers);
            __esDecorate(null, null, _styling_decorators, { kind: "field", name: "styling", static: false, private: false, access: { has: function (obj) { return "styling" in obj; }, get: function (obj) { return obj.styling; }, set: function (obj, value) { obj.styling = value; } }, metadata: _metadata }, _styling_initializers, _styling_extraInitializers);
            __esDecorate(null, null, _layout_decorators, { kind: "field", name: "layout", static: false, private: false, access: { has: function (obj) { return "layout" in obj; }, get: function (obj) { return obj.layout; }, set: function (obj, value) { obj.layout = value; } }, metadata: _metadata }, _layout_initializers, _layout_extraInitializers);
            __esDecorate(null, null, _columns_decorators, { kind: "field", name: "columns", static: false, private: false, access: { has: function (obj) { return "columns" in obj; }, get: function (obj) { return obj.columns; }, set: function (obj, value) { obj.columns = value; } }, metadata: _metadata }, _columns_initializers, _columns_extraInitializers);
            if (_metadata) Object.defineProperty(_a, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        })(),
        _a;
}();
exports.ExportOptionsDto = ExportOptionsDto;
var ExportColumnDto = function () {
    var _a;
    var _field_decorators;
    var _field_initializers = [];
    var _field_extraInitializers = [];
    var _header_decorators;
    var _header_initializers = [];
    var _header_extraInitializers = [];
    var _format_decorators;
    var _format_initializers = [];
    var _format_extraInitializers = [];
    var _width_decorators;
    var _width_initializers = [];
    var _width_extraInitializers = [];
    var _visible_decorators;
    var _visible_initializers = [];
    var _visible_extraInitializers = [];
    var _alignment_decorators;
    var _alignment_initializers = [];
    var _alignment_extraInitializers = [];
    var _type_decorators;
    var _type_initializers = [];
    var _type_extraInitializers = [];
    var _style_decorators;
    var _style_initializers = [];
    var _style_extraInitializers = [];
    return _a = /** @class */ (function () {
            function ExportColumnDto() {
                this.field = __runInitializers(this, _field_initializers, void 0);
                this.header = (__runInitializers(this, _field_extraInitializers), __runInitializers(this, _header_initializers, void 0));
                this.format = (__runInitializers(this, _header_extraInitializers), __runInitializers(this, _format_initializers, void 0));
                this.width = (__runInitializers(this, _format_extraInitializers), __runInitializers(this, _width_initializers, void 0));
                this.visible = (__runInitializers(this, _width_extraInitializers), __runInitializers(this, _visible_initializers, void 0));
                this.alignment = (__runInitializers(this, _visible_extraInitializers), __runInitializers(this, _alignment_initializers, void 0));
                this.type = (__runInitializers(this, _alignment_extraInitializers), __runInitializers(this, _type_initializers, void 0));
                this.style = (__runInitializers(this, _type_extraInitializers), __runInitializers(this, _style_initializers, void 0));
                __runInitializers(this, _style_extraInitializers);
            }
            return ExportColumnDto;
        }()),
        (function () {
            var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            _field_decorators = [(0, class_validator_1.IsString)()];
            _header_decorators = [(0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _format_decorators = [(0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _width_decorators = [(0, class_validator_1.IsNumber)(), (0, class_validator_1.IsOptional)()];
            _visible_decorators = [(0, class_validator_1.IsBoolean)(), (0, class_validator_1.IsOptional)()];
            _alignment_decorators = [(0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _type_decorators = [(0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _style_decorators = [(0, class_validator_1.IsObject)(), (0, class_validator_1.IsOptional)()];
            __esDecorate(null, null, _field_decorators, { kind: "field", name: "field", static: false, private: false, access: { has: function (obj) { return "field" in obj; }, get: function (obj) { return obj.field; }, set: function (obj, value) { obj.field = value; } }, metadata: _metadata }, _field_initializers, _field_extraInitializers);
            __esDecorate(null, null, _header_decorators, { kind: "field", name: "header", static: false, private: false, access: { has: function (obj) { return "header" in obj; }, get: function (obj) { return obj.header; }, set: function (obj, value) { obj.header = value; } }, metadata: _metadata }, _header_initializers, _header_extraInitializers);
            __esDecorate(null, null, _format_decorators, { kind: "field", name: "format", static: false, private: false, access: { has: function (obj) { return "format" in obj; }, get: function (obj) { return obj.format; }, set: function (obj, value) { obj.format = value; } }, metadata: _metadata }, _format_initializers, _format_extraInitializers);
            __esDecorate(null, null, _width_decorators, { kind: "field", name: "width", static: false, private: false, access: { has: function (obj) { return "width" in obj; }, get: function (obj) { return obj.width; }, set: function (obj, value) { obj.width = value; } }, metadata: _metadata }, _width_initializers, _width_extraInitializers);
            __esDecorate(null, null, _visible_decorators, { kind: "field", name: "visible", static: false, private: false, access: { has: function (obj) { return "visible" in obj; }, get: function (obj) { return obj.visible; }, set: function (obj, value) { obj.visible = value; } }, metadata: _metadata }, _visible_initializers, _visible_extraInitializers);
            __esDecorate(null, null, _alignment_decorators, { kind: "field", name: "alignment", static: false, private: false, access: { has: function (obj) { return "alignment" in obj; }, get: function (obj) { return obj.alignment; }, set: function (obj, value) { obj.alignment = value; } }, metadata: _metadata }, _alignment_initializers, _alignment_extraInitializers);
            __esDecorate(null, null, _type_decorators, { kind: "field", name: "type", static: false, private: false, access: { has: function (obj) { return "type" in obj; }, get: function (obj) { return obj.type; }, set: function (obj, value) { obj.type = value; } }, metadata: _metadata }, _type_initializers, _type_extraInitializers);
            __esDecorate(null, null, _style_decorators, { kind: "field", name: "style", static: false, private: false, access: { has: function (obj) { return "style" in obj; }, get: function (obj) { return obj.style; }, set: function (obj, value) { obj.style = value; } }, metadata: _metadata }, _style_initializers, _style_extraInitializers);
            if (_metadata) Object.defineProperty(_a, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        })(),
        _a;
}();
exports.ExportColumnDto = ExportColumnDto;
