import { Repository } from 'typeorm';
import { Product } from '../entities/product.entity';
import { CacheService } from './cache.service';
import { LoggerService } from './logger.service';
export interface SearchResult {
    products: Product[];
    total: number;
    page: number;
    pageSize: number;
}
export declare class ProductSearchService {
    private readonly productRepository;
    private readonly cacheService;
    private readonly logger;
    private readonly CACHE_TTL;
    constructor(productRepository: Repository<Product>, cacheService: CacheService, logger: LoggerService);
    searchProducts(params: {
        keyword?: string;
        category?: string;
        minAmount?: number;
        maxAmount?: number;
        minTerm?: number;
        maxTerm?: number;
        minInterestRate?: number;
        maxInterestRate?: number;
        isActive?: boolean;
        isFeatured?: boolean;
        page?: number;
        pageSize?: number;
        sortBy?: keyof Product;
        sortOrder?: 'ASC' | 'DESC';
    }): Promise<SearchResult>;
    getCategories(): Promise<string[]>;
    getTags(): Promise<string[]>;
    getHotKeywords(limit?: number): Promise<string[]>;
    searchByCategory(category: string): Promise<any>;
    searchByAmountRange(minAmount: number, maxAmount: number): Promise<any>;
    searchByTermRange(minTerm: number, maxTerm: number): Promise<any>;
    searchByInterestRate(maxInterestRate: number): Promise<any>;
    private processData;
    private mapHeaders;
    private parseRow;
    private transformProductData;
}
