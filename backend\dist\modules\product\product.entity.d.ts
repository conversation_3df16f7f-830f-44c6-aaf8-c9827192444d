import { LoanApplication } from '../loan/loan.entity';
export declare class LoanProduct {
    id: number;
    name: string;
    description: string;
    interest_rate: number;
    min_amount: number;
    max_amount: number;
    term_months: number;
    is_active: boolean;
    provider: string;
    product_type: string;
    requirements: {
        min_age?: number;
        max_age?: number;
        min_income?: number;
        credit_score_min?: number;
        employment_type?: string[];
        collateral_required?: boolean;
    };
    features: {
        fast_approval?: boolean;
        online_application?: boolean;
        flexible_repayment?: boolean;
        early_repayment?: boolean;
    };
    processing_fee_rate: number;
    approval_days: number;
    special_conditions: string;
    created_at: Date;
    updated_at: Date;
    applications: LoanApplication[];
}
