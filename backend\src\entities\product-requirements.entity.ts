import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, ManyToOne } from 'typeorm';
import { Product } from './product.entity';

@Entity('product_requirements')
export class ProductRequirements {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  value: string;

  @Column({ default: false })
  required: boolean;

  @Column({ type: 'text', nullable: true })
  description: string;

  @ManyToOne(() => Product, product => product.requirements)
  product: Product;
}
