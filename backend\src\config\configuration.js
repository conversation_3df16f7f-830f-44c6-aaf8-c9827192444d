"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = (function () {
    var _a, _b, _c, _d;
    return ({
        port: parseInt(process.env.PORT, 10) || 3000,
        database: {
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT, 10) || 5432,
            username: process.env.DB_USERNAME || 'postgres',
            password: process.env.DB_PASSWORD || 'postgres',
            database: process.env.DB_DATABASE || 'smartloan',
        },
        jwt: {
            secret: process.env.JWT_SECRET || 'your-secret-key',
            expiresIn: process.env.JWT_EXPIRES_IN || '1d',
        },
        upload: {
            dir: process.env.UPLOAD_DIR || 'uploads',
            maxSize: parseInt(process.env.MAX_FILE_SIZE, 10) || 5 * 1024 * 1024, // 5MB
            allowedTypes: ((_a = process.env.ALLOWED_FILE_TYPES) === null || _a === void 0 ? void 0 : _a.split(',')) || ['image/jpeg', 'image/png', 'application/pdf'],
        },
        cache: {
            ttl: parseInt(process.env.CACHE_TTL, 10) || 3600, // 1 hour
        },
        monitoring: {
            enabled: process.env.ENABLE_MONITORING === 'true',
            metrics: {
                enabled: process.env.ENABLE_METRICS === 'true',
                interval: parseInt(process.env.METRICS_INTERVAL, 10) || 60000, // 1 minute
            },
            alerts: {
                enabled: process.env.ENABLE_ALERTS === 'true',
                threshold: {
                    cpu: parseInt(process.env.CPU_THRESHOLD, 10) || 80,
                    memory: parseInt(process.env.MEMORY_THRESHOLD, 10) || 80,
                    disk: parseInt(process.env.DISK_THRESHOLD, 10) || 80,
                },
            },
        },
        logging: {
            level: process.env.LOG_LEVEL || 'info',
            format: process.env.LOG_FORMAT || 'json',
            file: process.env.LOG_FILE || 'logs/app.log',
        },
        cors: {
            origin: ((_b = process.env.CORS_ORIGIN) === null || _b === void 0 ? void 0 : _b.split(',')) || ['http://localhost:3000'],
            methods: ((_c = process.env.CORS_METHODS) === null || _c === void 0 ? void 0 : _c.split(',')) || ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
            allowedHeaders: ((_d = process.env.CORS_HEADERS) === null || _d === void 0 ? void 0 : _d.split(',')) || ['Content-Type', 'Authorization'],
        },
    });
});
