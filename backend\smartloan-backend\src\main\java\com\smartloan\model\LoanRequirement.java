package com.smartloan.model;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "loan_requirements")
public class LoanRequirement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(nullable = false)
    private BigDecimal requestedAmount;
    
    @Column(nullable = false)
    private Integer requestedTerm; // 期限（月）
    
    private String purpose;
    
    @Column(nullable = false)
    private String employmentStatus;
    
    private String collateral;
    
    private String additionalInfo;
    
    @Column(nullable = false)
    private String status; // PENDING, PROCESSING, MATCHED, REJECTED
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (status == null) {
            status = "PENDING";
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
