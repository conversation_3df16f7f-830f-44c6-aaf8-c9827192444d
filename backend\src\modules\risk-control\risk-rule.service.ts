import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
// import { RiskRule } from '../entities/risk-rule.entity';
// import { GpuService } from '../services/gpu.service';

@Injectable()
export class RiskRuleService {
  private readonly logger = new Logger(RiskRuleService.name);

  constructor(
    // @InjectRepository(RiskRule)
    // private riskRuleRepository: Repository<RiskRule>,
    // private gpuService: GpuService,
  ) {}

  async evaluateRules(data: any): Promise<any> {
    try {
      this.logger.log('开始风险规则评估');
      
      // 模拟风险规则评估
      const riskScore = this.calculateRiskScore(data);
      
      return {
        score: riskScore,
        level: this.getRiskLevel(riskScore),
        rules: this.getTriggeredRules(data),
        recommendation: this.getRecommendation(riskScore)
      };
    } catch (error) {
      this.logger.error('风险规则评估失败', error);
      throw error;
    }
  }

  private calculateRiskScore(data: any): number {
    let score = 50; // 基础分数

    // 信用分数影响
    if (data.creditScore >= 750) score += 25;
    else if (data.creditScore >= 700) score += 15;
    else if (data.creditScore < 600) score -= 20;

    // 收入稳定性
    if (data.incomeStability === 'HIGH') score += 15;
    else if (data.incomeStability === 'LOW') score -= 10;

    // 负债率
    if (data.debtRatio < 0.3) score += 10;
    else if (data.debtRatio > 0.5) score -= 15;

    return Math.max(0, Math.min(100, score));
  }

  private getRiskLevel(score: number): string {
    if (score >= 80) return 'LOW';
    if (score >= 60) return 'MEDIUM';
    return 'HIGH';
  }

  private getTriggeredRules(data: any): string[] {
    const rules = [];
    
    if (data.creditScore < 600) {
      rules.push('信用分数过低');
    }
    
    if (data.debtRatio > 0.5) {
      rules.push('负债率过高');
    }
    
    if (data.incomeStability === 'LOW') {
      rules.push('收入不稳定');
    }
    
    return rules;
  }

  private getRecommendation(score: number): string {
    if (score >= 80) return '建议批准';
    if (score >= 60) return '建议进一步审核';
    return '建议拒绝';
  }
}
