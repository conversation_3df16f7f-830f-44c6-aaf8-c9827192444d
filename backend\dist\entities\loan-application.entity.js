"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoanApplication = exports.LoanType = exports.LoanStatus = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./user.entity");
const product_entity_1 = require("./product.entity");
const loan_document_entity_1 = require("./loan-document.entity");
const loan_status_enum_1 = require("../enums/loan-status.enum");
const loan_type_enum_1 = require("../enums/loan-type.enum");
const loan_purpose_enum_1 = require("../enums/loan-purpose.enum");
const loan_review_entity_1 = require("./loan-review.entity");
const employment_status_enum_1 = require("../enums/employment-status.enum");
const collateral_type_enum_1 = require("../enums/collateral-type.enum");
var loan_status_enum_2 = require("../enums/loan-status.enum");
Object.defineProperty(exports, "LoanStatus", { enumerable: true, get: function () { return loan_status_enum_2.LoanStatus; } });
var loan_type_enum_2 = require("../enums/loan-type.enum");
Object.defineProperty(exports, "LoanType", { enumerable: true, get: function () { return loan_type_enum_2.LoanType; } });
let LoanApplication = class LoanApplication {
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], LoanApplication.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.loanApplications),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], LoanApplication.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], LoanApplication.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => product_entity_1.Product, product => product.applications),
    (0, typeorm_1.JoinColumn)({ name: 'product_id' }),
    __metadata("design:type", product_entity_1.Product)
], LoanApplication.prototype, "product", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], LoanApplication.prototype, "productId", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], LoanApplication.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)('int'),
    __metadata("design:type", Number)
], LoanApplication.prototype, "term", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: loan_type_enum_1.LoanType,
        default: loan_type_enum_1.LoanType.PERSONAL
    }),
    __metadata("design:type", String)
], LoanApplication.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: loan_purpose_enum_1.LoanPurpose,
        default: loan_purpose_enum_1.LoanPurpose.OTHER
    }),
    __metadata("design:type", String)
], LoanApplication.prototype, "purpose", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: loan_status_enum_1.LoanStatus,
        default: loan_status_enum_1.LoanStatus.PENDING
    }),
    __metadata("design:type", String)
], LoanApplication.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], LoanApplication.prototype, "monthlyPayment", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], LoanApplication.prototype, "totalPayment", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], LoanApplication.prototype, "annualIncome", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 5, scale: 2 }),
    __metadata("design:type", Number)
], LoanApplication.prototype, "debtToIncomeRatio", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], LoanApplication.prototype, "employmentStatus", void 0);
__decorate([
    (0, typeorm_1.Column)('int'),
    __metadata("design:type", Number)
], LoanApplication.prototype, "workExperience", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { nullable: true }),
    __metadata("design:type", Number)
], LoanApplication.prototype, "creditScore", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { nullable: true }),
    __metadata("design:type", Number)
], LoanApplication.prototype, "riskScore", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Object)
], LoanApplication.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Object)
], LoanApplication.prototype, "documentMetadata", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Object)
], LoanApplication.prototype, "riskAssessment", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], LoanApplication.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], LoanApplication.prototype, "rejectedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], LoanApplication.prototype, "cancelledAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], LoanApplication.prototype, "cancelledBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], LoanApplication.prototype, "cancellationReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], LoanApplication.prototype, "rejectionReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], LoanApplication.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], LoanApplication.prototype, "rejectedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: employment_status_enum_1.EmploymentStatus,
        default: employment_status_enum_1.EmploymentStatus.EMPLOYED
    }),
    __metadata("design:type", String)
], LoanApplication.prototype, "employmentStatusEnum", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: collateral_type_enum_1.CollateralType,
        default: collateral_type_enum_1.CollateralType.NONE
    }),
    __metadata("design:type", String)
], LoanApplication.prototype, "collateral", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { nullable: true }),
    __metadata("design:type", String)
], LoanApplication.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => loan_document_entity_1.LoanDocument, document => document.application),
    __metadata("design:type", Array)
], LoanApplication.prototype, "documents", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => loan_review_entity_1.LoanReview, review => review.loanApplication),
    __metadata("design:type", Array)
], LoanApplication.prototype, "reviews", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], LoanApplication.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], LoanApplication.prototype, "updatedAt", void 0);
LoanApplication = __decorate([
    (0, typeorm_1.Entity)('loan_applications')
], LoanApplication);
exports.LoanApplication = LoanApplication;
//# sourceMappingURL=loan-application.entity.js.map