import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { ErrorHandler } from '../utils/error-handler';
import { MonitorService } from './monitor.service';
import { CacheService } from './cache.service';
export declare class CreditReportService {
    private readonly configService;
    private readonly httpService;
    private readonly errorHandler;
    private readonly monitorService;
    private readonly cacheService;
    private readonly logger;
    private readonly apiUrl;
    private readonly apiKey;
    constructor(configService: ConfigService, httpService: HttpService, errorHandler: ErrorHandler, monitorService: MonitorService, cacheService: CacheService);
    getCreditReport(userId: string): Promise<unknown>;
    private fetchCreditReport;
    private cleanCreditReport;
    private calculateCreditLevel;
    private cleanLoanHistory;
    private cleanCreditCardHistory;
    private cleanGuaranteeHistory;
    private maskCardNumber;
    private assessRisk;
    private calculateRiskLevel;
    private generateRecommendations;
    private calculateScore;
}
