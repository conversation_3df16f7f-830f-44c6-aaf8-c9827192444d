@echo off
echo 🔧 SmartLoan 2025 - 一键修复所有Java问题
echo ==========================================

echo 📋 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置到PATH
    echo 💡 请安装Java 17 JDK并配置环境变量
    echo    下载地址: https://adoptium.net/temurin/releases/
    pause
    exit /b 1
)

echo 📋 检查Maven环境...
mvn --version
if %errorlevel% neq 0 (
    echo ❌ Maven未安装或未配置到PATH
    echo 💡 请安装Apache Maven并配置环境变量
    echo    下载地址: https://maven.apache.org/download.cgi
    pause
    exit /b 1
)

echo ✅ Java和Maven环境检查通过

cd backend\smartloan-backend

echo 🧹 清理项目...
call mvn clean

echo 📥 强制更新依赖...
call mvn dependency:resolve -U

echo 📚 下载源码...
call mvn dependency:resolve-sources

echo 🏗️ 编译项目...
call mvn compile

if %errorlevel% equ 0 (
    echo ✅ 编译成功！所有Java依赖问题已解决
) else (
    echo ❌ 编译失败，请检查以下可能的问题：
    echo    1. Java版本是否为17或更高
    echo    2. Maven版本是否为3.6或更高
    echo    3. 网络连接是否正常
    echo    4. IDE是否需要重新导入项目
)

echo.
echo 🎯 下一步操作建议：
echo    1. 在IDE中刷新Maven项目
echo    2. 重新导入项目
echo    3. 清理并重建项目
echo    4. 如果仍有问题，重启IDE
echo.

pause
