"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Validator = void 0;
const common_1 = require("@nestjs/common");
const class_validator_1 = require("class-validator");
const loan_purpose_enum_1 = require("../enums/loan-purpose.enum");
let Validator = class Validator {
    async validateObject(obj, options) {
        try {
            const errors = await (0, class_validator_1.validate)(obj, {
                skipMissingProperties: options?.skipMissingProperties || false,
                strictGroups: options?.strictGroups || false,
                groups: options?.groups,
            });
            if (errors.length === 0) {
                return { isValid: true, errors: [] };
            }
            const errorMessages = this.formatValidationErrors(errors);
            return { isValid: false, errors: errorMessages };
        }
        catch (error) {
            return {
                isValid: false,
                errors: [`Validation failed: ${error.message}`]
            };
        }
    }
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const isValid = emailRegex.test(email);
        return {
            isValid,
            errors: isValid ? [] : ['Invalid email format']
        };
    }
    validatePhone(phone) {
        const phoneRegex = /^(\+?86)?1[3-9]\d{9}$/;
        const isValid = phoneRegex.test(phone.replace(/[\s-]/g, ''));
        return {
            isValid,
            errors: isValid ? [] : ['Invalid phone number format']
        };
    }
    validateIdCard(idCard) {
        const idCardRegex = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        if (!idCardRegex.test(idCard)) {
            return { isValid: false, errors: ['Invalid ID card format'] };
        }
        const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
        const sum = idCard.slice(0, 17)
            .split('')
            .reduce((acc, char, index) => acc + parseInt(char) * weights[index], 0);
        const checkCode = checkCodes[sum % 11];
        const lastChar = idCard.slice(-1).toUpperCase();
        const isValid = checkCode === lastChar;
        return {
            isValid,
            errors: isValid ? [] : ['Invalid ID card check digit']
        };
    }
    validateBankCard(cardNumber) {
        const cleanNumber = cardNumber.replace(/\s/g, '');
        if (!/^\d{16,19}$/.test(cleanNumber)) {
            return { isValid: false, errors: ['Bank card number must be 16-19 digits'] };
        }
        let sum = 0;
        let alternate = false;
        for (let i = cleanNumber.length - 1; i >= 0; i--) {
            let digit = parseInt(cleanNumber.charAt(i));
            if (alternate) {
                digit *= 2;
                if (digit > 9) {
                    digit = (digit % 10) + 1;
                }
            }
            sum += digit;
            alternate = !alternate;
        }
        const isValid = sum % 10 === 0;
        return {
            isValid,
            errors: isValid ? [] : ['Invalid bank card number']
        };
    }
    validateAmount(amount, min, max) {
        const errors = [];
        if (isNaN(amount) || amount < 0) {
            errors.push('Amount must be a positive number');
        }
        if (min !== undefined && amount < min) {
            errors.push(`Amount must be at least ${min}`);
        }
        if (max !== undefined && amount > max) {
            errors.push(`Amount must not exceed ${max}`);
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    validateDateRange(startDate, endDate) {
        const errors = [];
        if (!(startDate instanceof Date) || isNaN(startDate.getTime())) {
            errors.push('Invalid start date');
        }
        if (!(endDate instanceof Date) || isNaN(endDate.getTime())) {
            errors.push('Invalid end date');
        }
        if (errors.length === 0 && startDate >= endDate) {
            errors.push('Start date must be before end date');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    validatePurpose(purpose) {
        if (!purpose) {
            return { isValid: false, errors: ['Purpose is required'] };
        }
        if (!Object.values(loan_purpose_enum_1.LoanPurpose).includes(purpose)) {
            return { isValid: false, errors: ['Invalid loan purpose'] };
        }
        return { isValid: true, errors: [] };
    }
    validateIncome(income) {
        if (income < 0) {
            return { isValid: false, errors: ['Income must be positive'] };
        }
        if (income < 10000) {
            return { isValid: false, errors: ['Income must be at least ¥10,000'] };
        }
        if (income > 10000000) {
            return { isValid: false, errors: ['Income exceeds maximum limit'] };
        }
        return { isValid: true, errors: [] };
    }
    validateTerm(term) {
        if (term < 1) {
            return { isValid: false, errors: ['Term must be at least 1 month'] };
        }
        if (term > 60) {
            return { isValid: false, errors: ['Term cannot exceed 60 months'] };
        }
        return { isValid: true, errors: [] };
    }
    async validateBatch(objects, options) {
        return Promise.all(objects.map(obj => this.validateObject(obj, options)));
    }
    validateConditional(value, condition, validator) {
        if (!condition) {
            return { isValid: true, errors: [] };
        }
        return validator(value);
    }
    formatValidationErrors(errors) {
        const messages = [];
        for (const error of errors) {
            if (error.constraints) {
                messages.push(...Object.values(error.constraints));
            }
            if (error.children && error.children.length > 0) {
                messages.push(...this.formatValidationErrors(error.children));
            }
        }
        return messages;
    }
    addCustomValidator(name, validator, message) {
    }
};
Validator = __decorate([
    (0, common_1.Injectable)()
], Validator);
exports.Validator = Validator;
//# sourceMappingURL=validator.js.map