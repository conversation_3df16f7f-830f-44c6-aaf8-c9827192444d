export declare const LOA<PERSON>_APPLICATION_CONSTANTS: {
    MIN_LOAN_AMOUNT: number;
    MAX_LOAN_AMOUNT: number;
    MIN_LOAN_TERM: number;
    MAX_LOAN_TERM: number;
    MIN_INTEREST_RATE: number;
    MAX_INTEREST_RATE: number;
    MIN_MONTHLY_PAYMENT: number;
    MAX_MONTHLY_PAYMENT: number;
    MIN_RISK_SCORE: number;
    MAX_RISK_SCORE: number;
    RISK_LEVELS: {
        LOW: number;
        MEDIUM: number;
        HIGH: number;
    };
    MIN_MONTHLY_INCOME: number;
    MAX_MONTHLY_INCOME: number;
    MIN_EMPLOYMENT_DURATION: number;
    MAX_EMPLOYMENT_DURATION: number;
    MIN_DEBT_TO_INCOME_RATIO: number;
    MAX_DEBT_TO_INCOME_RATIO: number;
    MAX_RECOMMENDED_DEBT_TO_INCOME_RATIO: number;
    CACHE_TTL: {
        APPLICATION: number;
        USER_APPLICATIONS: number;
        STATISTICS: number;
        TRENDS: number;
    };
    MAX_FILE_SIZE: number;
    ALLOWED_FILE_TYPES: string[];
    STATUS_TRANSITIONS: {
        pending: string[];
        reviewing: string[];
        approved: string[];
        rejected: string[];
        cancelled: any[];
    };
    DECISION_RULES: {
        FRAUD_THRESHOLD: number;
        RISK_SCORE_THRESHOLD: number;
        STRESS_TEST_THRESHOLD: number;
        MAX_DEFAULT_PROBABILITY: number;
        MIN_RECOVERY_RATE: number;
        MAX_EXPECTED_LOSS_RATIO: number;
    };
    RECOMMENDATION_WEIGHTS: {
        creditScore: number;
        income: number;
        employment: number;
        age: number;
        education: number;
        maritalStatus: number;
        houseStatus: number;
        carStatus: number;
    };
    ERROR_MESSAGES: {
        INVALID_AMOUNT: string;
        INVALID_TERM: string;
        INVALID_INCOME: string;
        INVALID_CREDIT_SCORE: string;
        INVALID_DEBT_RATIO: string;
        INVALID_FILE: string;
        INVALID_STATUS: string;
        APPLICATION_NOT_FOUND: string;
        UNAUTHORIZED: string;
        FORBIDDEN: string;
    };
    SUCCESS_MESSAGES: {
        CREATED: string;
        UPDATED: string;
        CANCELLED: string;
        APPROVED: string;
        REJECTED: string;
    };
};
