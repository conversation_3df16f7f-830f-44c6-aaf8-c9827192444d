/// <reference types="jest" />
import { User } from '../../types';
export declare const mockUser: User;
export declare const mockAdminUser: User;
export declare const createMockRepository: () => {
    find: jest.Mock<any, any, any>;
    findOne: jest.Mock<any, any, any>;
    save: jest.Mock<any, any, any>;
    update: jest.Mock<any, any, any>;
    delete: jest.Mock<any, any, any>;
    createQueryBuilder: jest.Mock<{
        leftJoinAndSelect: jest.Mock<any, any, any>;
        where: jest.Mock<any, any, any>;
        andWhere: jest.Mock<any, any, any>;
        getMany: jest.Mock<any, any, any>;
    }, [], any>;
};
export declare const createMockService: (methods: Record<string, jest.Mock>) => {
    [x: string]: jest.Mock<any, any, any>;
};
