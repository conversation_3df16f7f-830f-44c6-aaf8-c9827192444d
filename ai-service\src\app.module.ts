import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { OcrService } from './ocr/ocr.service';
import { RiskModelService } from './risk/risk-model.service';
import { HealthController } from './health/health.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
  ],
  controllers: [HealthController],
  providers: [OcrService, RiskModelService],
  exports: [OcrService, RiskModelService],
})
export class AppModule {}
