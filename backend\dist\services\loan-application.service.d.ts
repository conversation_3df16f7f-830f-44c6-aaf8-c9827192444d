/// <reference types="multer" />
/// <reference types="node" />
/// <reference types="node" />
import { ConfigService } from '@nestjs/config';
import { LoggerService } from './logger.service';
import { ErrorHandler } from '../utils/error-handler';
import { MonitorService } from './monitor.service';
import { CacheService } from './cache.service';
import { LoanApplication } from '../entities/loan-application.entity';
import { Repository } from 'typeorm';
import { RiskDecisionService } from './risk-decision.service';
import { ProductRecommendationService } from './product-recommendation.service';
import { LoanDocument, DocumentType } from '../entities/loan-document.entity';
import { User } from '../entities/user.entity';
import { LoanStatus } from '../enums/loan-status.enum';
import { CreateLoanApplicationDto } from '../dto/create-loan-application.dto';
import { LoanApplicationDto } from '../dto/loan-application.dto';
import { LoanPurpose } from '../enums/loan-purpose.enum';
interface UpdateLoanApplicationDto {
    loanType?: string;
    loanAmount?: number;
    loanTerm?: number;
    purpose?: LoanPurpose;
    status?: LoanStatus;
}
export declare class LoanApplicationService {
    private readonly loanApplicationRepository;
    private readonly loanDocumentRepository;
    private readonly configService;
    private readonly logger;
    private readonly errorHandler;
    private readonly monitorService;
    private readonly cacheService;
    private readonly riskDecisionService;
    private readonly productRecommendationService;
    private readonly userRepository;
    private readonly uploadDir;
    constructor(loanApplicationRepository: Repository<LoanApplication>, loanDocumentRepository: Repository<LoanDocument>, configService: ConfigService, logger: LoggerService, errorHandler: ErrorHandler, monitorService: MonitorService, cacheService: CacheService, riskDecisionService: RiskDecisionService, productRecommendationService: ProductRecommendationService, userRepository: Repository<User>);
    private ensureUploadDir;
    findAll(): Promise<LoanApplication[]>;
    findOne(id: string): Promise<LoanApplication>;
    update(id: string, updateLoanApplicationDto: UpdateLoanApplicationDto): Promise<LoanApplication>;
    remove(id: string): Promise<void>;
    submitForReview(id: string): Promise<LoanApplication>;
    processApplication(id: string): Promise<LoanApplication>;
    getApplication(id: string, user: any): Promise<LoanApplication>;
    updateApplication(id: string, user: any, updateData: Partial<LoanApplication>): Promise<LoanApplication>;
    cancelApplication(id: string, user: any): Promise<LoanApplication>;
    getUserApplications(userId: string, user: User): Promise<LoanApplicationDto[]>;
    getApplicationStatistics(user: User): Promise<any>;
    getApplicationTrends(user: User): Promise<any>;
    approveApplication(id: string, user: any): Promise<LoanApplication>;
    rejectApplication(id: string, reason: string, user: any): Promise<LoanApplication>;
    uploadDocument(applicationId: string, file: Express.Multer.File, type: DocumentType, user: User): Promise<LoanDocument>;
    verifyDocument(applicationId: string, documentId: string, user: User, isVerified: boolean, notes: string): Promise<LoanDocument>;
    private calculateMonthlyPayment;
    private mapToDto;
    findAllByUser(userId: string): Promise<LoanApplication[]>;
    create(createDto: CreateLoanApplicationDto, userId: string): Promise<LoanApplication>;
    findById(id: string): Promise<LoanApplication | null>;
    updateStatus(id: string, status: LoanStatus, rejectionReason?: string): Promise<LoanApplication | null>;
    isApplicationLimitExceeded(userId: string, limit: number): Promise<boolean>;
    batchProcessApplications(ids: string[], user: User): Promise<{
        success: string[];
        failed: string[];
    }>;
    exportApplications(filters: {
        startDate?: Date;
        endDate?: Date;
        status?: LoanStatus;
        loanType?: string;
    }, user: User): Promise<Buffer>;
    private validateLoanApplication;
    private convertToCSV;
    private isValidStatusTransition;
    processLoanApplication(id: string, user: User): Promise<LoanApplication>;
    uploadDocuments(applicationId: string, files: Express.Multer.File[], user: User): Promise<LoanDocument[]>;
    evaluateApplication(id: string, user: User): Promise<LoanApplication>;
    getLoanTypes(): Promise<string[]>;
    private calculatePayments;
}
export {};
