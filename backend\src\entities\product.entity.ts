import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { LoanApplication } from './loan-application.entity';
import { ProductRequirements } from './product-requirements.entity';
import { LoanType } from '../enums/loan-type.enum';
import { LoanPurpose } from '../enums/loan-purpose.enum';
import { ProductCategory } from '../enums/product-category.enum';

// 导出枚举以供其他模块使用
export { ProductCategory };

@Entity('products')
export class Product {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  code: string;

  @Column('text')
  description: string;

  @Column({
    type: 'enum',
    enum: LoanType,
    default: LoanType.PERSONAL
  })
  type: LoanType;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2
  })
  minAmount: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2
  })
  maxAmount: number;

  @Column()
  minTerm: number;

  @Column()
  maxTerm: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2
  })
  interestRate: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    nullable: true
  })
  processingFee: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    nullable: true
  })
  lateFee: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    nullable: true
  })
  earlyRepaymentFee: number;

  @Column('jsonb', { nullable: true })
  features: Array<{
    name: string;
    description: string;
    icon: string;
  }>;

  @OneToMany(() => ProductRequirements, requirement => requirement.product)
  requirements: ProductRequirements[];

  @Column('simple-array', { nullable: true })
  benefits: string[];

  @Column('jsonb', { nullable: true })
  metadata: {
    category: string;
    tags: string[];
    riskLevel: string;
    popularity: number;
    approvalRate: number;
    averageProcessingTime: number;
  };

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isFeatured: boolean;

  @OneToMany(() => LoanApplication, application => application.product)
  applications: LoanApplication[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({
    type: 'enum',
    enum: ProductCategory,
    default: ProductCategory.OTHER
  })
  category: ProductCategory;

  @Column('simple-array', { nullable: true })
  tags: string[];
}