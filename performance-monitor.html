<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartLoan 2025 - 性能监控系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; background: #0d1117; color: #c9d1d9; min-height: 100vh; }
        .monitor-container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; color: #58a6ff; margin-bottom: 10px; }
        .header p { color: #8b949e; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: #161b22; border: 1px solid #30363d; border-radius: 12px; padding: 20px; }
        .metric-title { color: #58a6ff; font-size: 1.1rem; margin-bottom: 15px; display: flex; align-items: center; }
        .metric-icon { margin-right: 10px; font-size: 1.3rem; }
        .metric-value { font-size: 2rem; font-weight: bold; margin-bottom: 10px; }
        .metric-value.good { color: #3fb950; }
        .metric-value.warning { color: #d29922; }
        .metric-value.error { color: #f85149; }
        .metric-trend { font-size: 0.8rem; color: #8b949e; }
        .trend-up { color: #3fb950; }
        .trend-down { color: #f85149; }
        .console-output { background: #0d1117; border: 1px solid #30363d; border-radius: 8px; padding: 15px; font-family: 'Monaco', monospace; font-size: 0.8rem; height: 300px; overflow-y: auto; margin-bottom: 20px; }
        .log-entry { margin-bottom: 5px; }
        .log-info { color: #58a6ff; }
        .log-success { color: #3fb950; }
        .log-warning { color: #d29922; }
        .log-error { color: #f85149; }
        .timestamp { color: #8b949e; }
        .performance-chart { background: #161b22; border: 1px solid #30363d; border-radius: 12px; padding: 20px; margin-bottom: 20px; }
        .chart-title { color: #58a6ff; font-size: 1.2rem; margin-bottom: 15px; }
        .chart-area { height: 200px; background: linear-gradient(45deg, #0d1117, #161b22); border-radius: 8px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden; }
        .chart-area::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: repeating-linear-gradient(90deg, transparent, transparent 20px, rgba(88, 166, 255, 0.1) 20px, rgba(88, 166, 255, 0.1) 40px); animation: slide 2s linear infinite; }
        @keyframes slide { 0% { transform: translateX(-40px); } 100% { transform: translateX(40px); } }
        .api-status { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .api-item { background: #161b22; border: 1px solid #30363d; border-radius: 8px; padding: 15px; }
        .api-endpoint { font-weight: bold; margin-bottom: 8px; }
        .api-metrics { display: flex; justify-content: space-between; font-size: 0.8rem; }
        .status-indicator { display: inline-block; width: 8px; height: 8px; border-radius: 50%; margin-right: 8px; }
        .status-online { background: #3fb950; }
        .status-slow { background: #d29922; }
        .status-offline { background: #f85149; }
        .gpu-monitor { background: #161b22; border: 1px solid #30363d; border-radius: 12px; padding: 20px; margin-bottom: 20px; }
        .gpu-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .gpu-card { background: #0d1117; border: 1px solid #30363d; border-radius: 8px; padding: 15px; text-align: center; }
        .gpu-usage { font-size: 1.5rem; font-weight: bold; margin-bottom: 10px; }
        .usage-bar { width: 100%; height: 8px; background: #30363d; border-radius: 4px; overflow: hidden; margin-bottom: 10px; }
        .usage-fill { height: 100%; background: linear-gradient(45deg, #3fb950, #58a6ff); transition: width 0.5s ease; }
        .control-panel { background: #161b22; border: 1px solid #30363d; border-radius: 12px; padding: 20px; }
        .control-buttons { display: flex; gap: 10px; margin-bottom: 15px; }
        .control-btn { background: #238636; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 0.8rem; transition: all 0.3s; }
        .control-btn:hover { background: #2ea043; }
        .control-btn.danger { background: #da3633; }
        .control-btn.danger:hover { background: #f85149; }
        .system-info { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .info-item { background: #0d1117; border: 1px solid #30363d; border-radius: 8px; padding: 15px; }
        .info-label { color: #8b949e; font-size: 0.8rem; margin-bottom: 5px; }
        .info-value { font-weight: bold; }
    </style>
</head>
<body>
    <div class="monitor-container">
        <!-- 头部 -->
        <div class="header">
            <h1>⚡ SmartLoan 性能监控系统</h1>
            <p>实时监控系统性能、API状态和GPU使用情况</p>
        </div>

        <!-- 核心指标 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-title">
                    <span class="metric-icon">🚀</span>
                    系统响应时间
                </div>
                <div class="metric-value good" id="response-time">0.3s</div>
                <div class="metric-trend trend-up">↗ 比昨日快15%</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">
                    <span class="metric-icon">👥</span>
                    并发用户数
                </div>
                <div class="metric-value good" id="concurrent-users">2,847</div>
                <div class="metric-trend trend-up">↗ +12.5%</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">
                    <span class="metric-icon">📊</span>
                    API成功率
                </div>
                <div class="metric-value good" id="success-rate">99.8%</div>
                <div class="metric-trend trend-up">↗ +0.2%</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">
                    <span class="metric-icon">🔥</span>
                    GPU使用率
                </div>
                <div class="metric-value warning" id="gpu-usage">67%</div>
                <div class="metric-trend">稳定运行</div>
            </div>
        </div>

        <!-- API状态监控 -->
        <div class="performance-chart">
            <div class="chart-title">🔗 API接口状态监控</div>
            <div class="api-status">
                <div class="api-item">
                    <div class="api-endpoint">
                        <span class="status-indicator status-online"></span>
                        /api/products/match/smart
                    </div>
                    <div class="api-metrics">
                        <span>响应: 0.2s</span>
                        <span>成功率: 99.9%</span>
                        <span>QPS: 1,247</span>
                    </div>
                </div>
                <div class="api-item">
                    <div class="api-endpoint">
                        <span class="status-indicator status-online"></span>
                        /api/ocr/recognize
                    </div>
                    <div class="api-metrics">
                        <span>响应: 0.8s</span>
                        <span>成功率: 98.5%</span>
                        <span>QPS: 856</span>
                    </div>
                </div>
                <div class="api-item">
                    <div class="api-endpoint">
                        <span class="status-indicator status-online"></span>
                        /api/face/liveness
                    </div>
                    <div class="api-metrics">
                        <span>响应: 0.5s</span>
                        <span>成功率: 99.2%</span>
                        <span>QPS: 634</span>
                    </div>
                </div>
                <div class="api-item">
                    <div class="api-endpoint">
                        <span class="status-indicator status-online"></span>
                        /api/loan/calculator
                    </div>
                    <div class="api-metrics">
                        <span>响应: 0.1s</span>
                        <span>成功率: 100%</span>
                        <span>QPS: 2,156</span>
                    </div>
                </div>
                <div class="api-item">
                    <div class="api-endpoint">
                        <span class="status-indicator status-online"></span>
                        /api/ai/advisor/chat
                    </div>
                    <div class="api-metrics">
                        <span>响应: 1.2s</span>
                        <span>成功率: 97.8%</span>
                        <span>QPS: 423</span>
                    </div>
                </div>
                <div class="api-item">
                    <div class="api-endpoint">
                        <span class="status-indicator status-online"></span>
                        /api/products/compare
                    </div>
                    <div class="api-metrics">
                        <span>响应: 0.3s</span>
                        <span>成功率: 99.5%</span>
                        <span>QPS: 789</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- GPU监控 -->
        <div class="gpu-monitor">
            <div class="chart-title">🎮 沐曦MetaX GPU监控</div>
            <div class="gpu-grid">
                <div class="gpu-card">
                    <div style="color: #58a6ff; margin-bottom: 10px;">GPU-0</div>
                    <div class="gpu-usage">67%</div>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: 67%;"></div>
                    </div>
                    <div style="font-size: 0.7rem; color: #8b949e;">OCR处理</div>
                </div>
                <div class="gpu-card">
                    <div style="color: #58a6ff; margin-bottom: 10px;">GPU-1</div>
                    <div class="gpu-usage">45%</div>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: 45%;"></div>
                    </div>
                    <div style="font-size: 0.7rem; color: #8b949e;">AI推理</div>
                </div>
                <div class="gpu-card">
                    <div style="color: #58a6ff; margin-bottom: 10px;">GPU-2</div>
                    <div class="gpu-usage">23%</div>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: 23%;"></div>
                    </div>
                    <div style="font-size: 0.7rem; color: #8b949e;">风控计算</div>
                </div>
                <div class="gpu-card">
                    <div style="color: #58a6ff; margin-bottom: 10px;">GPU-3</div>
                    <div class="gpu-usage">12%</div>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: 12%;"></div>
                    </div>
                    <div style="font-size: 0.7rem; color: #8b949e;">备用</div>
                </div>
            </div>
        </div>

        <!-- 性能图表 -->
        <div class="performance-chart">
            <div class="chart-title">📈 实时性能趋势</div>
            <div class="chart-area">
                <div style="text-align: center; z-index: 1; position: relative;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">📊</div>
                    <div>实时性能监控图表</div>
                    <div style="font-size: 0.8rem; color: #8b949e; margin-top: 10px;">
                        响应时间、吞吐量、错误率实时监控
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统日志 -->
        <div class="performance-chart">
            <div class="chart-title">📝 系统日志</div>
            <div class="console-output" id="console-log">
                <div class="log-entry log-info">
                    <span class="timestamp">[2025-01-20 14:30:25]</span> 
                    <span class="log-info">[INFO]</span> 
                    系统启动完成，所有服务正常运行
                </div>
                <div class="log-entry log-success">
                    <span class="timestamp">[2025-01-20 14:30:23]</span> 
                    <span class="log-success">[SUCCESS]</span> 
                    OCR识别服务响应时间: 0.8s
                </div>
                <div class="log-entry log-success">
                    <span class="timestamp">[2025-01-20 14:30:21]</span> 
                    <span class="log-success">[SUCCESS]</span> 
                    智能匹配API处理请求: 1,247 QPS
                </div>
                <div class="log-entry log-info">
                    <span class="timestamp">[2025-01-20 14:30:19]</span> 
                    <span class="log-info">[INFO]</span> 
                    GPU使用率: 67% (正常范围)
                </div>
                <div class="log-entry log-success">
                    <span class="timestamp">[2025-01-20 14:30:17]</span> 
                    <span class="log-success">[SUCCESS]</span> 
                    活体检测完成，置信度: 98%
                </div>
                <div class="log-entry log-info">
                    <span class="timestamp">[2025-01-20 14:30:15]</span> 
                    <span class="log-info">[INFO]</span> 
                    数据库连接池状态: 正常 (95/100)
                </div>
                <div class="log-entry log-success">
                    <span class="timestamp">[2025-01-20 14:30:13]</span> 
                    <span class="log-success">[SUCCESS]</span> 
                    AI顾问响应时间: 1.2s
                </div>
                <div class="log-entry log-info">
                    <span class="timestamp">[2025-01-20 14:30:11]</span> 
                    <span class="log-info">[INFO]</span> 
                    Redis缓存命中率: 94%
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="chart-title">🎛️ 系统控制面板</div>
            <div class="control-buttons">
                <button class="control-btn" onclick="refreshMetrics()">🔄 刷新数据</button>
                <button class="control-btn" onclick="clearLogs()">🗑️ 清空日志</button>
                <button class="control-btn" onclick="exportReport()">📊 导出报告</button>
                <button class="control-btn danger" onclick="emergencyStop()">🚨 紧急停止</button>
            </div>
            
            <div class="system-info">
                <div class="info-item">
                    <div class="info-label">系统版本</div>
                    <div class="info-value">SmartLoan 2025 v2.1.0</div>
                </div>
                <div class="info-item">
                    <div class="info-label">运行时间</div>
                    <div class="info-value" id="uptime">72小时15分钟</div>
                </div>
                <div class="info-item">
                    <div class="info-label">内存使用</div>
                    <div class="info-value">8.2GB / 32GB</div>
                </div>
                <div class="info-item">
                    <div class="info-label">CPU使用率</div>
                    <div class="info-value">23%</div>
                </div>
                <div class="info-item">
                    <div class="info-label">网络流量</div>
                    <div class="info-value">↑ 125MB/s ↓ 89MB/s</div>
                </div>
                <div class="info-item">
                    <div class="info-label">磁盘使用</div>
                    <div class="info-value">156GB / 1TB</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟实时数据更新
        function updateMetrics() {
            // 更新响应时间
            const responseTime = (Math.random() * 0.5 + 0.1).toFixed(1);
            document.getElementById('response-time').textContent = responseTime + 's';
            
            // 更新并发用户数
            const users = Math.floor(Math.random() * 1000 + 2000);
            document.getElementById('concurrent-users').textContent = users.toLocaleString();
            
            // 更新成功率
            const successRate = (Math.random() * 2 + 98).toFixed(1);
            document.getElementById('success-rate').textContent = successRate + '%';
            
            // 更新GPU使用率
            const gpuUsage = Math.floor(Math.random() * 30 + 50);
            document.getElementById('gpu-usage').textContent = gpuUsage + '%';
        }

        // 添加新日志
        function addLog() {
            const console = document.getElementById('console-log');
            const now = new Date();
            const timestamp = now.toLocaleString('zh-CN');
            
            const logTypes = [
                { type: 'log-info', label: '[INFO]', message: 'API请求处理完成' },
                { type: 'log-success', label: '[SUCCESS]', message: 'OCR识别成功，置信度: 96%' },
                { type: 'log-info', label: '[INFO]', message: 'GPU使用率更新: ' + (Math.floor(Math.random() * 30 + 50)) + '%' },
                { type: 'log-success', label: '[SUCCESS]', message: '活体检测完成' },
                { type: 'log-info', label: '[INFO]', message: '数据库查询响应时间: 0.3s' }
            ];
            
            const randomLog = logTypes[Math.floor(Math.random() * logTypes.length)];
            const newLog = document.createElement('div');
            newLog.className = `log-entry ${randomLog.type}`;
            newLog.innerHTML = `
                <span class="timestamp">[${timestamp}]</span> 
                <span class="${randomLog.type}">${randomLog.label}</span> 
                ${randomLog.message}
            `;
            
            console.insertBefore(newLog, console.firstChild);
            
            // 保持最多20条日志
            while (console.children.length > 20) {
                console.removeChild(console.lastChild);
            }
        }

        // 控制面板功能
        function refreshMetrics() {
            updateMetrics();
            addLog();
            console.log('📊 数据已刷新');
        }

        function clearLogs() {
            document.getElementById('console-log').innerHTML = '';
            console.log('🗑️ 日志已清空');
        }

        function exportReport() {
            console.log('📊 性能报告已导出');
            alert('📊 性能报告已导出到下载文件夹');
        }

        function emergencyStop() {
            if (confirm('⚠️ 确定要紧急停止系统吗？')) {
                console.log('🚨 系统紧急停止');
                alert('🚨 系统已紧急停止');
            }
        }

        // 启动实时监控
        setInterval(updateMetrics, 3000);
        setInterval(addLog, 5000);

        // 页面加载完成
        window.onload = function() {
            console.log('⚡ SmartLoan 性能监控系统已启动');
            updateMetrics();
        };
    </script>
</body>
</html>
