/**
 * 产品特征定义
 */
export interface ProductFeature {
  id: string;
  type: string;           // 特征类型
  value: string;          // 特征值
  name: string;          // 特征名称
  description?: string;   // 特征描述
}

/**
 * 产品要求定义
 */
export interface ProductRequirements {
  minAmount: number;          // 最小贷款金额
  maxAmount: number;          // 最大贷款金额
  minTerm: number;           // 最小贷款期限
  maxTerm: number;           // 最大贷款期限
  minIncome: number;         // 最低收入要求
  minCreditScore: number;    // 最低信用分
  location: LocationRequirement; // 地理位置要求
}

/**
 * 地理位置要求定义
 */
export interface LocationRequirement {
  exact?: string[];      // 精确匹配(区县级)
  city?: string[];       // 城市级匹配
  province?: string[];   // 省级匹配
}

/**
 * 位置匹配规则
 */
export interface LocationMatchRule {
  type: 'exact' | 'city' | 'province';
  locations: string[];
  score: number;
}

/**
 * 用户位置信息
 */
export interface UserLocation {
  province: string;
  city: string;
  district: string;
}

/**
 * 产品定义
 */
export interface Product {
  id: string;
  name: string;
  description?: string;
  interestRate: number;
  features: ProductFeature[];
  requirements: ProductRequirements;
  popularityScore?: number;
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
}
