import { User } from '../entities/user.entity';
import { LoanType } from '../enums/loan-type.enum';
import { EmploymentStatus } from '../enums/employment-status.enum';
import { CollateralType } from '../enums/collateral-type.enum';
import { LoanPurpose } from '../enums/loan-purpose.enum';
import { Role } from '../enums/role.enum';

export const createMockUser = (): User => ({
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  password: 'hashedPassword',
  name: 'Test User',
  phone: '13800138000',
  role: Role.USER,
  isActive: true,
  lastLoginAt: new Date(),
  createdAt: new Date(),
  updatedAt: new Date()
});
  roles: ['ADMIN']
};

export const createMockRepository = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getMany: jest.fn().mockResolvedValue([])
  }))
});

export const createMockService = (methods: Record<string, jest.Mock>) => ({
  ...methods
}); 