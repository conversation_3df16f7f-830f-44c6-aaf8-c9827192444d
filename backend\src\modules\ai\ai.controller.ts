import { Controller, Post, Body, UseInterceptors, UploadedFile, UseGuards } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { AIService } from './ai-service';
import { ApiTags, ApiOperation, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { AIAdvisorQueryDto } from './dto/ai-advisor.dto';
import { CreditAnalysisDto } from './dto/credit-analysis.dto';
import { CustomThrottlerGuard } from '../../common/guards/throttler.guard';
import { CacheInterceptor } from '../../common/interceptors/cache.interceptor';

@ApiTags('AI服务')
@Controller('ai')
@UseGuards(CustomThrottlerGuard)
export class AIController {
  constructor(private readonly aiService: AIService) {}

  @Post('ocr')
  @UseInterceptors(FileInterceptor('image'))
  @ApiOperation({ summary: 'OCR识别' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async performOCR(@UploadedFile() file: Express.Multer.File) {
    return this.aiService.performOCR(file.buffer);
  }

  @Post('liveness')
  @UseInterceptors(FileInterceptor('video'))
  @ApiOperation({ summary: '活体检测' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        video: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async performLivenessDetection(@UploadedFile() file: Express.Multer.File) {
    const result = await this.aiService.performLivenessDetection(file.buffer);
    return {
      success: true,
      data: result,
      message: '活体检测完成'
    };
  }

  @Post('risk-assessment')
  @ApiOperation({ summary: '风险评估' })
  async performRiskAssessment(@Body() data: {
    user_profile: any;
    loan_application: any;
    financial_data: any;
  }) {
    const assessment = await this.aiService.performRiskAssessment(data);
    return {
      success: true,
      data: assessment,
      message: '风险评估完成'
    };
  }

  @Post('credit-analysis')
  @ApiOperation({ summary: '征信分析' })
  async analyzeCreditReport(@Body() data: {
    credit_report: any;
    user_id: number;
  }) {
    const analysis = await this.aiService.analyzeCreditReport(data.credit_report);
    return {
      success: true,
      data: analysis,
      message: '征信分析完成'
    };
  }

  @Post('advisor/chat')
  @ApiOperation({ summary: 'AI顾问对话' })
  async chatWithAdvisor(@Body() data: {
    query: string;
    context?: any;
    user_id?: number;
  }) {
    const response = await this.aiService.getAIAdvisorResponse(data.query, data.context);
    return {
      success: true,
      data: response,
      message: 'AI顾问响应成功'
    };
  }
  async detectLiveness(@UploadedFile() file: Express.Multer.File) {
    return this.aiService.detectLiveness(file.buffer);
  }
  @Post('advisor')
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'AI顾问咨询' })
  async getAIAdvisorResponse(@Body() queryDto: AIAdvisorQueryDto) {
    return this.aiService.getAIAdvisorResponse(queryDto.query, queryDto.context);
  }
}