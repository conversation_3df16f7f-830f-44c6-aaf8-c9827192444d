import React from 'react';
import { Form, Input, But<PERSON>, Card, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import styles from './Login.module.less';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = React.useState(false);

  const onFinish = async (values: any) => {
    try {
      setLoading(true);
      // 调用登录API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();
      if (response.ok) {
        // 保存token
        localStorage.setItem('token', data.token);
        message.success('登录成功');
        navigate('/');
      } else {
        throw new Error(data.message);
      }
    } catch (error: any) {
      message.error('登录失败：' + (error?.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <Card className={styles.card}>
          <div className={styles.header}>
            <img src="/logo.png" alt="SmartLoan" className={styles.logo} />
            <h1>SmartLoan智能金融服务平台</h1>
          </div>
          <Form
            name="login"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            size="large"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名！' }]}
            >
              <Input prefix={<UserOutlined />} placeholder="用户名" />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码！' }]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
              />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} block>
                登录
              </Button>
            </Form.Item>

            <div className={styles.footer}>
              <a onClick={() => navigate('/register')}>注册账号</a>
              <a onClick={() => navigate('/forgot-password')}>忘记密码？</a>
            </div>
          </Form>
        </Card>
      </div>
    </div>
  );
};

export default Login;
