// SmartLoan Quick API Server
const http = require('http');

const PORT = 3001;

// 2025年金融产品数据
const products = [
  {
    id: 1,
    name: '工商银行融e借2025版',
    provider: '中国工商银行',
    interest_rate: 3.85,
    amount_max: 800000,
    description: '2025年全新升级，AI智能审批，支持数字人民币'
  },
  {
    id: 2,
    name: '建设银行快贷Pro 2025',
    provider: '中国建设银行',
    interest_rate: 3.95,
    amount_max: 500000,
    description: '支持元宇宙场景，区块链征信'
  },
  {
    id: 3,
    name: '蚂蚁借呗升级版2025',
    provider: '蚂蚁金服',
    interest_rate: 4.2,
    amount_max: 300000,
    description: '支持Web3.0身份认证，绿色金融'
  }
];

const server = http.createServer((req, res) => {
  // CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const url = req.url;
  console.log(`${req.method} ${url}`);

  // 健康检查
  if (url === '/api/health') {
    res.writeHead(200);
    res.end(JSON.stringify({
      status: 'ok',
      service: 'SmartLoan API 2025',
      timestamp: new Date().toISOString()
    }));
    return;
  }

  // 智能匹配
  if (url === '/api/products/match/smart' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      const matches = products.map((product, i) => ({
        product,
        match_score: 0.9 - i * 0.1,
        ai_reasoning: `基于2025年AI算法推荐`,
        recommended_amount: 100000,
        recommended_rate: product.interest_rate
      }));

      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: matches,
        message: '智能匹配成功'
      }));
    });
    return;
  }

  // 风险评估
  if (url === '/api/ai/risk-assessment' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          risk_score: 75,
          risk_level: 'low',
          recommendation: '建议批准',
          ai_version: '2025.1.0'
        },
        message: '风险评估完成'
      }));
    });
    return;
  }

  // AI顾问
  if (url === '/api/ai/advisor/chat' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          response: '您好！我是SmartLoan 2025年AI金融顾问。',
          confidence: 0.95,
          ai_model: 'Fin-R1-2025'
        },
        message: 'AI顾问响应成功'
      }));
    });
    return;
  }

  // OCR
  if (url === '/api/ai/ocr' && req.method === 'POST') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: {
        type: 'ID_CARD',
        name: '张三',
        id_number: '110101199001011234',
        confidence: 0.98
      },
      message: '证件识别成功'
    }));
    return;
  }

  // 活体检测
  if (url === '/api/ai/liveness' && req.method === 'POST') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: {
        is_live: true,
        confidence: 0.95,
        face_detected: true
      },
      message: '活体检测完成'
    }));
    return;
  }

  // 404
  res.writeHead(404);
  res.end(JSON.stringify({ error: 'Not found' }));
});

server.listen(PORT, () => {
  console.log('🚀 SmartLoan Quick API started');
  console.log(`📍 http://localhost:${PORT}`);
  console.log('✅ Ready for requests!');
});

server.on('error', (err) => {
  console.error('Server error:', err);
});

process.on('SIGINT', () => {
  console.log('\nShutting down...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
