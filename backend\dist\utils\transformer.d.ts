export declare class Transformer {
    transform<T>(source: any, schema: Record<string, any>): T;
    formatCurrency(value: number): string;
    formatDate(date: Date, options?: Intl.DateTimeFormatOptions): string;
    formatNumber(value: number, options?: Intl.NumberFormatOptions): string;
    formatPercentage(value: number, options?: Intl.NumberFormatOptions): string;
    cleanData(data: any): any;
    transformData(data: Record<string, any>, mapping: Record<string, string>): Record<string, any>;
}
