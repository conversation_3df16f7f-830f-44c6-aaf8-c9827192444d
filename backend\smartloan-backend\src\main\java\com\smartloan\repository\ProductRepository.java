package com.smartloan.repository;

import com.smartloan.model.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    List<Product> findByIsActiveTrue();
    List<Product> findByIsActiveTrueAndMinAmountLessThanEqualAndMaxAmountGreaterThanEqual(
        java.math.BigDecimal amount, java.math.BigDecimal amount2);
}
