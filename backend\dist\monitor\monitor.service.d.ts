import { LoggerService } from '../logger/logger.service';
export declare class MonitorService {
    private readonly logger;
    private operations;
    private metrics;
    constructor(logger: LoggerService);
    startOperation(operationName: string): void;
    endOperation(operationName: string): void;
    getOperationMetrics(operationName: string): {
        count: number;
        average: number;
        max: number;
        min: number;
    };
    clearMetrics(): void;
}
