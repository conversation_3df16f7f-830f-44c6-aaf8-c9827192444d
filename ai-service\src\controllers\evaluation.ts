import { Request, Response } from 'express'
import { evaluateLoan } from '../services/evaluation'

export const evaluateLoanApplication = async (req: Request, res: Response) => {
  try {
    const application = req.body

    // 验证必填字段
    if (!application.amount || !application.term || !application.purpose) {
      return res.status(400).json({
        success: false,
        error: '缺少必填字段：amount, term, purpose'
      })
    }

    // 调用评估服务
    const result = await evaluate<PERSON>oan(application)
    
    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('贷款评估失败：', error)
    res.status(500).json({
      success: false,
      error: '评估失败'
    })
  }
}