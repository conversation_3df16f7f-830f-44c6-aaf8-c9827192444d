import { ProductRecommendationService } from '../services/product-recommendation.service';
export declare class ProductRecommendationController {
    private readonly productRecommendationService;
    constructor(productRecommendationService: ProductRecommendationService);
    getRecommendationsByUser(userId: string): Promise<import("../entities/product.entity").Product[]>;
    getPopularProducts(): Promise<import("../entities/product.entity").Product[]>;
    getSimilarProducts(id: string): Promise<import("../entities/product.entity").Product[]>;
}
