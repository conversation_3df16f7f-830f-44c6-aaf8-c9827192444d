"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CurrentLoanApplicationFiles = exports.CurrentLoanApplicationDebtToIncomeRatio = exports.CurrentLoanApplicationCreditScore = exports.CurrentLoanApplicationMonthlyIncome = exports.CurrentLoanApplicationTerm = exports.CurrentLoanApplicationAmount = exports.CurrentLoanApplicationType = exports.CurrentLoanApplicationStatus = exports.CurrentLoanApplicationId = exports.CurrentLoanApplication = exports.CurrentUser = void 0;
var common_1 = require("@nestjs/common");
exports.CurrentUser = (0, common_1.createParamDecorator)(function (data, ctx) {
    var request = ctx.switchToHttp().getRequest();
    return request.user;
});
exports.CurrentLoanApplication = (0, common_1.createParamDecorator)(function (data, ctx) {
    var request = ctx.switchToHttp().getRequest();
    return request.loanApplication;
});
exports.CurrentLoanApplicationId = (0, common_1.createParamDecorator)(function (data, ctx) {
    var request = ctx.switchToHttp().getRequest();
    return request.params.id;
});
exports.CurrentLoanApplicationStatus = (0, common_1.createParamDecorator)(function (data, ctx) {
    var request = ctx.switchToHttp().getRequest();
    return request.body.status;
});
exports.CurrentLoanApplicationType = (0, common_1.createParamDecorator)(function (data, ctx) {
    var request = ctx.switchToHttp().getRequest();
    return request.body.type;
});
exports.CurrentLoanApplicationAmount = (0, common_1.createParamDecorator)(function (data, ctx) {
    var request = ctx.switchToHttp().getRequest();
    return request.body.amount;
});
exports.CurrentLoanApplicationTerm = (0, common_1.createParamDecorator)(function (data, ctx) {
    var request = ctx.switchToHttp().getRequest();
    return request.body.term;
});
exports.CurrentLoanApplicationMonthlyIncome = (0, common_1.createParamDecorator)(function (data, ctx) {
    var request = ctx.switchToHttp().getRequest();
    return request.body.monthlyIncome;
});
exports.CurrentLoanApplicationCreditScore = (0, common_1.createParamDecorator)(function (data, ctx) {
    var request = ctx.switchToHttp().getRequest();
    return request.body.creditScore;
});
exports.CurrentLoanApplicationDebtToIncomeRatio = (0, common_1.createParamDecorator)(function (data, ctx) {
    var request = ctx.switchToHttp().getRequest();
    return request.body.debtToIncomeRatio;
});
exports.CurrentLoanApplicationFiles = (0, common_1.createParamDecorator)(function (data, ctx) {
    var request = ctx.switchToHttp().getRequest();
    return request.files;
});
