<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartLoan 2025 - API功能测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .header h1 { font-size: 3rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
        .feature-card { background: white; border-radius: 15px; padding: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); transition: all 0.3s ease; }
        .feature-card:hover { transform: translateY(-5px); box-shadow: 0 15px 40px rgba(0,0,0,0.3); }
        .feature-title { font-size: 1.4rem; color: #333; margin-bottom: 15px; display: flex; align-items: center; }
        .feature-icon { font-size: 2rem; margin-right: 10px; }
        .btn { background: linear-gradient(45deg, #1890ff, #722ed1); color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-size: 16px; transition: all 0.3s; width: 100%; margin-bottom: 15px; }
        .btn:hover { transform: scale(1.02); box-shadow: 0 5px 15px rgba(0,0,0,0.3); }
        .btn:disabled { background: #ccc; cursor: not-allowed; transform: none; }
        .demo-area { padding: 20px; background: #f8f9fa; border-radius: 10px; min-height: 150px; }
        .result-box { background: #e8f5e8; border: 1px solid #4caf50; border-radius: 8px; padding: 15px; margin-top: 10px; }
        .error-box { background: #ffebee; border: 1px solid #f44336; border-radius: 8px; padding: 15px; margin-top: 10px; color: #d32f2f; }
        .loading { background: #fff3e0; border: 1px solid #ff9800; border-radius: 8px; padding: 15px; margin-top: 10px; color: #f57c00; }
        .status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 6px; }
        .status-success { background: #4caf50; }
        .status-processing { background: #ff9800; animation: pulse 1s infinite; }
        .status-error { background: #f44336; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        .api-status { background: rgba(255,255,255,0.1); border-radius: 10px; padding: 15px; margin-bottom: 20px; color: white; }
        .input-group { margin-bottom: 15px; }
        .input-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .input-group input, .input-group select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 SmartLoan 2025</h1>
            <p>智能金融服务平台 - API功能测试</p>
            <p style="font-size: 14px; opacity: 0.8;">🔗 实时调用后端API接口</p>
        </div>

        <!-- API状态检查 -->
        <div class="api-status">
            <h3>🔗 API服务状态</h3>
            <div id="api-status">检查中...</div>
            <button class="btn" onclick="checkAPIStatus()" style="width: auto; margin-top: 10px;">🔄 重新检查</button>
        </div>

        <div class="features-grid">
            <!-- 1. 智能产品匹配 -->
            <div class="feature-card">
                <div class="feature-title">
                    <span class="feature-icon">🎯</span>
                    智能产品匹配引擎
                </div>
                <div class="input-group">
                    <label>贷款金额 (元)</label>
                    <input type="number" id="matchAmount" value="500000" placeholder="请输入贷款金额">
                </div>
                <div class="input-group">
                    <label>贷款期限 (月)</label>
                    <select id="matchTerm">
                        <option value="12">12个月</option>
                        <option value="24" selected>24个月</option>
                        <option value="36">36个月</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>贷款用途</label>
                    <select id="matchPurpose">
                        <option value="个人消费">个人消费</option>
                        <option value="经营周转">经营周转</option>
                        <option value="购房装修">购房装修</option>
                    </select>
                </div>
                <button class="btn" onclick="testSmartMatch()" id="matchBtn">🚀 开始智能匹配</button>
                <div id="match-demo" class="demo-area">
                    <div>💡 基于500+金融机构产品库，AI智能匹配最适合您的贷款产品</div>
                    <div id="match-result"></div>
                </div>
            </div>

            <!-- 2. OCR证件识别 -->
            <div class="feature-card">
                <div class="feature-title">
                    <span class="feature-icon">📷</span>
                    OCR证件识别系统
                </div>
                <div class="input-group">
                    <label>证件类型</label>
                    <select id="ocrType">
                        <option value="identity_card">身份证</option>
                        <option value="business_license">营业执照</option>
                        <option value="bank_card">银行卡</option>
                    </select>
                </div>
                <button class="btn" onclick="testOCR()" id="ocrBtn">📷 模拟OCR识别</button>
                <div id="ocr-demo" class="demo-area">
                    <div>🎮 基于沐曦MetaX GPU加速的OCR识别系统</div>
                    <div id="ocr-result"></div>
                </div>
            </div>

            <!-- 3. 活体检测 -->
            <div class="feature-card">
                <div class="feature-title">
                    <span class="feature-icon">👤</span>
                    活体检测系统
                </div>
                <button class="btn" onclick="testLiveness()" id="livenessBtn">📹 开始活体检测</button>
                <div id="liveness-demo" class="demo-area">
                    <div>🔒 基于GPU加速的实时活体检测，防止照片欺诈</div>
                    <div id="liveness-result"></div>
                </div>
            </div>

            <!-- 4. 风险评估 -->
            <div class="feature-card">
                <div class="feature-title">
                    <span class="feature-icon">🛡️</span>
                    AI风险评估系统
                </div>
                <div class="input-group">
                    <label>用户ID</label>
                    <input type="text" id="riskUserId" value="user_12345" placeholder="请输入用户ID">
                </div>
                <button class="btn" onclick="testRiskAssessment()" id="riskBtn">🔍 开始风险评估</button>
                <div id="risk-demo" class="demo-area">
                    <div>🧠 基于联邦学习的隐私保护风险评估</div>
                    <div id="risk-result"></div>
                </div>
            </div>

            <!-- 5. 贷款计算器 -->
            <div class="feature-card">
                <div class="feature-title">
                    <span class="feature-icon">💰</span>
                    智能贷款计算器
                </div>
                <div class="input-group">
                    <label>贷款金额 (元)</label>
                    <input type="number" id="calcAmount" value="1000000" placeholder="请输入贷款金额">
                </div>
                <div class="input-group">
                    <label>贷款期限 (月)</label>
                    <select id="calcTerm">
                        <option value="240">20年 (240期)</option>
                        <option value="360">30年 (360期)</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>还款方式</label>
                    <select id="calcMethod">
                        <option value="等额本息">等额本息</option>
                        <option value="等额本金">等额本金</option>
                    </select>
                </div>
                <button class="btn" onclick="testCalculator()" id="calcBtn">🧮 计算贷款方案</button>
                <div id="calc-demo" class="demo-area">
                    <div id="calc-result"></div>
                </div>
            </div>

            <!-- 6. AI虚拟顾问 -->
            <div class="feature-card">
                <div class="feature-title">
                    <span class="feature-icon">🤖</span>
                    AI虚拟顾问 (Fin-R1)
                </div>
                <div class="input-group">
                    <label>咨询问题</label>
                    <input type="text" id="aiMessage" value="我想申请贷款，有什么推荐吗？" placeholder="请输入您的问题">
                </div>
                <button class="btn" onclick="testAIAdvisor()" id="aiBtn">💬 咨询AI顾问</button>
                <div id="ai-demo" class="demo-area">
                    <div>🧠 基于沐曦Fin-R1大模型，7×24小时智能金融咨询</div>
                    <div id="ai-result"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3006/api';

        // 检查API状态
        async function checkAPIStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('api-status').innerHTML = `
                        <span class="status-indicator status-success"></span>
                        <strong>API服务正常</strong> | 
                        数据库: ${data.services.database} | 
                        Redis: ${data.services.redis} | 
                        GPU: ${data.services.metax_gpu} | 
                        AI: ${data.services.gitee_ai}
                    `;
                } else {
                    throw new Error('API响应异常');
                }
            } catch (error) {
                document.getElementById('api-status').innerHTML = `
                    <span class="status-indicator status-error"></span>
                    <strong>API服务异常:</strong> ${error.message}
                `;
            }
        }

        // 智能产品匹配
        async function testSmartMatch() {
            const btn = document.getElementById('matchBtn');
            const result = document.getElementById('match-result');
            
            btn.disabled = true;
            btn.textContent = '匹配中...';
            
            result.innerHTML = '<div class="loading"><span class="status-indicator status-processing"></span>AI智能匹配中，请稍候...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/products/match/smart`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        amount: parseInt(document.getElementById('matchAmount').value),
                        term: parseInt(document.getElementById('matchTerm').value),
                        purpose: document.getElementById('matchPurpose').value
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.data.length > 0) {
                    const product = data.data[0];
                    result.innerHTML = `
                        <div class="result-box">
                            <h4>🏆 智能匹配成功</h4>
                            <p><strong>推荐产品:</strong> ${product.product.name}</p>
                            <p><strong>金融机构:</strong> ${product.product.institution}</p>
                            <p><strong>匹配度:</strong> ${(product.match_score * 100).toFixed(1)}%</p>
                            <p><strong>利率范围:</strong> ${product.product.rate_min}% - ${product.product.rate_max}%</p>
                            <p><strong>AI推荐理由:</strong> ${product.ai_reasoning}</p>
                            <p><strong>处理时间:</strong> ${product.processing_time}</p>
                        </div>
                    `;
                } else {
                    throw new Error('未找到匹配的产品');
                }
            } catch (error) {
                result.innerHTML = `<div class="error-box">❌ 匹配失败: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
                btn.textContent = '🚀 开始智能匹配';
            }
        }

        // OCR识别
        async function testOCR() {
            const btn = document.getElementById('ocrBtn');
            const result = document.getElementById('ocr-result');
            
            btn.disabled = true;
            btn.textContent = '识别中...';
            
            result.innerHTML = '<div class="loading"><span class="status-indicator status-processing"></span>GPU加速OCR识别中...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/ai/ocr`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        documentType: document.getElementById('ocrType').value,
                        imageData: 'base64_image_data_placeholder'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const extracted = data.data.extracted_data;
                    result.innerHTML = `
                        <div class="result-box">
                            <h4>✅ OCR识别成功</h4>
                            <p><strong>证件类型:</strong> ${data.data.document_type}</p>
                            <p><strong>识别结果:</strong></p>
                            <ul>
                                ${Object.entries(extracted).map(([key, value]) => 
                                    `<li><strong>${key}:</strong> ${value}</li>`
                                ).join('')}
                            </ul>
                            <p><strong>置信度:</strong> ${(data.data.confidence * 100).toFixed(1)}%</p>
                            <p><strong>处理时间:</strong> ${data.data.processing_time}</p>
                        </div>
                    `;
                } else {
                    throw new Error('OCR识别失败');
                }
            } catch (error) {
                result.innerHTML = `<div class="error-box">❌ OCR识别失败: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
                btn.textContent = '📷 模拟OCR识别';
            }
        }

        // 活体检测
        async function testLiveness() {
            const btn = document.getElementById('livenessBtn');
            const result = document.getElementById('liveness-result');
            
            btn.disabled = true;
            btn.textContent = '检测中...';
            
            result.innerHTML = '<div class="loading"><span class="status-indicator status-processing"></span>活体检测中，请保持正脸...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/ai/liveness`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        videoData: 'base64_video_data_placeholder'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <div class="result-box">
                            <h4>✅ 活体检测${data.data.is_live ? '通过' : '失败'}</h4>
                            <p><strong>检测结果:</strong> ${data.data.is_live ? '真人活体' : '非活体'}</p>
                            <p><strong>置信度:</strong> ${(data.data.confidence * 100).toFixed(1)}%</p>
                            <p><strong>处理时间:</strong> ${data.data.processing_time}</p>
                        </div>
                    `;
                } else {
                    throw new Error('活体检测失败');
                }
            } catch (error) {
                result.innerHTML = `<div class="error-box">❌ 活体检测失败: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
                btn.textContent = '📹 开始活体检测';
            }
        }

        // 风险评估
        async function testRiskAssessment() {
            const btn = document.getElementById('riskBtn');
            const result = document.getElementById('risk-result');
            
            btn.disabled = true;
            btn.textContent = '评估中...';
            
            result.innerHTML = '<div class="loading"><span class="status-indicator status-processing"></span>联邦学习风险评估中...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/ai/risk-assessment`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: document.getElementById('riskUserId').value,
                        assessmentType: 'comprehensive'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <div class="result-box">
                            <h4>📊 风险评估完成</h4>
                            <p><strong>风险评分:</strong> ${data.data.risk_score}</p>
                            <p><strong>风险等级:</strong> ${data.data.risk_level}</p>
                            <p><strong>处理时间:</strong> ${data.data.processing_time}</p>
                        </div>
                    `;
                } else {
                    throw new Error('风险评估失败');
                }
            } catch (error) {
                result.innerHTML = `<div class="error-box">❌ 风险评估失败: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
                btn.textContent = '🔍 开始风险评估';
            }
        }

        // 贷款计算器
        async function testCalculator() {
            const btn = document.getElementById('calcBtn');
            const result = document.getElementById('calc-result');
            
            btn.disabled = true;
            btn.textContent = '计算中...';
            
            try {
                const response = await fetch(`${API_BASE}/loan/calculator`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        totalAmount: parseInt(document.getElementById('calcAmount').value),
                        loanTerm: parseInt(document.getElementById('calcTerm').value),
                        repaymentMethod: document.getElementById('calcMethod').value
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <div class="result-box">
                            <h4>📊 计算完成</h4>
                            <p><strong>月供金额:</strong> ¥${data.data.monthlyPaymentAmount.toLocaleString()}</p>
                            <p><strong>总还款:</strong> ¥${data.data.totalPayment.toLocaleString()}</p>
                            <p><strong>总利息:</strong> ¥${data.data.totalInterest.toLocaleString()}</p>
                            <p><strong>年利率:</strong> ${(data.data.annualRate * 100).toFixed(2)}%</p>
                        </div>
                    `;
                } else {
                    throw new Error('计算失败');
                }
            } catch (error) {
                result.innerHTML = `<div class="error-box">❌ 计算失败: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
                btn.textContent = '🧮 计算贷款方案';
            }
        }

        // AI顾问
        async function testAIAdvisor() {
            const btn = document.getElementById('aiBtn');
            const result = document.getElementById('ai-result');
            
            btn.disabled = true;
            btn.textContent = '咨询中...';
            
            result.innerHTML = '<div class="loading"><span class="status-indicator status-processing"></span>Fin-R1大模型思考中...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/ai/advisor/chat`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: document.getElementById('aiMessage').value
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <div class="result-box">
                            <h4>🤖 AI顾问回复</h4>
                            <p>${data.data.response}</p>
                            <p><strong>AI模型:</strong> ${data.data.model}</p>
                        </div>
                    `;
                } else {
                    throw new Error('AI咨询失败');
                }
            } catch (error) {
                result.innerHTML = `<div class="error-box">❌ AI咨询失败: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
                btn.textContent = '💬 咨询AI顾问';
            }
        }

        // 页面加载时检查API状态
        window.onload = function() {
            checkAPIStatus();
        };
    </script>
</body>
</html>
