import { En<PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { LoanApplication } from '../../loan/entities/loan-application.entity';

@Entity('audits')
export class Audit {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => User)
  user: User;

  @ManyToOne(() => LoanApplication)
  loan_application: LoanApplication;

  @Column()
  type: string;

  @Column()
  status: string;

  @Column('jsonb')
  result: any;

  @Column('jsonb')
  documents: any;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
} 