import { LoanProduct } from '../entities/loan-product.entity';
import { UserProfile } from '../interfaces/user-profile.interface';
export declare class ProductMatcherService {
    private readonly defaultWeights;
    calculateProductScore(product: LoanProduct, userProfile: UserProfile): number;
    private getDynamicWeights;
    matchProducts(products: LoanProduct[], userProfile: UserProfile): LoanProduct[];
}
