"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.evaluateLoan = void 0;
const evaluateLoan = async (application) => {
    // 多维度评估逻辑
    let totalScore = 0;
    let confidence = 0.85;
    // 1. 金额评估 (权重: 30%)
    const amountScore = calculateAmountScore(application.amount);
    totalScore += amountScore * 0.3;
    // 2. 期限评估 (权重: 20%)
    const termScore = calculateTermScore(application.term);
    totalScore += termScore * 0.2;
    // 3. 用途评估 (权重: 25%)
    const purposeScore = calculatePurposeScore(application.purpose);
    totalScore += purposeScore * 0.25;
    // 4. 综合风险评估 (权重: 25%)
    const riskScore = calculateRiskScore(application);
    totalScore += riskScore * 0.25;
    // 确保分数在合理范围内
    const finalScore = Math.max(20, Math.min(95, Math.round(totalScore)));
    // 确定风险等级
    let risk = 'MEDIUM';
    if (finalScore >= 75) {
        risk = 'LOW';
        confidence = 0.92;
    }
    else if (finalScore >= 55) {
        risk = 'MEDIUM';
        confidence = 0.85;
    }
    else {
        risk = 'HIGH';
        confidence = 0.78;
    }
    // 生成个性化建议
    const recommendation = generateRecommendation(finalScore, risk, application);
    return {
        score: finalScore,
        risk,
        recommendation,
        confidence
    };
};
exports.evaluateLoan = evaluateLoan;
// 金额评估函数
const calculateAmountScore = (amount) => {
    if (amount <= 10000)
        return 90;
    if (amount <= 50000)
        return 80;
    if (amount <= 100000)
        return 70;
    if (amount <= 300000)
        return 60;
    return 50;
};
// 期限评估函数
const calculateTermScore = (term) => {
    if (term <= 12)
        return 85;
    if (term <= 24)
        return 75;
    if (term <= 36)
        return 65;
    if (term <= 48)
        return 55;
    return 45;
};
// 用途评估函数
const calculatePurposeScore = (purpose) => {
    const purposeScores = {
        'personal': 70,
        'business': 65,
        'house': 80,
        'car': 75,
        'education': 85,
        'medical': 90,
        '个人消费': 70,
        '商业投资': 65,
        '房屋购买': 80,
        '汽车贷款': 75,
        '教育培训': 85,
        '医疗健康': 90,
        '装修': 75,
        '旅游': 65,
        '其他': 60
    };
    return purposeScores[purpose] || 60;
};
// 综合风险评估函数
const calculateRiskScore = (application) => {
    let score = 70;
    // 金额与期限的组合风险
    const amountToTermRatio = application.amount / application.term;
    if (amountToTermRatio > 20000)
        score -= 15;
    else if (amountToTermRatio > 10000)
        score -= 10;
    else if (amountToTermRatio > 5000)
        score -= 5;
    // 收入评估
    if (application.income) {
        const debtToIncomeRatio = (application.amount / application.term) / application.income;
        if (debtToIncomeRatio > 0.5)
            score -= 20;
        else if (debtToIncomeRatio > 0.3)
            score -= 10;
        else if (debtToIncomeRatio < 0.1)
            score += 10;
    }
    // 模拟用户历史行为评分
    const userRiskFactor = Math.random() * 20 - 10; // -10 到 +10 的随机因子
    score += userRiskFactor;
    return Math.max(30, Math.min(95, score));
};
// 生成个性化建议
const generateRecommendation = (score, risk, application) => {
    if (score >= 85) {
        return `优质客户，建议快速通过审批。预估年利率：4.8%-6.2%`;
    }
    else if (score >= 75) {
        return `信用良好，建议通过。预估年利率：6.8%-8.5%`;
    }
    else if (score >= 65) {
        return `基本符合条件，建议适当降低贷款金额或延长还款期限。预估年利率：8.5%-12%`;
    }
    else if (score >= 55) {
        return `需要补充更多资质证明材料，建议提供收入证明、资产证明等。预估年利率：12%-15%`;
    }
    else {
        return `当前条件不满足放贷要求，建议6个月后重新申请或寻求担保人支持`;
    }
};
exports.default = {
    evaluateLoan: exports.evaluateLoan
};
