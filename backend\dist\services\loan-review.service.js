"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoanReviewService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const loan_review_entity_1 = require("../entities/loan-review.entity");
const loan_application_entity_1 = require("../entities/loan-application.entity");
const logger_service_1 = require("../logger/logger.service");
let LoanReviewService = class LoanReviewService {
    constructor(loanReviewRepository, loanApplicationRepository, logger) {
        this.loanReviewRepository = loanReviewRepository;
        this.loanApplicationRepository = loanApplicationRepository;
        this.logger = logger;
    }
    async create(createDto, reviewerId) {
        try {
            const application = await this.loanApplicationRepository.findOne({
                where: { id: createDto.loanApplicationId.toString() }
            });
            if (!application) {
                throw new common_1.NotFoundException(`Loan application ${createDto.loanApplicationId} not found`);
            }
            const reviewData = {
                loanApplicationId: createDto.loanApplicationId,
                reviewerId: reviewerId,
                status: createDto.status,
                type: createDto.type,
                comments: createDto.comments,
                riskFactors: createDto.riskFactors,
                verificationResults: createDto.verificationResults,
                decisionFactors: createDto.decisionFactors,
            };
            const review = this.loanReviewRepository.create(reviewData);
            const saved = await this.loanReviewRepository.save(review);
            if (Array.isArray(saved)) {
                this.logger.log(`Created loan review ${saved[0]?.id} for application ${createDto.loanApplicationId}`);
                return saved[0];
            }
            this.logger.log(`Created loan review ${saved.id} for application ${createDto.loanApplicationId}`);
            return saved;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to create loan review: ${error.message}`);
            throw new common_1.BadRequestException('Failed to create loan review');
        }
    }
    async findAll(loanApplicationId) {
        try {
            return await this.loanReviewRepository.find({
                where: { loanApplicationId },
                order: { createdAt: 'DESC' }
            });
        }
        catch (error) {
            this.logger.error(`Failed to fetch loan reviews: ${error.message}`);
            throw new common_1.BadRequestException('Failed to fetch loan reviews');
        }
    }
    async findOne(id) {
        try {
            const review = await this.loanReviewRepository.findOne({
                where: { id }
            });
            if (!review) {
                throw new common_1.NotFoundException(`Loan review ${id} not found`);
            }
            return review;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to fetch loan review ${id}: ${error.message}`);
            throw new common_1.BadRequestException('Failed to fetch loan review');
        }
    }
    async getReviewStatistics(loanApplicationId) {
        try {
            const reviews = await this.findAll(loanApplicationId);
            const statistics = {
                total: reviews.length,
                byType: {},
                byStatus: {},
                averageRiskScore: 0
            };
            reviews.forEach(review => {
                statistics.byType[review.type] = (statistics.byType[review.type] || 0) + 1;
                statistics.byStatus[review.status] = (statistics.byStatus[review.status] || 0) + 1;
                if (review.riskFactors) {
                    const riskScore = review.riskFactors.reduce((sum, factor) => sum + factor.score * factor.weight, 0);
                    statistics.averageRiskScore += riskScore;
                }
            });
            if (reviews.length > 0) {
                statistics.averageRiskScore /= reviews.length;
            }
            return statistics;
        }
        catch (error) {
            this.logger.error(`Failed to get review statistics: ${error.message}`);
            throw new common_1.BadRequestException('Failed to get review statistics');
        }
    }
};
LoanReviewService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(loan_review_entity_1.LoanReview)),
    __param(1, (0, typeorm_1.InjectRepository)(loan_application_entity_1.LoanApplication)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        logger_service_1.LoggerService])
], LoanReviewService);
exports.LoanReviewService = LoanReviewService;
//# sourceMappingURL=loan-review.service.js.map