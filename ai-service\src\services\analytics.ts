import { LoanApplication, EvaluationResult } from '../types'

interface AnalyticsData {
  totalApplications: number
  approvalRate: number
  averageScore: number
  averageAmount: number
  averageTerm: number
  monthlyStats: {
    [key: string]: {
      applications: number
      approvals: number
      averageScore: number
      averageAmount: number
      averageTerm: number
    }
  }
  incomeDistribution: {
    [key: string]: number
  }
  assetDistribution: {
    house: {
      [key: string]: number
    }
    car: {
      [key: string]: number
    }
  }
  scoreDistribution: {
    [key: string]: number
  }
  loanPurposeDistribution: {
    [key: string]: number
  }
  maritalStatusDistribution: {
    [key: string]: number
  }
  positionDistribution: {
    [key: string]: number
  }
  comparisonData: {
    incomeScore: {
      [key: string]: {
        count: number
        averageScore: number
        approvalRate: number
      }
    }
    assetApproval: {
      [key: string]: {
        count: number
        approvalRate: number
        averageScore: number
      }
    }
    amountTerm: {
      [key: string]: {
        count: number
        averageTerm: number
        approvalRate: number
      }
    }
    purposeApproval: {
      [key: string]: {
        count: number
        approvalRate: number
        averageAmount: number
      }
    }
  }
}

export class AnalyticsService {
  private analyticsData: AnalyticsData = {
    totalApplications: 0,
    approvalRate: 0,
    averageScore: 0,
    averageAmount: 0,
    averageTerm: 0,
    monthlyStats: {},
    incomeDistribution: {},
    assetDistribution: {
      house: {},
      car: {}
    },
    scoreDistribution: {
      '0-20': 0,
      '21-40': 0,
      '41-60': 0,
      '61-80': 0,
      '81-100': 0
    },
    loanPurposeDistribution: {},
    maritalStatusDistribution: {},
    positionDistribution: {},
    comparisonData: {
      incomeScore: {},
      assetApproval: {},
      amountTerm: {},
      purposeApproval: {}
    }
  }

  public updateAnalytics(application: LoanApplication, result: EvaluationResult) {
    // 更新总数
    this.analyticsData.totalApplications++

    // 更新月度统计
    const month = new Date().toISOString().slice(0, 7)
    if (!this.analyticsData.monthlyStats[month]) {
      this.analyticsData.monthlyStats[month] = {
        applications: 0,
        approvals: 0,
        averageScore: 0,
        averageAmount: 0,
        averageTerm: 0
      }
    }
    this.analyticsData.monthlyStats[month].applications++
    if (result.status === 'approved') {
      this.analyticsData.monthlyStats[month].approvals++
    }
    this.analyticsData.monthlyStats[month].averageScore = 
      (this.analyticsData.monthlyStats[month].averageScore * (this.analyticsData.monthlyStats[month].applications - 1) + result.score) / 
      this.analyticsData.monthlyStats[month].applications
    this.analyticsData.monthlyStats[month].averageAmount = 
      (this.analyticsData.monthlyStats[month].averageAmount * (this.analyticsData.monthlyStats[month].applications - 1) + application.loanAmount) / 
      this.analyticsData.monthlyStats[month].applications
    this.analyticsData.monthlyStats[month].averageTerm = 
      (this.analyticsData.monthlyStats[month].averageTerm * (this.analyticsData.monthlyStats[month].applications - 1) + application.loanTerm) / 
      this.analyticsData.monthlyStats[month].applications

    // 更新收入分布
    const incomeRange = this.getIncomeRange(application.monthlyIncome)
    this.analyticsData.incomeDistribution[incomeRange] = 
      (this.analyticsData.incomeDistribution[incomeRange] || 0) + 1

    // 更新资产分布
    this.analyticsData.assetDistribution.house[application.houseStatus] = 
      (this.analyticsData.assetDistribution.house[application.houseStatus] || 0) + 1
    this.analyticsData.assetDistribution.car[application.carStatus] = 
      (this.analyticsData.assetDistribution.car[application.carStatus] || 0) + 1

    // 更新评分分布
    const scoreRange = this.getScoreRange(result.score)
    this.analyticsData.scoreDistribution[scoreRange]++

    // 更新贷款用途分布
    this.analyticsData.loanPurposeDistribution[application.loanPurpose] = 
      (this.analyticsData.loanPurposeDistribution[application.loanPurpose] || 0) + 1

    // 更新婚姻状况分布
    this.analyticsData.maritalStatusDistribution[application.maritalStatus] = 
      (this.analyticsData.maritalStatusDistribution[application.maritalStatus] || 0) + 1

    // 更新职位分布
    this.analyticsData.positionDistribution[application.position] = 
      (this.analyticsData.positionDistribution[application.position] || 0) + 1

    // 更新对比数据
    this.updateComparisonData(application, result)

    // 更新平均值
    this.updateAverages(application, result)
  }

  private updateComparisonData(application: LoanApplication, result: EvaluationResult) {
    // 更新收入与评分关系
    const incomeRange = this.getIncomeRange(application.monthlyIncome)
    if (!this.analyticsData.comparisonData.incomeScore[incomeRange]) {
      this.analyticsData.comparisonData.incomeScore[incomeRange] = {
        count: 0,
        averageScore: 0,
        approvalRate: 0
      }
    }
    const incomeScoreData = this.analyticsData.comparisonData.incomeScore[incomeRange]
    incomeScoreData.count++
    incomeScoreData.averageScore = 
      (incomeScoreData.averageScore * (incomeScoreData.count - 1) + result.score) / incomeScoreData.count
    if (result.status === 'approved') {
      incomeScoreData.approvalRate = (incomeScoreData.approvalRate * (incomeScoreData.count - 1) + 1) / incomeScoreData.count
    }

    // 更新资产与通过率关系
    const assetKey = this.getAssetKey(application)
    if (!this.analyticsData.comparisonData.assetApproval[assetKey]) {
      this.analyticsData.comparisonData.assetApproval[assetKey] = {
        count: 0,
        approvalRate: 0,
        averageScore: 0
      }
    }
    const assetData = this.analyticsData.comparisonData.assetApproval[assetKey]
    assetData.count++
    assetData.averageScore = 
      (assetData.averageScore * (assetData.count - 1) + result.score) / assetData.count
    if (result.status === 'approved') {
      assetData.approvalRate = (assetData.approvalRate * (assetData.count - 1) + 1) / assetData.count
    }

    // 更新金额与期限关系
    const amountRange = this.getAmountRange(application.loanAmount)
    if (!this.analyticsData.comparisonData.amountTerm[amountRange]) {
      this.analyticsData.comparisonData.amountTerm[amountRange] = {
        count: 0,
        averageTerm: 0,
        approvalRate: 0
      }
    }
    const amountData = this.analyticsData.comparisonData.amountTerm[amountRange]
    amountData.count++
    amountData.averageTerm = 
      (amountData.averageTerm * (amountData.count - 1) + application.loanTerm) / amountData.count
    if (result.status === 'approved') {
      amountData.approvalRate = (amountData.approvalRate * (amountData.count - 1) + 1) / amountData.count
    }

    // 更新用途与通过率关系
    if (!this.analyticsData.comparisonData.purposeApproval[application.loanPurpose]) {
      this.analyticsData.comparisonData.purposeApproval[application.loanPurpose] = {
        count: 0,
        approvalRate: 0,
        averageAmount: 0
      }
    }
    const purposeData = this.analyticsData.comparisonData.purposeApproval[application.loanPurpose]
    purposeData.count++
    purposeData.averageAmount = 
      (purposeData.averageAmount * (purposeData.count - 1) + application.loanAmount) / purposeData.count
    if (result.status === 'approved') {
      purposeData.approvalRate = (purposeData.approvalRate * (purposeData.count - 1) + 1) / purposeData.count
    }
  }

  private getIncomeRange(income: number): string {
    if (income < 5000) return '<5000'
    if (income < 10000) return '5000-10000'
    if (income < 15000) return '10000-15000'
    if (income < 20000) return '15000-20000'
    return '>20000'
  }

  private getScoreRange(score: number): string {
    if (score <= 20) return '0-20'
    if (score <= 40) return '21-40'
    if (score <= 60) return '41-60'
    if (score <= 80) return '61-80'
    return '81-100'
  }

  private getAmountRange(amount: number): string {
    if (amount < 100000) return '<10万'
    if (amount < 300000) return '10-30万'
    if (amount < 500000) return '30-50万'
    if (amount < 1000000) return '50-100万'
    return '>100万'
  }

  private getAssetKey(application: LoanApplication): string {
    if (application.houseStatus === 'owned' && application.carStatus === 'owned') {
      return '有房有车'
    } else if (application.houseStatus === 'owned') {
      return '有房无车'
    } else if (application.carStatus === 'owned') {
      return '无房有车'
    } else {
      return '无房无车'
    }
  }

  private updateAverages(application: LoanApplication, result: EvaluationResult) {
    const n = this.analyticsData.totalApplications
    this.analyticsData.averageScore = 
      (this.analyticsData.averageScore * (n - 1) + result.score) / n
    this.analyticsData.averageAmount = 
      (this.analyticsData.averageAmount * (n - 1) + application.loanAmount) / n
    this.analyticsData.averageTerm = 
      (this.analyticsData.averageTerm * (n - 1) + application.loanTerm) / n

    // 计算通过率
    const approvedCount = Object.values(this.analyticsData.monthlyStats)
      .reduce((sum, stat) => sum + stat.approvals, 0)
    this.analyticsData.approvalRate = (approvedCount / n) * 100
  }

  public getAnalytics(): AnalyticsData {
    return this.analyticsData
  }

  public getFilteredAnalytics(filters: {
    dateRange?: [string, string]
    incomeRange?: string
    assetStatus?: string
    loanPurpose?: string
    maritalStatus?: string
  }): AnalyticsData {
    // 实现数据筛选逻辑
    const filteredData = { ...this.analyticsData }
    
    if (filters.dateRange) {
      const [startDate, endDate] = filters.dateRange
      filteredData.monthlyStats = Object.entries(this.analyticsData.monthlyStats)
        .filter(([month]) => month >= startDate && month <= endDate)
        .reduce((acc, [month, stats]) => {
          acc[month] = stats
          return acc
        }, {} as typeof filteredData.monthlyStats)
    }

    if (filters.incomeRange) {
      // 筛选收入分布
      filteredData.incomeDistribution = {
        [filters.incomeRange]: this.analyticsData.incomeDistribution[filters.incomeRange] || 0
      }
    }

    if (filters.assetStatus) {
      // 筛选资产分布
      if (filters.assetStatus === 'owned') {
        filteredData.assetDistribution = {
          house: { owned: this.analyticsData.assetDistribution.house.owned || 0 },
          car: { owned: this.analyticsData.assetDistribution.car.owned || 0 }
        }
      } else if (filters.assetStatus === 'none') {
        filteredData.assetDistribution = {
          house: { none: this.analyticsData.assetDistribution.house.none || 0 },
          car: { none: this.analyticsData.assetDistribution.car.none || 0 }
        }
      }
    }

    if (filters.loanPurpose) {
      // 筛选贷款用途分布
      filteredData.loanPurposeDistribution = {
        [filters.loanPurpose]: this.analyticsData.loanPurposeDistribution[filters.loanPurpose] || 0
      }
    }

    if (filters.maritalStatus) {
      // 筛选婚姻状况分布
      filteredData.maritalStatusDistribution = {
        [filters.maritalStatus]: this.analyticsData.maritalStatusDistribution[filters.maritalStatus] || 0
      }
    }

    // 重新计算统计数据
    this.recalculateStats(filteredData)
    
    return filteredData
  }

  private recalculateStats(data: AnalyticsData) {
    // 重新计算总数
    data.totalApplications = Object.values(data.monthlyStats)
      .reduce((sum, stat) => sum + stat.applications, 0)

    // 重新计算通过率
    const approvedCount = Object.values(data.monthlyStats)
      .reduce((sum, stat) => sum + stat.approvals, 0)
    data.approvalRate = (approvedCount / data.totalApplications) * 100

    // 重新计算平均分
    data.averageScore = Object.values(data.monthlyStats)
      .reduce((sum, stat) => sum + stat.averageScore * stat.applications, 0) / data.totalApplications

    // 重新计算平均金额
    data.averageAmount = Object.values(data.monthlyStats)
      .reduce((sum, stat) => sum + stat.averageAmount * stat.applications, 0) / data.totalApplications

    // 重新计算平均期限
    data.averageTerm = Object.values(data.monthlyStats)
      .reduce((sum, stat) => sum + stat.averageTerm * stat.applications, 0) / data.totalApplications
  }
} 