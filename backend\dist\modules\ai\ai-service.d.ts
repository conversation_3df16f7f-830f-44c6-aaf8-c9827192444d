/// <reference types="node" />
/// <reference types="node" />
import { LoggerService } from '../../logger/logger.service';
import { ConfigService } from '@nestjs/config';
export declare class AIService {
    private readonly loggerService;
    private readonly config;
    private readonly gpuEndpoint;
    private readonly modelEndpoint;
    private readonly timeout;
    private readonly retryAttempts;
    private readonly retryDelay;
    private readonly logger;
    constructor(loggerService: LoggerService, config: ConfigService);
    performOCR(image: Buffer): Promise<any>;
    detectLiveness(video: Buffer): Promise<any>;
    analyzeCreditReport(report: any): Promise<any>;
    performLivenessDetection(videoBuffer: Buffer): Promise<any>;
    performRiskAssessment(data: {
        user_profile: any;
        loan_application: any;
        financial_data: any;
    }): Promise<any>;
    private generateRiskRecommendation;
    getAIAdvisorResponse(query: string, context?: any): Promise<any>;
    private makeRequest;
}
