import { LoggerService } from '../services/logger.service';
export declare class Crypto {
    private logger;
    private cipher;
    private decipher;
    private randomBytes;
    constructor(logger: LoggerService);
    encrypt(data: string | object): string;
    decrypt(encrypted: string): string | object;
    hash(data: string | object): string;
    verifyHash(data: string | object, hashValue: string): boolean;
    sign(data: string | object): string;
    verify(data: string | object, signature: string): boolean;
    generateKey(): string;
    generateIV(): string;
}
