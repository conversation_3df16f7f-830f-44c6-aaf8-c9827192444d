export declare class RiskModel {
    id: number;
    name: string;
    version: string;
    description: string;
    configuration: {
        features: string[];
        weights: Record<string, number>;
        thresholds: {
            high: number;
            medium: number;
            low: number;
        };
        rules: Array<{
            condition: string;
            action: string;
            weight: number;
        }>;
    };
    performance: {
        accuracy?: number;
        precision?: number;
        recall?: number;
        f1Score?: number;
        lastEvaluation?: Date;
        evaluationMetrics?: Record<string, number>;
    };
    metadata: {
        author?: string;
        tags?: string[];
        dependencies?: string[];
        trainingData?: {
            startDate: Date;
            endDate: Date;
            sampleCount: number;
        };
    };
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
