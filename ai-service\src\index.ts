import express from 'express'
import cors from 'cors'
import { evaluateLoan } from './services/evaluation'

const app = express()
const port = process.env.PORT || 9527

// 中间件
app.use(cors())
app.use(express.json())

// 健康检查路由
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'AI评估服务'
  })
})

// 贷款评估路由
app.post('/evaluate', async (req, res) => {
  try {
    const result = await evaluateLoan(req.body)
    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('评估失败：', error)
    res.status(500).json({
      success: false,
      error: '评估失败'
    })
  }
})

app.listen(port, () => {
  console.log(`AI服务运行在 http://localhost:${port}`)
})