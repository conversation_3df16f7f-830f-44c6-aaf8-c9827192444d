import { Repository } from 'typeorm';
import { Audit } from './entities/audit.entity';
import { LoggerService } from '../../logger/logger.service';
export declare class MultiModalAuditService {
    private readonly auditRepository;
    private readonly logger;
    constructor(auditRepository: Repository<Audit>, logger: LoggerService);
    processDocument(document: any, type: string): Promise<any>;
    performLivenessDetection(video: any): Promise<any>;
    performComprehensiveAudit(documents: any[], userId: number): Promise<any>;
    getAuditResult(auditId: number, userId: number): Promise<any>;
    getAuditHistory(params: {
        userId?: number;
        page?: number;
        limit?: number;
    }): Promise<any>;
    private performOCR;
    private validateDocument;
    private detectLiveness;
    private recordAuditResult;
    private calculateOverallScore;
    private generateRecommendation;
}
