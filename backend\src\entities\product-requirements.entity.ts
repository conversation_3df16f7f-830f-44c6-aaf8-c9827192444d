import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Product } from './product.entity';

@Entity('product_requirements')
export class ProductRequirements {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  productId: string;

  @Column({ type: 'int', nullable: true })
  minAge: number;

  @Column({ type: 'int', nullable: true })
  maxAge: number;

  @Column({ type: 'decimal', precision: 12, scale: 2, nullable: true })
  minIncome: number;

  @Column({ type: 'int', nullable: true })
  minCreditScore: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  maxDebtToIncomeRatio: number;

  @Column({ type: 'simple-array', nullable: true })
  employmentTypes: string[];

  @Column({ type: 'simple-array', nullable: true })
  requiredDocuments: string[];

  @Column({ type: 'int', nullable: true })
  workExperienceYears: number;

  @Column({ type: 'simple-array', nullable: true })
  additionalFactors: string[];

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @ManyToOne(() => Product, product => product.requirements)
  @JoinColumn({ name: 'productId' })
  product: Product;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
