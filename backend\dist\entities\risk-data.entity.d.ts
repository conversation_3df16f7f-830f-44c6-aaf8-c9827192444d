interface RiskDataStructure {
    radar: {
        indicators: Array<{
            name: string;
            max: number;
        }>;
        values: number[];
    };
    trend: {
        dates: string[];
        values: number[];
    };
    heatmap: {
        cities: string[];
        riskTypes: string[];
        data: number[][];
    };
}
export declare class RiskData {
    id: number;
    timeRange: string;
    data: RiskDataStructure;
    timestamp: Date;
}
export {};
