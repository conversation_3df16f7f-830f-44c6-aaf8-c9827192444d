import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ProductRecommendationService } from '../services/product-recommendation.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

@Controller('product-recommendation')
export class ProductRecommendationController {
  constructor(
    private readonly productRecommendationService: ProductRecommendationService
  ) {}

  @Get('user/:userId')
  @UseGuards(JwtAuthGuard)
  async getRecommendationsByUser(@Param('userId') userId: string) {
    return this.productRecommendationService.recommendProductsByUserFeatures(userId);
  }

  @Get('popular')
  @UseGuards(JwtAuthGuard)
  async getPopularProducts() {
    return this.productRecommendationService.getPopularProducts();
  }

  @Get('similar/:id')
  @UseGuards(JwtAuthGuard)
  async getSimilarProducts(@Param('id') id: string) {
    return this.productRecommendationService.getSimilarProducts(id);
  }
} 