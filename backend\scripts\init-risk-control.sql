-- 创建风控规则表
CREATE TABLE IF NOT EXISTS risk_rules (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    conditions JSONB NOT NULL,
    priority INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    weight FLOAT NOT NULL DEFAULT 1.0,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建风险评估表
CREATE TABLE IF NOT EXISTS risk_assessments (
    id SERIAL PRIMARY KEY,
    application_id INTEGER NOT NULL,
    risk_score FLOAT NOT NULL,
    risk_factors JSONB NOT NULL,
    decision VARCHAR(20) NOT NULL,
    market_factors JSONB,
    behavioral_data JSONB,
    credit_data JSONB,
    processing_time_ms INTEGER,
    is_processed BOOLEAN NOT NULL DEFAULT FALSE,
    processed_by <PERSON><PERSON><PERSON><PERSON>(255),
    remarks TEXT,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

-- 创建风险评估索引
CREATE INDEX IF NOT EXISTS idx_risk_assessments_application_id ON risk_assessments(application_id);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_created_at ON risk_assessments(created_at);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_risk_score ON risk_assessments(risk_score);

-- 创建风控规则索引
CREATE INDEX IF NOT EXISTS idx_risk_rules_priority ON risk_rules(priority);
CREATE INDEX IF NOT EXISTS idx_risk_rules_is_active ON risk_rules(is_active);

-- 添加时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为风控规则表添加更新时间触发器
DROP TRIGGER IF EXISTS update_risk_rules_updated_at ON risk_rules;
CREATE TRIGGER update_risk_rules_updated_at
    BEFORE UPDATE ON risk_rules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 添加示例风控规则
INSERT INTO risk_rules (name, description, conditions, priority, weight) VALUES
('信用分阈值规则', '基于信用分的基础风控规则', 
 '{"creditScore": {"min": 580, "weight": 0.4}, "maxDebtRatio": 0.6}', 100, 1.0),
('多头借贷规则', '检查用户在多个平台的借贷情况',
 '{"loanCount": {"max": 3, "weight": 0.3}, "totalDebt": {"max": 200000}}', 90, 0.8),
('收入稳定性规则', '评估用户收入的稳定性',
 '{"monthlyIncome": {"min": 5000, "weight": 0.3}, "employmentMonths": {"min": 12}}', 80, 0.7)
ON CONFLICT (id) DO NOTHING;
