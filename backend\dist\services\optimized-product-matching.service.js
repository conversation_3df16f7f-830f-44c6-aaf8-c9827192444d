"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var OptimizedProductMatchingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OptimizedProductMatchingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const cache_manager_1 = require("@nestjs/cache-manager");
const product_entity_1 = require("../entities/product.entity");
const risk_assessment_entity_1 = require("../entities/risk-assessment.entity");
const monitoring_service_1 = require("../services/monitoring.service");
const gpu_service_1 = require("../services/gpu.service");
let OptimizedProductMatchingService = OptimizedProductMatchingService_1 = class OptimizedProductMatchingService {
    constructor(productRepository, riskRepository, cacheManager, gpuService, monitoringService) {
        this.productRepository = productRepository;
        this.riskRepository = riskRepository;
        this.cacheManager = cacheManager;
        this.gpuService = gpuService;
        this.monitoringService = monitoringService;
        this.logger = new common_1.Logger(OptimizedProductMatchingService_1.name);
        this.BATCH_SIZE = 100;
        this.CACHE_TTL = 3600;
        this.MATCH_WEIGHTS = {
            amount: 0.30,
            term: 0.20,
            rate: 0.25,
            features: 0.15,
            location: 0.10
        };
    }
    async matchProducts(user, criteria) {
        const startTime = Date.now();
        try {
            if (!user || !user.id) {
                throw new Error('用户信息无效');
            }
            const cacheKey = this.generateCacheKey(user.id, criteria);
            const cached = await this.cacheManager.get(cacheKey);
            if (cached) {
                return cached;
            }
            const products = await this.getEligibleProducts(criteria);
            if (!products.length) {
                return [];
            }
            const riskAssessment = await this.getRiskAssessment(user);
            let matchedProducts;
            if (products.length >= this.BATCH_SIZE && this.gpuService) {
                try {
                    matchedProducts = await this.performGpuMatching(products, criteria);
                }
                catch (error) {
                    this.logger.warn('GPU匹配失败，回退到CPU处理', error.message);
                    matchedProducts = await this.cpuMatchProducts(products, criteria);
                }
            }
            else {
                matchedProducts = await this.cpuMatchProducts(products, criteria);
            }
            const finalResults = this.postProcessResults(matchedProducts, riskAssessment, criteria);
            await this.cacheManager.set(cacheKey, finalResults, this.CACHE_TTL);
            this.monitoringService.recordMetric('product_matching_duration', Date.now() - startTime);
            return finalResults;
        }
        catch (error) {
            this.logger.error('产品匹配失败', error);
            this.monitoringService.recordError('product_matching', error);
            throw error;
        }
    }
    async getEligibleProducts(criteria) {
        const query = this.productRepository.createQueryBuilder('product')
            .where('product.isActive = :isActive', { isActive: true });
        if (criteria.amount) {
            query.andWhere('product.minAmount <= :amount AND product.maxAmount >= :amount', { amount: criteria.amount });
        }
        if (criteria.term) {
            query.andWhere('product.minTerm <= :term AND product.maxTerm >= :term', { term: criteria.term });
        }
        return query.getMany();
    }
    async performGpuMatching(products, criteria) {
        this.monitoringService.recordMetric('gpu_matching_started', 1);
        const startTime = Date.now();
        try {
            const scores = await this.gpuService.processMatchingBatch(products.map(p => ({ product: p, criteria: criteria })));
            const results = products.map((product, index) => ({
                product,
                score: scores[index]?.matchScore || 0,
                details: this.calculateDetailScores(product, criteria)
            }));
            const processingTime = Date.now() - startTime;
            this.monitoringService.recordMetric('gpu_processing_time', processingTime);
            return results.sort((a, b) => b.score - a.score);
        }
        catch (error) {
            this.logger.error('GPU处理失败,回退到CPU', error);
            this.monitoringService.recordMetric('gpu_processing_error', 1);
            throw error;
        }
    }
    async cpuMatchProducts(products, criteria) {
        const results = [];
        for (const product of products) {
            const details = this.calculateDetailScores(product, criteria);
            const score = this.calculateOverallScore(details);
            results.push({
                product,
                score,
                details
            });
        }
        return results.sort((a, b) => b.score - a.score);
    }
    calculateDetailScores(product, criteria) {
        return {
            amountScore: this.calculateAmountScore(product, criteria.amount),
            termScore: this.calculateTermScore(product, criteria.term),
            rateScore: this.calculateRateScore(product),
            featureScore: this.calculateFeatureScore(product, criteria),
            locationScore: this.calculateLocationScore(product, criteria.location)
        };
    }
    calculateOverallScore(details) {
        return (details.amountScore * this.MATCH_WEIGHTS.amount +
            details.termScore * this.MATCH_WEIGHTS.term +
            details.rateScore * this.MATCH_WEIGHTS.rate +
            details.featureScore * this.MATCH_WEIGHTS.features +
            details.locationScore * this.MATCH_WEIGHTS.location);
    }
    calculateAmountScore(product, amount) {
        if (!amount || !product.minAmount || !product.maxAmount) {
            return 0.5;
        }
        if (amount < product.minAmount || amount > product.maxAmount) {
            return 0;
        }
        const range = product.maxAmount - product.minAmount;
        const position = (amount - product.minAmount) / range;
        return 1 - Math.abs(0.5 - position);
    }
    calculateTermScore(product, term) {
        if (!term || !product.minTerm || !product.maxTerm) {
            return 0.5;
        }
        if (term < product.minTerm || term > product.maxTerm) {
            return 0;
        }
        const range = product.maxTerm - product.minTerm;
        const position = (term - product.minTerm) / range;
        return 1 - Math.abs(0.5 - position);
    }
    calculateRateScore(product) {
        if (typeof product.interestRate !== 'number') {
            return 0.5;
        }
        const marketAverageRate = 8.5;
        const rateDiff = marketAverageRate - product.interestRate;
        return Math.min(1, Math.max(0, 0.5 + rateDiff * 0.1));
    }
    calculateFeatureScore(product, criteria) {
        if (!product.features?.length) {
            return 0.5;
        }
        try {
            this.monitoringService.recordMetric('feature_matching_started', 1);
            const features = product.features;
            let score = 0.5;
            if (criteria.features?.length) {
                const hasRequiredFeatures = criteria.features.every(f => features.some(pf => pf.name === f));
                if (hasRequiredFeatures) {
                    score += 0.3;
                }
                const extraFeatures = features.filter(f => !criteria.features?.includes(f.name));
                score += extraFeatures.length * 0.05;
            }
            this.monitoringService.recordMetric('feature_match_calculation_success', 1);
            return Math.min(1, score);
        }
        catch (error) {
            this.logger.error('特征匹配计算错误', error);
            this.monitoringService.recordMetric('feature_matching_error', 1);
            return 0.5;
        }
    }
    calculateLocationScore(product, location) {
        if (!product.requirements || !location) {
            return 0.8;
        }
        try {
            this.monitoringService.recordMetric('location_matching_started', 1);
            const locationRules = this.parseLocationRules(product.requirements);
            const userLocation = this.parseUserLocation(location);
            let maxScore = 0;
            for (const rule of locationRules) {
                if (this.matchLocationRule(userLocation, rule)) {
                    maxScore = Math.max(maxScore, rule.score);
                }
            }
            this.monitoringService.recordMetric('location_match_calculation_success', 1);
            this.monitoringService.recordMetric('location_match_score', maxScore);
            return maxScore;
        }
        catch (error) {
            this.logger.error('位置匹配计算错误', error);
            this.monitoringService.recordMetric('location_matching_error', 1);
            return 0.8;
        }
    }
    parseLocationRules(requirements) {
        const rules = [];
        if (requirements.location?.exact?.length) {
            rules.push({
                type: 'exact',
                locations: requirements.location.exact,
                score: 1.0
            });
        }
        if (requirements.location?.city?.length) {
            rules.push({
                type: 'city',
                locations: requirements.location.city,
                score: 0.8
            });
        }
        if (requirements.location?.province?.length) {
            rules.push({
                type: 'province',
                locations: requirements.location.province,
                score: 0.6
            });
        }
        return rules;
    }
    parseUserLocation(location) {
        const [province, city, district] = location.split('-');
        return { province, city, district };
    }
    matchLocationRule(userLocation, rule) {
        switch (rule.type) {
            case 'exact':
                return rule.locations.includes(`${userLocation.province}-${userLocation.city}-${userLocation.district}`);
            case 'city':
                return rule.locations.includes(`${userLocation.province}-${userLocation.city}`);
            case 'province':
                return rule.locations.includes(userLocation.province);
            default:
                return false;
        }
    }
    postProcessResults(matchedProducts, riskAssessment, criteria) {
        const filteredProducts = matchedProducts.filter(match => {
            const product = match.product;
            return this.isProductSuitableForRisk(product, riskAssessment);
        });
        return filteredProducts.map(match => ({
            ...match,
            score: this.calculateFinalScore(match, riskAssessment)
        })).sort((a, b) => b.score - a.score);
    }
    calculateFinalScore(match, riskAssessment) {
        const baseScore = match.score;
        const product = match.product;
        const riskPenalty = this.calculateRiskPenalty(product, riskAssessment);
        return Math.max(0, baseScore - riskPenalty);
    }
    calculateRiskPenalty(product, riskAssessment) {
        if (!riskAssessment)
            return 0;
        const riskScore = riskAssessment.riskScore || 0;
        const penalty = riskScore > 50 ? 0.2 : 0;
        return penalty;
    }
    isProductSuitableForRisk(product, riskAssessment) {
        if (!riskAssessment)
            return true;
        const riskScore = riskAssessment.riskScore || 0;
        return riskScore <= 80;
    }
    generateCacheKey(userId, criteria) {
        const criteriaHash = JSON.stringify(criteria);
        return `product_match:${userId}:${Buffer.from(criteriaHash).toString('base64')}`;
    }
    async getRiskAssessment(user) {
        try {
            const cacheKey = `risk:${user.id}`;
            const cachedAssessment = await this.cacheManager.get(cacheKey);
            if (cachedAssessment) {
                return cachedAssessment;
            }
            const assessment = await this.riskRepository.createQueryBuilder('risk')
                .where('risk.applicationId = :userId', { userId: user.id })
                .orderBy('risk.createdAt', 'DESC')
                .getOne();
            if (assessment) {
                await this.cacheManager.set(cacheKey, assessment, 3600);
            }
            return assessment;
        }
        catch (error) {
            this.logger.error('获取风险评估失败', error);
            return null;
        }
    }
};
OptimizedProductMatchingService = OptimizedProductMatchingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __param(1, (0, typeorm_1.InjectRepository)(risk_assessment_entity_1.RiskAssessment)),
    __param(2, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository, Object, gpu_service_1.GpuService,
        monitoring_service_1.MonitoringService])
], OptimizedProductMatchingService);
exports.OptimizedProductMatchingService = OptimizedProductMatchingService;
//# sourceMappingURL=optimized-product-matching.service.js.map