import { ConfigService } from '@nestjs/config';
export declare class MuxiGPUService {
    private configService;
    private readonly apiUrl;
    private readonly apiKey;
    constructor(configService: ConfigService);
    calculateRiskMetrics(timeRange: string): Promise<{
        radar: {
            indicators: {
                name: string;
                max: number;
            }[];
            values: any[];
        };
        trend: {
            dates: any;
            values: any;
        };
        heatmap: {
            cities: any;
            riskTypes: any;
            data: any;
        };
    }>;
    checkRiskAlerts(): Promise<any>;
    private formatRiskData;
}
