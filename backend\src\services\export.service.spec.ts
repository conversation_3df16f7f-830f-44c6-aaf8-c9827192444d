import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExportService } from './export.service';
import { LoanApplication } from '../entities/loan-application.entity';
import { LoggerService } from './logger.service';
import { ErrorHandler } from '../utils/error-handler';
import * as fs from 'fs';
import * as path from 'path';
import { User } from '../entities/user.entity';
import { LoanStatus, LoanType } from '../entities/loan-application.entity';
import { EmploymentStatus } from '../enums/employment-status.enum';
import { CollateralType } from '../enums/collateral-type.enum';
import { LoanPurpose } from '../enums/loan-purpose.enum';
import { Product } from '../entities/product.entity';

jest.mock('fs');
jest.mock('path');

describe('ExportService', () => {
  let service: ExportService;
  let loanApplicationRepository: Repository<LoanApplication>;
  let logger: LoggerService;
  let errorHandler: ErrorHandler;

  const mockLoanApplication = {
    id: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    monthlyIncome: 5000,
    loanAmount: 100000,
    loanTerm: 12,
    status: 'PENDING'
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ExportService,
        {
          provide: getRepositoryToken(LoanApplication),
          useValue: {
            find: jest.fn(),
            count: jest.fn()
          }
        },
        {
          provide: LoggerService,
          useValue: {
            error: jest.fn(),
            info: jest.fn()
          }
        },
        {
          provide: ErrorHandler,
          useValue: {
            handle: jest.fn()
          }
        }
      ]
    }).compile();

    service = module.get<ExportService>(ExportService);
    loanApplicationRepository = module.get<Repository<LoanApplication>>(getRepositoryToken(LoanApplication));
    logger = module.get<LoggerService>(LoggerService);
    errorHandler = module.get<ErrorHandler>(ErrorHandler);
  });

  describe('exportData', () => {
    it('should handle empty data', async () => {      jest.spyOn(loanApplicationRepository, 'find').mockResolvedValue([]);
      jest.spyOn(loanApplicationRepository, 'count').mockResolvedValue(0);

      const result = await service.exportData({});
      expect(result).toBeInstanceOf(Buffer);
    });

    it('should export data with filters', async () => {
      const mockData: LoanApplication[] = [
        {
          id: '1',
          userId: 'user1',
          productId: 'product1',
          amount: 10000,
          term: 12,
          type: LoanType.PERSONAL,
          purpose: LoanPurpose.PERSONAL,
          status: LoanStatus.PENDING,
          creditScore: 700,
          employmentStatus: EmploymentStatus.EMPLOYED,
          annualIncome: 100000,
          debtToIncomeRatio: 0.3,
          collateralType: CollateralType.NONE,
          interestRate: 5.5,
          monthlyPayment: 1000,
          totalPayment: 12000,
          approvedAmount: null,
          approvedRate: null,
          approvedTerm: null,
          applicationDate: new Date(),
          lastModified: new Date(),
          submittedAt: new Date(),
          processedAt: null,
          riskAssessment: {},
          documents: [],
          notes: '',
          user: {} as User,
          product: {} as Product,
          reviews: [],
          createdAt: new Date(),
          updatedAt: new Date(),          rejectionReason: '',
          approvedBy: '',
          approvedAt: null,
          rejectedBy: '',
          rejectedAt: null,
          workExperience: 5,
          riskScore: 0.3,
          metadata: {},
          documentMetadata: {},
          monthlyIncome: 8333,
          loanAmount: 10000,
          loanTerm: 12,
          loanType: LoanType.PERSONAL,
          loanPurpose: LoanPurpose.PERSONAL,
          loanStatus: LoanStatus.PENDING,
          monthlyDebt: 2500,
          assets: 50000,
          cancelledAt: null,
          cancelledBy: '',
          cancellationReason: '',
          employmentStatusEnum: EmploymentStatus.EMPLOYED,
          collateral: {}
        } as unknown as LoanApplication
      ];

      jest.spyOn(loanApplicationRepository, 'find').mockResolvedValue(mockData);
      jest.spyOn(loanApplicationRepository, 'count').mockResolvedValue(1);

      const result = await service.exportData({
        startDate: new Date(),
        endDate: new Date(),
        status: LoanStatus.PENDING
      })

      expect(result).toBeInstanceOf(Buffer)
      expect(loanApplicationRepository.find).toHaveBeenCalled()
    })

    it('应该成功导出Excel数据', async () => {
      const query = {
        format: 'excel',
        filename: 'test'
      };

      const result = await service.exportData(query);
      expect(result).toBeInstanceOf(Buffer);
    });

    it('应该成功导出PDF数据', async () => {
      const query = {
        format: 'pdf',
        filename: 'test'
      };

      const result = await service.exportData(query);
      expect(result).toBeInstanceOf(Buffer);
    });

    it('应该成功导出CSV数据', async () => {
      const query = {
        format: 'csv',
        filename: 'test'
      };

      const result = await service.exportData(query);
      expect(result).toBeInstanceOf(Buffer);
    });

    it('应该处理无效的导出格式', async () => {
      const query = {
        format: 'invalid',
        filename: 'test'
      };

      await expect(service.exportData(query)).rejects.toThrow('不支持的导出格式');
    });

    it('应该处理空数据', async () => {
      jest.spyOn(loanApplicationRepository, 'find').mockResolvedValueOnce([]);
      
      const query = {
        format: 'excel',
        filename: 'test'
      };

      const result = await service.exportData(query);
      expect(result).toBeInstanceOf(Buffer);
    });
  });

  describe('数据验证', () => {
    it('应该验证必填字段', async () => {
      const invalidData = [{
        monthlyIncome: 5000,
        loanAmount: 100000
      }];

      jest.spyOn(loanApplicationRepository, 'find').mockResolvedValueOnce(invalidData as any);

      const query = {
        format: 'excel',
        filename: 'test'
      };

      await expect(service.exportData(query)).rejects.toThrow('缺少必填字段');
    });

    it('应该验证字段类型', async () => {
      const invalidData = [{
        id: '1',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
        monthlyIncome: 5000
      }];

      jest.spyOn(loanApplicationRepository, 'find').mockResolvedValueOnce(invalidData as any);

      const query = {
        format: 'excel',
        filename: 'test'
      };

      await expect(service.exportData(query)).rejects.toThrow('ID必须是数字类型');
    });
  });

  describe('缓存机制', () => {
    it('应该使用缓存的模板', async () => {
      const query = {
        format: 'excel',
        filename: 'test',
        template: 'test-template'
      };

      const mockTemplate = Buffer.from('test template');
      jest.spyOn(fs, 'readFileSync').mockReturnValueOnce(mockTemplate);

      await service.exportData(query);
      await service.exportData(query);

      expect(fs.readFileSync).toHaveBeenCalledTimes(1);
    });

    it('应该使用缓存的数据', async () => {
      const query = {
        format: 'excel',
        filename: 'test'
      };

      await service.exportData(query);
      await service.exportData(query);

      expect(loanApplicationRepository.find).toHaveBeenCalledTimes(1);
    });
  });

  describe('性能优化', () => {
    it('应该处理大数据量', async () => {
      const largeData = Array(15000).fill(mockLoanApplication);
      jest.spyOn(loanApplicationRepository, 'find').mockResolvedValueOnce(largeData as any);

      const query = {
        format: 'excel',
        filename: 'test'
      };

      const result = await service.exportData(query);
      expect(result).toBeInstanceOf(Buffer);
    });

    it('应该压缩数据', async () => {
      const data = [{
        ...mockLoanApplication,
        nullField: null,
        undefinedField: undefined
      }];

      jest.spyOn(loanApplicationRepository, 'find').mockResolvedValueOnce(data as any);

      const query = {
        format: 'excel',
        filename: 'test'
      };

      const result = await service.exportData(query);
      expect(result).toBeInstanceOf(Buffer);
    });
  });
}); 