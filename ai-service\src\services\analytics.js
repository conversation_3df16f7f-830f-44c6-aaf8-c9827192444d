"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
var AnalyticsService = /** @class */ (function () {
    function AnalyticsService() {
        this.analyticsData = {
            totalApplications: 0,
            approvalRate: 0,
            averageScore: 0,
            averageAmount: 0,
            averageTerm: 0,
            monthlyStats: {},
            incomeDistribution: {},
            assetDistribution: {
                house: {},
                car: {}
            },
            scoreDistribution: {
                '0-20': 0,
                '21-40': 0,
                '41-60': 0,
                '61-80': 0,
                '81-100': 0
            },
            loanPurposeDistribution: {},
            maritalStatusDistribution: {},
            positionDistribution: {},
            comparisonData: {
                incomeScore: {},
                assetApproval: {},
                amountTerm: {},
                purposeApproval: {}
            }
        };
    }
    AnalyticsService.prototype.updateAnalytics = function (application, result) {
        // 更新总数
        this.analyticsData.totalApplications++;
        // 更新月度统计
        var month = new Date().toISOString().slice(0, 7);
        if (!this.analyticsData.monthlyStats[month]) {
            this.analyticsData.monthlyStats[month] = {
                applications: 0,
                approvals: 0,
                averageScore: 0,
                averageAmount: 0,
                averageTerm: 0
            };
        }
        this.analyticsData.monthlyStats[month].applications++;
        if (result.status === 'approved') {
            this.analyticsData.monthlyStats[month].approvals++;
        }
        this.analyticsData.monthlyStats[month].averageScore =
            (this.analyticsData.monthlyStats[month].averageScore * (this.analyticsData.monthlyStats[month].applications - 1) + result.score) /
                this.analyticsData.monthlyStats[month].applications;
        this.analyticsData.monthlyStats[month].averageAmount =
            (this.analyticsData.monthlyStats[month].averageAmount * (this.analyticsData.monthlyStats[month].applications - 1) + application.loanAmount) /
                this.analyticsData.monthlyStats[month].applications;
        this.analyticsData.monthlyStats[month].averageTerm =
            (this.analyticsData.monthlyStats[month].averageTerm * (this.analyticsData.monthlyStats[month].applications - 1) + application.loanTerm) /
                this.analyticsData.monthlyStats[month].applications;
        // 更新收入分布
        var incomeRange = this.getIncomeRange(application.monthlyIncome);
        this.analyticsData.incomeDistribution[incomeRange] =
            (this.analyticsData.incomeDistribution[incomeRange] || 0) + 1;
        // 更新资产分布
        this.analyticsData.assetDistribution.house[application.houseStatus] =
            (this.analyticsData.assetDistribution.house[application.houseStatus] || 0) + 1;
        this.analyticsData.assetDistribution.car[application.carStatus] =
            (this.analyticsData.assetDistribution.car[application.carStatus] || 0) + 1;
        // 更新评分分布
        var scoreRange = this.getScoreRange(result.score);
        this.analyticsData.scoreDistribution[scoreRange]++;
        // 更新贷款用途分布
        this.analyticsData.loanPurposeDistribution[application.loanPurpose] =
            (this.analyticsData.loanPurposeDistribution[application.loanPurpose] || 0) + 1;
        // 更新婚姻状况分布
        this.analyticsData.maritalStatusDistribution[application.maritalStatus] =
            (this.analyticsData.maritalStatusDistribution[application.maritalStatus] || 0) + 1;
        // 更新职位分布
        this.analyticsData.positionDistribution[application.position] =
            (this.analyticsData.positionDistribution[application.position] || 0) + 1;
        // 更新对比数据
        this.updateComparisonData(application, result);
        // 更新平均值
        this.updateAverages(application, result);
    };
    AnalyticsService.prototype.updateComparisonData = function (application, result) {
        // 更新收入与评分关系
        var incomeRange = this.getIncomeRange(application.monthlyIncome);
        if (!this.analyticsData.comparisonData.incomeScore[incomeRange]) {
            this.analyticsData.comparisonData.incomeScore[incomeRange] = {
                count: 0,
                averageScore: 0,
                approvalRate: 0
            };
        }
        var incomeScoreData = this.analyticsData.comparisonData.incomeScore[incomeRange];
        incomeScoreData.count++;
        incomeScoreData.averageScore =
            (incomeScoreData.averageScore * (incomeScoreData.count - 1) + result.score) / incomeScoreData.count;
        if (result.status === 'approved') {
            incomeScoreData.approvalRate = (incomeScoreData.approvalRate * (incomeScoreData.count - 1) + 1) / incomeScoreData.count;
        }
        // 更新资产与通过率关系
        var assetKey = this.getAssetKey(application);
        if (!this.analyticsData.comparisonData.assetApproval[assetKey]) {
            this.analyticsData.comparisonData.assetApproval[assetKey] = {
                count: 0,
                approvalRate: 0,
                averageScore: 0
            };
        }
        var assetData = this.analyticsData.comparisonData.assetApproval[assetKey];
        assetData.count++;
        assetData.averageScore =
            (assetData.averageScore * (assetData.count - 1) + result.score) / assetData.count;
        if (result.status === 'approved') {
            assetData.approvalRate = (assetData.approvalRate * (assetData.count - 1) + 1) / assetData.count;
        }
        // 更新金额与期限关系
        var amountRange = this.getAmountRange(application.loanAmount);
        if (!this.analyticsData.comparisonData.amountTerm[amountRange]) {
            this.analyticsData.comparisonData.amountTerm[amountRange] = {
                count: 0,
                averageTerm: 0,
                approvalRate: 0
            };
        }
        var amountData = this.analyticsData.comparisonData.amountTerm[amountRange];
        amountData.count++;
        amountData.averageTerm =
            (amountData.averageTerm * (amountData.count - 1) + application.loanTerm) / amountData.count;
        if (result.status === 'approved') {
            amountData.approvalRate = (amountData.approvalRate * (amountData.count - 1) + 1) / amountData.count;
        }
        // 更新用途与通过率关系
        if (!this.analyticsData.comparisonData.purposeApproval[application.loanPurpose]) {
            this.analyticsData.comparisonData.purposeApproval[application.loanPurpose] = {
                count: 0,
                approvalRate: 0,
                averageAmount: 0
            };
        }
        var purposeData = this.analyticsData.comparisonData.purposeApproval[application.loanPurpose];
        purposeData.count++;
        purposeData.averageAmount =
            (purposeData.averageAmount * (purposeData.count - 1) + application.loanAmount) / purposeData.count;
        if (result.status === 'approved') {
            purposeData.approvalRate = (purposeData.approvalRate * (purposeData.count - 1) + 1) / purposeData.count;
        }
    };
    AnalyticsService.prototype.getIncomeRange = function (income) {
        if (income < 5000)
            return '<5000';
        if (income < 10000)
            return '5000-10000';
        if (income < 15000)
            return '10000-15000';
        if (income < 20000)
            return '15000-20000';
        return '>20000';
    };
    AnalyticsService.prototype.getScoreRange = function (score) {
        if (score <= 20)
            return '0-20';
        if (score <= 40)
            return '21-40';
        if (score <= 60)
            return '41-60';
        if (score <= 80)
            return '61-80';
        return '81-100';
    };
    AnalyticsService.prototype.getAmountRange = function (amount) {
        if (amount < 100000)
            return '<10万';
        if (amount < 300000)
            return '10-30万';
        if (amount < 500000)
            return '30-50万';
        if (amount < 1000000)
            return '50-100万';
        return '>100万';
    };
    AnalyticsService.prototype.getAssetKey = function (application) {
        if (application.houseStatus === 'owned' && application.carStatus === 'owned') {
            return '有房有车';
        }
        else if (application.houseStatus === 'owned') {
            return '有房无车';
        }
        else if (application.carStatus === 'owned') {
            return '无房有车';
        }
        else {
            return '无房无车';
        }
    };
    AnalyticsService.prototype.updateAverages = function (application, result) {
        var n = this.analyticsData.totalApplications;
        this.analyticsData.averageScore =
            (this.analyticsData.averageScore * (n - 1) + result.score) / n;
        this.analyticsData.averageAmount =
            (this.analyticsData.averageAmount * (n - 1) + application.loanAmount) / n;
        this.analyticsData.averageTerm =
            (this.analyticsData.averageTerm * (n - 1) + application.loanTerm) / n;
        // 计算通过率
        var approvedCount = Object.values(this.analyticsData.monthlyStats)
            .reduce(function (sum, stat) { return sum + stat.approvals; }, 0);
        this.analyticsData.approvalRate = (approvedCount / n) * 100;
    };
    AnalyticsService.prototype.getAnalytics = function () {
        return this.analyticsData;
    };
    AnalyticsService.prototype.getFilteredAnalytics = function (filters) {
        var _a, _b, _c;
        // 实现数据筛选逻辑
        var filteredData = __assign({}, this.analyticsData);
        if (filters.dateRange) {
            var _d = filters.dateRange, startDate_1 = _d[0], endDate_1 = _d[1];
            filteredData.monthlyStats = Object.entries(this.analyticsData.monthlyStats)
                .filter(function (_a) {
                var month = _a[0];
                return month >= startDate_1 && month <= endDate_1;
            })
                .reduce(function (acc, _a) {
                var month = _a[0], stats = _a[1];
                acc[month] = stats;
                return acc;
            }, {});
        }
        if (filters.incomeRange) {
            // 筛选收入分布
            filteredData.incomeDistribution = (_a = {},
                _a[filters.incomeRange] = this.analyticsData.incomeDistribution[filters.incomeRange] || 0,
                _a);
        }
        if (filters.assetStatus) {
            // 筛选资产分布
            if (filters.assetStatus === 'owned') {
                filteredData.assetDistribution = {
                    house: { owned: this.analyticsData.assetDistribution.house.owned || 0 },
                    car: { owned: this.analyticsData.assetDistribution.car.owned || 0 }
                };
            }
            else if (filters.assetStatus === 'none') {
                filteredData.assetDistribution = {
                    house: { none: this.analyticsData.assetDistribution.house.none || 0 },
                    car: { none: this.analyticsData.assetDistribution.car.none || 0 }
                };
            }
        }
        if (filters.loanPurpose) {
            // 筛选贷款用途分布
            filteredData.loanPurposeDistribution = (_b = {},
                _b[filters.loanPurpose] = this.analyticsData.loanPurposeDistribution[filters.loanPurpose] || 0,
                _b);
        }
        if (filters.maritalStatus) {
            // 筛选婚姻状况分布
            filteredData.maritalStatusDistribution = (_c = {},
                _c[filters.maritalStatus] = this.analyticsData.maritalStatusDistribution[filters.maritalStatus] || 0,
                _c);
        }
        // 重新计算统计数据
        this.recalculateStats(filteredData);
        return filteredData;
    };
    AnalyticsService.prototype.recalculateStats = function (data) {
        // 重新计算总数
        data.totalApplications = Object.values(data.monthlyStats)
            .reduce(function (sum, stat) { return sum + stat.applications; }, 0);
        // 重新计算通过率
        var approvedCount = Object.values(data.monthlyStats)
            .reduce(function (sum, stat) { return sum + stat.approvals; }, 0);
        data.approvalRate = (approvedCount / data.totalApplications) * 100;
        // 重新计算平均分
        data.averageScore = Object.values(data.monthlyStats)
            .reduce(function (sum, stat) { return sum + stat.averageScore * stat.applications; }, 0) / data.totalApplications;
        // 重新计算平均金额
        data.averageAmount = Object.values(data.monthlyStats)
            .reduce(function (sum, stat) { return sum + stat.averageAmount * stat.applications; }, 0) / data.totalApplications;
        // 重新计算平均期限
        data.averageTerm = Object.values(data.monthlyStats)
            .reduce(function (sum, stat) { return sum + stat.averageTerm * stat.applications; }, 0) / data.totalApplications;
    };
    return AnalyticsService;
}());
exports.AnalyticsService = AnalyticsService;
