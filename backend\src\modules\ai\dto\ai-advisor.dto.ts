import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsObject, IsOptional } from 'class-validator';

export class AIAdvisorQueryDto {
  @ApiProperty({
    description: '用户问题',
    example: '我想申请一笔10万元的贷款，请问需要什么条件？',
  })
  @IsString()
  query: string;

  @ApiProperty({
    description: '上下文信息',
    example: {
      userId: '123',
      loanAmount: 100000,
      creditScore: 750,
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  context?: Record<string, any>;
} 