# SmartLoan 2025 Simple Test
Write-Host "SmartLoan 2025 Simple Test" -ForegroundColor Green
Write-Host "==========================" -ForegroundColor Green

# Test backend API
Write-Host "`nTesting backend API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Backend API is running" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Backend API is not running" -ForegroundColor Red
    exit 1
}

# Test smart matching
Write-Host "`nTesting smart matching..." -ForegroundColor Yellow
try {
    $data = '{"amount":500000,"term_months":36,"product_type":"personal","user_profile":{"income":20000,"credit_score":780}}'
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/products/match/smart" -Method POST -Body $data -ContentType "application/json" -UseBasicParsing -TimeoutSec 10
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Smart matching works" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Smart matching failed" -ForegroundColor Red
}

# Test loan calculator
Write-Host "`nTesting loan calculator..." -ForegroundColor Yellow
try {
    $data = '{"loanType":"商业贷款","totalAmount":1000000,"loanTerm":240,"repaymentMethod":"等额本息"}'
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/loan/calculator" -Method POST -Body $data -ContentType "application/json" -UseBasicParsing -TimeoutSec 10
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Loan calculator works" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Loan calculator failed" -ForegroundColor Red
}

# Test AI advisor
Write-Host "`nTesting AI advisor..." -ForegroundColor Yellow
try {
    $data = '{"query":"loan rates","context":{"user_type":"test"}}'
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/ai/advisor/chat" -Method POST -Body $data -ContentType "application/json" -UseBasicParsing -TimeoutSec 10
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ AI advisor works" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ AI advisor failed" -ForegroundColor Red
}

# Test products list
Write-Host "`nTesting products list..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/products" -UseBasicParsing -TimeoutSec 5
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Products list works" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Products list failed" -ForegroundColor Red
}

# Check demo page
Write-Host "`nChecking demo page..." -ForegroundColor Yellow
if (Test-Path "demo.html") {
    Write-Host "✅ Demo page exists" -ForegroundColor Green
} else {
    Write-Host "❌ Demo page missing" -ForegroundColor Red
}

Write-Host "`nTest completed!" -ForegroundColor Green
Write-Host "Open demo.html to see all features" -ForegroundColor Cyan
