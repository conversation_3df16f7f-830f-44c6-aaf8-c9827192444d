import { Repository } from 'typeorm';
import { Product } from '../entities/product.entity';
import { CacheService } from './cache.service';
import { LoggerService } from './logger.service';
export declare class ProductComparisonService {
    private readonly productRepository;
    private readonly cacheService;
    private readonly logger;
    private readonly CACHE_TTL;
    constructor(productRepository: Repository<Product>, cacheService: CacheService, logger: LoggerService);
    compareProducts(productIds: string[]): Promise<any>;
    private compareBasicInfo;
    private compareFeatures;
    private compareRequirements;
    private compareCosts;
    private compareBenefits;
    getComparisonMatrix(productIds: string[]): Promise<any>;
    private getBasicMatrix;
    private getFeaturesMatrix;
    private getRequirementsMatrix;
    private getCostsMatrix;
    private getBenefitsMatrix;
}
