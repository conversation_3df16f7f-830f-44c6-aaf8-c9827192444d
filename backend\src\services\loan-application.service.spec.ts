import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LoanApplicationService } from './loan-application.service';
import { LoanApplication } from '../entities/loan-application.entity';
import { CreateLoanApplicationDto } from '../dto/create-loan-application.dto';
import { LoanType } from '../enums/loan-type.enum';
import { EmploymentStatus } from '../enums/employment-status.enum';
import { CollateralType } from '../enums/collateral-type.enum';
import { LoanPurpose } from '../enums/loan-purpose.enum';
import { LoanStatus } from '../entities/loan-application.entity';
import { BadRequestException, NotFoundException } from '@nestjs/common';

describe('LoanApplicationService', () => {
  let service: LoanApplicationService;
  let repository: Repository<LoanApplication>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LoanApplicationService,
        {
          provide: getRepositoryToken(LoanApplication),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<LoanApplicationService>(LoanApplicationService);
    repository = module.get<Repository<LoanApplication>>(getRepositoryToken(LoanApplication));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });  describe('create', () => {
    it('should create a new loan application', async () => {
      const dto: CreateLoanApplicationDto = {
        amount: 10000,
        term: 12,
        type: LoanType.PERSONAL,
        employmentStatus: EmploymentStatus.EMPLOYED,
        collateral: CollateralType.NONE,
        purpose: LoanPurpose.PERSONAL,
        annualIncome: 50000,
        debtToIncomeRatio: 0.3
      };

      const expectedResult = {
        id: '1',
        ...dto,
        status: LoanStatus.PENDING,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRepository.create.mockReturnValue(expectedResult);
      mockRepository.save.mockResolvedValue(expectedResult);

      const result = await service.create(dto, '1');

      expect(result).toBeDefined();
      expect(mockRepository.create).toHaveBeenCalled();
      expect(mockRepository.save).toHaveBeenCalled();
    });    it('should throw error for invalid data', async () => {
      const dto: CreateLoanApplicationDto = {
        amount: 10000,
        term: 12,
        type: LoanType.PERSONAL,
        employmentStatus: EmploymentStatus.EMPLOYED,
        collateral: CollateralType.NONE,
        purpose: LoanPurpose.PERSONAL,
        annualIncome: 50000,
        debtToIncomeRatio: 0.3,
      };

      mockRepository.create.mockImplementation(() => {
        throw new BadRequestException('Invalid data');
      });

      await expect(service.create(dto, '1')).rejects.toThrow(BadRequestException);
    });
  });

  describe('updateStatus', () => {
    it('should update application status', async () => {
      const expectedResult = {
        id: '1',
        status: LoanStatus.APPROVED,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRepository.findOne.mockResolvedValue(expectedResult);
      mockRepository.save.mockResolvedValue(expectedResult);

      const result = await service.updateStatus('1', LoanStatus.APPROVED);

      expect(result).toBeDefined();
      expect(mockRepository.findOne).toHaveBeenCalledWith({ where: { id: '1' } });
    });

    it('should throw error for non-existent application', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.updateStatus('1', LoanStatus.APPROVED)).rejects.toThrow(NotFoundException);
    });
  });
});