// SmartLoan Quick API Server (CommonJS)
const http = require('http');

const PORT = 3001;

// 2025年最新利率配置
const interestRates = {
  providentFund: {
    fiveYearsBelow: 0.026, // 2.60%
    fiveYearsAbove: 0.031  // 3.10%
  },
  commercial: {
    oneYearBelow: 0.0435,   // 4.35%
    oneToFiveYears: 0.0475, // 4.75%
    fiveYearsAbove: 0.049   // 4.90%
  }
};

// 贷款计算函数
function calculateLoan(params) {
  const { loanType, totalAmount, commercialAmount, providentAmount,
          commercialRate, providentRate, loanTerm, repaymentMethod } = params;

  switch (loanType) {
    case '公积金贷款':
      return calculateProvidentFundLoan(totalAmount, loanTerm, repaymentMethod);
    case '商业贷款':
      return calculateCommercialLoan(totalAmount, commercialRate, loanTerm, repaymentMethod);
    case '组合贷款':
      return calculateCombinedLoan(commercialAmount, providentAmount, commercialRate, providentRate, loanTerm, repaymentMethod);
    default:
      throw new Error('不支持的贷款类型');
  }
}

function calculateProvidentFundLoan(amount, termMonths, method) {
  const termYears = termMonths / 12;
  const rate = termYears <= 5 ? interestRates.providentFund.fiveYearsBelow : interestRates.providentFund.fiveYearsAbove;
  return performCalculation(amount, rate, termMonths, method);
}

function calculateCommercialLoan(amount, rate, termMonths, method) {
  const actualRate = rate || getCommercialRate(termMonths);
  return performCalculation(amount, actualRate, termMonths, method);
}

function calculateCombinedLoan(commercialAmount, providentAmount, commercialRate, providentRate, termMonths, method) {
  const commercial = calculateCommercialLoan(commercialAmount, commercialRate, termMonths, method);
  const provident = calculateProvidentFundLoan(providentAmount, termMonths, method);

  return {
    monthlyPaymentAmount: commercial.monthlyPaymentAmount + provident.monthlyPaymentAmount,
    totalInterest: commercial.totalInterest + provident.totalInterest,
    totalPayment: commercial.totalPayment + provident.totalPayment,
    summary: {
      loanAmount: commercialAmount + providentAmount,
      interestRate: ((commercialRate || getCommercialRate(termMonths)) * commercialAmount +
                    (providentRate || getProvidentRate(termMonths)) * providentAmount) / (commercialAmount + providentAmount),
      loanTerm: termMonths,
      repaymentMethod: method
    }
  };
}

function performCalculation(amount, annualRate, termMonths, method) {
  const monthlyRate = annualRate / 12;

  switch (method) {
    case '等额本息':
      return calculateEqualPayment(amount, monthlyRate, termMonths);
    case '等额本金':
      return calculateEqualPrincipal(amount, monthlyRate, termMonths);
    case '先息后本':
      return calculateInterestFirst(amount, monthlyRate, termMonths);
    default:
      throw new Error('不支持的还款方式');
  }
}

function calculateEqualPayment(amount, monthlyRate, termMonths) {
  const monthlyPayment = amount * monthlyRate * Math.pow(1 + monthlyRate, termMonths) /
                        (Math.pow(1 + monthlyRate, termMonths) - 1);
  const totalPayment = monthlyPayment * termMonths;
  const totalInterest = totalPayment - amount;

  return {
    monthlyPaymentAmount: Math.round(monthlyPayment * 100) / 100,
    totalInterest: Math.round(totalInterest * 100) / 100,
    totalPayment: Math.round(totalPayment * 100) / 100,
    summary: {
      loanAmount: amount,
      interestRate: monthlyRate * 12,
      loanTerm: termMonths,
      repaymentMethod: '等额本息'
    }
  };
}

function calculateEqualPrincipal(amount, monthlyRate, termMonths) {
  const monthlyPrincipal = amount / termMonths;
  let totalInterest = 0;
  let remainingPrincipal = amount;

  for (let month = 1; month <= termMonths; month++) {
    const interest = remainingPrincipal * monthlyRate;
    totalInterest += interest;
    remainingPrincipal -= monthlyPrincipal;
  }

  const firstMonthPayment = monthlyPrincipal + (amount * monthlyRate);

  return {
    monthlyPaymentAmount: Math.round(firstMonthPayment * 100) / 100,
    totalInterest: Math.round(totalInterest * 100) / 100,
    totalPayment: Math.round((amount + totalInterest) * 100) / 100,
    summary: {
      loanAmount: amount,
      interestRate: monthlyRate * 12,
      loanTerm: termMonths,
      repaymentMethod: '等额本金'
    }
  };
}

function calculateInterestFirst(amount, monthlyRate, termMonths) {
  const monthlyInterest = amount * monthlyRate;
  const totalInterest = monthlyInterest * termMonths;

  return {
    monthlyPaymentAmount: Math.round(monthlyInterest * 100) / 100,
    totalInterest: Math.round(totalInterest * 100) / 100,
    totalPayment: Math.round((amount + totalInterest) * 100) / 100,
    summary: {
      loanAmount: amount,
      interestRate: monthlyRate * 12,
      loanTerm: termMonths,
      repaymentMethod: '先息后本'
    }
  };
}

function getCommercialRate(termMonths) {
  const termYears = termMonths / 12;
  if (termYears <= 1) return interestRates.commercial.oneYearBelow;
  if (termYears <= 5) return interestRates.commercial.oneToFiveYears;
  return interestRates.commercial.fiveYearsAbove;
}

function getProvidentRate(termMonths) {
  const termYears = termMonths / 12;
  return termYears <= 5 ? interestRates.providentFund.fiveYearsBelow : interestRates.providentFund.fiveYearsAbove;
}

// 2025年金融产品数据
const products = [
  {
    id: 1,
    name: '工商银行融e借2025版',
    provider: '中国工商银行',
    interest_rate: 3.85,
    amount_max: 800000,
    description: '2025年全新升级，AI智能审批，支持数字人民币'
  },
  {
    id: 2,
    name: '建设银行快贷Pro 2025',
    provider: '中国建设银行',
    interest_rate: 3.95,
    amount_max: 500000,
    description: '支持元宇宙场景，区块链征信'
  },
  {
    id: 3,
    name: '蚂蚁借呗升级版2025',
    provider: '蚂蚁金服',
    interest_rate: 4.2,
    amount_max: 300000,
    description: '支持Web3.0身份认证，绿色金融'
  }
];

const server = http.createServer((req, res) => {
  // CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const url = req.url;
  console.log(`${new Date().toLocaleTimeString()} ${req.method} ${url}`);

  // 健康检查
  if (url === '/api/health') {
    res.writeHead(200);
    res.end(JSON.stringify({
      status: 'ok',
      service: 'SmartLoan API 2025',
      version: '2025.1.0',
      timestamp: new Date().toISOString(),
      features: ['AI智能匹配', '多模态审核', '实时风控', 'AI虚拟顾问']
    }));
    return;
  }

  // 智能匹配
  if (url === '/api/products/match/smart' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      try {
        const data = JSON.parse(body || '{}');
        const matches = products.map((product, i) => ({
          product,
          match_score: 0.9 - i * 0.1,
          ai_reasoning: `基于2025年AI算法，${product.name}非常适合您的需求`,
          recommended_amount: Math.min(data.amount || 100000, product.amount_max),
          recommended_rate: product.interest_rate
        }));

        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: matches,
          message: '智能产品匹配成功',
          total: matches.length,
          timestamp: new Date().toISOString()
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, error: '请求格式错误' }));
      }
    });
    return;
  }

  // 风险评估
  if (url === '/api/ai/risk-assessment' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      const riskScore = Math.floor(Math.random() * 40) + 60; // 60-100分
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          risk_score: riskScore,
          risk_level: riskScore >= 80 ? 'low' : riskScore >= 60 ? 'medium' : 'high',
          risk_factors: riskScore < 70 ? ['需要进一步评估'] : [],
          recommendation: riskScore >= 80 ? '建议批准' : '建议进一步审核',
          analysis_timestamp: new Date().toISOString(),
          ai_version: '2025.1.0'
        },
        message: 'AI风险评估完成'
      }));
    });
    return;
  }

  // AI顾问
  if (url === '/api/ai/advisor/chat' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      try {
        const data = JSON.parse(body || '{}');
        const responses = {
          '贷款利率': '2025年央行基准利率下调后，个人信用贷款利率普遍在3.8%-6.5%之间，具体利率根据您的信用状况确定。',
          '申请条件': '2025年贷款申请更加便民：年满18周岁、稳定收入、良好信用记录。支持数字人民币。',
          '审批时间': '采用2025年最新AI技术，结合区块链征信，最快30秒完成审批。',
          '数字货币': '全面支持数字人民币(CBDC)，享受更低手续费和更快到账速度。',
          '元宇宙': '支持元宇宙场景消费贷款，包括虚拟房产、NFT抵押等创新业务。',
          '绿色金融': '提供碳中和贷款产品，支持新能源、环保项目，享受利率优惠。'
        };
        
        let response = '您好！我是SmartLoan 2025年AI金融顾问。基于最新的AI技术，我可以为您提供个性化的金融服务建议。请问您想了解什么？';
        
        if (data.query) {
          for (const [keyword, answer] of Object.entries(responses)) {
            if (data.query.includes(keyword)) {
              response = answer;
              break;
            }
          }
        }

        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            response,
            confidence: 0.95,
            timestamp: new Date().toISOString(),
            ai_model: 'Fin-R1-2025',
            suggestions: ['了解贷款利率', '申请条件', '审批时间', '数字货币支付', '元宇宙金融', '绿色金融']
          },
          message: 'AI顾问响应成功'
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, error: '请求格式错误' }));
      }
    });
    return;
  }

  // OCR识别
  if (url === '/api/ai/ocr' && req.method === 'POST') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: {
        type: 'ID_CARD',
        confidence: 0.98,
        extracted_data: {
          name: '张三',
          id_number: '110101199001011234',
          address: '北京市朝阳区xxx街道',
          issue_date: '2020-01-01',
          expiry_date: '2030-01-01'
        },
        ai_analysis: {
          document_quality: 'excellent',
          authenticity_score: 0.96,
          risk_indicators: []
        },
        processing_time: '0.3s',
        ai_version: '2025.1.0'
      },
      message: '证件识别成功'
    }));
    return;
  }

  // 活体检测
  if (url === '/api/ai/liveness' && req.method === 'POST') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: {
        is_live: Math.random() > 0.1, // 90%通过率
        confidence: Math.random() * 0.3 + 0.7,
        face_detected: true,
        quality_score: Math.random() * 0.2 + 0.8,
        analysis: {
          blink_detected: true,
          head_movement: true,
          expression_change: true,
          depth_analysis: true // 2025年新增3D深度分析
        },
        ai_version: '2025.1.0',
        processing_time: '0.5s'
      },
      message: '活体检测完成'
    }));
    return;
  }

  // 获取所有产品
  if (url === '/api/products' && req.method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: products,
      message: '获取产品列表成功',
      total: products.length,
      timestamp: new Date().toISOString()
    }));
    return;
  }

  // OCR识别API
  if (url === '/api/ocr/recognize' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      const data = JSON.parse(body || '{}');
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          documentType: data.documentType || 'identity_card',
          extractedData: { name: '张三', idNumber: '110101199001011234', address: '北京市朝阳区' },
          confidence: 0.95, processed: true, timestamp: new Date().toISOString()
        }
      }));
    });
    return;
  }

  // 活体检测API
  if (url === '/api/face/liveness' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: { isLive: true, confidence: 0.98, faceQuality: 'high', timestamp: new Date().toISOString() }
      }));
    });
    return;
  }

  // 资质评估API
  if (url === '/api/qualification/assess' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      const score = Math.random() * 40 + 60;
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          qualificationScore: Math.round(score),
          decision: score >= 80 ? 'APPROVED' : 'MANUAL_REVIEW',
          riskLevel: score >= 80 ? 'LOW' : 'MEDIUM'
        }
      }));
    });
    return;
  }

  // 产品对比API
  if (url === '/api/products/compare' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          products: [
            { id: 'icbc_001', name: '工商银行融e借', rate: 3.85, amount: 800000 },
            { id: 'ccb_001', name: '建设银行快贷', rate: 3.95, amount: 500000 },
            { id: 'cmb_001', name: '招商银行闪电贷', rate: 4.20, amount: 500000 }
          ],
          comparison: { bestRate: 3.85, bestAmount: 800000, recommendation: 'icbc_001' }
        }
      }));
    });
    return;
  }

  // 金融知识图谱问答API
  if (url === '/api/knowledge/qa' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      const data = JSON.parse(body || '{}');
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          question: data.question || '什么是数字人民币贷款？',
          answer: '数字人民币贷款是基于央行数字货币(CBDC)的新型贷款产品，具有即时到账、低成本、可追溯等优势。2025年已在全国范围内推广，支持智能合约自动执行还款。',
          confidence: 0.94,
          knowledge_source: 'Fin-R1知识图谱',
          related_topics: ['数字人民币', 'CBDC', '智能合约', '央行政策']
        }
      }));
    });
    return;
  }

  // 同业产品比价API
  if (url === '/api/products/compare/market' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          product_type: '个人信用贷款',
          market_analysis: {
            average_rate: 4.35,
            lowest_rate: 3.85,
            highest_rate: 5.20,
            market_trend: 'stable'
          },
          top_products: [
            { bank: '工商银行', product: '融e借', rate: 3.85, features: ['数字人民币', '30秒审批'] },
            { bank: '建设银行', product: '快贷', rate: 3.95, features: ['线上申请', '随借随还'] },
            { bank: '招商银行', product: '闪电贷', rate: 4.20, features: ['秒级放款', 'AI风控'] }
          ]
        }
      }));
    });
    return;
  }

  // 贷款计算器API
  if (url === '/api/loan/calculator' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      try {
        const data = JSON.parse(body || '{}');
        const result = calculateLoan(data);

        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: result,
          message: '贷款计算成功',
          timestamp: new Date().toISOString()
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({
          success: false,
          error: '计算参数错误',
          message: error.message
        }));
      }
    });
    return;
  }

  // 404
  res.writeHead(404);
  res.end(JSON.stringify({ 
    success: false,
    error: 'API接口未找到',
    available_endpoints: [
      'GET /api/health - 健康检查',
      'GET /api/products - 获取产品列表',
      'POST /api/products/match/smart - 智能产品匹配',
      'POST /api/products/compare - 产品对比',
      'POST /api/products/compare/market - 同业产品比价',
      'POST /api/loan/calculator - 贷款计算器',
      'POST /api/ocr/recognize - OCR识别',
      'POST /api/face/liveness - 活体检测',
      'POST /api/qualification/assess - 资质评估',
      'POST /api/knowledge/qa - 金融知识问答',
      'POST /api/ai/advisor/chat - AI顾问对话'
    ]
  }));
});

server.listen(PORT, () => {
  console.log('🚀 SmartLoan 2025 Backend API 已启动');
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
  console.log('🎯 核心功能: AI智能匹配、多模态审核、实时风控');
  console.log('');
  console.log('📡 可用接口:');
  console.log('  GET  /api/health - 健康检查');
  console.log('  POST /api/products/match/smart - 智能产品匹配');
  console.log('  POST /api/ai/risk-assessment - AI风险评估');
  console.log('  POST /api/ai/advisor/chat - AI顾问对话');
  console.log('  POST /api/ai/ocr - OCR识别');
  console.log('  POST /api/ai/liveness - 活体检测');
  console.log('  GET  /api/products - 获取产品列表');
  console.log('');
  console.log('✅ 服务器启动成功！可以开始测试API了。');
});

server.on('error', (err) => {
  console.error('服务器错误:', err);
  if (err.code === 'EADDRINUSE') {
    console.log(`端口 ${PORT} 已被占用，请检查是否有其他服务在运行`);
  }
});

process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
