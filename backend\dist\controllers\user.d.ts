import { UserService } from '../services/user.service';
export declare class UserController {
    private readonly userService;
    constructor(userService: UserService);
    getUserProfile(req: any): Promise<{
        code: number;
        data: import("../entities/user.entity").User;
    }>;
    updateUserProfile(req: any, updateData: any): Promise<{
        code: number;
        message: string;
        data: import("../entities/user.entity").User;
    }>;
    getUserById(id: string): Promise<{
        code: number;
        data: import("../entities/user.entity").User;
    }>;
}
export declare const register: (req: any, res: any) => Promise<void>;
export declare const login: (req: any, res: any) => Promise<void>;
export declare const getUserInfo: (req: any, res: any) => Promise<void>;
export declare const updateUserInfo: (req: any, res: any) => Promise<void>;
