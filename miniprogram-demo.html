<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>SmartLoan 2025 - 小程序版</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; background: #f5f5f5; }
        .container { max-width: 375px; margin: 0 auto; background: white; min-height: 100vh; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
        .header h1 { font-size: 1.5rem; margin-bottom: 5px; }
        .header p { font-size: 0.8rem; opacity: 0.9; }
        .nav-tabs { display: flex; background: white; border-bottom: 1px solid #e0e0e0; }
        .nav-tab { flex: 1; padding: 15px 10px; text-align: center; font-size: 0.9rem; cursor: pointer; transition: all 0.3s; }
        .nav-tab.active { color: #1890ff; border-bottom: 2px solid #1890ff; }
        .tab-content { padding: 20px; }
        .feature-card { background: white; border-radius: 12px; padding: 20px; margin-bottom: 15px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .feature-icon { font-size: 2rem; text-align: center; margin-bottom: 10px; }
        .feature-title { font-size: 1.1rem; font-weight: bold; margin-bottom: 8px; text-align: center; }
        .feature-desc { font-size: 0.8rem; color: #666; text-align: center; margin-bottom: 15px; }
        .btn { background: linear-gradient(45deg, #1890ff, #722ed1); color: white; border: none; padding: 12px 20px; border-radius: 25px; cursor: pointer; font-size: 0.9rem; width: 100%; transition: all 0.3s; }
        .btn:active { transform: scale(0.98); }
        .quick-actions { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-bottom: 20px; }
        .quick-action { background: white; border-radius: 12px; padding: 15px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1); cursor: pointer; transition: all 0.3s; }
        .quick-action:active { transform: scale(0.95); }
        .result-popup { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); display: none; z-index: 1000; }
        .popup-content { position: absolute; bottom: 0; left: 0; right: 0; background: white; border-radius: 20px 20px 0 0; padding: 30px 20px; max-height: 80vh; overflow-y: auto; }
        .popup-header { text-align: center; margin-bottom: 20px; }
        .popup-close { position: absolute; top: 15px; right: 20px; font-size: 1.5rem; cursor: pointer; }
        .status-item { display: flex; align-items: center; padding: 10px 0; border-bottom: 1px solid #f0f0f0; }
        .status-icon { width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; }
        .status-success { background: #e8f5e8; color: #4caf50; }
        .status-processing { background: #fff3e0; color: #ff9800; }
        .bottom-nav { position: fixed; bottom: 0; left: 50%; transform: translateX(-50%); width: 100%; max-width: 375px; background: white; border-top: 1px solid #e0e0e0; display: flex; }
        .nav-item { flex: 1; padding: 10px; text-align: center; cursor: pointer; transition: all 0.3s; }
        .nav-item.active { color: #1890ff; }
        .nav-icon { font-size: 1.2rem; margin-bottom: 5px; }
        .nav-label { font-size: 0.7rem; }
        .floating-btn { position: fixed; bottom: 80px; right: 20px; width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(45deg, #1890ff, #722ed1); color: white; border: none; font-size: 1.2rem; cursor: pointer; box-shadow: 0 4px 12px rgba(0,0,0,0.3); z-index: 100; }
        .calculator-input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 10px; font-size: 1rem; }
        .calculator-result { background: #e3f2fd; border-radius: 8px; padding: 15px; margin-top: 15px; }
        .upload-area-mini { border: 2px dashed #ccc; border-radius: 8px; padding: 20px; text-align: center; margin-bottom: 15px; }
        .progress-mini { width: 100%; height: 4px; background: #e0e0e0; border-radius: 2px; overflow: hidden; margin: 10px 0; }
        .progress-fill-mini { height: 100%; background: #4caf50; transition: width 1s ease; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🚀 SmartLoan</h1>
            <p>智能金融服务小程序</p>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <div class="nav-tab active" onclick="switchTab('home')">首页</div>
            <div class="nav-tab" onclick="switchTab('loan')">贷款</div>
            <div class="nav-tab" onclick="switchTab('review')">审核</div>
            <div class="nav-tab" onclick="switchTab('profile')">我的</div>
        </div>

        <!-- 首页内容 -->
        <div id="home-content" class="tab-content">
            <!-- 快捷操作 -->
            <div class="quick-actions">
                <div class="quick-action" onclick="quickMatch()">
                    <div style="font-size: 1.5rem; margin-bottom: 5px;">🎯</div>
                    <div style="font-size: 0.8rem;">智能匹配</div>
                </div>
                <div class="quick-action" onclick="quickCalculator()">
                    <div style="font-size: 1.5rem; margin-bottom: 5px;">💰</div>
                    <div style="font-size: 0.8rem;">贷款计算</div>
                </div>
                <div class="quick-action" onclick="quickOCR()">
                    <div style="font-size: 1.5rem; margin-bottom: 5px;">📷</div>
                    <div style="font-size: 0.8rem;">证件识别</div>
                </div>
                <div class="quick-action" onclick="quickAI()">
                    <div style="font-size: 1.5rem; margin-bottom: 5px;">🤖</div>
                    <div style="font-size: 0.8rem;">AI顾问</div>
                </div>
            </div>

            <!-- 推荐产品 -->
            <div class="feature-card">
                <div class="feature-title">🏆 为您推荐</div>
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <div style="flex: 1;">
                        <div style="font-weight: bold;">工商银行融e借2025版</div>
                        <div style="font-size: 0.8rem; color: #666;">利率3.85% | 额度80万</div>
                    </div>
                    <div style="color: #4caf50; font-weight: bold;">95%匹配</div>
                </div>
                <button class="btn" onclick="applyLoan()">立即申请</button>
            </div>

            <!-- 服务特色 -->
            <div class="feature-card">
                <div class="feature-title">✨ 服务特色</div>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; font-size: 0.8rem;">
                    <div>⚡ 30秒审批</div>
                    <div>🔒 安全可靠</div>
                    <div>💱 支持数字人民币</div>
                    <div>🤖 AI智能推荐</div>
                </div>
            </div>
        </div>

        <!-- 贷款内容 -->
        <div id="loan-content" class="tab-content" style="display:none;">
            <div class="feature-card">
                <div class="feature-title">💰 贷款计算器</div>
                <input type="number" class="calculator-input" placeholder="贷款金额 (万元)" value="100" id="loanAmount">
                <select class="calculator-input" id="loanTerm">
                    <option value="240">20年</option>
                    <option value="360">30年</option>
                </select>
                <select class="calculator-input" id="loanType">
                    <option value="商业贷款">商业贷款</option>
                    <option value="公积金贷款">公积金贷款</option>
                    <option value="组合贷款">组合贷款</option>
                </select>
                <button class="btn" onclick="calculateLoan()">计算月供</button>
                <div id="calc-result" style="display:none;">
                    <div class="calculator-result">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>月供金额:</span>
                            <span style="font-weight: bold; color: #1890ff;" id="monthlyPayment">¥6,544</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>总利息:</span>
                            <span id="totalInterest">¥570,560</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>年利率:</span>
                            <span>4.90%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 热门产品 -->
            <div class="feature-card">
                <div class="feature-title">🔥 热门产品</div>
                <div style="space-y: 10px;">
                    <div style="border-bottom: 1px solid #f0f0f0; padding-bottom: 10px; margin-bottom: 10px;">
                        <div style="font-weight: bold;">建设银行快贷Pro</div>
                        <div style="font-size: 0.8rem; color: #666;">利率3.95% | 最高50万</div>
                    </div>
                    <div style="border-bottom: 1px solid #f0f0f0; padding-bottom: 10px; margin-bottom: 10px;">
                        <div style="font-weight: bold;">招商银行闪电贷AI版</div>
                        <div style="font-size: 0.8rem; color: #666;">利率4.20% | 最高50万</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 审核内容 -->
        <div id="review-content" class="tab-content" style="display:none;">
            <div class="feature-card">
                <div class="feature-title">📋 快速审核</div>
                <div class="upload-area-mini" onclick="uploadDocument()">
                    <div style="font-size: 1.5rem; margin-bottom: 5px;">📄</div>
                    <div style="font-size: 0.8rem;">点击上传证件</div>
                </div>
                <button class="btn" onclick="startReview()">开始审核</button>
            </div>

            <div class="feature-card">
                <div class="feature-title">👤 活体检测</div>
                <div style="text-align: center; margin-bottom: 15px;">
                    <div style="font-size: 3rem; margin-bottom: 10px;">📹</div>
                    <div style="font-size: 0.8rem; color: #666;">确保账户安全</div>
                </div>
                <button class="btn" onclick="startLiveness()">开始检测</button>
            </div>
        </div>

        <!-- 我的内容 -->
        <div id="profile-content" class="tab-content" style="display:none;">
            <div class="feature-card">
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                    <div style="width: 50px; height: 50px; border-radius: 50%; background: #1890ff; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2rem; margin-right: 15px;">👤</div>
                    <div>
                        <div style="font-weight: bold;">张三</div>
                        <div style="font-size: 0.8rem; color: #666;">信用评分: 85分</div>
                    </div>
                </div>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                    <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-size: 1.2rem; font-weight: bold; color: #1890ff;">3</div>
                        <div style="font-size: 0.7rem;">申请记录</div>
                    </div>
                    <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-size: 1.2rem; font-weight: bold; color: #4caf50;">2</div>
                        <div style="font-size: 0.7rem;">审核通过</div>
                    </div>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-title">📊 我的数据</div>
                <div class="status-item">
                    <div class="status-icon status-success">✓</div>
                    <div>
                        <div>身份认证</div>
                        <div style="font-size: 0.7rem; color: #666;">已完成</div>
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-icon status-success">✓</div>
                    <div>
                        <div>银行卡绑定</div>
                        <div style="font-size: 0.7rem; color: #666;">已绑定</div>
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-icon status-processing">⏳</div>
                    <div>
                        <div>收入证明</div>
                        <div style="font-size: 0.7rem; color: #666;">审核中</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="switchTab('home')">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">首页</div>
            </div>
            <div class="nav-item" onclick="switchTab('loan')">
                <div class="nav-icon">💰</div>
                <div class="nav-label">贷款</div>
            </div>
            <div class="nav-item" onclick="switchTab('review')">
                <div class="nav-icon">📋</div>
                <div class="nav-label">审核</div>
            </div>
            <div class="nav-item" onclick="switchTab('profile')">
                <div class="nav-icon">👤</div>
                <div class="nav-label">我的</div>
            </div>
        </div>

        <!-- 浮动AI助手按钮 -->
        <button class="floating-btn" onclick="openAI()">🤖</button>
    </div>

    <!-- 结果弹窗 -->
    <div class="result-popup" id="resultPopup">
        <div class="popup-content">
            <div class="popup-close" onclick="closePopup()">×</div>
            <div class="popup-header">
                <h3 id="popup-title">处理结果</h3>
            </div>
            <div id="popup-body">
                <!-- 动态内容 -->
            </div>
            <button class="btn" onclick="closePopup()" style="margin-top: 20px;">确定</button>
        </div>
    </div>

    <script>
        function switchTab(tab) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            document.querySelectorAll('.nav-tab, .nav-item').forEach(navItem => {
                navItem.classList.remove('active');
            });
            
            // 显示选中内容
            document.getElementById(tab + '-content').style.display = 'block';
            document.querySelector(`[onclick="switchTab('${tab}')"]`).classList.add('active');
            document.querySelectorAll(`[onclick="switchTab('${tab}')"]`).forEach(item => {
                item.classList.add('active');
            });
        }

        function showPopup(title, content) {
            document.getElementById('popup-title').textContent = title;
            document.getElementById('popup-body').innerHTML = content;
            document.getElementById('resultPopup').style.display = 'block';
        }

        function closePopup() {
            document.getElementById('resultPopup').style.display = 'none';
        }

        function quickMatch() {
            showPopup('🎯 智能匹配结果', `
                <div class="status-item">
                    <div class="status-icon status-success">🏆</div>
                    <div>
                        <div>工商银行融e借2025版</div>
                        <div style="font-size: 0.7rem; color: #666;">匹配度95% | 利率3.85%</div>
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-icon status-success">🥈</div>
                    <div>
                        <div>建设银行快贷Pro</div>
                        <div style="font-size: 0.7rem; color: #666;">匹配度92% | 利率3.95%</div>
                    </div>
                </div>
            `);
        }

        function quickCalculator() {
            switchTab('loan');
        }

        function quickOCR() {
            showPopup('📷 OCR识别结果', `
                <div class="progress-mini">
                    <div class="progress-fill-mini" style="width: 100%;"></div>
                </div>
                <div class="status-item">
                    <div class="status-icon status-success">✓</div>
                    <div>
                        <div>身份证识别成功</div>
                        <div style="font-size: 0.7rem; color: #666;">姓名: 张三 | 置信度: 95%</div>
                    </div>
                </div>
            `);
        }

        function quickAI() {
            showPopup('🤖 AI顾问回复', `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                    <div style="font-size: 0.9rem;">您好！基于您的信用状况，我推荐工商银行融e借2025版，支持数字人民币，30秒审批。</div>
                </div>
                <div style="font-size: 0.7rem; color: #666; text-align: center;">AI模型: Fin-R1-2025 | 置信度: 96%</div>
            `);
        }

        function calculateLoan() {
            const amount = document.getElementById('loanAmount').value;
            const term = document.getElementById('loanTerm').value;
            const monthlyPayment = (amount * 10000 * 0.049 / 12 * Math.pow(1 + 0.049/12, term)) / (Math.pow(1 + 0.049/12, term) - 1);
            
            document.getElementById('monthlyPayment').textContent = `¥${Math.round(monthlyPayment).toLocaleString()}`;
            document.getElementById('totalInterest').textContent = `¥${Math.round(monthlyPayment * term - amount * 10000).toLocaleString()}`;
            document.getElementById('calc-result').style.display = 'block';
        }

        function startReview() {
            showPopup('📋 审核进度', `
                <div class="progress-mini">
                    <div class="progress-fill-mini" style="width: 80%;"></div>
                </div>
                <div class="status-item">
                    <div class="status-icon status-success">✓</div>
                    <div>证件上传完成</div>
                </div>
                <div class="status-item">
                    <div class="status-icon status-processing">⏳</div>
                    <div>AI审核中...</div>
                </div>
            `);
        }

        function startLiveness() {
            showPopup('👤 活体检测', `
                <div style="text-align: center; margin-bottom: 15px;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">📹</div>
                    <div>检测中...</div>
                </div>
                <div class="progress-mini">
                    <div class="progress-fill-mini" style="width: 100%;"></div>
                </div>
                <div class="status-item">
                    <div class="status-icon status-success">✓</div>
                    <div>活体检测通过 (置信度: 98%)</div>
                </div>
            `);
        }

        function openAI() {
            quickAI();
        }

        // 页面加载完成
        window.onload = function() {
            console.log('📱 SmartLoan 小程序版已加载');
        };
    </script>
</body>
</html>
