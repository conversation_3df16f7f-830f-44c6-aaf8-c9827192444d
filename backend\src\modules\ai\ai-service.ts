import { Injectable, Logger } from '@nestjs/common';
import { LoggerService } from '../../logger/logger.service';
import { ConfigService } from '@nestjs/config';
import { retry } from 'rxjs/operators';
import { timeout } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { of } from 'rxjs';
import { Observable } from 'rxjs';

@Injectable()
export class AIService {
  private readonly gpuEndpoint: string;
  private readonly modelEndpoint: string;
  private readonly timeout: number;
  private readonly retryAttempts: number;
  private readonly retryDelay: number;
  private readonly logger = new Logger(AIService.name);

  constructor(
    private readonly loggerService: LoggerService,
    private readonly config: ConfigService,
  ) {
    const aiConfig = this.config.get('ai');
    this.gpuEndpoint = aiConfig.gpuEndpoint;
    this.modelEndpoint = aiConfig.modelEndpoint;
    this.timeout = aiConfig.timeout;
    this.retryAttempts = aiConfig.retryAttempts;
    this.retryDelay = aiConfig.retryDelay;
  }

  async performOCR(image: Buffer): Promise<any> {
    try {
      const response = await this.makeRequest(
        `${this.gpuEndpoint}/ocr`,
        {
          image: image.toString('base64'),
        },
        'OCR服务调用失败',
      );

      return response;
    } catch (error) {
      this.loggerService.error('OCR处理失败', { error });
      throw error;
    }
  }

  async detectLiveness(video: Buffer): Promise<any> {
    try {
      const response = await this.makeRequest(
        `${this.gpuEndpoint}/liveness`,
        {
          video: video.toString('base64'),
        },
        '活体检测服务调用失败',
      );

      return response;
    } catch (error) {
      this.loggerService.error('活体检测失败', { error });
      throw error;
    }
  }

  async getAIAdvisorResponse(query: string, context: any): Promise<any> {
    try {
      const response = await this.makeRequest(
        `${this.modelEndpoint}/chat`,
        {
          query,
          context,
        },
        'AI顾问服务调用失败',
      );

      return response;
    } catch (error) {
      this.loggerService.error('AI顾问响应失败', { error });
      throw error;
    }
  }

  async analyzeCreditReport(report: any): Promise<any> {
    try {
      const response = await this.makeRequest(
        `${this.gpuEndpoint}/credit-analysis`,
        {
          report,
        },
        '征信分析服务调用失败',
      );

      return response;
    } catch (error) {
      this.loggerService.error('征信分析失败', { error });
      throw error;
    }
  }

  async performLivenessDetection(videoBuffer: Buffer): Promise<any> {
    try {
      // 模拟活体检测逻辑
      const mockResult = {
        is_live: Math.random() > 0.1, // 90% 通过率
        confidence: Math.random() * 0.3 + 0.7, // 0.7-1.0 置信度
        face_detected: true,
        quality_score: Math.random() * 0.2 + 0.8, // 0.8-1.0 质量分
        timestamp: new Date().toISOString(),
        analysis: {
          blink_detected: true,
          head_movement: true,
          expression_change: true,
        }
      };

      this.loggerService.debug('活体检测完成', mockResult);
      return mockResult;
    } catch (error) {
      this.loggerService.error('活体检测失败', { error });
      throw error;
    }
  }

  async performRiskAssessment(data: {
    user_profile: any;
    loan_application: any;
    financial_data: any;
  }): Promise<any> {
    try {
      const { user_profile, loan_application, financial_data } = data;

      // 计算风险分数
      let riskScore = 50; // 基础分数
      let riskFactors = [];

      // 信用分数影响
      if (financial_data.credit_score >= 750) {
        riskScore += 20;
      } else if (financial_data.credit_score >= 700) {
        riskScore += 10;
      } else if (financial_data.credit_score < 600) {
        riskScore -= 20;
        riskFactors.push('信用分数较低');
      }

      // 收入稳定性
      if (user_profile.employment_type === 'full_time') {
        riskScore += 15;
      } else if (user_profile.employment_type === 'self_employed') {
        riskScore -= 5;
        riskFactors.push('自雇人员收入不稳定');
      }

      // 债务收入比
      const debtToIncomeRatio = financial_data.liabilities / financial_data.monthly_income;
      if (debtToIncomeRatio > 0.5) {
        riskScore -= 15;
        riskFactors.push('债务收入比过高');
      } else if (debtToIncomeRatio < 0.3) {
        riskScore += 10;
      }

      // 贷款金额与收入比
      const loanToIncomeRatio = loan_application.amount / (financial_data.monthly_income * 12);
      if (loanToIncomeRatio > 5) {
        riskScore -= 10;
        riskFactors.push('贷款金额相对收入过高');
      }

      // 确定风险等级
      let riskLevel = 'medium';
      if (riskScore >= 80) riskLevel = 'low';
      else if (riskScore <= 40) riskLevel = 'high';

      const assessment = {
        risk_score: Math.max(0, Math.min(100, riskScore)),
        risk_level: riskLevel,
        risk_factors: riskFactors,
        recommendation: this.generateRiskRecommendation(riskScore, riskFactors),
        analysis_timestamp: new Date().toISOString(),
        details: {
          credit_score_impact: financial_data.credit_score >= 700 ? 'positive' : 'negative',
          employment_stability: user_profile.employment_type === 'full_time' ? 'stable' : 'unstable',
          debt_to_income_ratio: debtToIncomeRatio,
          loan_to_income_ratio: loanToIncomeRatio
        }
      };

      this.loggerService.debug('风险评估完成', assessment);
      return assessment;
    } catch (error) {
      this.loggerService.error('风险评估失败', { error });
      throw error;
    }
  }

  private generateRiskRecommendation(riskScore: number, riskFactors: string[]): string {
    if (riskScore >= 80) {
      return '风险较低，建议批准贷款申请';
    } else if (riskScore >= 60) {
      return '风险中等，建议进一步审核后决定';
    } else if (riskScore >= 40) {
      return '风险较高，建议谨慎审核或降低贷款金额';
    } else {
      return '风险很高，建议拒绝申请或要求额外担保';
    }
  }

  async getAIAdvisorResponse(query: string, context?: any): Promise<any> {
    try {
      // 模拟Fin-R1大模型响应
      const responses = {
        '贷款利率': '当前市场贷款利率在4.5%-8.5%之间，具体利率根据您的信用状况和贷款类型确定。',
        '申请条件': '一般需要稳定收入、良好信用记录、年满18周岁等基本条件。',
        '还款方式': '支持等额本息、等额本金、先息后本等多种还款方式。',
        '审批时间': '通过我们的AI智能审核，最快1小时内可完成审批。',
        '贷款额度': '个人信用贷款最高可达100万元，具体额度根据您的资质评估确定。'
      };

      // 简单的关键词匹配
      let response = '很抱歉，我没有理解您的问题。您可以询问关于贷款利率、申请条件、还款方式、审批时间或贷款额度的问题。';

      for (const [keyword, answer] of Object.entries(responses)) {
        if (query.includes(keyword)) {
          response = answer;
          break;
        }
      }

      const result = {
        response,
        confidence: 0.85,
        timestamp: new Date().toISOString(),
        context_used: !!context,
        suggestions: [
          '了解更多贷款产品',
          '开始贷款申请',
          '查看利率计算器',
          '联系人工客服'
        ]
      };

      this.loggerService.debug('AI顾问响应', result);
      return result;
    } catch (error) {
      this.loggerService.error('AI顾问服务失败', { error });
      throw error;
    }
  }

  private async makeRequest(url: string, data: any, errorMessage: string): Promise<any> {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`${errorMessage}: ${response.statusText}`);
    }

    return response.json();
  }
} 