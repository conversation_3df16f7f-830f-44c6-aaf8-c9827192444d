import mongoose, { Document, Schema } from 'mongoose'

export interface ILoan extends Document {
  userId: mongoose.Types.ObjectId
  loanId: string
  amount: number
  term: number
  purpose: string
  status: 'pending' | 'approved' | 'rejected'
  creditScore: number
  monthlyIncome: number
  houseStatus: 'none' | 'owned' | 'mortgaged'
  carStatus: 'none' | 'owned' | 'loaned'
  createdAt: Date
  updatedAt: Date
}

const loanSchema = new Schema<ILoan>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    loanId: {
      type: String,
      required: true,
      unique: true
    },
    amount: {
      type: Number,
      required: true,
      min: 1000,
      max: 1000000
    },
    term: {
      type: Number,
      required: true,
      enum: [12, 24, 36, 48, 60]
    },
    purpose: {
      type: String,
      required: true,
      minlength: 10,
      maxlength: 200
    },
    status: {
      type: String,
      required: true,
      enum: ['pending', 'approved', 'rejected'],
      default: 'pending'
    },
    creditScore: {
      type: Number,
      required: true,
      min: 0,
      max: 1000
    },
    monthlyIncome: {
      type: Number,
      required: true,
      min: 0
    },
    houseStatus: {
      type: String,
      required: true,
      enum: ['none', 'owned', 'mortgaged']
    },
    carStatus: {
      type: String,
      required: true,
      enum: ['none', 'owned', 'loaned']
    }
  },
  {
    timestamps: true
  }
)

// 生成贷款编号
loanSchema.pre('save', async function (next) {
  if (this.isNew) {
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const count = await (this.constructor as any).countDocuments()
    this.loanId = `L${year}${month}${String(count + 1).padStart(3, '0')}`
  }
  next()
})

export const Loan = mongoose.model<ILoan>('Loan', loanSchema) 