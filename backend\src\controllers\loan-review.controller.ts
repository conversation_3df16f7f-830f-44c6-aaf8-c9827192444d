import { Controller, Get, Post, Body, Param, UseGuards, Request } from '@nestjs/common';
import { LoanReviewService } from '../services/loan-review.service';
import { CreateLoanReviewDto } from '../dto/create-loan-review.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RolesGuard } from '../guards/roles.guard';
import { Roles } from '../decorators/roles.decorator';
import { Role } from '../enums/role.enum';

@Controller('loan-reviews')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.ADMIN, Role.REVIEWER)
export class LoanReviewController {
  constructor(private readonly loanReviewService: LoanReviewService) {}

  @Post()
  async create(@Body() createDto: CreateLoanReviewDto, @Request() req) {
    return this.loanReviewService.create(createDto, req.user.id);
  }

  @Get('application/:applicationId')
  async findAll(@Param('applicationId') applicationId: string) {
    return this.loanReviewService.findAll(+applicationId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.loanReviewService.findOne(+id);
  }

  @Get('application/:applicationId/statistics')
  async getReviewStatistics(@Param('applicationId') applicationId: string) {
    return this.loanReviewService.getReviewStatistics(+applicationId);
  }
} 