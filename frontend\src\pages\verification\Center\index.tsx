import React, { useState } from 'react';
import { 
  Card, 
  Steps, 
  Upload, 
  Button, 
  message, 
  Progress, 
  Image,
  Descriptions,
  Tag,
  Row,
  Col
} from 'antd';
import { 
  UploadOutlined, 
  CheckCircleOutlined, 
  LoadingOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import styles from './Center.module.less';

const { Step } = Steps;

interface VerificationResult {
  status: 'success' | 'error' | 'processing';
  data?: {
    name?: string;
    idNumber?: string;
    validDate?: string;
    address?: string;
    [key: string]: any;
  };
  message?: string;
}

const VerificationCenter: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [verifying, setVerifying] = useState(false);
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);

  const handleUpload = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      setVerifying(true);
      // 调用OCR服务
      const response = await fetch('/api/verification/ocr', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      if (response.ok) {
        setVerificationResult({
          status: 'success',
          data: result.data,
        });
        setCurrentStep(2);
        message.success('资质审核通过！');
      } else {
        throw new Error(result.message);
      }
    } catch (error: any) {
      setVerificationResult({
        status: 'error',
        message: error?.message || '未知错误',
      });
      message.error('资质审核失败：' + (error?.message || '未知错误'));
    } finally {
      setVerifying(false);
    }
  };

  const renderVerificationStatus = () => {
    if (!verificationResult) return null;

    const { status, data, message } = verificationResult;

    return (
      <Card className={styles.resultCard}>
        <div className={styles.statusIcon}>
          {status === 'success' && <CheckCircleOutlined className={styles.successIcon} />}
          {status === 'processing' && <LoadingOutlined className={styles.processingIcon} />}
          {status === 'error' && <CloseCircleOutlined className={styles.errorIcon} />}
        </div>

        {status === 'success' && data && (
          <Descriptions title="验证结果" bordered>
            <Descriptions.Item label="姓名">{data.name}</Descriptions.Item>
            <Descriptions.Item label="身份证号">{data.idNumber}</Descriptions.Item>
            <Descriptions.Item label="有效期">{data.validDate}</Descriptions.Item>
            <Descriptions.Item label="地址" span={3}>{data.address}</Descriptions.Item>
          </Descriptions>
        )}

        {status === 'error' && (
          <div className={styles.errorMessage}>
            <h3>验证失败</h3>
            <p>{message}</p>
          </div>
        )}
      </Card>
    );
  };

  return (
    <div className={styles.container}>
      <Card title="资质审核中心">
        <Steps current={currentStep} className={styles.steps}>
          <Step title="上传证件" description="请上传清晰的证件照片" />
          <Step title="智能审核" description="AI自动识别验证" />
          <Step title="审核结果" description="查看审核结果" />
        </Steps>

        <div className={styles.content}>
          {currentStep === 0 && (
            <Upload.Dragger
              fileList={fileList}
              onChange={({ fileList }) => setFileList(fileList)}
              beforeUpload={(file) => {
                handleUpload(file);
                return false;
              }}
              accept="image/*"
            >
              <p className={styles.uploadIcon}>
                <UploadOutlined />
              </p>
              <p className={styles.uploadText}>
                点击或拖拽上传证件照片
              </p>
              <p className={styles.uploadHint}>
                支持身份证、营业执照等证件的正反面照片
              </p>
            </Upload.Dragger>
          )}

          {currentStep === 1 && (
            <div className={styles.verifying}>
              <Progress type="circle" percent={75} />
              <p>正在进行智能审核，请稍候...</p>
            </div>
          )}

          {currentStep === 2 && renderVerificationStatus()}
        </div>

        <div className={styles.actions}>
          {currentStep > 0 && (
            <Button onClick={() => setCurrentStep(currentStep - 1)}>
              上一步
            </Button>
          )}
          {currentStep < 2 && !verifying && (
            <Button type="primary" onClick={() => setCurrentStep(currentStep + 1)}>
              下一步
            </Button>
          )}
        </div>
      </Card>
    </div>
  );
};

export default VerificationCenter;
