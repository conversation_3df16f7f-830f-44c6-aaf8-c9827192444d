/**
 * SmartLoan 2025 智能金融服务平台
 * Spring Boot 3.2 主应用入口
 * 基于沐曦MetaX GPU算力与Gitee AI平台
 */

package com.smartloan;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootApplication
@EnableFeignClients
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@EnableCaching
@EnableWebSecurity
public class SmartLoanApplication {

    public static void main(String[] args) {
        // 启动横幅
        printStartupBanner();
        
        // 设置系统属性
        System.setProperty("spring.application.name", "smartloan-backend");
        System.setProperty("metax.gpu.enabled", "true");
        System.setProperty("gitee.ai.enabled", "true");
        
        try {
            SpringApplication app = new SpringApplication(SmartLoanApplication.class);
            
            // 自定义启动配置
            app.setAdditionalProfiles("production");
            app.setBannerMode(org.springframework.boot.Banner.Mode.OFF);
            
            var context = app.run(args);
            
            log.info("🚀 SmartLoan 2025 智能金融服务平台启动成功!");
            log.info("🎮 沐曦MetaX GPU服务已启用");
            log.info("📡 Gitee AI平台已连接");
            log.info("🏦 支持500+金融机构产品");
            log.info("⚡ 系统就绪，等待请求...");
            
            // 启动后检查
            performStartupChecks(context);
            
        } catch (Exception e) {
            log.error("❌ SmartLoan应用启动失败: {}", e.getMessage(), e);
            System.exit(1);
        }
    }
    
    /**
     * 打印启动横幅
     */
    private static void printStartupBanner() {
        String banner = """
            
            ███████╗███╗   ███╗ █████╗ ██████╗ ████████╗██╗      ██████╗  █████╗ ███╗   ██╗
            ██╔════╝████╗ ████║██╔══██╗██╔══██╗╚══██╔══╝██║     ██╔═══██╗██╔══██╗████╗  ██║
            ███████╗██╔████╔██║███████║██████╔╝   ██║   ██║     ██║   ██║███████║██╔██╗ ██║
            ╚════██║██║╚██╔╝██║██╔══██║██╔══██╗   ██║   ██║     ██║   ██║██╔══██║██║╚██╗██║
            ███████║██║ ╚═╝ ██║██║  ██║██║  ██║   ██║   ███████╗╚██████╔╝██║  ██║██║ ╚████║
            ╚══════╝╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚══════╝ ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═══╝
            
                        🏆 2025年智能金融服务平台 | 基于沐曦MetaX GPU算力
                        🎯 智能匹配 | 精准评估 | 实时风控 | AI驱动
            
            """;
        System.out.println(banner);
    }
    
    /**
     * 启动后检查
     */
    private static void performStartupChecks(org.springframework.context.ConfigurableApplicationContext context) {
        try {
            // 检查数据库连接
            var dataSource = context.getBean(javax.sql.DataSource.class);
            try (var connection = dataSource.getConnection()) {
                log.info("✅ PostgreSQL数据库连接正常");
            }
            
            // 检查Redis连接
            try {
                var redisTemplate = context.getBean("redisTemplate");
                log.info("✅ Redis缓存服务连接正常");
            } catch (Exception e) {
                log.warn("⚠️ Redis连接异常: {}", e.getMessage());
            }
            
            // 检查沐曦GPU服务
            try {
                var gpuService = context.getBean("metaXGPUService");
                log.info("✅ 沐曦MetaX GPU服务连接正常");
            } catch (Exception e) {
                log.warn("⚠️ GPU服务连接异常: {}", e.getMessage());
            }
            
            // 检查Gitee AI服务
            try {
                var aiService = context.getBean("giteeAIService");
                log.info("✅ Gitee AI服务连接正常");
            } catch (Exception e) {
                log.warn("⚠️ AI服务连接异常: {}", e.getMessage());
            }
            
            log.info("🎉 所有系统组件检查完成，服务正常运行");
            
        } catch (Exception e) {
            log.error("❌ 启动检查失败: {}", e.getMessage(), e);
        }
    }
}
