# SmartLoan 2025 智能金融服务平台
# Docker Compose 生产环境部署配置
# 支持PostgreSQL、Redis、监控、GPU服务

version: '3.8'

services:
  # PostgreSQL 主数据库
  postgres:
    image: postgres:15-alpine
    container_name: smartloan-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: smartloan_2025
      POSTGRES_USER: smartloan
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-SmartLoan2025!}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - smartloan-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U smartloan -d smartloan_2025"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis 缓存集群
  redis:
    image: redis:7-alpine
    container_name: smartloan-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-SmartLoan2025Redis}
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - smartloan-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # SmartLoan 后端应用
  smartloan-backend:
    build:
      context: ./smartloan-backend
      dockerfile: Dockerfile
    container_name: smartloan-backend
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # 数据库配置
      SPRING_DATASOURCE_URL: **********************************************
      SPRING_DATASOURCE_USERNAME: smartloan
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD:-SmartLoan2025!}
      
      # Redis配置
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD:-SmartLoan2025Redis}
      
      # 应用配置
      SPRING_PROFILES_ACTIVE: production
      SERVER_PORT: 8080
      MANAGEMENT_SERVER_PORT: 8081
      
      # GPU配置
      METAX_GPU_ENABLED: true
      METAX_GPU_ENDPOINT: ${METAX_GPU_ENDPOINT:-http://metax-gpu:8080}
      METAX_API_KEY: ${METAX_API_KEY:-demo_key_2025}
      
      # AI配置
      GITEE_AI_ENABLED: true
      GITEE_AI_ENDPOINT: ${GITEE_AI_ENDPOINT:-https://ai.gitee.com/api/v2025}
      GITEE_AI_API_KEY: ${GITEE_AI_API_KEY:-gitee_ai_demo_2025}
      
      # 安全配置
      JWT_SECRET: ${JWT_SECRET:-SmartLoan2025SecretKeyForJWTTokenGenerationAndValidation}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-SmartLoan2025EncryptionKey}
      
      # 监控配置
      MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: health,info,metrics,prometheus
      MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: always
    ports:
      - "8080:8080"  # 应用端口
      - "8081:8081"  # 管理端口
    volumes:
      - app_logs:/app/logs
      - ./config/application-production.yml:/app/config/application-production.yml:ro
    networks:
      - smartloan-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: smartloan-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - smartloan-network
    depends_on:
      - smartloan-backend

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: smartloan-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-SmartLoan2025}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-piechart-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3000:3000"
    networks:
      - smartloan-network
    depends_on:
      - prometheus

  # Elasticsearch (日志存储)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: smartloan-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - smartloan-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana (日志可视化)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: smartloan-kibana
    restart: unless-stopped
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - smartloan-network
    depends_on:
      elasticsearch:
        condition: service_healthy

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: smartloan-nginx
    restart: unless-stopped
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    networks:
      - smartloan-network
    depends_on:
      - smartloan-backend

# 网络配置
networks:
  smartloan-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
