import { User } from './user.entity';
export declare class AuditRecord {
    id: number;
    userId: string;
    user: User;
    applicationId: string;
    status: 'pending' | 'approved' | 'rejected' | 'reviewing';
    reviewerId: string;
    reviewer: User;
    reviewNotes: string;
    reviewDetails: {
        riskScore?: number;
        creditScore?: number;
        incomeVerification?: boolean;
        documentVerification?: boolean;
        additionalChecks?: Record<string, any>;
    };
    metadata: {
        reviewTime?: number;
        reviewSteps?: string[];
        reviewHistory?: Array<{
            status: string;
            timestamp: Date;
            notes?: string;
            reviewer?: string;
        }>;
    };
    createdAt: Date;
    updatedAt: Date;
}
