import { User } from '../../entities/user.entity';
import { LoanType } from '../../enums/loan-type.enum';
import { EmploymentStatus } from '../../enums/employment-status.enum';
import { CollateralType } from '../../enums/collateral-type.enum';
import { LoanPurpose } from '../../enums/loan-purpose.enum';
import { Role } from '../../enums/role.enum';

export const createMockUser = (): Partial<User> => ({
  id: 'test-user-uuid-1',
  username: 'testuser',
  email: '<EMAIL>',
  password: 'hashedPassword',
  name: 'Test User',
  phone: '13800138000',
  salt: 'testsalt',
  password_hash: 'hashedPassword',
  real_name: 'Test User',
  credit_score: 750,
  risk_level: 'LOW',
  role: Role.USER,
  employmentStatus: EmploymentStatus.EMPLOYED,
  monthlyIncome: 8333.33,
  annualIncome: 100000,
  last_login_at: new Date(),
  metadata: {},
  permissions: {},
  isActive: true,
  lastLoginAt: new Date(),
  loanApplications: [],
  loanReviews: [],
  createdAt: new Date(),
  updatedAt: new Date(),
  isVerified: true,
  verificationDate: new Date(),
  documentsVerified: true,
  documentsVerificationDate: new Date(),
  livenessVerified: true,
  livenessVerificationDate: new Date(),
  verificationDocuments: {},
  faceData: {}
});

export const createMockRepository = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getMany: jest.fn().mockResolvedValue([])
  }))
});

export const createMockService = (methods: Record<string, jest.Mock>) => ({
  ...methods
}); 