export declare enum ExportFormat {
    EXCEL = "excel",
    PDF = "pdf",
    CSV = "csv",
    JSON = "json",
    XML = "xml",
    HTML = "html"
}
export declare class ExportOptionsDto {
    format: ExportFormat;
    template?: string;
    filename?: string;
    fields?: string[];
    filters?: Record<string, any>;
    includeHeaders?: boolean;
    includeMetadata?: boolean;
    pageSize?: number;
    page?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    encrypt?: boolean;
    password?: string;
    compress?: boolean;
    language?: string;
    styling?: {
        headerColor?: string;
        headerFont?: string;
        headerSize?: number;
        rowColor?: string;
        rowFont?: string;
        rowSize?: number;
    };
    layout?: {
        orientation?: 'portrait' | 'landscape';
        pageSize?: string;
        margins?: {
            top?: number;
            right?: number;
            bottom?: number;
            left?: number;
        };
    };
    columns?: ExportColumnDto[];
}
export declare class ExportColumnDto {
    field: string;
    header?: string;
    format?: string;
    width?: number;
    visible?: boolean;
    alignment?: 'left' | 'center' | 'right';
    type?: 'string' | 'number' | 'date' | 'boolean' | 'currency';
    style?: {
        font?: string;
        size?: number;
        color?: string;
        backgroundColor?: string;
        bold?: boolean;
        italic?: boolean;
        underline?: boolean;
    };
}
