export declare const createMockUser: () => User;
export declare const createMockRepository: () => {
    find: jest.<PERSON>ck<any, any, any>;
    findOne: jest.<PERSON>ck<any, any, any>;
    save: jest.<PERSON>ck<any, any, any>;
    update: jest.<PERSON>ck<any, any, any>;
    delete: jest.Mock<any, any, any>;
    createQueryBuilder: jest.Mock<{
        leftJoinAndSelect: jest.Mock<any, any, any>;
        where: jest.<PERSON><PERSON><any, any, any>;
        andWhere: jest.Mock<any, any, any>;
        getMany: jest.Mock<any, any, any>;
    }, [], any>;
};
export declare const createMockService: (methods: Record<string, jest.Mock>) => {
    [x: string]: jest.Mock<any, any, any>;
};
