import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuditLog } from '../entities/audit-log.entity';
import { MonitorService } from './monitor.service';

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  constructor(
    @InjectRepository(AuditLog)
    private auditLogRepository: Repository<AuditLog>,
    private readonly monitorService: MonitorService,
  ) {}

  async log(
    operation: string,
    details: any,
    options: {
      userId?: string;
      ipAddress?: string;
      userAgent?: string;
      module?: string;
      action?: string;
      resource?: string;
    } = {},
  ): Promise<AuditLog> {
    const startTime = Date.now();
    try {
      const auditLog = this.auditLogRepository.create({
        operation,
        details,
        userId: options.userId,
        ipAddress: options.ipAddress,
        userAgent: options.userAgent,
        metadata: {
          module: options.module,
          action: options.action,
          resource: options.resource,
          status: 'success',
          duration: Date.now() - startTime,
        },
      });

      const savedLog = await this.auditLogRepository.save(auditLog);
      this.monitorService.logApplicationEvent('audit.log', savedLog);
      return savedLog;
    } catch (error) {
      this.logger.error(`Failed to create audit log: ${error.message}`, error.stack);
      throw error;
    }
  }

  async logError(
    operation: string,
    error: Error,
    options: {
      userId?: string;
      ipAddress?: string;
      userAgent?: string;
      module?: string;
      action?: string;
      resource?: string;
    } = {},
  ): Promise<AuditLog> {
    const startTime = Date.now();
    try {
      const auditLog = this.auditLogRepository.create({
        operation,
        details: {
          error: {
            name: error.name,
            message: error.message,
            stack: error.stack,
          },
        },
        userId: options.userId,
        ipAddress: options.ipAddress,
        userAgent: options.userAgent,
        metadata: {
          module: options.module,
          action: options.action,
          resource: options.resource,
          status: 'error',
          duration: Date.now() - startTime,
          error: {
            name: error.name,
            message: error.message,
          },
        },
      });

      const savedLog = await this.auditLogRepository.save(auditLog);
      this.monitorService.logApplicationEvent('audit.error', savedLog);
      return savedLog;
    } catch (err) {
      this.logger.error(`Failed to create error audit log: ${err.message}`, err.stack);
      throw err;
    }
  }

  async getLogs(options: {
    startDate?: Date;
    endDate?: Date;
    userId?: string;
    operation?: string;
    module?: string;
    status?: string;
    skip?: number;
    take?: number;
  } = {}): Promise<{ logs: AuditLog[]; total: number }> {
    const query = this.auditLogRepository.createQueryBuilder('log');

    if (options.startDate) {
      query.andWhere('log.timestamp >= :startDate', { startDate: options.startDate });
    }

    if (options.endDate) {
      query.andWhere('log.timestamp <= :endDate', { endDate: options.endDate });
    }

    if (options.userId) {
      query.andWhere('log.userId = :userId', { userId: options.userId });
    }

    if (options.operation) {
      query.andWhere('log.operation = :operation', { operation: options.operation });
    }

    if (options.module) {
      query.andWhere('log.metadata->>\'module\' = :module', { module: options.module });
    }

    if (options.status) {
      query.andWhere('log.metadata->>\'status\' = :status', { status: options.status });
    }

    const total = await query.getCount();

    if (options.skip !== undefined) {
      query.skip(options.skip);
    }

    if (options.take !== undefined) {
      query.take(options.take);
    }

    query.orderBy('log.timestamp', 'DESC');

    const logs = await query.getMany();

    return { logs, total };
  }
  async getLogById(id: string): Promise<AuditLog> {
    const log = await this.auditLogRepository.findOne({ where: { id: parseInt(id) } });
    if (!log) {
      throw new Error(`Audit log with ID ${id} not found`);
    }
    return log;
  }

  async getLogsByUserId(userId: string, options: {
    startDate?: Date;
    endDate?: Date;
    skip?: number;
    take?: number;
  } = {}): Promise<{ logs: AuditLog[]; total: number }> {
    return this.getLogs({
      ...options,
      userId,
    });
  }

  async getLogsByOperation(operation: string, options: {
    startDate?: Date;
    endDate?: Date;
    skip?: number;
    take?: number;
  } = {}): Promise<{ logs: AuditLog[]; total: number }> {
    return this.getLogs({
      ...options,
      operation,
    });
  }

  async getLogsByModule(module: string, options: {
    startDate?: Date;
    endDate?: Date;
    skip?: number;
    take?: number;
  } = {}): Promise<{ logs: AuditLog[]; total: number }> {
    return this.getLogs({
      ...options,
      module,
    });
  }

  async getLogsByStatus(status: string, options: {
    startDate?: Date;
    endDate?: Date;
    skip?: number;
    take?: number;
  } = {}): Promise<{ logs: AuditLog[]; total: number }> {
    return this.getLogs({
      ...options,
      status,
    });
  }
} 