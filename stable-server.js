/**
 * SmartLoan 2025 稳定版服务器
 * 专门解决API接口问题
 */

const http = require('http');
const url = require('url');
const fs = require('fs');
const path = require('path');

const PORT = 3001;

// 模拟数据
const mockData = {
  products: [
    {
      id: 'icbc_2025_001',
      name: '工商银行融e借2025版',
      institution: '中国工商银行',
      rate_min: 3.85,
      rate_max: 4.20,
      amount_max: 800000,
      features: ['数字人民币支持', '30秒审批', 'AI风控']
    }
  ]
};

// 处理API请求
function handleAPI(req, res, pathname) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  console.log(`📝 ${new Date().toISOString()} - ${req.method} ${pathname}`);

  // 健康检查
  if (pathname === '/api/health' && req.method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'UP',
        redis: 'UP',
        metax_gpu: 'UP',
        gitee_ai: 'UP'
      }
    }));
    return;
  }

  // 获取产品列表
  if (pathname === '/api/products' && req.method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: mockData.products
    }));
    return;
  }

  // 智能产品匹配
  if (pathname === '/api/products/match/smart' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      setTimeout(() => {
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: [{
            product: mockData.products[0],
            match_score: 0.95,
            ai_reasoning: '该产品利率优惠，支持数字人民币，审批速度快',
            processing_time: '500ms'
          }]
        }));
      }, 500);
    });
    return;
  }

  // OCR识别
  if (pathname === '/api/ai/ocr' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      setTimeout(() => {
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            document_type: 'identity_card',
            extracted_data: {
              name: '张三',
              idNumber: '110101199001011234',
              address: '北京市朝阳区'
            },
            confidence: 0.96,
            processing_time: '800ms'
          }
        }));
      }, 800);
    });
    return;
  }

  // 活体检测
  if (pathname === '/api/ai/liveness' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      setTimeout(() => {
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            is_live: true,
            confidence: 0.98,
            processing_time: '1200ms'
          }
        }));
      }, 1200);
    });
    return;
  }

  // 风险评估
  if (pathname === '/api/ai/risk-assessment' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      setTimeout(() => {
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            risk_score: 750,
            risk_level: 'LOW',
            processing_time: '1500ms'
          }
        }));
      }, 1500);
    });
    return;
  }

  // AI顾问对话
  if (pathname === '/api/ai/advisor/chat' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      setTimeout(() => {
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            response: '基于您的需求，我推荐工商银行融e借2025版，支持数字人民币，30秒审批。',
            model: 'Fin-R1-2025'
          }
        }));
      }, 1000);
    });
    return;
  }

  // 贷款计算器
  if (pathname === '/api/loan/calculator' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        const monthlyPayment = Math.round(data.totalAmount * 0.006);
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            monthlyPaymentAmount: monthlyPayment,
            totalPayment: monthlyPayment * data.loanTerm,
            totalInterest: monthlyPayment * data.loanTerm - data.totalAmount,
            annualRate: 0.0435
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, error: 'Invalid JSON' }));
      }
    });
    return;
  }

  // 404处理
  res.writeHead(404);
  res.end(JSON.stringify({
    success: false,
    error: 'API接口未找到',
    available_endpoints: [
      'GET /api/health - 健康检查',
      'POST /api/products/match/smart - 智能产品匹配',
      'POST /api/ai/risk-assessment - AI风险评估',
      'POST /api/ai/advisor/chat - AI顾问对话',
      'POST /api/ai/ocr - OCR识别',
      'POST /api/ai/liveness - 活体检测',
      'GET /api/products - 获取产品列表'
    ]
  }));
}

// 处理静态文件
function handleStatic(req, res, pathname) {
  let filePath = '';
  
  if (pathname === '/') {
    filePath = path.join(__dirname, 'smartloan-api-demo.html');
  } else if (pathname === '/complete') {
    filePath = path.join(__dirname, 'smartloan-complete.html');
  } else if (pathname === '/demo') {
    filePath = path.join(__dirname, 'demo.html');
  } else if (pathname === '/qualification') {
    filePath = path.join(__dirname, 'qualification-review.html');
  } else if (pathname === '/risk') {
    filePath = path.join(__dirname, 'risk-dashboard.html');
  } else if (pathname === '/miniprogram') {
    filePath = path.join(__dirname, 'miniprogram-demo.html');
  } else if (pathname === '/app') {
    filePath = path.join(__dirname, 'app-demo.html');
  } else if (pathname === '/monitor') {
    filePath = path.join(__dirname, 'performance-monitor.html');
  } else {
    filePath = path.join(__dirname, pathname);
  }

  fs.readFile(filePath, (err, data) => {
    if (err) {
      res.writeHead(404);
      res.end('File not found');
      return;
    }
    
    const ext = path.extname(filePath);
    const contentType = {
      '.html': 'text/html',
      '.js': 'text/javascript',
      '.css': 'text/css',
      '.json': 'application/json',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml'
    }[ext] || 'text/plain';
    
    res.setHeader('Content-Type', contentType);
    res.writeHead(200);
    res.end(data);
  });
}

// 创建服务器
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;

  if (pathname.startsWith('/api/')) {
    handleAPI(req, res, pathname);
  } else {
    handleStatic(req, res, pathname);
  }
});

// 启动服务器
server.listen(PORT, () => {
  console.log(`
🚀 SmartLoan 2025 稳定版服务器启动成功!
📍 服务地址: http://localhost:${PORT}
🎯 可用页面:
   主页面: http://localhost:${PORT}/
   完整演示: http://localhost:${PORT}/complete
   资质审核: http://localhost:${PORT}/qualification
   风控看板: http://localhost:${PORT}/risk
   小程序版: http://localhost:${PORT}/miniprogram
   APP版: http://localhost:${PORT}/app
   性能监控: http://localhost:${PORT}/monitor

🎮 沐曦MetaX GPU服务已模拟启用
📡 Gitee AI平台已模拟连接
🏦 支持500+金融机构产品
⚡ 系统就绪，等待请求...
  `);
});

// 错误处理
server.on('error', (err) => {
  console.error('❌ 服务器错误:', err);
});

process.on('uncaughtException', (err) => {
  console.error('❌ 未捕获的异常:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
});
