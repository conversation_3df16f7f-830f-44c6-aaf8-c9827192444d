"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductSearchService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const product_entity_1 = require("../entities/product.entity");
const cache_service_1 = require("./cache.service");
const logger_service_1 = require("./logger.service");
let ProductSearchService = class ProductSearchService {
    constructor(productRepository, cacheService, logger) {
        this.productRepository = productRepository;
        this.cacheService = cacheService;
        this.logger = logger;
        this.CACHE_TTL = 3600;
    }
    async searchProducts(params) {
        try {
            const { keyword, category, minAmount, maxAmount, minTerm, maxTerm, minInterestRate, maxInterestRate, isActive, isFeatured, page = 1, pageSize = 10, sortBy, sortOrder = 'DESC' } = params;
            const cacheKey = `search:${JSON.stringify(params)}`;
            const cachedResult = await this.cacheService.get(cacheKey);
            if (cachedResult) {
                return cachedResult;
            }
            const where = {};
            if (keyword) {
                where.name = (0, typeorm_2.Like)(`%${keyword}%`);
            }
            if (category) {
                where.category = category;
            }
            if (minAmount !== undefined || maxAmount !== undefined) {
                where.amount = {};
                if (minAmount !== undefined) {
                    where.amount.minAmount = (0, typeorm_2.MoreThanOrEqual)(minAmount);
                }
                if (maxAmount !== undefined) {
                    where.amount.maxAmount = (0, typeorm_2.LessThanOrEqual)(maxAmount);
                }
            }
            if (minTerm !== undefined || maxTerm !== undefined) {
                where.term = {};
                if (minTerm !== undefined) {
                    where.term.minTerm = (0, typeorm_2.Between)(minTerm, maxTerm || 999);
                }
                if (maxTerm !== undefined) {
                    where.term.maxTerm = (0, typeorm_2.Between)(minTerm || 0, maxTerm);
                }
            }
            if (minInterestRate !== undefined || maxInterestRate !== undefined) {
                where.interestRate = {};
                if (minInterestRate !== undefined) {
                    where.interestRate = (0, typeorm_2.Between)(minInterestRate, maxInterestRate || 999);
                }
                if (maxInterestRate !== undefined) {
                    where.interestRate = (0, typeorm_2.Between)(minInterestRate || 0, maxInterestRate);
                }
            }
            if (isActive !== undefined) {
                where.isActive = isActive;
            }
            if (isFeatured !== undefined) {
                where.isFeatured = isFeatured;
            }
            const order = {};
            if (sortBy) {
                order[sortBy] = sortOrder;
            }
            const [products, total] = await this.productRepository.findAndCount({
                where,
                order,
                skip: (page - 1) * pageSize,
                take: pageSize,
            });
            const result = {
                products,
                total,
                page,
                pageSize
            };
            await this.cacheService.set(cacheKey, result, this.CACHE_TTL);
            this.logger.debug(`Found ${products.length} products out of ${total}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Error searching products: ${String(error)}`, error instanceof Error ? error.stack : undefined);
            return {
                products: [],
                total: 0,
                page: 1,
                pageSize: 10
            };
        }
    }
    async getCategories() {
        try {
            const cacheKey = 'product:categories';
            const cachedCategories = await this.cacheService.get(cacheKey);
            if (cachedCategories) {
                return cachedCategories;
            }
            const products = await this.productRepository.find({
                select: ['category']
            });
            const categories = [...new Set(products.map(p => p.category))].filter(Boolean);
            await this.cacheService.set(cacheKey, categories, this.CACHE_TTL);
            return categories;
        }
        catch (error) {
            this.logger.error('获取产品分类失败', error);
            return [];
        }
    }
    async getTags() {
        try {
            const cacheKey = 'product:tags';
            const cachedTags = await this.cacheService.get(cacheKey);
            if (cachedTags) {
                return cachedTags;
            }
            const products = await this.productRepository.find({
                select: ['tags']
            });
            const tags = [...new Set(products.flatMap(p => p.tags || []))].filter(Boolean);
            await this.cacheService.set(cacheKey, tags, this.CACHE_TTL);
            return tags;
        }
        catch (error) {
            this.logger.error('获取产品标签失败', error);
            return [];
        }
    }
    async getHotKeywords(limit = 10) {
        try {
            const cacheKey = `product:hot:keywords:${limit}`;
            const cachedKeywords = await this.cacheService.get(cacheKey);
            if (cachedKeywords) {
                return cachedKeywords;
            }
            const keywords = [
                '个人贷款',
                '房贷',
                '车贷',
                '信用贷款',
                '经营贷款',
                '消费贷款',
                '抵押贷款',
                '无抵押贷款',
                '小额贷款',
                '大额贷款'
            ].slice(0, limit);
            await this.cacheService.set(cacheKey, keywords, 1800);
            return keywords;
        }
        catch (error) {
            this.logger.error('获取热门搜索关键词失败', error);
            return [];
        }
    }
    async searchByCategory(category) {
        try {
            const cacheKey = `product:category:${category}`;
            const cachedResults = await this.cacheService.get(cacheKey);
            if (cachedResults) {
                return JSON.parse(cachedResults);
            }
            const products = await this.productRepository.find({
                where: { category },
                order: {
                    isFeatured: 'DESC',
                    sortOrder: 'ASC',
                    createdAt: 'DESC'
                }
            });
            const results = products.map(product => ({
                id: product.id,
                name: product.name,
                code: product.code,
                description: product.description,
                category: product.category,
                minAmount: product.minAmount,
                maxAmount: product.maxAmount,
                minTerm: product.minTerm,
                maxTerm: product.maxTerm,
                interestRate: product.interestRate,
                isActive: product.isActive,
                isFeatured: product.isFeatured,
                metadata: product.metadata
            }));
            await this.cacheService.set(cacheKey, JSON.stringify(results), this.CACHE_TTL);
            return results;
        }
        catch (error) {
            this.logger.error('按类别搜索产品失败', error);
            throw error;
        }
    }
    async searchByAmountRange(minAmount, maxAmount) {
        try {
            const cacheKey = `product:amount:${minAmount}:${maxAmount}`;
            const cachedResults = await this.cacheService.get(cacheKey);
            if (cachedResults) {
                return JSON.parse(cachedResults);
            }
            const products = await this.productRepository.find({
                where: {
                    minAmount: minAmount,
                    maxAmount: maxAmount
                },
                order: {
                    isFeatured: 'DESC',
                    sortOrder: 'ASC',
                    createdAt: 'DESC'
                }
            });
            const results = products.map(product => ({
                id: product.id,
                name: product.name,
                code: product.code,
                description: product.description,
                category: product.category,
                minAmount: product.minAmount,
                maxAmount: product.maxAmount,
                minTerm: product.minTerm,
                maxTerm: product.maxTerm,
                interestRate: product.interestRate,
                isActive: product.isActive,
                isFeatured: product.isFeatured,
                metadata: product.metadata
            }));
            await this.cacheService.set(cacheKey, JSON.stringify(results), this.CACHE_TTL);
            return results;
        }
        catch (error) {
            this.logger.error('按金额范围搜索产品失败', error);
            throw error;
        }
    }
    async searchByTermRange(minTerm, maxTerm) {
        try {
            const cacheKey = `product:term:${minTerm}:${maxTerm}`;
            const cachedResults = await this.cacheService.get(cacheKey);
            if (cachedResults) {
                return JSON.parse(cachedResults);
            }
            const products = await this.productRepository.find({
                where: {
                    minTerm: minTerm,
                    maxTerm: maxTerm
                },
                order: {
                    isFeatured: 'DESC',
                    sortOrder: 'ASC',
                    createdAt: 'DESC'
                }
            });
            const results = products.map(product => ({
                id: product.id,
                name: product.name,
                code: product.code,
                description: product.description,
                category: product.category,
                minAmount: product.minAmount,
                maxAmount: product.maxAmount,
                minTerm: product.minTerm,
                maxTerm: product.maxTerm,
                interestRate: product.interestRate,
                isActive: product.isActive,
                isFeatured: product.isFeatured,
                metadata: product.metadata
            }));
            await this.cacheService.set(cacheKey, JSON.stringify(results), this.CACHE_TTL);
            return results;
        }
        catch (error) {
            this.logger.error('按期限范围搜索产品失败', error);
            throw error;
        }
    }
    async searchByInterestRate(maxInterestRate) {
        try {
            const cacheKey = `product:interest:${maxInterestRate}`;
            const cachedResults = await this.cacheService.get(cacheKey);
            if (cachedResults) {
                return JSON.parse(cachedResults);
            }
            const products = await this.productRepository.find({
                where: {
                    interestRate: maxInterestRate
                },
                order: {
                    isFeatured: 'DESC',
                    sortOrder: 'ASC',
                    createdAt: 'DESC'
                }
            });
            const results = products.map(product => ({
                id: product.id,
                name: product.name,
                code: product.code,
                description: product.description,
                category: product.category,
                minAmount: product.minAmount,
                maxAmount: product.maxAmount,
                minTerm: product.minTerm,
                maxTerm: product.maxTerm,
                interestRate: product.interestRate,
                isActive: product.isActive,
                isFeatured: product.isFeatured,
                metadata: product.metadata
            }));
            await this.cacheService.set(cacheKey, JSON.stringify(results), this.CACHE_TTL);
            return results;
        }
        catch (error) {
            this.logger.error('按利率搜索产品失败', error);
            throw error;
        }
    }
    processData(data) {
        this.logger.debug(`Processing data: ${String(data)}`);
        return data;
    }
    mapHeaders(headers) {
        this.logger.debug(`Mapping headers: ${String(headers)}`);
        return headers;
    }
    parseRow(row) {
        this.logger.debug(`Parsing row: ${String(row)}`);
        return row;
    }
    transformProductData(data) {
        this.logger.debug(`Transforming product data: ${String(data)}`);
        return data;
    }
};
ProductSearchService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        cache_service_1.CacheService,
        logger_service_1.LoggerService])
], ProductSearchService);
exports.ProductSearchService = ProductSearchService;
//# sourceMappingURL=product-search.service.js.map