/// <reference types="node" />
/// <reference types="node" />
import { ConfigService } from '@nestjs/config';
import { GpuService } from './gpu.service';
import { RedisService } from './redis.service';
export declare class AIAccelerationService {
    private gpuService;
    private redisService;
    private configService;
    private readonly logger;
    private readonly BATCH_SIZE;
    constructor(gpuService: GpuService, redisService: RedisService, configService: ConfigService);
    accelerateOcr(images: Buffer[], documentTypes: string[]): Promise<any[]>;
    accelerateRiskModel(data: any[]): Promise<any[]>;
    accelerateProductMatching(userProfiles: any[], products: any[]): Promise<any[]>;
    private createBatches;
}
