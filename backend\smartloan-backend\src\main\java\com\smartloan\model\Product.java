package com.smartloan.model;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "products")
public class Product {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    private String description;
    
    @Column(nullable = false)
    private BigDecimal minAmount;
    
    @Column(nullable = false)
    private BigDecimal maxAmount;
    
    @Column(nullable = false)
    private Double interestRate;
    
    @Column(nullable = false)
    private Integer minTerm; // 最短期限（月）
    
    @Column(nullable = false)
    private Integer maxTerm; // 最长期限（月）
    
    @Column(nullable = false)
    private String requirements; // 申请要求（JSON格式）
    
    @Column(nullable = false)
    private Boolean isActive = true;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
