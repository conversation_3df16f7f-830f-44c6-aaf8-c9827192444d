import { register, Histogram } from 'prom-client';
import { WinstonModuleOptions } from 'nest-winston';
import * as winston from 'winston';
import 'winston-daily-rotate-file';
import React from 'react';
import { Input, Select } from 'antd';

// Prometheus指标配置
export const prometheusConfig = {
  defaultMetrics: {
    enabled: true,
    config: {
      prefix: 'smartloan_',
      timeout: 10000,
      labels: {
        application: 'smartloan'
      }
    }
  },
  customMetrics: {
    httpRequestDuration: new Histogram({
      name: 'http_request_duration_seconds',
      help: 'HTTP请求处理时间',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [0.1, 0.5, 1, 2, 5],
      registers: [register]
    }),
    riskCalculationDuration: new Histogram({
      name: 'risk_calculation_duration_seconds',
      help: '风险计算处理时间',
      labelNames: ['type'],
      buckets: [0.1, 0.5, 1, 2, 5],
      registers: [register]
    }),
    alertProcessingDuration: new Histogram({
      name: 'alert_processing_duration_seconds',
      help: '告警处理时间',
      labelNames: ['level'],
      buckets: [0.1, 0.5, 1, 2, 5],
    }),
  }
};

// <PERSON>日志配置
export const winstonConfig: WinstonModuleOptions = {
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.colorize(),
        winston.format.printf(({ timestamp, level, message, context, trace }) => {
          return `${timestamp} [${level}] [${context || 'Application'}] ${message}${trace ? `\n${trace}` : ''}`;
        })
      )
    }),
    new winston.transports.DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      maxFiles: '30d',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),
    new winston.transports.DailyRotateFile({
      filename: 'logs/combined-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxFiles: '30d',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  ]
};

// 告警配置
export const alertConfig = {
  thresholds: {
    responseTime: 1000, // 1秒
    riskCalculation: 2000, // 2秒
    alertProcessing: 500, // 0.5秒
  },
  email: {
    enabled: true,
    from: '<EMAIL>',
    to: '<EMAIL>',
    smtp: {
      host: 'smtp.example.com',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'your-password',
      },
    },
  },
  slack: {
    enabled: true,
    webhookUrl: 'https://hooks.slack.com/services/your-webhook-url',
  },
};

// 表单配置
export const formConfig = {
  items: [
    {
      name: 'username',
      label: '用户名',
      component: React.createElement(Input),
      required: true
    },
    {
      name: 'email',
      label: '邮箱',
      component: React.createElement(Input),
      required: true,
      rules: [{ type: 'email', message: '请输入有效的邮箱地址' }]
    },    {
      name: 'role',
      label: '角色',
      component: React.createElement(Select, {}, [
        React.createElement(Select.Option, { key: 'admin', value: 'admin', children: '管理员' }),
        React.createElement(Select.Option, { key: 'user', value: 'user', children: '用户' })
      ]),
      span: 12
    }
  ]
}; 