# 🔧 SmartLoan 2025 - IDE配置指南

## 📋 **Java依赖问题解决方案**

### **问题分析**
您遇到的错误主要是：
1. `jakarta` 包无法解析
2. `lombok` 注解无法识别
3. `org.springframework` 包无法解析

### **解决步骤**

#### **步骤1：确认Java环境**
```bash
java -version
# 应该显示Java 17或更高版本
```

#### **步骤2：运行依赖刷新脚本**
```bash
# 在项目根目录运行
refresh-dependencies.bat
```

#### **步骤3：IDE配置（VS Code）**

1. **安装必要扩展**：
   - Extension Pack for Java
   - Spring Boot Extension Pack
   - Lombok Annotations Support

2. **配置Java路径**：
   ```json
   // settings.json
   {
     "java.home": "C:\\Program Files\\Java\\jdk-17",
     "java.configuration.runtimes": [
       {
         "name": "JavaSE-17",
         "path": "C:\\Program Files\\Java\\jdk-17"
       }
     ]
   }
   ```

3. **刷新项目**：
   - 按 `Ctrl+Shift+P`
   - 运行 `Java: Reload Projects`

#### **步骤4：IDE配置（IntelliJ IDEA）**

1. **设置项目JDK**：
   - File → Project Structure → Project
   - 设置 Project SDK 为 Java 17

2. **启用注解处理**：
   - File → Settings → Build → Compiler → Annotation Processors
   - 勾选 "Enable annotation processing"

3. **安装Lombok插件**：
   - File → Settings → Plugins
   - 搜索并安装 "Lombok"

4. **刷新Maven项目**：
   - 右键点击 pom.xml
   - 选择 "Maven → Reload project"

#### **步骤5：IDE配置（Eclipse）**

1. **设置Java版本**：
   - Project → Properties → Java Build Path
   - 设置 JRE 为 Java 17

2. **安装Lombok**：
   - 下载 lombok.jar
   - 运行 `java -jar lombok.jar`
   - 选择Eclipse安装目录

3. **刷新Maven项目**：
   - 右键项目 → Maven → Reload

### **验证配置**

运行以下命令验证配置：
```bash
cd backend/smartloan-backend
mvn clean compile
```

如果编译成功，说明依赖配置正确。

### **常见问题解决**

#### **问题1：Java版本不匹配**
```bash
# 检查Java版本
java -version
javac -version

# 设置JAVA_HOME
set JAVA_HOME=C:\Program Files\Java\jdk-17
set PATH=%JAVA_HOME%\bin;%PATH%
```

#### **问题2：Maven缓存问题**
```bash
# 清理Maven缓存
mvn dependency:purge-local-repository
mvn clean install
```

#### **问题3：IDE缓存问题**
- **VS Code**: 重启VS Code
- **IntelliJ**: File → Invalidate Caches and Restart
- **Eclipse**: Project → Clean → Clean all projects

### **快速修复脚本**

如果上述步骤仍有问题，运行以下命令：

```bash
# 1. 清理所有缓存
mvn clean
mvn dependency:purge-local-repository

# 2. 重新下载依赖
mvn dependency:resolve
mvn dependency:resolve-sources

# 3. 编译项目
mvn compile

# 4. 如果仍有问题，强制更新
mvn clean install -U
```

### **最终验证**

成功配置后，以下代码应该无错误：

```java
import lombok.Data;
import jakarta.persistence.Entity;
import org.springframework.boot.SpringApplication;

@Data
@Entity
public class TestClass {
    private String name;
}
```

## 🎯 **推荐方案**

### **方案1：使用现有Node.js版本（推荐）**
- ✅ **立即可用**: http://localhost:3006/
- ✅ **所有功能正常**: 7个核心API全部工作
- ✅ **无需配置**: 直接使用

### **方案2：修复Java环境**
- 🔄 **需要时间**: 配置Java 17环境
- 🔄 **需要依赖**: 下载Maven依赖
- 🔄 **需要IDE配置**: 重新配置开发环境

## 🏆 **建议**

1. **立即使用Node.js版本**进行演示和测试
2. **并行配置Java环境**（如有需要）
3. **优先保证功能可用性**

---

**当前可用地址**: http://localhost:3006/  
**状态**: 100%功能可用  
**建议**: 立即测试使用！
