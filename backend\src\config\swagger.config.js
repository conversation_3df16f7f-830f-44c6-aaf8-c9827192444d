"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupSwagger = setupSwagger;
var swagger_1 = require("@nestjs/swagger");
function setupSwagger(app) {
    var options = new swagger_1.DocumentBuilder()
        .setTitle('SmartLoan API')
        .setDescription('智能贷款申请系统API文档')
        .setVersion('1.0')
        .addBearerAuth()
        .addTag('认证')
        .addTag('用户')
        .addTag('贷款申请')
        .addTag('文件上传')
        .build();
    var document = swagger_1.SwaggerModule.createDocument(app, options);
    swagger_1.SwaggerModule.setup('api/docs', app, document);
}
