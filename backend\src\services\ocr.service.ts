import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from './logger.service';
import { ErrorHandler } from '../utils/error-handler';
import { MonitorService } from './monitor.service';
import { CacheService } from './cache.service';
import * as tf from '@tensorflow/tfjs';
import * as cv from '@techstark/opencv-js';

@Injectable()
export class OcrService {
  private readonly supportedDocuments = [
    'idCard',
    'passport',
    'driverLicense',
    'businessLicense',
    'taxRegistration',
    'bankStatement',
    'incomeProof',
    'propertyCertificate',
    'vehicleRegistration',
    'educationCertificate',
    'marriageCertificate',
    'divorceCertificate',
    'birthCertificate',
    'deathCertificate',
    'householdRegister'
  ];

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
    private readonly errorHandler: ErrorHandler,
    private readonly monitorService: MonitorService,
    private readonly cacheService: CacheService
  ) {}
  async processDocument(documentType: string, imageBuffer: Buffer) {
    try {
      this.monitorService.startOperation('ocr_processing');
      
      // 验证文档类型
      if (!this.supportedDocuments.includes(documentType)) {
        throw new Error(`不支持的文档类型: ${documentType}`);
      }

      // 图像预处理（简化版）
      const processedImage = await this.preprocessImage(imageBuffer);
      
      // 文本识别
      const textResult = await this.recognizeText(processedImage);
      
      // 信息提取
      const extractedInfo = await this.extractInformation(documentType, textResult);
      
      // 验证结果
      const validatedInfo = await this.validateInformation(documentType, extractedInfo);
      
      this.monitorService.endOperation('ocr_processing');
      
      return validatedInfo;
    } catch (error) {
      this.logger.error('OCR处理失败', error);
      throw this.errorHandler.handle(error);
    }
  }
  private async preprocessImage(imageBuffer: Buffer) {
    try {
      // 简化图像预处理，避免OpenCV复杂操作
      return imageBuffer; // 直接返回原始buffer，让OCR引擎处理
    } catch (error) {
      this.logger.error('图像预处理失败', error);
      throw error;
    }
  }

  private enhanceImage(image: cv.Mat) {
    // 对比度增强
    const enhanced = new cv.Mat();
    cv.convertScaleAbs(image, enhanced, 1.2, 0);
    
    // 锐化
    const kernel = cv.matFromArray(3, 3, cv.CV_32F, [
      0, -1, 0,
      -1, 5, -1,
      0, -1, 0
    ]);
    cv.filter2D(enhanced, enhanced, -1, kernel);
    
    return enhanced;
  }

  private removeNoise(image: cv.Mat) {
    const denoised = new cv.Mat();
    cv.fastNlMeansDenoisingColored(image, denoised);
    return denoised;
  }

  private binarize(image: cv.Mat) {
    const binary = new cv.Mat();
    cv.cvtColor(image, image, cv.COLOR_BGR2GRAY);
    cv.adaptiveThreshold(
      image,
      binary,
      255,
      cv.ADAPTIVE_THRESH_GAUSSIAN_C,
      cv.THRESH_BINARY,
      11,
      2
    );
    return binary;
  }

  private async detectAndCorrectDocument(image: cv.Mat) {
    try {
      // 边缘检测
      const edges = new cv.Mat();
      cv.Canny(image, edges, 50, 150);
      
      // 轮廓检测
      const contours = new cv.MatVector();
      const hierarchy = new cv.Mat();
      cv.findContours(edges, contours, hierarchy, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE);
      
      // 找到最大轮廓（假设是文档）
      let maxArea = 0;
      let maxContourIndex = -1;
      
      for (let i = 0; i < contours.size(); i++) {
        const contour = contours.get(i);
        const area = cv.contourArea(contour);
        if (area > maxArea) {
          maxArea = area;
          maxContourIndex = i;
        }
      }
      
      if (maxContourIndex === -1) {
        throw new Error('未检测到文档');
      }
      
      // 获取文档角点
      const maxContour = contours.get(maxContourIndex);
      const epsilon = 0.02 * cv.arcLength(maxContour, true);
      const approx = new cv.Mat();
      cv.approxPolyDP(maxContour, approx, epsilon, true);
      
      // 透视变换
      const srcPoints = approx.data32F;
      const dstPoints = new Float32Array([
        0, 0,
        image.cols, 0,
        image.cols, image.rows,
        0, image.rows
      ]);
      
      const M = cv.getPerspectiveTransform(srcPoints, dstPoints);
      const corrected = new cv.Mat();
      cv.warpPerspective(image, corrected, M, new cv.Size(image.cols, image.rows));
      
      return corrected;
    } catch (error) {
      this.logger.error('文档检测和校正失败', error);
      throw error;
    }
  }  async recognizeText(imageBuffer: Buffer) {
    try {
      // 使用Tesseract.js进行OCR
      const { createWorker } = require('tesseract.js');
      const worker = await createWorker('chi_sim');
      
      const result = await worker.recognize(imageBuffer);
      await worker.terminate();
      
      return result.data.text;
    } catch (error) {
      this.logger.error('文本识别失败', error);
      throw error;
    }
  }

  private async extractInformation(documentType: string, text: string) {
    try {
      // 根据文档类型使用不同的提取规则
      switch (documentType) {
        case 'idCard':
          return this.extractIdCardInfo(text);
        case 'passport':
          return this.extractPassportInfo(text);
        case 'driverLicense':
          return this.extractDriverLicenseInfo(text);
        case 'businessLicense':
          return this.extractBusinessLicenseInfo(text);
        default:
          return this.extractGenericInfo(text);
      }
    } catch (error) {
      this.logger.error('信息提取失败', error);
      throw error;
    }
  }

  private async validateInformation(documentType: string, info: any) {
    try {
      // 根据文档类型进行验证
      switch (documentType) {
        case 'idCard':
          return this.validateIdCardInfo(info);
        case 'passport':
          return this.validatePassportInfo(info);
        case 'driverLicense':
          return this.validateDriverLicenseInfo(info);
        case 'businessLicense':
          return this.validateBusinessLicenseInfo(info);
        default:
          return this.validateGenericInfo(info);
      }
    } catch (error) {
      this.logger.error('信息验证失败', error);
      throw error;
    }
  }

  // 具体的信息提取和验证方法
  private extractIdCardInfo(text: string) {
    // 实现身份证信息提取逻辑
    return {
      name: this.extractName(text),
      idNumber: this.extractIdNumber(text),
      gender: this.extractGender(text),
      nationality: this.extractNationality(text),
      birthDate: this.extractBirthDate(text),
      address: this.extractAddress(text)
    };
  }

  private validateIdCardInfo(info: any) {
    // 实现身份证信息验证逻辑
    const validations = {
      name: this.validateName(info.name),
      idNumber: this.validateIdNumber(info.idNumber),
      gender: this.validateGender(info.gender),
      nationality: this.validateNationality(info.nationality),
      birthDate: this.validateBirthDate(info.birthDate),
      address: this.validateAddress(info.address)
    };

    return {
      ...info,
      validations,
      isValid: Object.values(validations).every(v => v)
    };
  }

  async recognizeIdentityCard(imageBuffer: Buffer) {
    try {
      this.logger.log('开始身份证识别');
      const result = await this.processDocument('idCard', imageBuffer);
      
      return {
        success: true,
        data: {
          name: result.name || '',
          idNumber: result.idNumber || '',
          gender: result.gender || '',
          birthDate: result.birthDate || '',
          address: result.address || '',
          issuingAuthority: result.issuingAuthority || '',
          validPeriod: result.validPeriod || ''
        }
      };
    } catch (error) {
      this.logger.error('身份证识别失败', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async recognizeBankCard(imageBuffer: Buffer) {
    try {
      this.logger.log('开始银行卡识别');
      const result = await this.processDocument('bankCard', imageBuffer);
      
      return {
        success: true,
        data: {
          cardNumber: result.cardNumber || '',
          bankName: result.bankName || '',
          cardType: result.cardType || '',
          validThru: result.validThru || '',
          holderName: result.holderName || ''
        }
      };
    } catch (error) {
      this.logger.error('银行卡识别失败', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async recognizeIncomeProof(imageBuffer: Buffer) {
    try {
      this.logger.log('开始收入证明识别');
      const result = await this.processDocument('incomeProof', imageBuffer);
      
      return {
        success: true,
        data: {
          employerName: result.employerName || '',
          employeeName: result.employeeName || '',
          position: result.position || '',
          monthlyIncome: result.monthlyIncome || '',
          workYears: result.workYears || '',
          issueDate: result.issueDate || ''
        }
      };
    } catch (error) {
      this.logger.error('收入证明识别失败', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 其他辅助方法
  private extractName(text: string) {
    // 实现姓名提取逻辑
    return '';
  }

  private extractIdNumber(text: string) {
    // 实现身份证号提取逻辑
    return '';
  }

    private extractPassportInfo(text: string) {
    // 实现护照信息提取逻辑
    return {};
  }

  private extractDriverLicenseInfo(text: string) {
    // 实现驾驶证信息提取逻辑
    return {};
  }

  private extractBusinessLicenseInfo(text: string) {
    // 实现营业执照信息提取逻辑
    return {};
  }

  private extractGenericInfo(text: string) {
    // 实现通用信息提取逻辑
    return {};
  }

  private validatePassportInfo(info: any) {
    // 实现护照信息验证逻辑
    return {
      ...info,
      isValid: true
    };
  }

  private validateDriverLicenseInfo(info: any) {
    // 实现驾驶证信息验证逻辑
    return {
      ...info,
      isValid: true
    };
  }

  private validateBusinessLicenseInfo(info: any) {
    // 实现营业执照信息验证逻辑
    return {
      ...info,
      isValid: true
    };
  }

  private validateGenericInfo(info: any) {
    // 实现通用信息验证逻辑
    return {
      ...info,
      isValid: true
    };
  }

  private extractGender(text: string) {
    // 实现性别提取逻辑
    return '';
  }

  private extractNationality(text: string) {
    // 实现国籍提取逻辑
    return '';
  }

  private extractBirthDate(text: string) {
    // 实现出生日期提取逻辑
    return '';
  }

  private extractAddress(text: string) {
    // 实现地址提取逻辑
    return '';
  }

  private validateName(name: string) {
    // 实现姓名验证逻辑
    return true;
  }

  private validateIdNumber(idNumber: string) {
    // 实现身份证号验证逻辑
    return true;
  }

  private validateGender(gender: string) {
    // 实现性别验证逻辑
    return true;
  }

  private validateNationality(nationality: string) {
    // 实现国籍验证逻辑
    return true;
  }

  private validateBirthDate(birthDate: string) {
    // 实现出生日期验证逻辑
    return true;
  }

  private validateAddress(address: string) {
    // 实现地址验证逻辑
    return true;
  }

}