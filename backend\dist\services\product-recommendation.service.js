"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ProductRecommendationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductRecommendationService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const cache_service_1 = require("./cache.service");
const monitor_service_1 = require("./monitor.service");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const product_entity_1 = require("../entities/product.entity");
const loan_application_entity_1 = require("../entities/loan-application.entity");
const user_entity_1 = require("../entities/user.entity");
const redis_service_1 = require("./redis.service");
let ProductRecommendationService = ProductRecommendationService_1 = class ProductRecommendationService {
    constructor(productRepository, loanApplicationRepository, userRepository, redisService, cacheService, monitorService, configService) {
        this.productRepository = productRepository;
        this.loanApplicationRepository = loanApplicationRepository;
        this.userRepository = userRepository;
        this.redisService = redisService;
        this.cacheService = cacheService;
        this.monitorService = monitorService;
        this.configService = configService;
        this.logger = new common_1.Logger(ProductRecommendationService_1.name);
        this.CACHE_TTL = 3600;
        this.products = [
            {
                id: '1',
                name: '个人消费贷款',
                type: 'CONSUMER',
                minAmount: 10000,
                maxAmount: 500000,
                minTerm: 12,
                maxTerm: 60,
                baseInterestRate: 0.0495,
                description: '用于个人消费的信用贷款产品',
                features: ['快速审批', '灵活还款', '无抵押'],
                requirements: ['年满18周岁', '有稳定收入', '信用记录良好'],
            },
            {
                id: '2',
                name: '经营贷款',
                type: 'BUSINESS',
                minAmount: 100000,
                maxAmount: 2000000,
                minTerm: 12,
                maxTerm: 36,
                baseInterestRate: 0.0545,
                description: '用于企业经营周转的贷款产品',
                features: ['大额授信', '专款专用', '灵活还款'],
                requirements: ['企业注册满1年', '有稳定经营', '有抵押物'],
            },
            {
                id: '3',
                name: '房贷',
                type: 'MORTGAGE',
                minAmount: 200000,
                maxAmount: 5000000,
                minTerm: 60,
                maxTerm: 360,
                baseInterestRate: 0.0395,
                description: '用于购买住房的贷款产品',
                features: ['长期限', '低利率', '等额本息'],
                requirements: ['有购房资格', '有首付款', '有稳定收入'],
            },
        ];
    }
    async getRecommendedProducts(userId, amount, term) {
        const cacheKey = `recommendations:${userId}:${amount}:${term}`;
        const cachedRecommendations = await this.redisService.get(cacheKey);
        if (cachedRecommendations) {
            return JSON.parse(cachedRecommendations);
        }
        const userApplications = await this.loanApplicationRepository.find({
            where: { userId },
            relations: ['product']
        });
        const products = await this.productRepository.find({
            where: { isActive: true }
        });
        const scoredProducts = products.map(product => {
            let score = 0;
            if (amount >= product.minAmount && amount <= product.maxAmount) {
                score += 3;
            }
            if (term >= product.minTerm && term <= product.maxTerm) {
                score += 3;
            }
            const userProductHistory = userApplications.filter(app => app.product.id === product.id);
            if (userProductHistory.length > 0) {
                score += 2;
            }
            if (product.isFeatured) {
                score += 1;
            }
            if (product.metadata?.popularity) {
                score += product.metadata.popularity * 2;
            }
            if (product.metadata?.approvalRate) {
                score += product.metadata.approvalRate * 2;
            }
            return { product, score };
        });
        const recommendations = scoredProducts
            .sort((a, b) => b.score - a.score)
            .slice(0, 5)
            .map(item => item.product);
        await this.redisService.set(cacheKey, JSON.stringify(recommendations), this.CACHE_TTL);
        return recommendations;
    }
    async getPopularProducts() {
        const cacheKey = 'popular_products';
        return this.cacheService.getOrSet(cacheKey, async () => {
            const products = await this.productRepository.find({
                where: {
                    isActive: true,
                    isFeatured: true
                },
                order: {
                    createdAt: 'DESC'
                },
                take: 5
            });
            return products;
        }, this.CACHE_TTL);
    }
    async adjustInterestRate(productId, riskScore) {
        const product = this.products.find((p) => p.id === productId);
        if (!product) {
            throw new Error('产品不存在');
        }
        let adjustedRate = product.baseInterestRate;
        if (riskScore > 80) {
            adjustedRate *= 0.9;
        }
        else if (riskScore > 60) {
            adjustedRate *= 0.95;
        }
        else if (riskScore < 40) {
            adjustedRate *= 1.1;
        }
        else if (riskScore < 20) {
            adjustedRate *= 1.2;
        }
        this.monitorService.logApplicationEvent('interest_rate_adjusted', {
            productId,
            riskScore,
            baseRate: product.baseInterestRate,
            adjustedRate,
        });
        return adjustedRate;
    }
    async getProductDetails(productId) {
        const cacheKey = `product:${productId}`;
        const cachedProduct = await this.cacheService.get(cacheKey);
        if (cachedProduct) {
            return cachedProduct;
        }
        const product = this.products.find((p) => p.id === productId);
        if (product) {
            await this.cacheService.set(cacheKey, product, 86400);
        }
        return product || null;
    }
    async getAllProducts() {
        const cacheKey = 'all_products';
        return this.cacheService.getOrSet(cacheKey, async () => this.products, 3600);
    }
    async getProductsByType(type) {
        const cacheKey = `products:type:${type}`;
        const cachedProducts = await this.cacheService.get(cacheKey);
        if (cachedProducts) {
            return cachedProducts;
        }
        const products = this.products.filter(p => p.type === type);
        await this.cacheService.set(cacheKey, products, 3600);
        return products;
    }
    async recommendProductsByUserFeatures(userId, features) {
        try {
            const cacheKey = `user_features_recommendations:${userId}:${JSON.stringify(features)}`;
            return this.cacheService.getOrSet(cacheKey, async () => {
                const userApplications = await this.loanApplicationRepository.find({
                    where: { userId },
                    relations: ['product'],
                    order: { createdAt: 'DESC' },
                    take: 10
                });
                const allProducts = await this.productRepository.find({
                    where: { isActive: true }
                });
                const scoredProducts = allProducts.map(product => {
                    let score = 0;
                    if (features.income && product.minAmount) {
                        const affordabilityRatio = (features.income * 12) / product.minAmount;
                        if (affordabilityRatio >= 3)
                            score += 30;
                        else if (affordabilityRatio >= 2)
                            score += 20;
                        else if (affordabilityRatio >= 1.5)
                            score += 10;
                    }
                    if (features.creditScore) {
                        if (features.creditScore >= 750)
                            score += 25;
                        else if (features.creditScore >= 650)
                            score += 15;
                        else if (features.creditScore >= 550)
                            score += 5;
                    }
                    const historicalPreference = userApplications.find(app => app.product.category === product.category);
                    if (historicalPreference)
                        score += 20;
                    if (product.isFeatured)
                        score += 10;
                    return { product, score };
                });
                return scoredProducts
                    .sort((a, b) => b.score - a.score)
                    .slice(0, 5)
                    .map(item => item.product);
            }, this.CACHE_TTL);
        }
        catch (error) {
            this.logger.error(`基于用户特征推荐产品失败: ${error.message}`);
            throw error;
        }
    }
    async getSimilarProducts(productId) {
        try {
            const cacheKey = `similar_products:${productId}`;
            return this.cacheService.getOrSet(cacheKey, async () => {
                const targetProduct = await this.productRepository.findOne({
                    where: { id: productId }
                });
                if (!targetProduct) {
                    throw new Error('目标产品不存在');
                }
                const similarProducts = await this.productRepository.find({
                    where: {
                        id: (0, typeorm_2.Not)(productId),
                        category: targetProduct.category,
                        isActive: true
                    },
                    order: { interestRate: 'ASC' },
                    take: 5
                });
                if (similarProducts.length < 3) {
                    const additionalProducts = await this.productRepository.find({
                        where: {
                            id: (0, typeorm_2.Not)(productId),
                            category: (0, typeorm_2.Not)(targetProduct.category),
                            isActive: true
                        },
                        order: { interestRate: 'ASC' },
                        take: 5 - similarProducts.length
                    });
                    similarProducts.push(...additionalProducts);
                }
                return similarProducts;
            }, this.CACHE_TTL);
        }
        catch (error) {
            this.logger.error(`获取相似产品失败: ${error.message}`);
            throw error;
        }
    }
};
ProductRecommendationService = ProductRecommendationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __param(1, (0, typeorm_1.InjectRepository)(loan_application_entity_1.LoanApplication)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        redis_service_1.RedisService,
        cache_service_1.CacheService,
        monitor_service_1.MonitorService,
        config_1.ConfigService])
], ProductRecommendationService);
exports.ProductRecommendationService = ProductRecommendationService;
//# sourceMappingURL=product-recommendation.service.js.map