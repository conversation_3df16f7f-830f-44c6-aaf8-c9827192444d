import { DataSource } from 'typeorm';
import { FinancialProduct } from '../modules/product/entities/financial-product.entity';
import { User } from '../modules/user/user.entity';
import seedFinancialProducts from './seeds/financial-products.seed';

// 数据库配置
const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  username: process.env.DB_USERNAME || 'smartloan_user',
  password: process.env.DB_PASSWORD || 'smartloan_pass',
  database: process.env.DB_DATABASE || 'smartloan_db',
  entities: [FinancialProduct, User],
  synchronize: true, // 开发环境自动同步表结构
  logging: true,
});

async function initializeDatabase() {
  try {
    console.log('🔄 正在连接数据库...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    console.log('🔄 正在同步表结构...');
    await AppDataSource.synchronize();
    console.log('✅ 表结构同步完成');

    console.log('🌱 正在初始化种子数据...');
    await seedFinancialProducts(AppDataSource);
    console.log('✅ 种子数据初始化完成');

    // 创建测试用户
    console.log('👤 正在创建测试用户...');
    const userRepository = AppDataSource.getRepository(User);
    
    const testUsers = [
      {
        username: 'test_user_2025',
        email: '<EMAIL>',
        phone: '13800138000',
        password_hash: '$2b$10$hash', // 实际应用中应该是加密后的密码
        full_name: '张三',
        birth_date: new Date('1990-01-01'),
        gender: 'male',
        id_number: '110101199001011234',
        employment_info: {
          company_name: '科技创新公司',
          position: '高级工程师',
          employment_type: 'full_time',
          monthly_income: 15000,
          work_years: 5,
          industry: 'technology'
        },
        financial_info: {
          monthly_income: 15000,
          other_income: 2000,
          monthly_expenses: 8000,
          assets: 500000,
          liabilities: 100000,
          credit_score: 750,
          bank_accounts: ['工商银行', '建设银行']
        },
        status: 'active',
        is_verified: true,
        kyc_completed: true
      },
      {
        username: 'premium_user_2025',
        email: '<EMAIL>',
        phone: '***********',
        password_hash: '$2b$10$hash',
        full_name: '李四',
        birth_date: new Date('1985-05-15'),
        gender: 'female',
        id_number: '110101198505151234',
        employment_info: {
          company_name: '金融科技集团',
          position: '产品总监',
          employment_type: 'full_time',
          monthly_income: 35000,
          work_years: 10,
          industry: 'finance'
        },
        financial_info: {
          monthly_income: 35000,
          other_income: 8000,
          monthly_expenses: 15000,
          assets: 2000000,
          liabilities: 300000,
          credit_score: 820,
          bank_accounts: ['招商银行', '平安银行', '浦发银行']
        },
        status: 'active',
        is_verified: true,
        kyc_completed: true
      }
    ];

    for (const userData of testUsers) {
      const existingUser = await userRepository.findOne({ 
        where: { email: userData.email } 
      });
      
      if (!existingUser) {
        const user = userRepository.create(userData);
        await userRepository.save(user);
        console.log(`✅ 创建测试用户: ${userData.full_name}`);
      } else {
        console.log(`⚠️ 用户已存在: ${userData.full_name}`);
      }
    }

    console.log('🎉 数据库初始化完成！');
    
    // 显示统计信息
    const productCount = await AppDataSource.getRepository(FinancialProduct).count();
    const userCount = await userRepository.count();
    
    console.log('📊 数据统计:');
    console.log(`   金融产品: ${productCount} 个`);
    console.log(`   测试用户: ${userCount} 个`);
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    throw error;
  } finally {
    await AppDataSource.destroy();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此文件，则执行初始化
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('✅ 初始化脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 初始化脚本执行失败:', error);
      process.exit(1);
    });
}

export { initializeDatabase, AppDataSource };
