const http = require('http');

console.log('启动测试服务器...');

const server = http.createServer((req, res) => {
  console.log(`请求: ${req.method} ${req.url}`);
  
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Content-Type', 'application/json');
  
  if (req.url === '/api/health') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      status: 'healthy',
      message: 'API服务正常运行'
    }));
    return;
  }
  
  res.writeHead(404);
  res.end(JSON.stringify({
    success: false,
    error: 'API接口未找到'
  }));
});

server.listen(3003, () => {
  console.log('✅ 测试服务器启动成功: http://localhost:3003');
  console.log('🔗 测试健康检查: http://localhost:3003/api/health');
});

server.on('error', (err) => {
  console.error('❌ 服务器错误:', err);
});
