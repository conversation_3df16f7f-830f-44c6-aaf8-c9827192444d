/**
 * 安全配置
 * OAuth 2.0 + JWT + 生物识别验证
 * 等保三级安全标准
 */

package com.smartloan.security;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.spec.SecretKeySpec;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final BiometricAuthenticationFilter biometricAuthenticationFilter;
    private final DeviceFingerprintFilter deviceFingerprintFilter;

    /**
     * 安全过滤器链配置
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF（使用JWT无状态认证）
            .csrf(csrf -> csrf.disable())
            
            // CORS配置
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            
            // 会话管理 - 无状态
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            
            // 请求授权配置
            .authorizeHttpRequests(authz -> authz
                // 公开端点
                .requestMatchers(
                    "/api/health",
                    "/api/v1/auth/login",
                    "/api/v1/auth/register",
                    "/api/v1/auth/refresh",
                    "/api/v1/products/public",
                    "/actuator/health",
                    "/swagger-ui/**",
                    "/v3/api-docs/**"
                ).permitAll()
                
                // 需要基础认证的端点
                .requestMatchers(
                    "/api/v1/products/**",
                    "/api/v1/users/profile"
                ).hasRole("USER")
                
                // 需要高级认证的端点（包含生物识别）
                .requestMatchers(
                    "/api/v1/audits/**",
                    "/api/v1/risk/**",
                    "/api/v1/loans/apply"
                ).hasRole("VERIFIED_USER")
                
                // 管理员端点
                .requestMatchers(
                    "/api/v1/admin/**",
                    "/api/v1/risk/management/**"
                ).hasRole("ADMIN")
                
                // 其他所有请求需要认证
                .anyRequest().authenticated())
            
            // JWT认证过滤器
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
            
            // 生物识别认证过滤器
            .addFilterAfter(biometricAuthenticationFilter, JwtAuthenticationFilter.class)
            
            // 设备指纹过滤器
            .addFilterAfter(deviceFingerprintFilter, BiometricAuthenticationFilter.class)
            
            // OAuth2资源服务器配置
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(jwt -> jwt
                    .decoder(jwtDecoder())
                    .jwtAuthenticationConverter(new CustomJwtAuthenticationConverter())))
            
            // 异常处理
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(new CustomAuthenticationEntryPoint())
                .accessDeniedHandler(new CustomAccessDeniedHandler()));

        return http.build();
    }

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12); // 强度12，符合金融级安全要求
    }

    /**
     * JWT解码器
     */
    @Bean
    public JwtDecoder jwtDecoder() {
        // 使用HMAC-SHA256算法
        String secretKey = "SmartLoan2025SecretKeyForJWTTokenGenerationAndValidation";
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(), "HmacSHA256");
        return NimbusJwtDecoder.withSecretKey(secretKeySpec).build();
    }

    /**
     * CORS配置
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 允许的源
        configuration.setAllowedOriginPatterns(Arrays.asList(
            "https://*.smartloan.com",
            "https://localhost:*",
            "http://localhost:*"
        ));
        
        // 允许的方法
        configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"
        ));
        
        // 允许的头部
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization",
            "Content-Type",
            "X-Requested-With",
            "X-User-ID",
            "X-Device-ID",
            "X-Biometric-Token",
            "X-Request-ID"
        ));
        
        // 暴露的头部
        configuration.setExposedHeaders(Arrays.asList(
            "X-Request-ID",
            "X-Response-Time",
            "X-Rate-Limit-Remaining"
        ));
        
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    /**
     * 自定义JWT认证转换器
     */
    public static class CustomJwtAuthenticationConverter 
            implements org.springframework.core.convert.converter.Converter<
                org.springframework.security.oauth2.jwt.Jwt, 
                org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken> {

        @Override
        public org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken convert(
                org.springframework.security.oauth2.jwt.Jwt jwt) {
            
            // 提取权限
            List<String> authorities = jwt.getClaimAsStringList("authorities");
            if (authorities == null) {
                authorities = Arrays.asList("ROLE_USER");
            }
            
            var grantedAuthorities = authorities.stream()
                .map(authority -> new org.springframework.security.core.authority.SimpleGrantedAuthority(authority))
                .toList();
            
            return new org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken(
                jwt, grantedAuthorities);
        }
    }

    /**
     * 自定义认证入口点
     */
    public static class CustomAuthenticationEntryPoint 
            implements org.springframework.security.web.AuthenticationEntryPoint {

        @Override
        public void commence(
                jakarta.servlet.http.HttpServletRequest request,
                jakarta.servlet.http.HttpServletResponse response,
                org.springframework.security.core.AuthenticationException authException) 
                throws java.io.IOException {
            
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(jakarta.servlet.http.HttpServletResponse.SC_UNAUTHORIZED);
            
            String jsonResponse = """
                {
                    "code": 401,
                    "message": "认证失败，请重新登录",
                    "timestamp": "%s",
                    "path": "%s"
                }
                """.formatted(
                    java.time.Instant.now().toString(),
                    request.getRequestURI()
                );
            
            response.getWriter().write(jsonResponse);
        }
    }

    /**
     * 自定义访问拒绝处理器
     */
    public static class CustomAccessDeniedHandler 
            implements org.springframework.security.web.access.AccessDeniedHandler {

        @Override
        public void handle(
                jakarta.servlet.http.HttpServletRequest request,
                jakarta.servlet.http.HttpServletResponse response,
                org.springframework.security.access.AccessDeniedException accessDeniedException) 
                throws java.io.IOException {
            
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(jakarta.servlet.http.HttpServletResponse.SC_FORBIDDEN);
            
            String jsonResponse = """
                {
                    "code": 403,
                    "message": "权限不足，无法访问该资源",
                    "timestamp": "%s",
                    "path": "%s"
                }
                """.formatted(
                    java.time.Instant.now().toString(),
                    request.getRequestURI()
                );
            
            response.getWriter().write(jsonResponse);
        }
    }
}
