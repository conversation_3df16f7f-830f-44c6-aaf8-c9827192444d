# SmartLoan 系统状态检查脚本
Write-Host "🔍 SmartLoan 系统状态检查" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# 检查Docker容器状态
Write-Host "📦 检查Docker容器状态..." -ForegroundColor Yellow
try {
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    Write-Host $containers -ForegroundColor White
} catch {
    Write-Host "❌ Docker未运行或无容器" -ForegroundColor Red
}

Write-Host ""

# 检查服务端口
Write-Host "🌐 检查服务端口状态..." -ForegroundColor Yellow

$services = @(
    @{Name="前端服务"; Port=5173; Url="http://localhost:5173"},
    @{Name="后端API"; Port=3001; Url="http://localhost:3001/api/health"},
    @{Name="AI服务"; Port=3002; Url="http://localhost:3002/health"},
    @{Name="PostgreSQL"; Port=5432; Url=$null},
    @{Name="Redis"; Port=6379; Url=$null}
)

foreach ($service in $services) {
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $service.Port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "✅ $($service.Name) (端口 $($service.Port)) - 运行正常" -ForegroundColor Green

            # 如果有URL，检查HTTP响应
            if ($service.Url) {
                try {
                    $response = Invoke-WebRequest -Uri $service.Url -TimeoutSec 3 -ErrorAction Stop
                    Write-Host "   HTTP状态: $($response.StatusCode)" -ForegroundColor Cyan
                } catch {
                    Write-Host "   HTTP检查失败: $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
        } else {
            Write-Host "❌ $($service.Name) (端口 $($service.Port)) - 未运行" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $($service.Name) (端口 $($service.Port)) - 检查失败" -ForegroundColor Red
    }
}

Write-Host ""

# 检查系统资源
Write-Host "💻 系统资源使用情况..." -ForegroundColor Yellow
$cpu = Get-WmiObject -Class Win32_Processor | Measure-Object -Property LoadPercentage -Average
$memory = Get-WmiObject -Class Win32_OperatingSystem
$memoryUsed = [math]::Round((($memory.TotalVisibleMemorySize - $memory.FreePhysicalMemory) / $memory.TotalVisibleMemorySize) * 100, 2)

Write-Host "CPU使用率: $([math]::Round($cpu.Average, 2))%" -ForegroundColor Cyan
Write-Host "内存使用率: $memoryUsed%" -ForegroundColor Cyan

# 检查磁盘空间
$disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
$diskUsed = [math]::Round((($disk.Size - $disk.FreeSpace) / $disk.Size) * 100, 2)
Write-Host "磁盘使用率: $diskUsed%" -ForegroundColor Cyan

Write-Host ""

# 检查Node.js进程
Write-Host "🔧 检查Node.js进程..." -ForegroundColor Yellow
try {
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($nodeProcesses) {
        Write-Host "发现 $($nodeProcesses.Count) 个Node.js进程:" -ForegroundColor Green
        foreach ($process in $nodeProcesses) {
            $memoryMB = [math]::Round($process.WorkingSet / 1MB, 2)
            Write-Host "  PID: $($process.Id), 内存: ${memoryMB}MB" -ForegroundColor Cyan
        }
    } else {
        Write-Host "❌ 未发现Node.js进程" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 检查Node.js进程失败" -ForegroundColor Red
}

Write-Host ""

# 检查日志文件
Write-Host "📋 检查日志文件..." -ForegroundColor Yellow
$logPaths = @(
    "backend/logs",
    "ai-service/logs",
    "frontend/logs"
)

foreach ($logPath in $logPaths) {
    if (Test-Path $logPath) {
        $logFiles = Get-ChildItem $logPath -Filter "*.log" | Sort-Object LastWriteTime -Descending | Select-Object -First 3
        if ($logFiles) {
            Write-Host "✅ $logPath - 发现 $($logFiles.Count) 个日志文件" -ForegroundColor Green
            foreach ($file in $logFiles) {
                Write-Host "   $($file.Name) - $($file.LastWriteTime)" -ForegroundColor Cyan
            }
        } else {
            Write-Host "⚠️ $logPath - 无日志文件" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ $logPath - 目录不存在" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎯 快速操作命令:" -ForegroundColor Green
Write-Host "启动系统: .\start-system.ps1" -ForegroundColor White
Write-Host "查看日志: docker-compose logs -f" -ForegroundColor White
Write-Host "重启数据库: docker-compose restart postgres redis" -ForegroundColor White
Write-Host "停止系统: docker-compose down" -ForegroundColor White
Write-Host ""