# 智能贷款系统 API 文档

## 目录
1. [认证接口](#认证接口)
2. [数据分析接口](#数据分析接口)
3. [风控接口](#风控接口)
4. [导出接口](#导出接口)

## 认证接口

### 登录
```http
POST /auth/login
```

请求体：
```json
{
  "username": "string",
  "password": "string"
}
```

响应：
```json
{
  "success": true,
  "data": {
    "token": "string",
    "user": {
      "id": "number",
      "username": "string",
      "roles": ["string"]
    }
  }
}
```

### 刷新令牌
```http
POST /auth/refresh
```

请求头：
```
Authorization: Bearer <token>
```

响应：
```json
{
  "success": true,
  "data": {
    "token": "string"
  }
}
```

## 数据分析接口

### 获取仪表盘数据
```http
GET /analytics/dashboard
```

请求头：
```
Authorization: Bearer <token>
```

查询参数：
- startDate: 开始日期 (YYYY-MM-DD)
- endDate: 结束日期 (YYYY-MM-DD)
- loanType: 贷款类型 (personal/business)
- status: 状态 (pending/approved/rejected)
- minAmount: 最小金额
- maxAmount: 最大金额
- minScore: 最小分数
- maxScore: 最大分数

响应：
```json
{
  "success": true,
  "data": {
    "total": "number",
    "applications": [
      {
        "id": "string",
        "loanType": "string",
        "amount": "number",
        "term": "number",
        "status": "string",
        "createdAt": "string"
      }
    ],
    "statistics": {
      "totalAmount": "number",
      "averageAmount": "number",
      "approvalRate": "number",
      "averageScore": "number"
    }
  }
}
```

### 预测趋势
```http
POST /analytics/predict
```

请求头：
```
Authorization: Bearer <token>
```

请求体：
```json
{
  "metric": "string",
  "period": "string",
  "method": "string"
}
```

响应：
```json
{
  "success": true,
  "data": {
    "slope": "number",
    "intercept": "number",
    "predictions": ["number"]
  }
}
```

### 检测异常
```http
GET /analytics/anomalies
```

请求头：
```
Authorization: Bearer <token>
```

查询参数：
- metric: 指标名称
- threshold: 阈值

响应：
```json
{
  "success": true,
  "data": [
    {
      "date": "string",
      "value": "number",
      "deviation": "number"
    }
  ]
}
```

### 获取推荐
```http
GET /analytics/recommendations
```

请求头：
```
Authorization: Bearer <token>
```

查询参数：
- type: 推荐类型
- limit: 推荐数量

响应：
```json
{
  "success": true,
  "data": [
    {
      "type": "string",
      "message": "string",
      "priority": "string"
    }
  ]
}
```

## 风控接口

### 风控评估
```http
POST /risk/assess
```

请求头：
```
Authorization: Bearer <token>
```

请求体：
```json
{
  "applicationId": "string",
  "data": {
    "amount": "number",
    "term": "number",
    "creditScore": "number"
  }
}
```

响应：
```json
{
  "success": true,
  "data": {
    "riskLevel": "string",
    "score": "number",
    "factors": ["string"]
  }
}
```

### 获取风控规则
```http
GET /risk/rules
```

请求头：
```
Authorization: Bearer <token>
```

响应：
```json
{
  "success": true,
  "data": {
    "rules": [
      {
        "id": "string",
        "name": "string",
        "conditions": ["string"],
        "actions": ["string"]
      }
    ]
  }
}
```

### 更新风控规则
```http
PUT /risk/rules
```

请求头：
```
Authorization: Bearer <token>
```

请求体：
```json
{
  "rules": [
    {
      "id": "string",
      "name": "string",
      "conditions": ["string"],
      "actions": ["string"]
    }
  ]
}
```

响应：
```json
{
  "success": true,
  "data": {
    "updated": "number"
  }
}
```

## 导出接口

### 导出数据
```http
GET /analytics/export
```

请求头：
```
Authorization: Bearer <token>
```

查询参数：
- format: 导出格式 (excel/csv/json)
- template: 导出模板 (basic/detailed)

响应：
```json
{
  "success": true,
  "data": {
    "filename": "string",
    "content": "string/buffer"
  }
}
```

## 错误处理

所有接口在发生错误时都会返回以下格式的响应：

```json
{
  "success": false,
  "error": {
    "message": "string",
    "code": "string",
    "details": "any"
  }
}
```

常见错误代码：
- VALIDATION_ERROR: 参数验证失败
- UNAUTHORIZED: 未授权访问
- FORBIDDEN: 权限不足
- NOT_FOUND: 资源不存在
- DATABASE_ERROR: 数据库错误
- NETWORK_ERROR: 网络错误
- TIMEOUT: 请求超时
- BUSINESS_ERROR: 业务逻辑错误
- INTERNAL_ERROR: 内部服务器错误
- UNKNOWN_ERROR: 未知错误 