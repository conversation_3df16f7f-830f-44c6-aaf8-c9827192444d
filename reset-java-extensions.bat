@echo off
echo 🔧 SmartLoan 2025 - Java扩展重置工具
echo =====================================

echo 📋 正在检查VS Code进程...
tasklist /FI "IMAGENAME eq Code.exe" 2>NUL | find /I /N "Code.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ⚠️ 检测到VS Code正在运行
    echo 💡 请先关闭VS Code，然后重新运行此脚本
    pause
    exit /b 1
)

echo ✅ VS Code已关闭，开始重置...

echo.
echo 🧹 清理Java扩展缓存...
set VSCODE_EXTENSIONS=%USERPROFILE%\.vscode\extensions
if exist "%VSCODE_EXTENSIONS%" (
    echo 📁 清理扩展目录: %VSCODE_EXTENSIONS%
    for /d %%i in ("%VSCODE_EXTENSIONS%\redhat.java*") do (
        echo   删除: %%i
        rmdir /s /q "%%i" 2>nul
    )
    for /d %%i in ("%VSCODE_EXTENSIONS%\vscjava.*") do (
        echo   删除: %%i
        rmdir /s /q "%%i" 2>nul
    )
    for /d %%i in ("%VSCODE_EXTENSIONS%\ms-vscode.vscode-java*") do (
        echo   删除: %%i
        rmdir /s /q "%%i" 2>nul
    )
)

echo.
echo 🧹 清理工作区缓存...
set WORKSPACE_STORAGE=%APPDATA%\Code\User\workspaceStorage
if exist "%WORKSPACE_STORAGE%" (
    echo 📁 清理工作区缓存: %WORKSPACE_STORAGE%
    for /d %%i in ("%WORKSPACE_STORAGE%\*") do (
        if exist "%%i\.metadata" (
            echo   删除Java工作区: %%i
            rmdir /s /q "%%i" 2>nul
        )
    )
)

echo.
echo 🧹 清理Java Language Server缓存...
set JLS_CACHE=%USERPROFILE%\.cache\jdtls
if exist "%JLS_CACHE%" (
    echo 📁 清理JLS缓存: %JLS_CACHE%
    rmdir /s /q "%JLS_CACHE%" 2>nul
)

echo.
echo ✅ 清理完成！

echo.
echo 📋 下一步操作指南:
echo ==================
echo.
echo 1. 🚀 启动VS Code
echo    - 打开项目文件夹
echo.
echo 2. 📦 重新安装Java扩展 (可选)
echo    - 按 Ctrl+Shift+X 打开扩展面板
echo    - 搜索 "Extension Pack for Java"
echo    - 点击安装
echo.
echo 3. ⚙️ 或者禁用Java扩展 (推荐)
echo    - 如果主要使用Node.js版本
echo    - 可以完全禁用Java扩展
echo    - 避免所有Java相关错误
echo.
echo 4. 🌐 使用Node.js版本
echo    - 访问: http://localhost:3006/
echo    - 所有功能100%%可用
echo.
echo 💡 建议: 如果不需要Java开发，建议禁用Java扩展
echo    这样可以完全避免 "Auto connect failed" 错误
echo.
pause
