import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInitialTables1709123456789 implements MigrationInterface {
  name = 'CreateInitialTables1709123456789';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建用户表
    await queryRunner.query(`
      CREATE TABLE "user" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "username" character varying NOT NULL,
        "email" character varying NOT NULL,
        "password" character varying NOT NULL,
        "role" character varying NOT NULL DEFAULT 'user',
        "isActive" boolean NOT NULL DEFAULT true,
        "lastLoginAt" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_78a916df40e02a9deb1c4b75edb" UNIQUE ("username"),
        CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"),
        CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id")
      )
    `);

    // 创建贷款申请表
    await queryRunner.query(`
      CREATE TABLE "loan_application" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "userId" uuid NOT NULL,
        "loanType" character varying NOT NULL,
        "loanAmount" numeric NOT NULL,
        "loanTerm" integer NOT NULL,
        "purpose" character varying NOT NULL,
        "status" character varying NOT NULL DEFAULT 'pending',
        "riskScore" integer,
        "rejectionReason" character varying,
        "approvedAt" TIMESTAMP,
        "rejectedAt" TIMESTAMP,
        "cancelledAt" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_loan_application" PRIMARY KEY ("id"),
        CONSTRAINT "FK_loan_application_user" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE
      )
    `);

    // 创建贷款文档表
    await queryRunner.query(`
      CREATE TABLE "loan_document" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "loanApplicationId" uuid NOT NULL,
        "type" character varying NOT NULL,
        "fileName" character varying NOT NULL,
        "filePath" character varying NOT NULL,
        "fileSize" integer NOT NULL,
        "mimeType" character varying NOT NULL,
        "isVerified" boolean NOT NULL DEFAULT false,
        "verificationNotes" character varying,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_loan_document" PRIMARY KEY ("id"),
        CONSTRAINT "FK_loan_document_loan_application" FOREIGN KEY ("loanApplicationId") REFERENCES "loan_application"("id") ON DELETE CASCADE
      )
    `);

    // 创建审计日志表
    await queryRunner.query(`
      CREATE TABLE "audit_log" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "userId" uuid,
        "action" character varying NOT NULL,
        "entityType" character varying NOT NULL,
        "entityId" uuid,
        "details" jsonb,
        "ipAddress" character varying,
        "userAgent" character varying,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_audit_log" PRIMARY KEY ("id"),
        CONSTRAINT "FK_audit_log_user" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "audit_log"`);
    await queryRunner.query(`DROP TABLE "loan_document"`);
    await queryRunner.query(`DROP TABLE "loan_application"`);
    await queryRunner.query(`DROP TABLE "user"`);
  }
} 