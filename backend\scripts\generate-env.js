const fs = require('fs');
const path = require('path');

const envExamplePath = path.join(__dirname, '..', '.env.example');
const envPath = path.join(__dirname, '..', '.env');

// 读取 .env.example 文件
const envExample = fs.readFileSync(envExamplePath, 'utf8');

// 生成随机密钥
const generateSecret = (length = 32) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 替换默认值
const envContent = envExample
  .replace('your-jwt-secret-key', generateSecret())
  .replace('your-smtp-username', '')
  .replace('your-smtp-password', '')
  .replace('your-webhook-url', '');

// 写入 .env 文件
fs.writeFileSync(envPath, envContent);

console.log('环境变量文件已生成：', envPath);
console.log('请检查并更新以下配置：');
console.log('1. 数据库连接信息');
console.log('2. SMTP邮件服务器配置');
console.log('3. Slack Webhook URL'); 