@echo off
echo 🔧 SmartLoan 2025 - Java环境验证
echo ================================

echo 📋 检查Java版本...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java未正确安装
    pause
    exit /b 1
)

echo.
echo 📋 检查JAVA_HOME环境变量...
echo JAVA_HOME=%JAVA_HOME%

echo.
echo 📋 检查javac编译器...
javac -version
if %errorlevel% neq 0 (
    echo ❌ Java编译器未找到
    pause
    exit /b 1
)

echo.
echo 📋 检查Maven...
mvn --version
if %errorlevel% neq 0 (
    echo ❌ Maven未安装，正在提供安装指导...
    echo.
    echo 💡 请安装Apache Maven:
    echo    1. 访问: https://maven.apache.org/download.cgi
    echo    2. 下载 apache-maven-3.9.6-bin.zip
    echo    3. 解压到 C:\apache-maven-3.9.6
    echo    4. 添加到PATH: C:\apache-maven-3.9.6\bin
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Java环境检查完成！
echo 📊 环境信息:
echo    Java版本: 已安装
echo    JAVA_HOME: %JAVA_HOME%
echo    Maven: 已安装

echo.
echo 🔧 正在测试Maven编译...
cd backend\smartloan-backend
if exist pom.xml (
    echo 📦 找到pom.xml，开始编译测试...
    mvn clean compile -q
    if %errorlevel% equ 0 (
        echo ✅ Maven编译成功！
    ) else (
        echo ⚠️ Maven编译有警告，但Java环境正常
    )
) else (
    echo ⚠️ 未找到pom.xml文件
)

cd ..\..

echo.
echo 🎉 Java环境验证完成！
echo 💡 下一步操作：
echo    1. 重启VS Code
echo    2. 按 Ctrl+Shift+P
echo    3. 运行 "Java: Reload Projects"
echo    4. 运行 "Developer: Reload Window"
echo.
pause
