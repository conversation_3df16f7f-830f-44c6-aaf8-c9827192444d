import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import * as path from 'path';
import * as fs from 'fs';
import { format } from 'winston';

@Injectable()
export class LoggerService implements NestLoggerService {
  private logger: winston.Logger;
  private readonly logDir: string;
  private readonly context?: string;
  constructor(configServiceOrContext?: ConfigService | string) {
    let logLevel: string;
    
    if (typeof configServiceOrContext === 'string') {
      this.context = configServiceOrContext;
      // 使用默认配置
      this.logDir = 'logs';
      logLevel = 'info';
    } else {
      const configService = configServiceOrContext;
      this.logDir = configService?.get<string>('LOG_DIR') || 'logs';
      logLevel = configService?.get<string>('LOG_LEVEL') || 'info';
    }

    // 确保日志目录存在
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }

    this.logger = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        // 控制台输出
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          )
        }),
        // 按日期轮转的文件输出
        new winston.transports.DailyRotateFile({
          filename: path.join(this.logDir, 'application-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '14d',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        }),        // 错误日志单独存储
        new winston.transports.DailyRotateFile({
          filename: path.join(this.logDir, 'error-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '14d',
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        })
      ]
    });
  }

  private formatMessage(message: any, meta?: any): string {
    if (typeof message === 'object') {
      return JSON.stringify(message);
    }
    return message;
  }
  log(message: any, context?: string) {
    this.logger.info(this.formatMessage(message), { context });
  }

  error(message: any, traceOrMeta?: string | any, context?: string) {
    if (typeof traceOrMeta === 'object') {
      // 如果第二个参数是对象，那么它是meta数据
      this.logger.error(this.formatMessage(message), traceOrMeta);
    } else {
      // 如果第二个参数是字符串，那么它是trace
      this.logger.error(this.formatMessage(message), { trace: traceOrMeta, context });
    }
  }

  warn(message: any, metaOrContext?: any | string) {
    if (typeof metaOrContext === 'object') {
      this.logger.warn(this.formatMessage(message), metaOrContext);
    } else {
      this.logger.warn(this.formatMessage(message), { context: metaOrContext });
    }
  }

  debug(message: any, metaOrContext?: any | string) {
    if (typeof metaOrContext === 'object') {
      this.logger.debug(this.formatMessage(message), metaOrContext);
    } else {
      this.logger.debug(this.formatMessage(message), { context: metaOrContext });
    }
  }

  verbose(message: any, context?: string) {
    this.logger.verbose(this.formatMessage(message), { context });
  }

  // 自定义日志方法
  info(message: any, meta?: any) {
    this.logger.info(this.formatMessage(message), meta);
  }

  http(message: string, meta?: any) {
    this.logger.http(message, meta);
  }

  // 记录性能指标
  performance(operation: string, duration: number, meta?: any) {
    this.logger.info(`Performance: ${operation}`, {
      duration,
      ...meta
    });
  }

  // 记录安全相关日志
  security(message: string, meta?: any) {
    this.logger.warn(`Security: ${message}`, meta);
  }

  // 记录业务操作日志
  business(operation: string, userId: string, meta?: any) {
    this.logger.info(`Business: ${operation}`, {
      userId,
      ...meta
    });
  }

  // 记录系统状态日志
  system(message: string, meta?: any) {
    this.logger.info(`System: ${message}`, meta);
  }

  // 分析日志
  async analyzeLogs(options: {
    startDate?: Date;
    endDate?: Date;
    level?: string;
    searchText?: string;
  }) {
    try {
      const { startDate, endDate, level, searchText } = options;
      const logFiles = await this.getLogFiles();
      const logs = [];

      for (const file of logFiles) {
        const content = await this.readFileAsync(file, 'utf8');
        const fileLogs = content.split('\n')
          .filter(line => line.trim())
          .map(line => JSON.parse(line))
          .filter(log => {
            const logDate = new Date(log.timestamp);
            const matchesDate = (!startDate || logDate >= startDate) &&
                              (!endDate || logDate <= endDate);
            const matchesLevel = !level || log.level === level;
            const matchesText = !searchText || 
                              JSON.stringify(log).toLowerCase().includes(searchText.toLowerCase());
            return matchesDate && matchesLevel && matchesText;
          });

        logs.push(...fileLogs);
      }

      return this.generateLogAnalysis(logs);
    } catch (error) {
      this.error('分析日志失败', error);
      throw error;
    }
  }

  // 获取日志文件列表
  private async getLogFiles() {
    const files = await fs.promises.readdir(this.logDir);
    return files
      .filter(file => file.endsWith('.log'))
      .map(file => path.join(this.logDir, file));
  }

  // 生成日志分析报告
  private generateLogAnalysis(logs: any[]) {
    const analysis = {
      totalLogs: logs.length,
      levelDistribution: {},
      errorTypes: {},
      timeDistribution: {},
      topErrors: [],
      performanceMetrics: {
        averageResponseTime: 0,
        slowestEndpoints: []
      }
    };

    // 统计日志级别分布
    logs.forEach(log => {
      analysis.levelDistribution[log.level] = (analysis.levelDistribution[log.level] || 0) + 1;
    });

    // 统计错误类型
    logs.filter(log => log.level === 'error').forEach(log => {
      const errorType = log.error?.name || 'Unknown';
      analysis.errorTypes[errorType] = (analysis.errorTypes[errorType] || 0) + 1;
    });

    // 统计时间分布
    logs.forEach(log => {
      const hour = new Date(log.timestamp).getHours();
      analysis.timeDistribution[hour] = (analysis.timeDistribution[hour] || 0) + 1;
    });

    // 获取最常见的错误
    analysis.topErrors = Object.entries(analysis.errorTypes)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, 10)
      .map(([type, count]) => ({ type, count }));

    // 计算性能指标
    const responseTimes = logs
      .filter(log => log.responseTime)
      .map(log => log.responseTime);
    
    if (responseTimes.length > 0) {
      analysis.performanceMetrics.averageResponseTime = 
        responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    }

    // 获取最慢的端点
    analysis.performanceMetrics.slowestEndpoints = logs
      .filter(log => log.endpoint && log.responseTime)
      .reduce((acc, log) => {
        const existing = acc.find(item => item.endpoint === log.endpoint);
        if (existing) {
          existing.totalTime += log.responseTime;
          existing.count++;
        } else {
          acc.push({
            endpoint: log.endpoint,
            totalTime: log.responseTime,
            count: 1
          });
        }
        return acc;
      }, [])
      .map(item => ({
        endpoint: item.endpoint,
        averageTime: item.totalTime / item.count
      }))
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 10);

    return analysis;
  }

  // 清理旧日志
  async cleanOldLogs(daysToKeep: number = 14) {
    try {
      const files = await this.getLogFiles();
      const now = new Date();
      
      for (const file of files) {
        const stats = await fs.promises.stat(file);
        const fileAge = (now.getTime() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
        
        if (fileAge > daysToKeep) {
          await fs.promises.unlink(file);
          this.log(`已删除旧日志文件: ${file}`);
        }
      }
    } catch (error) {
      this.error('清理旧日志失败', error);
      throw error;
    }
  }

  // 导出日志分析报告
  async exportLogAnalysis(options: any) {
    try {
      const analysis = await this.analyzeLogs(options);
      const report = this.formatAnalysisReport(analysis);
      
      const filename = `log-analysis-${new Date().toISOString()}.txt`;
      await this.writeFileAsync(
        path.join(this.logDir, filename),
        report
      );

      return {
        filename,
        path: path.join(this.logDir, filename)
      };
    } catch (error) {
      this.error('导出日志分析报告失败', error);
      throw error;
    }
  }

  // 格式化分析报告
  private formatAnalysisReport(analysis: any) {
    let report = '日志分析报告\n';
    report += '='.repeat(50) + '\n\n';

    report += `总日志数: ${analysis.totalLogs}\n\n`;

    report += '日志级别分布:\n';
    Object.entries(analysis.levelDistribution).forEach(([level, count]) => {
      report += `  ${level}: ${count}\n`;
    });
    report += '\n';

    report += '错误类型统计:\n';
    Object.entries(analysis.errorTypes).forEach(([type, count]) => {
      report += `  ${type}: ${count}\n`;
    });
    report += '\n';

    report += '时间分布:\n';
    Object.entries(analysis.timeDistribution).forEach(([hour, count]) => {
      report += `  ${hour}:00 - ${count} 条日志\n`;
    });
    report += '\n';

    report += '最常见的错误:\n';
    analysis.topErrors.forEach((error, index) => {
      report += `  ${index + 1}. ${error.type}: ${error.count} 次\n`;
    });
    report += '\n';

    report += '性能指标:\n';
    report += `  平均响应时间: ${analysis.performanceMetrics.averageResponseTime.toFixed(2)}ms\n\n`;
    
    report += '最慢的端点:\n';
    analysis.performanceMetrics.slowestEndpoints.forEach((endpoint, index) => {
      report += `  ${index + 1}. ${endpoint.endpoint}: ${endpoint.averageTime.toFixed(2)}ms\n`;
    });

    return report;
  }

private readFileAsync(file: string, encoding: string): Promise<string> {
    return fs.promises.readFile(file, { encoding: encoding as BufferEncoding });
  }

  private writeFileAsync(file: string, data: string): Promise<void> {
    return fs.promises.writeFile(file, data);
  }
}