import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('系统健康检查')
@Controller('api/health')
export class HealthController {
  @Get()
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({ status: 200, description: '系统运行正常' })
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'SmartLoan Backend API',
      version: '1.0.0',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV || 'development'
    };
  }

  @Get('detailed')
  @ApiOperation({ summary: '详细健康检查' })
  @ApiResponse({ status: 200, description: '详细系统状态' })
  getDetailedHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'SmartLoan Backend API',
      version: '1.0.0',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV || 'development',
      database: {
        status: 'connected',
        type: 'postgresql'
      },
      redis: {
        status: 'connected'
      },
      ai_service: {
        status: 'available',
        endpoint: process.env.AI_SERVICE_URL || 'http://localhost:3002'
      },
      features: {
        ocr_recognition: true,
        liveness_detection: true,
        risk_assessment: true,
        product_matching: true,
        ai_advisor: true
      }
    };
  }
}
