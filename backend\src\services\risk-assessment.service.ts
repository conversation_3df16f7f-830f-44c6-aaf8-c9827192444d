import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { LoanApplication } from '../entities/loan-application.entity';
import { LoggerService } from './logger.service';
import { ErrorHandler } from '../utils/error-handler';
import { GpuService } from './gpu.service';

@Injectable()
export class RiskAssessmentService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(LoanApplication)
    private readonly loanApplicationRepository: Repository<LoanApplication>,
    private readonly logger: LoggerService,
    private readonly errorHandler: ErrorHandler,
    private readonly gpuService: GpuService
  ) {}

  async assessRisk(applicationId: number) {
    try {
      const application = await this.loanApplicationRepository.findOne({
        where: { id: applicationId },
        relations: ['user']
      });

      if (!application) {
        throw new Error('申请不存在');
      }

      // 收集风险评估所需数据
      const riskData = await this.collectRiskData(application);

      // 使用GPU加速进行风险评估
      const assessmentResult = await this.gpuService.accelerateRiskAssessment(riskData);

      // 更新申请的风险评估结果
      await this.loanApplicationRepository.update(
        { id: applicationId.toString() },
        {
          riskAssessment: JSON.stringify({
            score: assessmentResult.score,
            factors: assessmentResult.factors,
            decision: assessmentResult.decision,
            timestamp: new Date()
          })
        }
      );

      // 如果风险等级高，触发预警
      if (assessmentResult.score > 0.7) {
        await this.triggerRiskAlert(application, assessmentResult);
      }

      return assessmentResult;
    } catch (error) {
      this.logger.error('风险评估失败', { error, applicationId });
      throw this.errorHandler.handle(error);
    }
  }

  private async collectRiskData(application: LoanApplication) {
    // 收集用户历史数据
    const userHistory = await this.loanApplicationRepository.find({
      where: { userId: application.userId },
      order: { createdAt: 'DESC' }
    });

    // 收集用户基本信息
    const user = await this.userRepository.findOne({
      where: { id: application.userId }
    });

    return {
      application,
      userHistory,
      user,
      timestamp: new Date()
    };
  }

  private async triggerRiskAlert(application: LoanApplication, assessmentResult: any) {
    // 实现风险预警逻辑
    this.logger.warn('触发风险预警', {
      applicationId: application.id,
      userId: application.userId,
      riskScore: assessmentResult.score,
      factors: assessmentResult.factors
    });

    // TODO: 实现预警通知机制
  }

  async getRiskMetrics() {
    try {
      const applications = await this.loanApplicationRepository.find({
        where: {
          riskScore: { $ne: null }
        }
      });

      return {
        totalApplications: applications.length,
        averageRiskScore: this.calculateAverageRiskScore(applications),
        riskDistribution: this.calculateRiskDistribution(applications),
        recentAlerts: await this.getRecentAlerts()
      };
    } catch (error) {
      this.logger.error('获取风险指标失败', { error });
      throw this.errorHandler.handle(error);
    }
  }

  private calculateAverageRiskScore(applications: LoanApplication[]) {
    const scores = applications.map(app => app.riskScore);
    return scores.reduce((a, b) => a + b, 0) / scores.length;
  }

  private calculateRiskDistribution(applications: LoanApplication[]) {
    const distribution = {
      low: 0,
      medium: 0,
      high: 0
    };

    applications.forEach(app => {
      if (app.riskScore < 0.3) distribution.low++;
      else if (app.riskScore < 0.7) distribution.medium++;
      else distribution.high++;
    });

    return distribution;
  }

  private async getRecentAlerts() {
    // 实现获取最近预警的逻辑
    return [];
  }
}