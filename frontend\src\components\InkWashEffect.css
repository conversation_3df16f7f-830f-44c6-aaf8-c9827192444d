.ink-wash-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.ink-wash-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.ink-wash-canvas.loaded {
  opacity: 1;
}

.ink-wash-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.ink-wash-content {
  text-align: center;
  color: #2c3e50;
  z-index: 20;
}

.ink-wash-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  animation: titleFadeIn 2s ease-out;
}

.ink-wash-subtitle {
  font-size: 1.5rem;
  font-weight: 300;
  opacity: 0.8;
  animation: subtitleFadeIn 2s ease-out 0.5s both;
}

@keyframes titleFadeIn {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes subtitleFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 0.8;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ink-wash-title {
    font-size: 2.5rem;
  }
  
  .ink-wash-subtitle {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .ink-wash-title {
    font-size: 2rem;
  }
  
  .ink-wash-subtitle {
    font-size: 1rem;
  }
}
