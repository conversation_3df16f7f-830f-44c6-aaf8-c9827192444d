/**
 * ProductCard 产品对比卡片样式
 * 支持3D效果和金融级设计
 */

@import '../../styles/design-tokens.scss';

.product-card {
  position: relative;
  background: var(--color-neutral-100);
  border-radius: var(--border-radius-card);
  padding: var(--component-padding-md);
  box-shadow: var(--shadow-card);
  transition: var(--transition-all);
  overflow: hidden;
  cursor: pointer;
  transform-style: preserve-3d;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--duration-normal) var(--ease-out);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);

    &::before {
      transform: scaleX(1);
    }
  }

  &--hovered {
    .product-card__hover-bg {
      opacity: 1;
      transform: scale(1.1);
    }
  }

  &--3d {
    transform-style: preserve-3d;
    transition: transform var(--duration-slow) var(--ease-out);

    .product-card__3d-indicator {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &--premium {
    background: linear-gradient(135deg, 
      rgba(212, 175, 55, 0.05) 0%, 
      rgba(26, 58, 143, 0.05) 100%);
    border: 1px solid var(--color-secondary-alpha-20);

    &::before {
      background: var(--gradient-secondary);
    }
  }

  &--compact {
    padding: var(--component-padding-sm);

    .product-card__title h3 {
      font-size: var(--text-md);
    }

    .product-card__features {
      gap: var(--spacing-sm);
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
  }

  &__institution {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__logo {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    background: var(--gradient-primary);
    @include flex-center;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__logo-placeholder {
    color: var(--color-neutral-100);
    font-weight: var(--font-weight-bold);
    font-size: var(--text-lg);
  }

  &__institution-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  &__institution-name {
    font-size: var(--text-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--color-neutral-900);
    margin: 0;
  }

  &__product-type {
    font-size: var(--text-xs);
    color: var(--color-neutral-600);
  }

  &__risk-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
  }

  &__title {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);

    h3 {
      font-size: var(--text-lg);
      font-weight: var(--font-weight-bold);
      color: var(--color-neutral-900);
      margin: 0;
      line-height: var(--line-height-tight);
      flex: 1;
      margin-right: var(--spacing-sm);
    }
  }

  &__match-score {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--color-secondary-alpha-10);
    color: var(--color-secondary-dark);
    border-radius: var(--border-radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-semibold);
    white-space: nowrap;
  }

  &__match-icon {
    width: 12px;
    height: 12px;
    fill: currentColor;
  }

  &__features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
  }

  &__feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__feature-icon {
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius-sm);
    background: var(--color-primary-alpha-10);
    color: var(--color-primary);
    @include flex-center;
    flex-shrink: 0;
  }

  &__feature-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    flex: 1;
  }

  &__feature-label {
    font-size: var(--text-xs);
    color: var(--color-neutral-600);
    font-weight: var(--font-weight-medium);
  }

  &__feature-value {
    font-size: var(--text-sm);
    color: var(--color-neutral-900);
    font-weight: var(--font-weight-semibold);
  }

  &__badges {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
  }

  &__badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--color-neutral-200);
    border-radius: var(--border-radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
  }

  &__badge-icon {
    font-size: var(--text-sm);
  }

  &__ai-reasoning {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--color-primary-alpha-10);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-lg);
    border-left: 3px solid var(--color-primary);

    p {
      font-size: var(--text-sm);
      color: var(--color-neutral-700);
      margin: 0;
      line-height: var(--line-height-normal);
    }
  }

  &__ai-icon {
    font-size: var(--text-md);
    flex-shrink: 0;
  }

  &__actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
  }

  &__compare-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--color-neutral-200);
    border: 1px solid var(--color-neutral-300);
    border-radius: var(--border-radius-button);
    color: var(--color-neutral-700);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-all);

    &:hover {
      background: var(--color-neutral-300);
      transform: translateY(-1px);
    }
  }

  &__apply-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-button);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: var(--transition-all);
    flex: 1;
    min-height: 44px;
    position: relative;
    overflow: hidden;

    &--primary {
      background: var(--gradient-primary);
      color: var(--color-neutral-100);

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-button);
      }
    }

    &--gradient {
      background: var(--gradient-secondary);
      color: var(--color-neutral-900);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
      }
    }

    &--applying {
      background: var(--color-neutral-400);
      cursor: not-allowed;
      transform: none !important;

      .product-card__loading-spinner {
        animation: spin 1s linear infinite;
      }
    }

    &--success {
      background: var(--gradient-success);
      color: var(--color-neutral-100);

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, 
          transparent, 
          rgba(255, 255, 255, 0.3), 
          transparent);
        animation: shimmer 1s ease-out;
      }
    }

    span {
      position: relative;
      z-index: 1;
    }
  }

  &__loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
  }

  &__3d-indicator {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(26, 58, 143, 0.9);
    color: var(--color-neutral-100);
    border-radius: var(--border-radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
    opacity: 0;
    transform: translateY(-10px);
    transition: var(--transition-all);
    z-index: 10;
  }

  &__hover-bg {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, 
      rgba(26, 58, 143, 0.05) 0%, 
      transparent 70%);
    transform: translate(-50%, -50%) scale(0);
    transition: var(--transition-all);
    opacity: 0;
    z-index: -1;
  }

  // 响应式设计
  @include mobile-only {
    padding: var(--component-padding-sm);

    &__title h3 {
      font-size: var(--text-md);
    }

    &__features {
      gap: var(--spacing-sm);
    }

    &__actions {
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    &__compare-btn,
    &__apply-btn {
      width: 100%;
    }
  }

  // 深色模式
  @media (prefers-color-scheme: dark) {
    background: var(--color-neutral-800);
    color: var(--color-neutral-100);

    &__institution-name,
    &__feature-value {
      color: var(--color-neutral-100);
    }

    &__product-type,
    &__feature-label {
      color: var(--color-neutral-400);
    }

    &__badge {
      background: var(--color-neutral-700);
      color: var(--color-neutral-200);
    }

    &__ai-reasoning {
      background: rgba(26, 58, 143, 0.2);
      
      p {
        color: var(--color-neutral-300);
      }
    }
  }

  // 高对比度模式
  @media (prefers-contrast: high) {
    border: 2px solid var(--color-neutral-900);

    &__apply-btn {
      border: 2px solid currentColor;
    }
  }

  // 减少动画
  @media (prefers-reduced-motion: reduce) {
    &,
    &__apply-btn,
    &__compare-btn,
    &__hover-bg,
    &__3d-indicator {
      transition: none !important;
      animation: none !important;
    }

    &:hover {
      transform: none !important;
    }

    &__loading-spinner {
      animation: none !important;
    }
  }
}

// 卡片网格布局
.product-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);

  @include mobile-only {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  @include desktop-up {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  }
}

// 3D对比模式特殊样式
.product-comparison-3d {
  perspective: 1200px;
  perspective-origin: center center;

  .product-card {
    transform-style: preserve-3d;
    transition: transform var(--duration-slow) var(--ease-out);

    &:nth-child(odd) {
      transform: rotateY(-5deg);
    }

    &:nth-child(even) {
      transform: rotateY(5deg);
    }

    &:hover {
      transform: rotateY(0deg) scale(1.05);
      z-index: 10;
    }
  }
}
