import { Product } from '../entities/product.entity';

/**
 * 特征权重配置
 */
export interface FeatureWeights {
  instant_approval: number;
  no_collateral: number;
  flexible_repayment: number;
  no_early_fee: number;
  purpose_match: number;
}

/**
 * 匹配分数明细
 */
export interface MatchScoreDetails {
  amountScore: number;
  termScore: number;
  rateScore: number;
  featureScore: number;
  locationScore: number;
}

/**
 * 匹配的产品结果
 */
export interface MatchedProduct {
  product: Product;
  score: number;
  details: MatchScoreDetails;
}

/**
 * 产品匹配查询条件
 */
export interface ProductMatchCriteria {
  amount: number;
  term: number;
  purpose?: string;
  location?: string;
  features?: string[];
}

/**
 * 地理位置匹配规则
 */
export interface LocationMatchRule {
  type: 'exact' | 'city' | 'province';
  locations: string[];
  score: number;
}

/**
 * 用户位置信息
 */
export interface UserLocation {
  province: string;
  city: string;
  district: string;
}

/**
 * Redis缓存管理器接口
 */
export interface CacheManager {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  del(key: string): Promise<void>;
}

/**
 * GPU计算服务接口
 */
export interface IGpuService {
  batchCalculate(
    productTensors: number[][],
    criteriaTensor: number[],
    weights: Record<string, number>
  ): Promise<number[]>;

  prepareProductTensor(product: any): number[];
  prepareCriteriaTensor(criteria: any): number[];
}
