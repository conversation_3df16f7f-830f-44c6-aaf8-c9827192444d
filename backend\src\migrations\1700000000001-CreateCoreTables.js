"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCoreTables1700000000001 = void 0;
var CreateCoreTables1700000000001 = /** @class */ (function () {
    function CreateCoreTables1700000000001() {
        this.name = 'CreateCoreTables1700000000001';
    }
    CreateCoreTables1700000000001.prototype.up = function (queryRunner) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: 
                    // 创建金融机构表
                    return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"institutions\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"name\" VARCHAR NOT NULL,\n        \"type\" VARCHAR NOT NULL,\n        \"license_number\" VARCHAR NOT NULL,\n        \"contact_info\" JSONB,\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now(),\n        \"updated_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 1:
                        // 创建金融机构表
                        _a.sent();
                        // 创建产品表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"products\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"institution_id\" INTEGER NOT NULL REFERENCES \"institutions\"(\"id\"),\n        \"name\" VARCHAR NOT NULL,\n        \"type\" VARCHAR NOT NULL,\n        \"min_amount\" DECIMAL(10,2) NOT NULL,\n        \"max_amount\" DECIMAL(10,2) NOT NULL,\n        \"interest_rate\" DECIMAL(5,2) NOT NULL,\n        \"term_range\" INTEGER[] NOT NULL,\n        \"requirements\" JSONB,\n        \"features\" JSONB,\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now(),\n        \"updated_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 2:
                        // 创建产品表
                        _a.sent();
                        // 创建风控规则表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"risk_rules\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"name\" VARCHAR NOT NULL,\n        \"type\" VARCHAR NOT NULL,\n        \"conditions\" JSONB NOT NULL,\n        \"actions\" JSONB NOT NULL,\n        \"priority\" INTEGER NOT NULL,\n        \"is_active\" BOOLEAN NOT NULL DEFAULT true,\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now(),\n        \"updated_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 3:
                        // 创建风控规则表
                        _a.sent();
                        // 创建审核记录表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"audits\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"user_id\" INTEGER NOT NULL REFERENCES \"users\"(\"id\"),\n        \"loan_application_id\" INTEGER NOT NULL REFERENCES \"loan_applications\"(\"id\"),\n        \"type\" VARCHAR NOT NULL,\n        \"status\" VARCHAR NOT NULL,\n        \"result\" JSONB,\n        \"documents\" JSONB,\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now(),\n        \"updated_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 4:
                        // 创建审核记录表
                        _a.sent();
                        // 创建用户行为日志表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"user_logs\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"user_id\" INTEGER NOT NULL REFERENCES \"users\"(\"id\"),\n        \"action\" VARCHAR NOT NULL,\n        \"details\" JSONB,\n        \"ip_address\" VARCHAR,\n        \"user_agent\" VARCHAR,\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 5:
                        // 创建用户行为日志表
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    CreateCoreTables1700000000001.prototype.down = function (queryRunner) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, queryRunner.query("DROP TABLE \"user_logs\"")];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"audits\"")];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"risk_rules\"")];
                    case 3:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"products\"")];
                    case 4:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"institutions\"")];
                    case 5:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    return CreateCoreTables1700000000001;
}());
exports.CreateCoreTables1700000000001 = CreateCoreTables1700000000001;
