import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn, BeforeInsert, BeforeUpdate, OneToMany } from 'typeorm';
import { Exclude } from 'class-transformer';
import { Role } from '../enums/role.enum';
import { EmploymentStatus } from '../enums/employment-status.enum';
import * as bcrypt from 'bcrypt';
import { LoanApplication } from './loan-application.entity';
import { LoanReview } from './loan-review.entity';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  username: string;

  @Column({ unique: true })
  email: string;

  @Column()
  @Exclude()
  password: string;

  @Column()
  name: string;
  @Column({ nullable: true })
  phone: string;

  @Column({ nullable: true })
  salt: string;

  @Column({ nullable: true })
  password_hash: string;

  @Column({ nullable: true })
  real_name: string;

  @Column({ type: 'int', nullable: true })
  credit_score: number;

  @Column({ nullable: true })
  risk_level: string;

  @Column({
    type: 'enum',
    enum: Role,
    default: Role.USER
  })
  role: Role;

  @Column({
    type: 'enum',
    enum: EmploymentStatus,
    default: EmploymentStatus.UNKNOWN,
    nullable: true
  })
  employmentStatus: EmploymentStatus;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true
  })
  monthlyIncome: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true
  })
  annualIncome: number;

  @Column({ nullable: true })
  last_login_at: Date;

  @Column({ type: 'jsonb', nullable: true })
  metadata: any;

  @Column({ type: 'jsonb', nullable: true })
  permissions: any;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  lastLoginAt: Date;

  @OneToMany(() => LoanApplication, application => application.user)
  loanApplications: LoanApplication[];

  @OneToMany(() => LoanReview, review => review.reviewer)
  loanReviews: LoanReview[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ default: false, name: 'is_verified' })
  isVerified: boolean;

  @Column({ nullable: true, name: 'verification_date' })
  verificationDate: Date;

  @Column({ default: false, name: 'documents_verified' })
  documentsVerified: boolean;

  @Column({ nullable: true, name: 'documents_verification_date' })
  documentsVerificationDate: Date;

  @Column({ default: false, name: 'liveness_verified' })
  livenessVerified: boolean;

  @Column({ nullable: true, name: 'liveness_verification_date' })
  livenessVerificationDate: Date;

  @Column({ type: 'jsonb', nullable: true, name: 'verification_documents' })
  verificationDocuments: any;

  @Column({ type: 'jsonb', nullable: true, name: 'face_data' })
  faceData: any;

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password) {
      const salt = await bcrypt.genSalt();
      this.password = await bcrypt.hash(this.password, salt);
    }
  }

  async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password);
  }
}