import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateRiskTables1709123456789 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建风险数据表
    await queryRunner.query(`
      CREATE TABLE risk_data (
        id SERIAL PRIMARY KEY,
        time_range VARCHAR(50) NOT NULL,
        data JSONB NOT NULL,
        timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 创建预警表
    await queryRunner.query(`
      CREATE TABLE alert (
        id SERIAL PRIMARY KEY,
        level VARCHAR(10) NOT NULL CHECK (level IN ('high', 'medium', 'low')),
        content TEXT NOT NULL,
        type VARCHAR(50) NOT NULL,
        timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 创建索引
    await queryRunner.query(`
      CREATE INDEX idx_risk_data_time_range ON risk_data(time_range);
      CREATE INDEX idx_risk_data_timestamp ON risk_data(timestamp);
      CREATE INDEX idx_alert_level ON alert(level);
      CREATE INDEX idx_alert_timestamp ON alert(timestamp);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS risk_data;`);
    await queryRunner.query(`DROP TABLE IF EXISTS alert;`);
  }
} 