import { ExceptionFilter, ArgumentsHost } from '@nestjs/common';
import { LoggerService } from '../services/logger.service';
import { ErrorHandlerService } from '../services/error-handler.service';
export declare class AllExceptionsFilter implements ExceptionFilter {
    private readonly logger;
    private readonly errorHandler;
    constructor(logger: LoggerService, errorHandler: ErrorHandlerService);
    catch(exception: any, host: ArgumentsHost): void;
}
