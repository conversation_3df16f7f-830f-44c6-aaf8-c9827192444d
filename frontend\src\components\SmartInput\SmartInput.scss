/**
 * SmartInput 智能输入组件样式
 * 基于设计Token系统的一致性设计
 */

@import '../../styles/design-tokens.scss';

.smart-input {
  position: relative;
  margin-bottom: var(--spacing-md);

  &__label {
    display: block;
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-neutral-900);
    margin-bottom: var(--spacing-sm);
    font-family: var(--font-family-primary);
  }

  &__required {
    color: var(--color-error);
    margin-left: var(--spacing-xs);
  }

  &__wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--color-neutral-100);
    border: var(--input-border-width) solid var(--input-border-color);
    border-radius: var(--border-radius-button);
    transition: var(--transition-all);
    overflow: hidden;

    &:hover {
      border-color: var(--color-primary-light);
      box-shadow: var(--shadow-sm);
    }

    &:focus-within {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px var(--color-primary-alpha-10);
    }

    &--success {
      border-color: var(--color-success);
      
      &:focus-within {
        box-shadow: 0 0 0 3px var(--color-success-alpha-10);
      }
    }

    &--error {
      border-color: var(--color-error);
      
      &:focus-within {
        box-shadow: 0 0 0 3px var(--color-error-alpha-10);
      }
    }
  }

  &__field {
    flex: 1;
    height: var(--input-height);
    padding: 0 var(--input-padding-x);
    border: none;
    outline: none;
    background: transparent;
    font-size: var(--text-md);
    font-family: var(--font-family-primary);
    color: var(--color-neutral-900);
    
    &::placeholder {
      color: var(--color-neutral-500);
    }

    &:disabled {
      background: var(--color-neutral-200);
      color: var(--color-neutral-500);
      cursor: not-allowed;
    }
  }

  &__ai-indicator {
    position: absolute;
    top: 50%;
    right: 80px;
    transform: translateY(-50%);
    z-index: 1;
  }

  &__ai-icon {
    font-size: var(--text-md);
    animation: pulse 2s infinite;
    filter: drop-shadow(0 0 4px rgba(212, 175, 55, 0.3));
  }

  &__scan-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: var(--spacing-sm);
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--border-radius-sm);
    color: var(--color-neutral-100);
    cursor: pointer;
    transition: var(--transition-all);

    &:hover {
      transform: scale(1.05);
      box-shadow: var(--shadow-button);
    }

    &:active {
      transform: scale(0.95);
    }

    &:disabled {
      background: var(--color-neutral-400);
      cursor: not-allowed;
      transform: none;
    }
  }

  &__scan-icon {
    width: 18px;
    height: 18px;

    &--loading {
      animation: spin 1s linear infinite;
    }
  }

  &__status-icon {
    position: absolute;
    top: 50%;
    right: var(--spacing-md);
    transform: translateY(-50%);
    z-index: 1;
  }

  &__icon {
    width: 18px;
    height: 18px;

    &--success {
      color: var(--color-success);
    }

    &--error {
      color: var(--color-error);
    }
  }

  &__suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--color-neutral-100);
    border: 1px solid var(--color-neutral-300);
    border-top: none;
    border-radius: 0 0 var(--border-radius-button) var(--border-radius-button);
    box-shadow: var(--shadow-lg);
    z-index: var(--z-index-dropdown);
    max-height: 200px;
    overflow-y: auto;
  }

  &__suggestion {
    display: flex;
    align-items: center;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: transparent;
    text-align: left;
    font-size: var(--text-sm);
    font-family: var(--font-family-primary);
    color: var(--color-neutral-900);
    cursor: pointer;
    transition: var(--transition-all);

    &:hover {
      background: var(--color-primary-alpha-10);
      color: var(--color-primary);
    }

    &:not(:last-child) {
      border-bottom: 1px solid var(--color-neutral-300);
    }
  }

  &__suggestion-icon {
    width: 14px;
    height: 14px;
    margin-right: var(--spacing-sm);
    color: var(--color-primary);
  }

  &__error {
    display: flex;
    align-items: center;
    margin-top: var(--spacing-sm);
    font-size: var(--text-xs);
    color: var(--color-error);
    animation: fadeInUp var(--duration-normal) var(--ease-out);
  }

  &__error-icon {
    width: 14px;
    height: 14px;
    margin-right: var(--spacing-xs);
    flex-shrink: 0;
  }

  &__hint {
    display: flex;
    align-items: center;
    margin-top: var(--spacing-sm);
    font-size: var(--text-xs);
    color: var(--color-neutral-600);
    animation: fadeInUp var(--duration-normal) var(--ease-out);
  }

  &__hint-icon {
    margin-right: var(--spacing-xs);
    font-size: var(--text-sm);
  }

  // 响应式设计
  @include mobile-only {
    &__field {
      font-size: var(--text-md);
      height: 44px;
    }

    &__scan-button {
      width: 36px;
      height: 36px;
    }

    &__scan-icon {
      width: 16px;
      height: 16px;
    }
  }

  // 深色模式支持
  @media (prefers-color-scheme: dark) {
    &__wrapper {
      background: var(--color-neutral-800);
      border-color: var(--color-neutral-600);
    }

    &__field {
      color: var(--color-neutral-100);

      &::placeholder {
        color: var(--color-neutral-400);
      }
    }

    &__suggestions {
      background: var(--color-neutral-800);
      border-color: var(--color-neutral-600);
    }

    &__suggestion {
      color: var(--color-neutral-100);

      &:hover {
        background: var(--color-primary-alpha-20);
      }
    }
  }

  // 高对比度模式
  @media (prefers-contrast: high) {
    &__wrapper {
      border-width: 2px;
    }

    &__field {
      font-weight: var(--font-weight-medium);
    }

    &__icon {
      &--success {
        color: #008000;
      }

      &--error {
        color: #ff0000;
      }
    }
  }

  // 减少动画模式
  @media (prefers-reduced-motion: reduce) {
    &__ai-icon {
      animation: none;
    }

    &__scan-icon--loading {
      animation: none;
    }

    &__error,
    &__hint {
      animation: none;
    }

    * {
      transition: none !important;
    }
  }
}

// 动画关键帧
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 特殊输入类型样式
.smart-input {
  &--id-card {
    .smart-input__field {
      letter-spacing: 1px;
      font-family: var(--font-family-mono);
    }
  }

  &--bank-card {
    .smart-input__field {
      letter-spacing: 2px;
      font-family: var(--font-family-mono);
    }
  }

  &--phone {
    .smart-input__field {
      letter-spacing: 1px;
    }
  }

  // 金融特色样式
  &--premium {
    .smart-input__wrapper {
      background: linear-gradient(135deg, 
        rgba(212, 175, 55, 0.05) 0%, 
        rgba(26, 58, 143, 0.05) 100%);
      border: 2px solid transparent;
      background-clip: padding-box;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--gradient-secondary);
        border-radius: inherit;
        z-index: -1;
        margin: -2px;
      }
    }

    .smart-input__label {
      background: var(--gradient-tech);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-weight: var(--font-weight-semibold);
    }
  }
}
