import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateC<PERSON>umn, UpdateDateColumn, OneToMany } from 'typeorm';
import { LoanApplication } from '../loan/loan.entity';

@Entity('loan_products')
export class LoanProduct {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column('decimal', { precision: 5, scale: 2 })
  interest_rate: number;

  @Column('decimal', { precision: 15, scale: 2 })
  min_amount: number;

  @Column('decimal', { precision: 15, scale: 2 })
  max_amount: number;

  @Column('int')
  term_months: number;

  @Column({ default: true })
  is_active: boolean;

  @Column({ length: 100 })
  provider: string;

  @Column({ length: 50 })
  product_type: string; // 'personal', 'business', 'mortgage', 'auto'

  @Column('json', { nullable: true })
  requirements: {
    min_age?: number;
    max_age?: number;
    min_income?: number;
    credit_score_min?: number;
    employment_type?: string[];
    collateral_required?: boolean;
  };

  @Column('json', { nullable: true })
  features: {
    fast_approval?: boolean;
    online_application?: boolean;
    flexible_repayment?: boolean;
    early_repayment?: boolean;
  };

  @Column('decimal', { precision: 3, scale: 2, default: 0 })
  processing_fee_rate: number;

  @Column('int', { default: 7 })
  approval_days: number;

  @Column('text', { nullable: true })
  special_conditions: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @OneToMany(() => LoanApplication, application => application.product)
  applications: LoanApplication[];
}
