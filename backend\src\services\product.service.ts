import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, DeepPartial } from 'typeorm';
import { Product } from '../modules/product/entities/product.entity';
import { CreateProductDto, UpdateProductDto, ProductFilterDto } from '../dto/product.dto';
import { RedisService } from './redis.service';
import * as XLSX from 'xlsx';
import { Readable } from 'stream';
import { CacheService } from './cache.service';
import { LoggerService } from './logger.service';
// import { ProductRequirements } from '../modules/product/entities/product-requirements.entity';
// import { ProductRequirementDto } from '../dto/product-requirement.dto';

@Injectable()
export class ProductService {
  private readonly CACHE_TTL = 300; // 5分钟缓存

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    // @InjectRepository(ProductRequirements)
    // private readonly productRequirementsRepository: Repository<ProductRequirements>,
    private readonly redisService: RedisService,
    private readonly cacheService: CacheService,
    private readonly logger: LoggerService
  ) {}

  private getCacheKey(key: string): string {
    return `product:${key}`;
  }

  async create(createProductDto: CreateProductDto): Promise<Product> {
    try {
      const product = this.productRepository.create({
        ...createProductDto,
      } as DeepPartial<Product>);
      const savedProduct = await this.productRepository.save(product);
      
      // 清除相关缓存
      await this.clearProductCache();
      
      return savedProduct;
    } catch (error) {
      this.logger.error('创建产品失败', error);
      throw error;
    }
  }

  async update(id: number, updateProductDto: UpdateProductDto) {
    try {
      const product = await this.productRepository.findOne({
        where: { id }
      });

      if (!product) {
        throw new NotFoundException('产品不存在');
      }

      const updatedProduct = await this.productRepository.save({
        ...product,
        ...updateProductDto
      });

      // 清除相关缓存
      await this.clearProductCache(id);

      return updatedProduct;
    } catch (error) {
      this.logger.error('更新产品失败', error);
      throw error;
    }
  }

  async delete(id: number) {
    try {
      const product = await this.productRepository.findOne({
        where: { id }
      });

      if (!product) {
        throw new NotFoundException('产品不存在');
      }

      await this.productRepository.remove(product);

      // 清除相关缓存
      await this.clearProductCache(id);

      return { message: '产品删除成功' };
    } catch (error) {
      this.logger.error('删除产品失败', error);
      throw error;
    }
  }

  async findAll(filterDto: ProductFilterDto) {
    try {
      const cacheKey = `product:list:${JSON.stringify(filterDto)}`;
      const cachedResults = await this.cacheService.get(cacheKey);
        if (cachedResults) {
        return JSON.parse(cachedResults as string);
      }

      const queryBuilder = this.productRepository.createQueryBuilder('product');

      // 应用过滤条件
      if (filterDto.search) {
        queryBuilder.andWhere(
          '(product.name LIKE :search OR product.description LIKE :search OR product.code LIKE :search)',
          { search: `%${filterDto.search}%` }
        );
      }

      if (filterDto.category) {
        queryBuilder.andWhere('product.category = :category', {
          category: filterDto.category
        });
      }

      if (filterDto.minAmount) {
        queryBuilder.andWhere('product.minAmount >= :minAmount', {
          minAmount: filterDto.minAmount
        });
      }

      if (filterDto.maxAmount) {
        queryBuilder.andWhere('product.maxAmount <= :maxAmount', {
          maxAmount: filterDto.maxAmount
        });
      }

      if (filterDto.minTerm) {
        queryBuilder.andWhere('product.minTerm >= :minTerm', {
          minTerm: filterDto.minTerm
        });
      }

      if (filterDto.maxTerm) {
        queryBuilder.andWhere('product.maxTerm <= :maxTerm', {
          maxTerm: filterDto.maxTerm
        });
      }

      if (filterDto.maxInterestRate) {
        queryBuilder.andWhere('product.interestRate <= :maxInterestRate', {
          maxInterestRate: filterDto.maxInterestRate
        });
      }

      if (filterDto.isActive !== undefined) {
        queryBuilder.andWhere('product.isActive = :isActive', {
          isActive: filterDto.isActive
        });
      }

      if (filterDto.isFeatured !== undefined) {
        queryBuilder.andWhere('product.isFeatured = :isFeatured', {
          isFeatured: filterDto.isFeatured
        });
      }

      // 应用排序
      if (filterDto.sortBy) {
        queryBuilder.orderBy(`product.${filterDto.sortBy}`, filterDto.sortOrder || 'ASC');
      } else {
        queryBuilder.orderBy('product.isFeatured', 'DESC')
          .addOrderBy('product.sortOrder', 'ASC')
          .addOrderBy('product.createdAt', 'DESC');
      }

      // 应用分页
      if (filterDto.page && filterDto.limit) {
        queryBuilder.skip((filterDto.page - 1) * filterDto.limit)
          .take(filterDto.limit);
      }

      const [products, total] = await queryBuilder.getManyAndCount();

      const result = {
        items: products,
        total,
        page: filterDto.page || 1,
        limit: filterDto.limit || total,
        totalPages: filterDto.limit ? Math.ceil(total / filterDto.limit) : 1
      };

      await this.cacheService.set(cacheKey, JSON.stringify(result), this.CACHE_TTL);
      
      return result;
    } catch (error) {
      this.logger.error('获取产品列表失败', error);
      throw error;
    }
  }

  async findById(id: number) {
    try {
      const cacheKey = `product:${id}`;
      const cachedProduct = await this.cacheService.get(cacheKey);
        if (cachedProduct) {
        return JSON.parse(cachedProduct as string);
      }

      const product = await this.productRepository.findOne({
        where: { id }
      });

      if (!product) {
        throw new NotFoundException('产品不存在');
      }

      await this.cacheService.set(cacheKey, JSON.stringify(product), this.CACHE_TTL);
      
      return product;
    } catch (error) {
      this.logger.error('获取产品详情失败', error);
      throw error;
    }
  }

  async findByCode(code: string) {
    try {
      const cacheKey = `product:code:${code}`;
      const cachedProduct = await this.cacheService.get(cacheKey);
        if (cachedProduct) {
        return JSON.parse(cachedProduct as string);
      }

      const product = await this.productRepository.findOne({
        where: { code }
      });

      if (!product) {
        throw new NotFoundException('产品不存在');
      }

      await this.cacheService.set(cacheKey, JSON.stringify(product), this.CACHE_TTL);
      
      return product;
    } catch (error) {
      this.logger.error('获取产品详情失败', error);
      throw error;
    }
  }

  private async clearProductCache(id?: number) {
    try {
      if (id) {
        await this.cacheService.del(`product:${id}`);
      }
      await this.cacheService.del('product:list:*');
      await this.cacheService.del('product:popular');
      await this.cacheService.del('product:recommend:*');
      await this.cacheService.del('product:similar:*');
    } catch (error) {
      this.logger.error('清除产品缓存失败', error);
    }
  }

  async getProductStatistics(): Promise<any> {
    const cacheKey = this.getCacheKey('statistics');
    
    // 尝试从缓存获取
    const cachedStats = await this.redisService.get(cacheKey);    if (cachedStats) {
      return JSON.parse(cachedStats as string);
    }

    const totalProducts = await this.productRepository.count();
    const activeProducts = await this.productRepository.count({ where: { isActive: true } });
    const featuredProducts = await this.productRepository.count({ where: { isFeatured: true } });

    const categoryStats = await this.productRepository
      .createQueryBuilder('product')
      .select('product.metadata->>\'category\'', 'category')
      .addSelect('COUNT(*)', 'count')
      .groupBy('product.metadata->>\'category\'')
      .getRawMany();

    const stats = {
      totalProducts,
      activeProducts,
      featuredProducts,
      categoryStats
    };

    // 缓存结果
    await this.redisService.set(cacheKey, JSON.stringify(stats), this.CACHE_TTL);
    
    return stats;
  }

  async exportToExcel(): Promise<Buffer> {
    const products = await this.productRepository.find();
    
    const worksheet = XLSX.utils.json_to_sheet(products.map(p => ({
      '产品名称': p.name,
      '产品代码': p.code,
      '描述': p.description,
      '最小金额': p.minAmount,
      '最大金额': p.maxAmount,
      '最小期限': p.minTerm,
      '最大期限': p.maxTerm,
      '年利率': p.interestRate,
      '手续费率': p.processingFee,
      '提前还款费率': p.earlyRepaymentFee,
      '是否激活': p.isActive,
      '是否推荐': p.isFeatured,
      '排序': p.sortOrder,
      '创建时间': p.createdAt,
      '更新时间': p.updatedAt
    })));

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Products');
    
    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }

  async importFromExcel(file: Express.Multer.File): Promise<void> {
    const workbook = XLSX.read(file.buffer);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const products = XLSX.utils.sheet_to_json(worksheet);

    for (const product of products) {
      const existingProduct = await this.productRepository.findOne({
        where: { code: product['产品代码'] }
      });

      if (existingProduct) {
        await this.update(existingProduct.id, {
          name: product['产品名称'],
          description: product['描述'],
          minAmount: product['最小金额'],
          maxAmount: product['最大金额'],
          minTerm: product['最小期限'],
          maxTerm: product['最大期限'],
          interestRate: product['年利率'],
          processingFee: product['手续费率'],
          earlyRepaymentFee: product['提前还款费率'],
          isActive: product['是否激活'],
          isFeatured: product['是否推荐'],
          sortOrder: product['排序']
        });
      } else {
        await this.create({
          name: product['产品名称'],
          code: product['产品代码'],
          description: product['描述'],
          category: product['产品分类'] || '',
          minAmount: product['最小金额'],
          maxAmount: product['最大金额'],
          minTerm: product['最小期限'],
          maxTerm: product['最大期限'],
          interestRate: product['年利率'],
          processingFee: product['手续费率'],
          earlyRepaymentFee: product['提前还款费率'],          isActive: product['是否激活'],
          isFeatured: product['是否推荐'],
          sortOrder: product['排序'],
          requirements: [],
          features: [],
          benefits: [],
          metadata: {}
        });
      }
    }
  }

  // 获取产品列表
  async findAllProducts(params: {
    page?: number;
    pageSize?: number;
    category?: string;
    isActive?: boolean;
    isFeatured?: boolean;
  }): Promise<{
    products: Product[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const {
        page = 1,
        pageSize = 10,
        category,
        isActive,
        isFeatured
      } = params;

      const cacheKey = `products:list:${JSON.stringify(params)}`;
      const cachedResult = await this.cacheService.get<{
        products: Product[];
        total: number;
        page: number;
        pageSize: number;
      }>(cacheKey);
      if (cachedResult) {
        return cachedResult;
      }

      const where: any = {};
      if (category) {
        where.category = category;
      }
      if (isActive !== undefined) {
        where.isActive = isActive;
      }
      if (isFeatured !== undefined) {
        where.isFeatured = isFeatured;
      }

      const [products, total] = await this.productRepository.findAndCount({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        order: {
          sortOrder: 'ASC',
          createdAt: 'DESC'
        }
      });

      const result = {
        products,
        total,
        page,
        pageSize
      };

      await this.cacheService.set(cacheKey, result, 300); // 5分钟过期

      return result;
    } catch (error) {
      this.logger.error('获取产品列表失败', error);
      throw error;
    }
  }

  // 获取产品详情
  async findProductById(id: number): Promise<Product> {
    try {
      const cacheKey = `product:${id}`;
      const cachedProduct = await this.cacheService.get<Product>(cacheKey);
      if (cachedProduct) {
        return cachedProduct;
      }

      const product = await this.productRepository.findOne({
        where: { id }
      });

      if (!product) {
        throw new NotFoundException('产品不存在');
      }

      await this.cacheService.set(cacheKey, product, 300); // 5分钟过期

      return product;
    } catch (error) {
      this.logger.error('获取产品详情失败', error);
      throw error;
    }
  }

  // 获取产品详情（通过代码）
  async findProductByCode(code: string): Promise<Product> {
    try {
      const cacheKey = `product:code:${code}`;
      const cachedProduct = await this.cacheService.get<Product>(cacheKey);
      if (cachedProduct) {
        return cachedProduct;
      }

      const product = await this.productRepository.findOne({
        where: { code }
      });

      if (!product) {
        throw new NotFoundException('产品不存在');
      }

      await this.cacheService.set(cacheKey, product, 300); // 5分钟过期

      return product;
    } catch (error) {
      this.logger.error('获取产品详情失败', error);
      throw error;
    }
  }

  // 更新产品状态
  async updateProductStatus(id: number, isActive: boolean): Promise<Product> {
    try {
      const existingProduct = await this.findProductById(id);
      if (!existingProduct) {
        throw new NotFoundException('产品不存在');
      }

      const updatedProduct = await this.productRepository.save({
        ...existingProduct,
        isActive
      });

      await this.clearProductCache();
      return updatedProduct;
    } catch (error) {
      this.logger.error('更新产品状态失败', error);
      throw error;
    }
  }

  // 更新产品排序
  async updateProductSortOrder(id: number, sortOrder: number): Promise<Product> {
    try {
      const existingProduct = await this.findProductById(id);
      if (!existingProduct) {
        throw new NotFoundException('产品不存在');
      }

      const updatedProduct = await this.productRepository.save({
        ...existingProduct,
        sortOrder
      });

      await this.clearProductCache();
      return updatedProduct;
    } catch (error) {
      this.logger.error('更新产品排序失败', error);
      throw error;
    }
  }

  // 更新产品推荐状态
  async updateProductFeatured(id: number, isFeatured: boolean): Promise<Product> {
    try {
      const existingProduct = await this.findProductById(id);
      if (!existingProduct) {
        throw new NotFoundException('产品不存在');
      }

      const updatedProduct = await this.productRepository.save({
        ...existingProduct,
        isFeatured
      });

      await this.clearProductCache();
      return updatedProduct;
    } catch (error) {
      this.logger.error('更新产品推荐状态失败', error);
      throw error;
    }
  }
  async uploadProducts(file: Express.Multer.File): Promise<{ products: Product[], errors: any[] }> {
    try {
      // 解析Excel文件
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const rows = XLSX.utils.sheet_to_json(worksheet);

      const products: Product[] = [];
      const errors: any[] = [];

      const productsToSave: DeepPartial<Product>[] = rows.map((row: any) => {
        const productPartial: DeepPartial<Product> = {
          name: row['产品名称'] || '',
          code: row['产品编码'] || '',
          description: row['产品描述'] || '',
          category: row['产品分类'] || '',
          min_amount: parseFloat(row['最小金额']) || 0,
          max_amount: parseFloat(row['最大金额']) || 0,
          minTerm: parseInt(row['最小期限']) || 1,
          maxTerm: parseInt(row['最大期限']) || 12,
          interest_rate: parseFloat(row['利率']) || 0,
          processingFee: parseFloat(row['手续费']) || 0,
          earlyRepaymentFee: parseFloat(row['提前还款费']) || 0,
          isActive: row['是否激活'] === true || row['是否激活'] === '是',
          isFeatured: row['是否推荐'] === true || row['是否推荐'] === '是',
          sortOrder: parseInt(row['排序']) || 0,
          requirements: {},
          features: {},
        };

        return productPartial;
      });

      // 保存产品
      const createdProducts = await this.productRepository.save(productsToSave);
      
      return {
        products: createdProducts,
        errors
      };

    } catch (error) {
      this.logger.error(`Error uploading products: ${error}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async updateProduct(id: number, updateProductDto: UpdateProductDto): Promise<Product> {
    try {
        const product = await this.productRepository.findOne({ where: { id } });
        if (!product) {
          throw new NotFoundException(`Product with ID ${id} not found`);
        }

        // Apply updates from DTO. Ensure updateProductDto structure aligns with Product entity.
        // 应用 DTO 中的更新。确保 updateProductDto 结构与 Product 实体对齐。
        // For relationships like 'requirements', handle them based on entity definition.
        // 对于 'requirements' 这样的关系，根据实体定义处理。

        // Example mapping from updateProductDto to product entity:
        // Object.assign(product, updateProductDto); // This works if DTO fields match entity fields directly.
        // For complex relationships like 'requirements', you might need specific logic:
        // if (updateProductDto.requirements !== undefined) {
        //    // Logic to update related requirements entities
        // }

        // Example of updating with a plain object (if updateProductDto is a plain object from request body)
        // Make sure the structure matches the entity partially.
        // If updating from parsed Excel data (L328-341 error), map properties explicitly:
        // product.name = parsedData.name;
        // product.description = parsedData.description;
        // ... map other properties ...
        // Ensure all properties expected by UpdateProductDto that are being updated are handled.
        // 确保 UpdateProductDto 期望的所有正在更新的属性都得到处理。

        const updatedProduct = await this.productRepository.save(product);
        this.logger.debug(`Updated product with ID: ${id}`); // Logger message is string
        return updatedProduct;
     } catch (error) {
        this.logger.error(`Error updating product ${id}: ${String(error)}`, error instanceof Error ? error.stack : undefined); // Convert error to string
        throw error;
     }
  }

  private mapHeaders(headers: any): any {
      // ... existing code ...
      this.logger.debug(`Mapping headers: ${String(headers)}`); // Convert to string
      // ... existing code ...
  }

  private parseRow(row: any): any {
      // ... existing code ...
      this.logger.debug(`Parsing row: ${String(row)}`); // Convert to string
      // ... existing code ...
  }

  private transformProductData(data: any): any {
      // ... existing code ...
      this.logger.debug(`Transforming product data: ${String(data)}`); // Convert to string
      // ... existing code ...
  }
} 