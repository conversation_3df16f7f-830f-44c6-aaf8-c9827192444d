import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('qualification_reviews')
export class QualificationReview {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  user_id: string;

  @Column()
  application_id: string;

  @Column()
  document_type: string; // ID_CARD, BUSINESS_LICENSE, etc.

  @Column('text')
  document_url: string;

  @Column('jsonb')
  ocr_result: any; // OCR识别结果

  @Column('jsonb')
  verification_result: any; // 验证结果

  @Column()
  status: string; // PENDING, APPROVED, REJECTED, MANUAL_REVIEW

  @Column()
  verification_status: string; // 验证状态

  @Column('jsonb', { nullable: true })
  review_result: any; // 审核结果

  @Column('decimal', { precision: 5, scale: 2 })
  confidence_score: number;

  @Column('jsonb', { nullable: true })
  risk_indicators: any; // 风险指标

  @Column({ nullable: true })
  reviewer_id: string;

  @Column('text', { nullable: true })
  review_notes: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ nullable: true })
  processed_at: Date;

  @Column()
  processing_time: number; // 处理时间（毫秒）

  @Column({ default: false })
  is_manual_review: boolean;

  @Column('jsonb', { nullable: true })
  metadata: any; // 额外元数据
}
