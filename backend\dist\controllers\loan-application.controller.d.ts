/// <reference types="multer" />
import { LoanApplicationService } from '../services/loan-application.service';
import { DocumentType } from '../entities/loan-document.entity';
import { CreateLoanApplicationDto } from '../dto/create-loan-application.dto';
import { User as UserEntity } from '../entities/user.entity';
import { LoanApplication } from '../entities/loan-application.entity';
import { LoanStatus } from '../enums/loan-status.enum';
import { LoanApplicationDto } from '../dto/loan-application.dto';
export declare class LoanApplicationController {
    private readonly loanApplicationService;
    constructor(loanApplicationService: LoanApplicationService);
    create(req: any, createDto: CreateLoanApplicationDto): Promise<LoanApplication>;
    findAll(req: any): Promise<LoanApplicationDto[]>;
    findOne(req: any, id: string): Promise<LoanApplication>;
    updateApplication(id: string, updateData: any, user: UserEntity): Promise<LoanApplication>;
    cancelApplication(id: string, user: UserEntity): Promise<LoanApplication>;
    getUserApplications(userId: string, user: UserEntity): Promise<LoanApplicationDto[]>;
    private mapToDto;
    getApplicationStatistics(user: UserEntity): Promise<any>;
    getApplicationTrends(user: UserEntity): Promise<any>;
    approveApplication(id: string, user: UserEntity): Promise<LoanApplication>;
    rejectApplication(id: string, reason: string, user: UserEntity): Promise<LoanApplication>;
    uploadDocument(applicationId: string, file: Express.Multer.File, type: DocumentType, user: UserEntity): Promise<import("../entities/loan-document.entity").LoanDocument>;
    verifyDocument(applicationId: string, documentId: string, isVerified: boolean, notes: string, user: UserEntity): Promise<import("../entities/loan-document.entity").LoanDocument>;
    updateStatus(id: string, status: LoanStatus, req: any): Promise<LoanApplication>;
}
