import { OnM<PERSON>ule<PERSON><PERSON>roy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../logger/logger.service';
import { MonitorService } from '../monitor/monitor.service';
export declare class CacheService implements OnModuleDestroy {
    private readonly configService;
    private readonly logger;
    private readonly monitor;
    private readonly redis;
    constructor(configService: ConfigService, logger: LoggerService, monitor: MonitorService);
    get<T>(key: string): Promise<T | null>;
    set(key: string, value: any, ttl?: number): Promise<void>;
    delete(key: string): Promise<void>;
    clear(): Promise<void>;
    getOrSet<T>(key: string, factory: () => Promise<T>, ttlSeconds?: number): Promise<T>;
    onModuleDestroy(): Promise<void>;
}
