import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FinancialProduct } from './entities/financial-product.entity';

export interface ProductMatchCriteria {
  amount: number;
  term_months: number;
  product_type: string;
  user_profile: {
    age: number;
    income: number;
    credit_score: number;
    employment_type: string;
  };
}

export interface ProductMatch {
  product: FinancialProduct;
  match_score: number;
  ai_reasoning: string;
  recommended_amount: number;
  recommended_rate: number;
}

@Injectable()
export class ProductService {
  private readonly logger = new Logger(ProductService.name);

  constructor(
    @InjectRepository(FinancialProduct)
    private productRepository: Repository<FinancialProduct>,
  ) {}

  async findAll(): Promise<FinancialProduct[]> {
    return this.productRepository.find();
  }

  async findByType(type: string): Promise<FinancialProduct[]> {
    return this.productRepository.find({ where: { product_type: type } });
  }

  async findById(id: number): Promise<FinancialProduct> {
    return this.productRepository.findOneOrFail({ where: { id } });
  }

  async matchProducts(criteria: {
    amount: number;
    term_months: number;
    product_type?: string;
  }): Promise<FinancialProduct[]> {
    const query = this.productRepository
      .createQueryBuilder('product')
      .where('product.amount_min <= :amount', { amount: criteria.amount })
      .andWhere('product.amount_max >= :amount', { amount: criteria.amount })
      .andWhere('product.loan_term_min <= :term', { term: criteria.term_months })
      .andWhere('product.loan_term_max >= :term', { term: criteria.term_months });

    if (criteria.product_type) {
      query.andWhere('product.product_type = :type', { type: criteria.product_type });
    }

    // 按利率升序排序
    query.orderBy('product.interest_rate', 'ASC');

    // 限制返回前5个最佳匹配
    return query.take(5).getMany();
  }

  async findMatchingProducts(criteria: ProductMatchCriteria): Promise<ProductMatch[]> {
    this.logger.debug('开始智能产品匹配', criteria);

    // 获取所有活跃产品
    const products = await this.productRepository.find({
      where: { product_type: criteria.product_type }
    });

    // 计算匹配分数
    const matches = products
      .map(product => this.calculateMatchScore(product, criteria))
      .filter(match => match.match_score > 0.3) // 过滤低匹配度产品
      .sort((a, b) => b.match_score - a.match_score)
      .slice(0, 3); // 返回TOP3

    this.logger.debug(`找到${matches.length}个匹配产品`);
    return matches;
  }

  private calculateMatchScore(product: FinancialProduct, criteria: ProductMatchCriteria): ProductMatch {
    let score = 0;
    let reasoning = [];

    // 金额匹配度 (30%)
    const amountScore = this.calculateAmountScore(product, criteria.amount);
    score += amountScore * 0.3;
    if (amountScore > 0.8) reasoning.push('贷款金额完全符合');
    else if (amountScore > 0.5) reasoning.push('贷款金额基本符合');

    // 期限匹配度 (20%)
    const termScore = this.calculateTermScore(product, criteria.term_months);
    score += termScore * 0.2;
    if (termScore > 0.8) reasoning.push('还款期限理想');

    // 用户资质匹配度 (40%)
    const qualificationScore = this.calculateQualificationScore(product, criteria.user_profile);
    score += qualificationScore * 0.4;
    if (qualificationScore > 0.8) reasoning.push('个人资质优秀');
    else if (qualificationScore > 0.6) reasoning.push('个人资质良好');

    // 产品特性加分 (10%)
    const featureScore = 0.8; // 基础特性分
    score += featureScore * 0.1;
    reasoning.push('产品特性良好');

    // 计算推荐金额和利率
    const recommendedAmount = Math.min(criteria.amount, product.amount_max || criteria.amount);
    const recommendedRate = this.calculateRecommendedRate(product, criteria.user_profile);

    return {
      product,
      match_score: Math.round(score * 100) / 100,
      ai_reasoning: reasoning.join('，'),
      recommended_amount: recommendedAmount,
      recommended_rate: recommendedRate
    };
  }

  private calculateAmountScore(product: FinancialProduct, amount: number): number {
    const minAmount = product.amount_min || 0;
    const maxAmount = product.amount_max || 1000000;

    if (amount < minAmount || amount > maxAmount) {
      return 0;
    }

    const range = maxAmount - minAmount;
    const position = amount - minAmount;
    const ratio = position / range;

    // 在中间范围的金额得分更高
    return 1 - Math.abs(0.5 - ratio);
  }

  private calculateTermScore(product: FinancialProduct, termMonths: number): number {
    const minTerm = product.loan_term_min || 12;
    const maxTerm = product.loan_term_max || 360;

    if (termMonths < minTerm || termMonths > maxTerm) {
      return 0.3;
    }

    const idealTerm = (minTerm + maxTerm) / 2;
    const difference = Math.abs(termMonths - idealTerm);

    if (difference === 0) return 1;
    if (difference <= 6) return 0.9;
    if (difference <= 12) return 0.7;
    if (difference <= 24) return 0.5;
    return 0.3;
  }

  private calculateQualificationScore(product: FinancialProduct, userProfile: any): number {
    let score = 1;

    // 信用分检查 - 基础逻辑
    if (userProfile.credit_score >= 750) score *= 1.1;
    else if (userProfile.credit_score >= 700) score *= 1.0;
    else if (userProfile.credit_score >= 650) score *= 0.9;
    else if (userProfile.credit_score < 600) score *= 0.6;

    // 收入检查
    if (userProfile.income >= 20000) score *= 1.05;
    else if (userProfile.income >= 10000) score *= 1.0;
    else if (userProfile.income < 5000) score *= 0.8;

    // 年龄检查
    if (userProfile.age >= 25 && userProfile.age <= 45) score *= 1.0;
    else if (userProfile.age < 25 || userProfile.age > 55) score *= 0.9;

    return Math.min(score, 1);
  }

  private calculateRecommendedRate(product: FinancialProduct, userProfile: any): number {
    let rate = product.interest_rate || 8.0;

    // 根据信用分调整利率
    if (userProfile.credit_score >= 750) rate *= 0.95;
    else if (userProfile.credit_score >= 700) rate *= 0.98;
    else if (userProfile.credit_score < 600) rate *= 1.1;

    // 根据收入调整利率
    if (userProfile.income >= 20000) rate *= 0.97;
    else if (userProfile.income < 5000) rate *= 1.05;

    return Math.round(rate * 100) / 100;
  }

  async calculateMonthlyPayment(
    amount: number,
    term_months: number,
    annual_rate: number,
  ): Promise<{
    monthlyPayment: number;
    totalInterest: number;
    totalPayment: number;
  }> {
    const monthlyRate = annual_rate / 12 / 100;
    const monthlyPayment =
      (amount * monthlyRate * Math.pow(1 + monthlyRate, term_months)) /
      (Math.pow(1 + monthlyRate, term_months) - 1);
    const totalPayment = monthlyPayment * term_months;
    const totalInterest = totalPayment - amount;

    return {
      monthlyPayment: Math.round(monthlyPayment * 100) / 100,
      totalInterest: Math.round(totalInterest * 100) / 100,
      totalPayment: Math.round(totalPayment * 100) / 100,
    };
  }
}
