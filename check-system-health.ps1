# SmartLoan System Health Check
Write-Host "🔍 SmartLoan 系统健康检查" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

$currentDir = Get-Location
Write-Host "📍 检查目录: $currentDir" -ForegroundColor Cyan

# 检查后端API状态
Write-Host "`n🔧 检查后端API状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 后端API服务正常运行" -ForegroundColor Green
        $healthData = $response.Content | ConvertFrom-Json
        Write-Host "   服务: $($healthData.service)" -ForegroundColor White
        Write-Host "   版本: $($healthData.version)" -ForegroundColor White
        Write-Host "   时间: $($healthData.timestamp)" -ForegroundColor White
    } else {
        Write-Host "⚠️ 后端API响应异常 (状态码: $($response.StatusCode))" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 后端API服务未运行或无法连接" -ForegroundColor Red
    Write-Host "   请运行: node quick-api.cjs" -ForegroundColor Gray
}

# 检查数据库状态
Write-Host "`n🗄️ 检查数据库状态..." -ForegroundColor Yellow
try {
    $dockerStatus = docker ps --filter "name=postgres" --format "table {{.Names}}\t{{.Status}}"
    if ($dockerStatus -match "postgres") {
        Write-Host "✅ PostgreSQL数据库容器正在运行" -ForegroundColor Green
    } else {
        Write-Host "⚠️ PostgreSQL数据库容器未运行" -ForegroundColor Yellow
        Write-Host "   请运行: docker-compose up -d" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 无法检查Docker状态" -ForegroundColor Red
}

# 检查Redis状态
Write-Host "`n📦 检查Redis状态..." -ForegroundColor Yellow
try {
    $redisStatus = docker ps --filter "name=redis" --format "table {{.Names}}\t{{.Status}}"
    if ($redisStatus -match "redis") {
        Write-Host "✅ Redis缓存服务正在运行" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Redis缓存服务未运行" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 无法检查Redis状态" -ForegroundColor Red
}

# 检查关键文件
Write-Host "`n📁 检查关键文件..." -ForegroundColor Yellow
$criticalFiles = @(
    "demo.html",
    "quick-api.cjs", 
    "README.md",
    "使用指南.md",
    "PROJECT_COMPLETION_REPORT.md"
)

foreach ($file in $criticalFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file" -ForegroundColor Red
    }
}

# 检查目录结构
Write-Host "`n📂 检查目录结构..." -ForegroundColor Yellow
$requiredDirs = @("frontend", "backend", "ai-service")
foreach ($dir in $requiredDirs) {
    if (Test-Path $dir) {
        Write-Host "✅ $dir/" -ForegroundColor Green
        
        # 检查package.json
        $packageJson = Join-Path $dir "package.json"
        if (Test-Path $packageJson) {
            Write-Host "   ✅ package.json" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️ package.json 缺失" -ForegroundColor Yellow
        }
        
        # 检查node_modules
        $nodeModules = Join-Path $dir "node_modules"
        if (Test-Path $nodeModules) {
            Write-Host "   ✅ node_modules" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️ node_modules 缺失 (运行 npm install)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ $dir/" -ForegroundColor Red
    }
}

# 检查端口占用
Write-Host "`n🔌 检查端口状态..." -ForegroundColor Yellow
$ports = @(
    @{Port=3000; Service="前端开发服务器"},
    @{Port=3001; Service="后端API服务"},
    @{Port=3002; Service="AI服务"},
    @{Port=5432; Service="PostgreSQL数据库"},
    @{Port=6379; Service="Redis缓存"}
)

foreach ($portInfo in $ports) {
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $portInfo.Port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "✅ 端口 $($portInfo.Port) - $($portInfo.Service)" -ForegroundColor Green
        } else {
            Write-Host "⚪ 端口 $($portInfo.Port) - $($portInfo.Service) (未使用)" -ForegroundColor Gray
        }
    } catch {
        Write-Host "⚪ 端口 $($portInfo.Port) - $($portInfo.Service) (未使用)" -ForegroundColor Gray
    }
}

# 测试API功能
Write-Host "`n🧪 测试API功能..." -ForegroundColor Yellow
try {
    # 测试智能匹配API
    $matchData = @{
        amount = 100000
        term_months = 24
        product_type = "personal"
        user_profile = @{
            income = 15000
            credit_score = 750
            employment_type = "full_time"
        }
    } | ConvertTo-Json -Depth 3

    $matchResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/products/match/smart" -Method POST -Body $matchData -ContentType "application/json" -UseBasicParsing -TimeoutSec 10
    
    if ($matchResponse.StatusCode -eq 200) {
        Write-Host "✅ 智能产品匹配API正常" -ForegroundColor Green
        $matchResult = $matchResponse.Content | ConvertFrom-Json
        Write-Host "   匹配到 $($matchResult.total) 个产品" -ForegroundColor White
    }
} catch {
    Write-Host "❌ 智能产品匹配API测试失败" -ForegroundColor Red
}

try {
    # 测试AI顾问API
    $advisorData = @{
        query = "贷款利率"
    } | ConvertTo-Json

    $advisorResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/ai/advisor/chat" -Method POST -Body $advisorData -ContentType "application/json" -UseBasicParsing -TimeoutSec 10
    
    if ($advisorResponse.StatusCode -eq 200) {
        Write-Host "✅ AI虚拟顾问API正常" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ AI虚拟顾问API测试失败" -ForegroundColor Red
}

# 系统总结
Write-Host "`n📊 系统状态总结" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan

$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
Write-Host "检查时间: $timestamp" -ForegroundColor White

Write-Host "`n🎯 推荐操作:" -ForegroundColor Green
Write-Host "1. 如果后端API未运行，执行: node quick-api.cjs" -ForegroundColor White
Write-Host "2. 如果数据库未运行，执行: docker-compose up -d" -ForegroundColor White
Write-Host "3. 打开演示页面: .\demo-start.ps1 或双击 demo.html" -ForegroundColor White
Write-Host "4. 查看完整文档: README.md 和 使用指南.md" -ForegroundColor White

Write-Host "`n✅ 健康检查完成！" -ForegroundColor Green
