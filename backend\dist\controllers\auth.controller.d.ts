import { AuthService } from '../services/auth.service';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(registerDto: {
        phone: string;
        email: string;
        password: string;
    }): Promise<any>;
    login(req: any): Promise<any>;
    changePassword(req: any, changePasswordDto: {
        oldPassword: string;
        newPassword: string;
    }): Promise<{
        message: string;
    }>;
    resetPassword(resetPasswordDto: {
        email: string;
    }): Promise<{
        message: string;
    }>;
    getProfile(req: any): any;
}
