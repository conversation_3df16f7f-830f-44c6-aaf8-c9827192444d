import { MigrationInterface, QueryRunner } from 'typeorm';
import * as bcrypt from 'bcrypt';

export class SeedData1709123456790 implements MigrationInterface {
  name = 'SeedData1709123456790';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建管理员用户
    const adminPassword = await bcrypt.hash('admin123', 10);
    await queryRunner.query(`
      INSERT INTO "users" ("username", "email", "password", "role", "is_active")
      VALUES ('admin', '<EMAIL>', '${adminPassword}', 'admin', true)
    `);

    // 创建测试用户
    const userPassword = await bcrypt.hash('user123', 10);
    await queryRunner.query(`
      INSERT INTO "users" ("username", "email", "password", "role", "is_active")
      VALUES ('testuser', '<EMAIL>', '${userPassword}', 'user', true)
    `);

    // 创建审核员用户
    const reviewerPassword = await bcrypt.hash('reviewer123', 10);
    await queryRunner.query(`
      INSERT INTO "users" ("username", "email", "password", "role", "is_active")
      VALUES ('reviewer', '<EMAIL>', '${reviewerPassword}', 'reviewer', true)
    `);

    // 创建风险分析师用户
    const analystPassword = await bcrypt.hash('analyst123', 10);
    await queryRunner.query(`
      INSERT INTO "users" ("username", "email", "password", "role", "is_active")
      VALUES ('analyst', '<EMAIL>', '${analystPassword}', 'risk_analyst', true)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DELETE FROM "users" WHERE "email" IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>')`);
  }
} 