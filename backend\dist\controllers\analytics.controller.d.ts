import { AnalyticsService } from '../services/analytics.service';
import { ExportService } from '../services/export.service';
import { LoggerService } from '../services/logger.service';
import { <PERSON>rror<PERSON>and<PERSON> } from '../utils/error-handler';
import { Request } from 'express';
export declare class AnalyticsController {
    private readonly analyticsService;
    private readonly exportService;
    private readonly logger;
    private readonly errorHandler;
    constructor(analyticsService: AnalyticsService, exportService: ExportService, logger: LoggerService, errorHandler: ErrorHandler);
    getDashboardData(query: any, req: Request): Promise<{
        success: boolean;
        data: import("../interfaces/analytics.interface").DashboardData;
    }>;
    getAnalyticsData(query: any): Promise<{
        success: boolean;
        data: import("../interfaces/analytics.interface").AnalyticsData;
    }>;
    exportData(query: any, req: Request): Promise<{
        success: boolean;
        data: import("../interfaces/analytics.interface").ExportData;
    }>;
    getExportData(id: string, req: Request): Promise<{
        success: boolean;
        data: import("../interfaces/analytics.interface").ExportData;
    }>;
    predictTrends(query: any, req: Request): Promise<{
        success: boolean;
        data: import("../interfaces/analytics.interface").TrendPrediction[];
    }>;
    detectAnomalies(query: any, req: Request): Promise<{
        success: boolean;
        data: import("../interfaces/analytics.interface").Anomaly[];
    }>;
    getRecommendations(query: any, req: Request): Promise<{
        success: boolean;
        data: import("../interfaces/analytics.interface").Recommendation[];
    }>;
}
