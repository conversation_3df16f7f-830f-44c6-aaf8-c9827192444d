<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>SmartLoan 2025 - APP版</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; background: #000; color: white; overflow-x: hidden; }
        .app-container { max-width: 414px; margin: 0 auto; background: linear-gradient(180deg, #0a0e27 0%, #1a1f3a 100%); min-height: 100vh; position: relative; }
        .status-bar { height: 44px; background: rgba(0,0,0,0.8); display: flex; justify-content: space-between; align-items: center; padding: 0 20px; font-size: 0.8rem; }
        .app-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; position: relative; overflow: hidden; }
        .app-header::before { content: ''; position: absolute; top: -50%; right: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%); animation: rotate 20s linear infinite; }
        @keyframes rotate { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .header-content { position: relative; z-index: 2; }
        .app-title { font-size: 2rem; font-weight: bold; margin-bottom: 5px; }
        .app-subtitle { font-size: 0.9rem; opacity: 0.9; }
        .user-info { display: flex; align-items: center; margin-top: 15px; }
        .avatar { width: 40px; height: 40px; border-radius: 50%; background: rgba(255,255,255,0.2); display: flex; align-items: center; justify-content: center; margin-right: 12px; }
        .greeting { flex: 1; }
        .notification-btn { width: 35px; height: 35px; border-radius: 50%; background: rgba(255,255,255,0.2); display: flex; align-items: center; justify-content: center; cursor: pointer; }
        .main-content { padding: 20px; }
        .feature-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 25px; }
        .feature-card { background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); border-radius: 20px; padding: 25px; text-align: center; cursor: pointer; transition: all 0.3s; position: relative; overflow: hidden; }
        .feature-card::before { content: ''; position: absolute; top: 0; right: 0; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%; transform: translate(20px, -20px); }
        .feature-card:active { transform: scale(0.95); }
        .feature-icon { font-size: 2.5rem; margin-bottom: 10px; }
        .feature-title { font-size: 1rem; font-weight: bold; margin-bottom: 5px; }
        .feature-desc { font-size: 0.7rem; opacity: 0.8; }
        .section-title { font-size: 1.3rem; font-weight: bold; margin-bottom: 15px; color: #4fc3f7; }
        .product-card { background: rgba(255,255,255,0.05); border-radius: 15px; padding: 20px; margin-bottom: 15px; border: 1px solid rgba(255,255,255,0.1); }
        .product-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .product-name { font-weight: bold; font-size: 1.1rem; }
        .product-rate { color: #4caf50; font-weight: bold; }
        .product-details { display: flex; justify-content: space-between; font-size: 0.8rem; opacity: 0.8; margin-bottom: 15px; }
        .apply-btn { background: linear-gradient(45deg, #4caf50, #8bc34a); color: white; border: none; padding: 10px 20px; border-radius: 20px; font-size: 0.9rem; cursor: pointer; width: 100%; }
        .stats-row { display: flex; justify-content: space-between; margin-bottom: 25px; }
        .stat-item { text-align: center; flex: 1; }
        .stat-value { font-size: 1.5rem; font-weight: bold; color: #4fc3f7; }
        .stat-label { font-size: 0.7rem; opacity: 0.7; margin-top: 5px; }
        .quick-actions { display: flex; justify-content: space-around; margin-bottom: 25px; }
        .quick-action { text-align: center; cursor: pointer; }
        .quick-icon { width: 50px; height: 50px; border-radius: 50%; background: rgba(255,255,255,0.1); display: flex; align-items: center; justify-content: center; margin: 0 auto 8px; font-size: 1.5rem; }
        .quick-label { font-size: 0.7rem; opacity: 0.8; }
        .bottom-tabs { position: fixed; bottom: 0; left: 50%; transform: translateX(-50%); width: 100%; max-width: 414px; background: rgba(0,0,0,0.9); backdrop-filter: blur(20px); border-top: 1px solid rgba(255,255,255,0.1); display: flex; }
        .tab-item { flex: 1; padding: 12px 10px; text-align: center; cursor: pointer; transition: all 0.3s; }
        .tab-item.active { color: #4fc3f7; }
        .tab-icon { font-size: 1.3rem; margin-bottom: 5px; }
        .tab-label { font-size: 0.6rem; }
        .floating-ai { position: fixed; bottom: 80px; right: 20px; width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); display: flex; align-items: center; justify-content: center; font-size: 1.5rem; cursor: pointer; box-shadow: 0 8px 25px rgba(0,0,0,0.3); z-index: 100; animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.1); } }
        .modal { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.8); display: none; z-index: 1000; }
        .modal-content { position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(180deg, #1a1f3a 0%, #0a0e27 100%); border-radius: 25px 25px 0 0; padding: 30px 20px; max-height: 80vh; overflow-y: auto; }
        .modal-header { text-align: center; margin-bottom: 20px; }
        .modal-close { position: absolute; top: 15px; right: 20px; font-size: 1.5rem; cursor: pointer; }
        .input-group { margin-bottom: 15px; }
        .input-label { font-size: 0.8rem; opacity: 0.8; margin-bottom: 5px; }
        .input-field { width: 100%; padding: 15px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 10px; color: white; font-size: 1rem; }
        .input-field::placeholder { color: rgba(255,255,255,0.5); }
        .result-card { background: rgba(76, 175, 80, 0.1); border: 1px solid #4caf50; border-radius: 10px; padding: 20px; margin-top: 15px; }
        .camera-preview { width: 100%; height: 200px; background: #000; border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-bottom: 15px; }
        .progress-ring { width: 80px; height: 80px; margin: 0 auto 15px; }
        .progress-circle { fill: none; stroke: #4fc3f7; stroke-width: 4; stroke-dasharray: 251; stroke-dashoffset: 251; animation: progress 2s ease-in-out forwards; }
        @keyframes progress { to { stroke-dashoffset: 50; } }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div>9:41</div>
            <div>SmartLoan</div>
            <div>100% 🔋</div>
        </div>

        <!-- 应用头部 -->
        <div class="app-header">
            <div class="header-content">
                <div class="app-title">SmartLoan</div>
                <div class="app-subtitle">智能金融服务平台</div>
                <div class="user-info">
                    <div class="avatar">👤</div>
                    <div class="greeting">
                        <div style="font-size: 0.9rem;">早上好，张三</div>
                        <div style="font-size: 0.7rem; opacity: 0.8;">信用评分: 85分</div>
                    </div>
                    <div class="notification-btn" onclick="showNotifications()">🔔</div>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content" id="main-content">
            <!-- 统计数据 -->
            <div class="stats-row">
                <div class="stat-item">
                    <div class="stat-value">3</div>
                    <div class="stat-label">申请记录</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">2</div>
                    <div class="stat-label">审核通过</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">85</div>
                    <div class="stat-label">信用评分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">0.8s</div>
                    <div class="stat-label">处理速度</div>
                </div>
            </div>

            <!-- 快捷操作 -->
            <div class="quick-actions">
                <div class="quick-action" onclick="openFeature('match')">
                    <div class="quick-icon">🎯</div>
                    <div class="quick-label">智能匹配</div>
                </div>
                <div class="quick-action" onclick="openFeature('calculator')">
                    <div class="quick-icon">💰</div>
                    <div class="quick-label">贷款计算</div>
                </div>
                <div class="quick-action" onclick="openFeature('ocr')">
                    <div class="quick-icon">📷</div>
                    <div class="quick-label">证件识别</div>
                </div>
                <div class="quick-action" onclick="openFeature('liveness')">
                    <div class="quick-icon">👤</div>
                    <div class="quick-label">活体检测</div>
                </div>
            </div>

            <!-- 功能卡片 -->
            <div class="feature-grid">
                <div class="feature-card" onclick="openFeature('match')">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-title">智能匹配</div>
                    <div class="feature-desc">AI推荐最适合的产品</div>
                </div>
                <div class="feature-card" onclick="openFeature('review')">
                    <div class="feature-icon">📋</div>
                    <div class="feature-title">资质审核</div>
                    <div class="feature-desc">快速完成资质认证</div>
                </div>
                <div class="feature-card" onclick="openFeature('3d')">
                    <div class="feature-icon">🎮</div>
                    <div class="feature-title">3D对比</div>
                    <div class="feature-desc">可视化产品对比</div>
                </div>
                <div class="feature-card" onclick="openFeature('dashboard')">
                    <div class="feature-icon">📊</div>
                    <div class="feature-title">风控看板</div>
                    <div class="feature-desc">实时风险监控</div>
                </div>
            </div>

            <!-- 推荐产品 -->
            <div class="section-title">🏆 为您推荐</div>
            <div class="product-card">
                <div class="product-header">
                    <div class="product-name">工商银行融e借2025版</div>
                    <div class="product-rate">3.85%</div>
                </div>
                <div class="product-details">
                    <span>最高额度: 80万</span>
                    <span>匹配度: 95%</span>
                    <span>审批: 30秒</span>
                </div>
                <button class="apply-btn" onclick="applyLoan()">立即申请</button>
            </div>

            <div class="product-card">
                <div class="product-header">
                    <div class="product-name">建设银行快贷Pro</div>
                    <div class="product-rate">3.95%</div>
                </div>
                <div class="product-details">
                    <span>最高额度: 50万</span>
                    <span>匹配度: 92%</span>
                    <span>审批: 1分钟</span>
                </div>
                <button class="apply-btn" onclick="applyLoan()">立即申请</button>
            </div>

            <!-- 底部间距 -->
            <div style="height: 100px;"></div>
        </div>

        <!-- 底部标签栏 -->
        <div class="bottom-tabs">
            <div class="tab-item active" onclick="switchTab('home')">
                <div class="tab-icon">🏠</div>
                <div class="tab-label">首页</div>
            </div>
            <div class="tab-item" onclick="switchTab('products')">
                <div class="tab-icon">💳</div>
                <div class="tab-label">产品</div>
            </div>
            <div class="tab-item" onclick="switchTab('applications')">
                <div class="tab-icon">📋</div>
                <div class="tab-label">申请</div>
            </div>
            <div class="tab-item" onclick="switchTab('profile')">
                <div class="tab-icon">👤</div>
                <div class="tab-label">我的</div>
            </div>
        </div>

        <!-- 浮动AI助手 -->
        <div class="floating-ai" onclick="openAI()">🤖</div>
    </div>

    <!-- 功能模态框 -->
    <div class="modal" id="featureModal">
        <div class="modal-content">
            <div class="modal-close" onclick="closeModal()">×</div>
            <div class="modal-header">
                <h3 id="modal-title">功能</h3>
            </div>
            <div id="modal-body">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <script>
        function switchTab(tab) {
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.tab-item').classList.add('active');
            
            // 这里可以切换不同的页面内容
            console.log('切换到标签:', tab);
        }

        function openFeature(feature) {
            const modal = document.getElementById('featureModal');
            const title = document.getElementById('modal-title');
            const body = document.getElementById('modal-body');
            
            switch(feature) {
                case 'match':
                    title.textContent = '🎯 智能产品匹配';
                    body.innerHTML = `
                        <div class="input-group">
                            <div class="input-label">贷款金额 (万元)</div>
                            <input type="number" class="input-field" placeholder="请输入贷款金额" value="50">
                        </div>
                        <div class="input-group">
                            <div class="input-label">贷款期限</div>
                            <select class="input-field">
                                <option>12个月</option>
                                <option>24个月</option>
                                <option>36个月</option>
                            </select>
                        </div>
                        <button class="apply-btn" onclick="performMatch()">开始匹配</button>
                        <div id="match-result" style="display:none;">
                            <div class="result-card">
                                <h4>🏆 匹配结果</h4>
                                <p>为您找到3款合适的产品</p>
                                <p>最佳推荐: 工商银行融e借 (匹配度95%)</p>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'calculator':
                    title.textContent = '💰 贷款计算器';
                    body.innerHTML = `
                        <div class="input-group">
                            <div class="input-label">贷款金额 (万元)</div>
                            <input type="number" class="input-field" id="calc-amount" placeholder="100" value="100">
                        </div>
                        <div class="input-group">
                            <div class="input-label">贷款期限</div>
                            <select class="input-field" id="calc-term">
                                <option value="240">20年</option>
                                <option value="360">30年</option>
                            </select>
                        </div>
                        <button class="apply-btn" onclick="calculateLoan()">计算月供</button>
                        <div id="calc-result" style="display:none;">
                            <div class="result-card">
                                <h4>📊 计算结果</h4>
                                <p>月供金额: <span id="monthly-payment">¥6,544</span></p>
                                <p>总利息: <span id="total-interest">¥570,560</span></p>
                                <p>年利率: 4.90%</p>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'ocr':
                    title.textContent = '📷 证件识别';
                    body.innerHTML = `
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="font-size: 3rem; margin-bottom: 10px;">📄</div>
                            <div>请选择要识别的证件类型</div>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-bottom: 20px;">
                            <button class="apply-btn" onclick="recognizeDocument('identity_card')">身份证</button>
                            <button class="apply-btn" onclick="recognizeDocument('business_license')">营业执照</button>
                            <button class="apply-btn" onclick="recognizeDocument('bank_card')">银行卡</button>
                            <button class="apply-btn" onclick="recognizeDocument('driving_license')">驾驶证</button>
                        </div>
                        <div id="ocr-result" style="display:none;">
                            <div class="result-card">
                                <h4>✅ 识别成功</h4>
                                <p>姓名: 张三</p>
                                <p>身份证号: 110101199001011234</p>
                                <p>置信度: 95%</p>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'liveness':
                    title.textContent = '👤 活体检测';
                    body.innerHTML = `
                        <div class="camera-preview">
                            <div style="text-align: center;">
                                <div style="font-size: 2rem; margin-bottom: 10px;">📹</div>
                                <div>请将面部对准摄像头</div>
                            </div>
                        </div>
                        <button class="apply-btn" onclick="startLivenessDetection()">开始检测</button>
                        <div id="liveness-result" style="display:none;">
                            <div style="text-align: center; margin-bottom: 15px;">
                                <svg class="progress-ring">
                                    <circle class="progress-circle" cx="40" cy="40" r="36"></circle>
                                </svg>
                            </div>
                            <div class="result-card">
                                <h4>✅ 检测通过</h4>
                                <p>活体状态: 真人</p>
                                <p>置信度: 98%</p>
                                <p>人脸质量: 高</p>
                            </div>
                        </div>
                    `;
                    break;
            }
            
            modal.style.display = 'block';
        }

        function closeModal() {
            document.getElementById('featureModal').style.display = 'none';
        }

        function performMatch() {
            document.getElementById('match-result').style.display = 'block';
        }

        function calculateLoan() {
            const amount = document.getElementById('calc-amount').value;
            const term = document.getElementById('calc-term').value;
            const monthlyPayment = (amount * 10000 * 0.049 / 12 * Math.pow(1 + 0.049/12, term)) / (Math.pow(1 + 0.049/12, term) - 1);
            
            document.getElementById('monthly-payment').textContent = `¥${Math.round(monthlyPayment).toLocaleString()}`;
            document.getElementById('total-interest').textContent = `¥${Math.round(monthlyPayment * term - amount * 10000).toLocaleString()}`;
            document.getElementById('calc-result').style.display = 'block';
        }

        function recognizeDocument(type) {
            document.getElementById('ocr-result').style.display = 'block';
        }

        function startLivenessDetection() {
            document.getElementById('liveness-result').style.display = 'block';
        }

        function openAI() {
            openFeature('ai');
        }

        function showNotifications() {
            alert('📢 您有2条新消息\n1. 贷款申请审核通过\n2. 新产品推荐');
        }

        function applyLoan() {
            alert('🎉 申请已提交，预计30秒内完成审核');
        }

        // 页面加载完成
        window.onload = function() {
            console.log('📱 SmartLoan APP版已加载');
        };
    </script>
</body>
</html>
