import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Tag, 
  Space, 
  Button, 
  Modal, 
  Descriptions, 
  Progress,
  Alert
} from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  ExclamationCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import styles from './Assessment.module.less';

interface RiskAssessment {
  id: number;
  applicationId: number;
  riskScore: number;
  riskFactors: {
    creditScore: number;
    debtRatio: number;
    incomeStability: string;
    [key: string]: any;
  };
  decision: 'APPROVED' | 'REJECTED' | 'MANUAL_REVIEW';
  createdAt: string;
}

const RiskAssessment: React.FC = () => {
  const navigate = useNavigate();
  const [assessments, setAssessments] = useState<RiskAssessment[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAssessment, setSelectedAssessment] = useState<RiskAssessment | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    fetchAssessments();
  }, []);

  const fetchAssessments = async () => {
    try {
      const response = await fetch('/api/risk-assessments');
      const data = await response.json();
      setAssessments(data);
    } catch (error) {
      console.error('获取风控评估记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (decision: string) => {
    switch (decision) {
      case 'APPROVED':
        return <Tag color="success" icon={<CheckCircleOutlined />}>通过</Tag>;
      case 'REJECTED':
        return <Tag color="error" icon={<CloseCircleOutlined />}>拒绝</Tag>;
      case 'MANUAL_REVIEW':
        return <Tag color="warning" icon={<ExclamationCircleOutlined />}>人工审核</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const getRiskLevel = (score: number) => {
    if (score <= 20) return { text: '低风险', color: 'success' };
    if (score <= 50) return { text: '中风险', color: 'warning' };
    return { text: '高风险', color: 'error' };
  };

  const columns = [
    {
      title: '申请ID',
      dataIndex: 'applicationId',
      key: 'applicationId',
    },
    {
      title: '风险评分',
      dataIndex: 'riskScore',
      key: 'riskScore',
      render: (score: number) => (
        <Space direction="vertical" size="small">
          <Progress
            percent={score}
            size="small"
            status={score > 50 ? 'exception' : 'normal'}
            format={percent => `${percent}分`}
          />
          <Tag color={getRiskLevel(score).color}>
            {getRiskLevel(score).text}
          </Tag>
        </Space>
      ),
    },
    {
      title: '决策结果',
      dataIndex: 'decision',
      key: 'decision',
      render: (decision: string) => getStatusTag(decision),
    },
    {
      title: '评估时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: RiskAssessment) => (
        <Space>
          <Button type="link" onClick={() => showDetails(record)}>
            查看详情
          </Button>
          {record.decision === 'MANUAL_REVIEW' && (
            <Button type="primary" onClick={() => navigate(`/applications/${record.applicationId}`)}>
              人工审核
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const showDetails = (assessment: RiskAssessment) => {
    setSelectedAssessment(assessment);
    setModalVisible(true);
  };

  const renderRiskFactors = (factors: any) => {
    return (
      <Descriptions bordered column={2}>
        <Descriptions.Item label="信用评分">
          {factors.creditScore}
          <Tag color={factors.creditScore > 700 ? 'success' : 'warning'} style={{ marginLeft: 8 }}>
            {factors.creditScore > 700 ? '良好' : '一般'}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="负债率">
          {(factors.debtRatio * 100).toFixed(2)}%
          <Tag color={factors.debtRatio < 0.5 ? 'success' : 'error'} style={{ marginLeft: 8 }}>
            {factors.debtRatio < 0.5 ? '正常' : '偏高'}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="收入稳定性">
          {factors.incomeStability}
          <Tag color={factors.incomeStability === 'HIGH' ? 'success' : 'warning'} style={{ marginLeft: 8 }}>
            {factors.incomeStability === 'HIGH' ? '稳定' : '波动'}
          </Tag>
        </Descriptions.Item>
        {/* 可以根据实际风控因素添加更多项 */}
      </Descriptions>
    );
  };

  return (
    <div className={styles.container}>
      <Card title="风控决策中心">
        {loading ? (
          <div className={styles.loading}>
            <LoadingOutlined style={{ fontSize: 24 }} />
            <p>加载中...</p>
          </div>
        ) : (
          <>
            <Alert
              message="风控系统状态"
              description={
                <Space direction="vertical">
                  <div>GPU加速：已启用 | 模型版本：v2.5.0</div>
                  <div>今日评估：{assessments.length} | 平均响应时间：0.8s</div>
                </Space>
              }
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />
            <Table
              columns={columns}
              dataSource={assessments}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </>
        )}
      </Card>

      <Modal
        title="风控评估详情"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedAssessment && (
          <div className={styles.modalContent}>
            <div className={styles.scoreSection}>
              <div className={styles.score}>
                <Progress
                  type="circle"
                  percent={selectedAssessment.riskScore}
                  status={selectedAssessment.riskScore > 50 ? 'exception' : 'normal'}
                  format={percent => `${percent}分`}
                />
                <div className={styles.decision}>
                  {getStatusTag(selectedAssessment.decision)}
                </div>
              </div>
            </div>
            
            <div className={styles.factorsSection}>
              <h3>风险因素分析</h3>
              {renderRiskFactors(selectedAssessment.riskFactors)}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default RiskAssessment;
