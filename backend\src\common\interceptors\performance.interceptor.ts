import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class PerformanceInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const start = Date.now();
    const req = context.switchToHttp().getRequest();
    const { method, url } = req;

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - start;
        
        // 记录接口响应时间
        console.log(`${method} ${url} - ${duration}ms`);
        
        // 如果响应时间超过1s,记录为慢请求
        if (duration > 1000) {
          console.warn(`Slow API detected: ${method} ${url} - ${duration}ms`);
        }
      }),
    );
  }
}
