"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const export_service_1 = require("./export.service");
const loan_application_entity_1 = require("../entities/loan-application.entity");
const logger_service_1 = require("./logger.service");
const error_handler_1 = require("../utils/error-handler");
const fs = __importStar(require("fs"));
const loan_application_entity_2 = require("../entities/loan-application.entity");
const employment_status_enum_1 = require("../enums/employment-status.enum");
const collateral_type_enum_1 = require("../enums/collateral-type.enum");
const loan_purpose_enum_1 = require("../enums/loan-purpose.enum");
jest.mock('fs');
jest.mock('path');
describe('ExportService', () => {
    let service;
    let loanApplicationRepository;
    let logger;
    let errorHandler;
    const mockLoanApplication = {
        id: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
        monthlyIncome: 5000,
        loanAmount: 100000,
        loanTerm: 12,
        status: 'PENDING'
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                export_service_1.ExportService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(loan_application_entity_1.LoanApplication),
                    useValue: {
                        find: jest.fn(),
                        count: jest.fn()
                    }
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: {
                        error: jest.fn(),
                        info: jest.fn()
                    }
                },
                {
                    provide: error_handler_1.ErrorHandler,
                    useValue: {
                        handle: jest.fn()
                    }
                }
            ]
        }).compile();
        service = module.get(export_service_1.ExportService);
        loanApplicationRepository = module.get((0, typeorm_1.getRepositoryToken)(loan_application_entity_1.LoanApplication));
        logger = module.get(logger_service_1.LoggerService);
        errorHandler = module.get(error_handler_1.ErrorHandler);
    });
    describe('exportData', () => {
        it('should handle empty data', async () => {
            jest.spyOn(loanApplicationRepository, 'find').mockResolvedValue([]);
            jest.spyOn(loanApplicationRepository, 'count').mockResolvedValue(0);
            const result = await service.exportData({});
            expect(result).toBeInstanceOf(Buffer);
        });
        it('should export data with filters', async () => {
            const mockData = [
                {
                    id: '1',
                    userId: 'user1',
                    productId: 'product1',
                    amount: 10000,
                    term: 12,
                    type: loan_application_entity_2.LoanType.PERSONAL,
                    purpose: loan_purpose_enum_1.LoanPurpose.PERSONAL,
                    status: loan_application_entity_2.LoanStatus.PENDING,
                    creditScore: 700,
                    employmentStatus: employment_status_enum_1.EmploymentStatus.EMPLOYED,
                    annualIncome: 100000,
                    debtToIncomeRatio: 0.3,
                    collateralType: collateral_type_enum_1.CollateralType.NONE,
                    interestRate: 5.5,
                    monthlyPayment: 1000,
                    totalPayment: 12000,
                    approvedAmount: null,
                    approvedRate: null,
                    approvedTerm: null,
                    applicationDate: new Date(),
                    lastModified: new Date(),
                    submittedAt: new Date(),
                    processedAt: null,
                    riskAssessment: {},
                    documents: [],
                    notes: '',
                    user: {},
                    product: {},
                    reviews: [],
                    createdAt: new Date(),
                    updatedAt: new Date(), rejectionReason: '',
                    approvedBy: '',
                    approvedAt: null,
                    rejectedBy: '',
                    rejectedAt: null,
                    workExperience: 5,
                    riskScore: 0.3,
                    metadata: {},
                    documentMetadata: {},
                    monthlyIncome: 8333,
                    loanAmount: 10000,
                    loanTerm: 12,
                    loanType: loan_application_entity_2.LoanType.PERSONAL,
                    loanPurpose: loan_purpose_enum_1.LoanPurpose.PERSONAL,
                    loanStatus: loan_application_entity_2.LoanStatus.PENDING,
                    monthlyDebt: 2500,
                    assets: 50000,
                    cancelledAt: null,
                    cancelledBy: '',
                    cancellationReason: '',
                    employmentStatusEnum: employment_status_enum_1.EmploymentStatus.EMPLOYED,
                    collateral: {}
                }
            ];
            jest.spyOn(loanApplicationRepository, 'find').mockResolvedValue(mockData);
            jest.spyOn(loanApplicationRepository, 'count').mockResolvedValue(1);
            const result = await service.exportData({
                startDate: new Date(),
                endDate: new Date(),
                status: loan_application_entity_2.LoanStatus.PENDING
            });
            expect(result).toBeInstanceOf(Buffer);
            expect(loanApplicationRepository.find).toHaveBeenCalled();
        });
        it('应该成功导出Excel数据', async () => {
            const query = {
                format: 'excel',
                filename: 'test'
            };
            const result = await service.exportData(query);
            expect(result).toBeInstanceOf(Buffer);
        });
        it('应该成功导出PDF数据', async () => {
            const query = {
                format: 'pdf',
                filename: 'test'
            };
            const result = await service.exportData(query);
            expect(result).toBeInstanceOf(Buffer);
        });
        it('应该成功导出CSV数据', async () => {
            const query = {
                format: 'csv',
                filename: 'test'
            };
            const result = await service.exportData(query);
            expect(result).toBeInstanceOf(Buffer);
        });
        it('应该处理无效的导出格式', async () => {
            const query = {
                format: 'invalid',
                filename: 'test'
            };
            await expect(service.exportData(query)).rejects.toThrow('不支持的导出格式');
        });
        it('应该处理空数据', async () => {
            jest.spyOn(loanApplicationRepository, 'find').mockResolvedValueOnce([]);
            const query = {
                format: 'excel',
                filename: 'test'
            };
            const result = await service.exportData(query);
            expect(result).toBeInstanceOf(Buffer);
        });
    });
    describe('数据验证', () => {
        it('应该验证必填字段', async () => {
            const invalidData = [{
                    monthlyIncome: 5000,
                    loanAmount: 100000
                }];
            jest.spyOn(loanApplicationRepository, 'find').mockResolvedValueOnce(invalidData);
            const query = {
                format: 'excel',
                filename: 'test'
            };
            await expect(service.exportData(query)).rejects.toThrow('缺少必填字段');
        });
        it('应该验证字段类型', async () => {
            const invalidData = [{
                    id: '1',
                    createdAt: '2024-01-01',
                    updatedAt: '2024-01-01',
                    monthlyIncome: 5000
                }];
            jest.spyOn(loanApplicationRepository, 'find').mockResolvedValueOnce(invalidData);
            const query = {
                format: 'excel',
                filename: 'test'
            };
            await expect(service.exportData(query)).rejects.toThrow('ID必须是数字类型');
        });
    });
    describe('缓存机制', () => {
        it('应该使用缓存的模板', async () => {
            const query = {
                format: 'excel',
                filename: 'test',
                template: 'test-template'
            };
            const mockTemplate = Buffer.from('test template');
            jest.spyOn(fs, 'readFileSync').mockReturnValueOnce(mockTemplate);
            await service.exportData(query);
            await service.exportData(query);
            expect(fs.readFileSync).toHaveBeenCalledTimes(1);
        });
        it('应该使用缓存的数据', async () => {
            const query = {
                format: 'excel',
                filename: 'test'
            };
            await service.exportData(query);
            await service.exportData(query);
            expect(loanApplicationRepository.find).toHaveBeenCalledTimes(1);
        });
    });
    describe('性能优化', () => {
        it('应该处理大数据量', async () => {
            const largeData = Array(15000).fill(mockLoanApplication);
            jest.spyOn(loanApplicationRepository, 'find').mockResolvedValueOnce(largeData);
            const query = {
                format: 'excel',
                filename: 'test'
            };
            const result = await service.exportData(query);
            expect(result).toBeInstanceOf(Buffer);
        });
        it('应该压缩数据', async () => {
            const data = [{
                    ...mockLoanApplication,
                    nullField: null,
                    undefinedField: undefined
                }];
            jest.spyOn(loanApplicationRepository, 'find').mockResolvedValueOnce(data);
            const query = {
                format: 'excel',
                filename: 'test'
            };
            const result = await service.exportData(query);
            expect(result).toBeInstanceOf(Buffer);
        });
    });
});
//# sourceMappingURL=export.service.spec.js.map