import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuditLog } from '../../entities/audit-log.entity';
import { MonitorService } from '../../services/monitor.service';
import { LoggerService } from '../../services/logger.service';
import { AuditService } from '../../services/audit.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([AuditLog]),
  ],
  providers: [
    MonitorService,
    LoggerService,
    AuditService,
  ],
  exports: [
    MonitorService,
    AuditService,
  ],
})
export class AuditModule {} 