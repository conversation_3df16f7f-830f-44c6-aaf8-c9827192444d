import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Product } from '../entities/product.entity';
import { RiskAssessment } from '../entities/risk-assessment.entity';
import { User } from '../entities/user.entity';
import { MonitoringService } from '../services/monitoring.service';
import { GpuService } from '../services/gpu.service';
import {
  FeatureWeights,
  MatchScoreDetails,
  MatchedProduct,
  ProductMatchCriteria
} from '../interfaces/matching.interface';
import { RiskAssessment } from '../entities/risk-assessment.entity';

@Injectable()
export class OptimizedProductMatchingService {
  private readonly logger = new Logger(OptimizedProductMatchingService.name);
  private readonly BATCH_SIZE = 100;
  private readonly CACHE_TTL = 3600; // 1小时缓存
  
  private readonly MATCH_WEIGHTS = {
    amount: 0.25,    // 金额匹配权重
    term: 0.15,     // 期限匹配权重
    rate: 0.20,     // 利率匹配权重  
    risk: 0.20,     // 风险匹配权重
    features: 0.10,  // 特征匹配权重
    location: 0.10   // 位置匹配权重
  };

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(RiskAssessment)  
    private readonly riskRepository: Repository<RiskAssessment>,
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
    private readonly gpuService: GpuService,
    private readonly monitoringService: MonitoringService
  ) {}

  /**
   * 智能产品匹配主入口
   */
  async matchProducts(user: User, criteria: ProductMatchCriteria): Promise<MatchedProduct[]> {
    const startTime = Date.now();
    this.monitoringService.recordMetric('product_matching_started', 1);

    try {
      // 1. 从缓存获取匹配结果
      const cacheKey = this.generateCacheKey(user.id, criteria);
      const cachedResult = await this.cacheManager.get<MatchedProduct[]>(cacheKey);
      
      if (cachedResult) {
        this.logger.log('从缓存获取匹配结果');
        return cachedResult;
      }

      // 2. 预筛选符合条件的产品
      const products = await this.loadAndPrefilterProducts(criteria);
      
      // 3. 获取用户风险评估
      const riskAssessment = await this.getRiskAssessment(user);

      // 4. 计算匹配度
      let matchedProducts: MatchedProduct[];
      
      if (products.length > this.BATCH_SIZE) {
        // 使用GPU批量计算
        matchedProducts = await this.gpuMatchProducts(user, products, criteria);
      } else {
        // 使用CPU计算
        matchedProducts = await this.cpuMatchProducts(user, products, criteria);
      }

      // 5. 进行后处理
      const finalResults = this.postProcessResults(
        matchedProducts,
        riskAssessment,
        criteria
      );

      // 6. 缓存结果
      await this.cacheManager.set(cacheKey, finalResults, this.CACHE_TTL);

      // 7. 记录性能指标
      const processingTime = Date.now() - startTime;
      this.monitoringService.recordMetric('product_matching_time', processingTime);
      this.monitoringService.recordMetric('matched_products_count', finalResults.length);

      return finalResults;
    } catch (error) {
      this.logger.error('产品匹配失败', error);
      this.monitoringService.recordMetric('product_matching_error', 1);
      throw error;
    }
  }

  /**
   * 预筛选产品
   */
  private async loadAndPrefilterProducts(criteria: ProductMatchCriteria): Promise<Product[]> {
    const startTime = Date.now();
    
    const queryBuilder = this.productRepository.createQueryBuilder('product')
      .where('product.status = :status', { status: 'active' })
      .andWhere('product.minAmount <= :amount', { amount: criteria.amount })
      .andWhere('product.maxAmount >= :amount', { amount: criteria.amount })
      .andWhere('product.minTerm <= :term', { term: criteria.term })
      .andWhere('product.maxTerm >= :term', { term: criteria.term });

    // 添加位置过滤
    if (criteria.location) {
      queryBuilder.andWhere(
        'product.requirements @> :location',
        { location: { location: criteria.location } }
      );
    }

    // 使用索引优化的排序
    queryBuilder
      .orderBy('product.popularityScore', 'DESC')
      .addOrderBy('product.interestRate', 'ASC')
      .take(1000); // 限制最大结果数

    const products = await queryBuilder.getMany();
    
    const processingTime = Date.now() - startTime;
    this.monitoringService.recordMetric('product_prefilter_time', processingTime);
    this.monitoringService.recordMetric('prefiltered_products_count', products.length);

    return products;
  }

  /**
   * CPU匹配产品
   */
  private async cpuMatchProducts(
    user: User,
    products: Product[],
    criteria: ProductMatchCriteria
  ): Promise<MatchedProduct[]> {
    this.monitoringService.recordMetric('cpu_matching_started', 1);
    const startTime = Date.now();

    // 并行处理产品批次
    const batches = this.chunkArray(products, this.BATCH_SIZE);
    const scoredProducts = await Promise.all(
      batches.map(async batch => {
        return Promise.all(
          batch.map(async product => {
            const amountScore = this.calculateAmountScore(product, criteria.amount);
            const termScore = this.calculateTermScore(product, criteria.term);
            const rateScore = this.calculateRateScore(product);
            const featureScore = this.calculateFeatureScore(product, criteria);
            const locationScore = this.calculateLocationScore(product, criteria.location);

            const totalScore = 
              amountScore * this.MATCH_WEIGHTS.amount +
              termScore * this.MATCH_WEIGHTS.term +
              rateScore * this.MATCH_WEIGHTS.rate +
              featureScore * this.MATCH_WEIGHTS.features +
              locationScore * this.MATCH_WEIGHTS.location;

            return {
              product,
              score: totalScore,
              details: {
                amountScore,
                termScore,
                rateScore,
                featureScore,
                locationScore
              }
            };
          })
        );
      })
    );

    const processingTime = Date.now() - startTime;
    this.monitoringService.recordMetric('cpu_processing_time', processingTime);
    
    return scoredProducts.flat().sort((a, b) => b.score - a.score);
  }

  /**
   * GPU匹配产品
   */
  private async gpuMatchProducts(
    user: User,
    products: Product[],
    criteria: ProductMatchCriteria
  ): Promise<MatchedProduct[]> {
    this.monitoringService.recordMetric('gpu_matching_started', 1);
    const startTime = Date.now();

    try {
      // 准备数据为GPU友好格式
      const productTensors = products.map(p => 
        this.gpuService.prepareProductTensor(p)
      );
      const criteriaTensor = this.gpuService.prepareCriteriaTensor(criteria);

      // GPU批处理计算
      const scores = await this.gpuService.batchCalculate(
        productTensors,
        criteriaTensor,
        this.MATCH_WEIGHTS
      );

      // 合并结果
      const results = products.map((product, index) => ({
        product,
        score: scores[index],
        details: this.calculateDetailScores(product, criteria)
      }));

      const processingTime = Date.now() - startTime;
      this.monitoringService.recordMetric('gpu_processing_time', processingTime);
      
      return results.sort((a, b) => b.score - a.score);
    } catch (error) {
      this.logger.error('GPU处理失败,回退到CPU', error);
      this.monitoringService.recordMetric('gpu_processing_error', 1);
      return this.cpuMatchProducts(user, products, criteria);
    }
  }

  /**
   * 后处理匹配结果
   */
  private postProcessResults(
    matchedProducts: MatchedProduct[],
    riskAssessment: RiskAssessment,
    criteria: ProductMatchCriteria
  ): MatchedProduct[] {
    // 根据用户风险等级筛选
    const filteredProducts = matchedProducts.filter(match => {
      const product = match.product;
      return this.isProductSuitableForRisk(product, riskAssessment);
    });

    // 对结果重新排序,考虑转化率和下款率
    return filteredProducts
      .map(match => ({
        ...match,
        score: this.calculateFinalScore(match, riskAssessment)
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 10); // 只返回前10个最佳匹配
  }

  /**
   * 计算最终分数
   */
  private calculateFinalScore(
    match: MatchedProduct,
    riskAssessment: RiskAssessment
  ): number {
    const baseScore = match.score;
    const product = match.product;
    
    // 考虑历史转化率
    const conversionBonus = product.popularityScore * 0.1;
    
    // 考虑风险匹配度
    const riskPenalty = this.calculateRiskPenalty(product, riskAssessment);
    
    return Math.min(1, baseScore + conversionBonus - riskPenalty);
  }

  /**
   * 计算风险惩罚因子
   */
  private calculateRiskPenalty(
    product: Product,
    riskAssessment: RiskAssessment
  ): number {
    // TODO: 基于风险评估和产品风险等级计算惩罚因子
    return 0;
  }

  /**
   * 检查产品是否符合风险评级
   */
  private isProductSuitableForRisk(
    product: Product,
    riskAssessment: RiskAssessment
  ): boolean {
    // TODO: 实现风险匹配逻辑
    return true;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(userId: string, criteria: ProductMatchCriteria): string {
    const criteriaHash = JSON.stringify(criteria);
    return `product_match:${userId}:${criteriaHash}`;
  }

  /**
   * 辅助方法:数组分块
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
