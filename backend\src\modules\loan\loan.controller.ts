import { Controller, Get, Post, Body, Param, Query, UseGuards, Patch } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { LoanService, CreateLoanApplicationDto } from './loan.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('贷款申请')
@Controller('api/loans')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class LoanController {
  constructor(private readonly loanService: LoanService) {}

  @Post()
  @ApiOperation({ summary: '创建贷款申请' })
  @ApiResponse({ status: 201, description: '申请创建成功' })
  async createApplication(@Body() createDto: CreateLoanApplicationDto) {
    const application = await this.loanService.createApplication(createDto);
    return {
      success: true,
      data: application,
      message: '贷款申请创建成功'
    };
  }

  @Get()
  @ApiOperation({ summary: '获取所有贷款申请' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getAllApplications(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10
  ) {
    const result = await this.loanService.getAllApplications(page, limit);
    return {
      success: true,
      data: result.applications,
      pagination: {
        page: result.page,
        limit,
        total: result.total,
        totalPages: result.totalPages
      },
      message: '获取贷款申请列表成功'
    };
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取贷款申请统计' })
  @ApiResponse({ status: 200, description: '获取统计成功' })
  async getStatistics() {
    const stats = await this.loanService.getApplicationStatistics();
    return {
      success: true,
      data: stats,
      message: '获取统计数据成功'
    };
  }

  @Get('user/:userId')
  @ApiOperation({ summary: '获取用户的贷款申请' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUserApplications(@Param('userId') userId: number) {
    const applications = await this.loanService.getApplicationsByUser(userId);
    return {
      success: true,
      data: applications,
      message: '获取用户贷款申请成功'
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '获取贷款申请详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getApplicationById(@Param('id') id: number) {
    const application = await this.loanService.getApplicationById(id);
    return {
      success: true,
      data: application,
      message: '获取贷款申请详情成功'
    };
  }

  @Patch(':id/status')
  @ApiOperation({ summary: '更新申请状态' })
  @ApiResponse({ status: 200, description: '状态更新成功' })
  async updateStatus(
    @Param('id') id: number,
    @Body() updateData: {
      status: string;
      officer_id?: number;
      comment?: string;
    }
  ) {
    const application = await this.loanService.updateApplicationStatus(
      id,
      updateData.status,
      updateData.officer_id,
      updateData.comment
    );
    return {
      success: true,
      data: application,
      message: '申请状态更新成功'
    };
  }

  @Post(':id/approve')
  @ApiOperation({ summary: '批准贷款申请' })
  @ApiResponse({ status: 200, description: '批准成功' })
  async approveApplication(
    @Param('id') id: number,
    @Body() approvalData: {
      approval_amount: number;
      approved_rate: number;
      approved_term: number;
      officer_id: number;
      comment?: string;
    }
  ) {
    const application = await this.loanService.approveApplication(id, approvalData);
    return {
      success: true,
      data: application,
      message: '贷款申请批准成功'
    };
  }

  @Post(':id/reject')
  @ApiOperation({ summary: '拒绝贷款申请' })
  @ApiResponse({ status: 200, description: '拒绝成功' })
  async rejectApplication(
    @Param('id') id: number,
    @Body() rejectionData: {
      rejection_reason: string;
      officer_id: number;
    }
  ) {
    const application = await this.loanService.rejectApplication(id, rejectionData);
    return {
      success: true,
      data: application,
      message: '贷款申请已拒绝'
    };
  }

  @Post(':id/disburse')
  @ApiOperation({ summary: '放款' })
  @ApiResponse({ status: 200, description: '放款成功' })
  async disburseApplication(
    @Param('id') id: number,
    @Body() disbursementData: {
      officer_id: number;
    }
  ) {
    const application = await this.loanService.disburseApplication(id, disbursementData.officer_id);
    return {
      success: true,
      data: application,
      message: '放款成功'
    };
  }

  @Patch(':id/ai-assessment')
  @ApiOperation({ summary: '更新AI评估结果' })
  @ApiResponse({ status: 200, description: 'AI评估更新成功' })
  async updateAIAssessment(
    @Param('id') id: number,
    @Body() aiData: {
      ai_score: number;
      ai_recommendation: string;
      risk_assessment?: any;
    }
  ) {
    const application = await this.loanService.updateAIAssessment(id, aiData);
    return {
      success: true,
      data: application,
      message: 'AI评估结果更新成功'
    };
  }
}
