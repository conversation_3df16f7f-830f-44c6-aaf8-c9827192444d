import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RiskAssessment } from '../../entities/risk-assessment.entity';
import { LoanApplication } from '../../entities/loan-application.entity';
import { RedisService } from '../../services/redis.service';
import { MonitoringService } from '../../services/monitoring.service';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class RiskControlService {
  private readonly logger = new Logger(RiskControlService.name);
  private readonly gpuServiceUrl: string;  private readonly CACHE_TTL = 3600; // 缓存1小时
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000; // 1秒
  private readonly BATCH_SIZE = 32; // GPU批处理大小
  private readonly CACHE_KEYS = {
    RISK_ASSESSMENT: 'risk:assessment:',
    RISK_MODEL: 'risk:model:',
    RISK_RULES: 'risk:rules:'
  };

  constructor(
    @InjectRepository(RiskAssessment)
    private riskRepository: Repository<RiskAssessment>,
    @InjectRepository(LoanApplication)
    private applicationRepository: Repository<LoanApplication>,
    private redisService: RedisService,
    private monitoringService: MonitoringService,
    private configService: ConfigService,
  ) {
    this.gpuServiceUrl = this.configService.get('GPU_SERVICE_URL') || 'http://localhost:3001';
  }
  async assessRisk(applicationId: string): Promise<RiskAssessment> {
    const startTime = Date.now();
    try {
      // 检查缓存
      const cacheKey = `risk:assessment:${applicationId}`;
      const cachedResult = await this.redisService.get(cacheKey);
      if (cachedResult) {
        this.logger.log(`Using cached risk assessment for application ${applicationId}`);
        return JSON.parse(cachedResult);
      }

      const application = await this.applicationRepository.findOne({
        where: { id: applicationId },
        relations: ['user', 'product']
      });

      if (!application) {
        throw new Error(`Application not found: ${applicationId}`);
      }

      // 收集风险评估数据
      const assessmentData = await this.collectRiskData(application);
      
      // 使用GPU加速风控模型，带重试机制
      const riskScore = await this.retryWithFallback(
        () => this.calculateRiskScore(assessmentData),
        async () => this.calculateRiskScoreFallback(assessmentData)
      );

      // 决策结果
      const decision = this.makeDecision(riskScore);      // 创建风控评估结果
      const assessment = new RiskAssessment();
      assessment.applicationId = parseInt(applicationId);
      assessment.riskScore = riskScore;
      assessment.riskFactors = assessmentData;
      assessment.decision = decision;
      assessment.processingTimeMs = Date.now() - startTime;

      // 保存结果
      const savedAssessment = await this.riskRepository.save(assessment);

      // 缓存结果
      await this.redisService.set(cacheKey, JSON.stringify(savedAssessment), this.CACHE_TTL);

      // 记录性能指标
      this.monitoringService.recordMetric('risk_assessment_duration', Date.now() - startTime);
      this.monitoringService.recordMetric('risk_assessment_success', 1);

      return savedAssessment;
    } catch (error) {
      this.logger.error(`Risk assessment failed for application ${applicationId}`, error.stack);
      this.monitoringService.recordMetric('risk_assessment_failure', 1);
      throw error;
    }
  }

  private async retryWithFallback<T>(
    primaryFn: () => Promise<T>,
    fallbackFn: () => Promise<T>,
    retries = this.MAX_RETRIES
  ): Promise<T> {
    for (let i = 0; i < retries; i++) {
      try {
        return await primaryFn();
      } catch (error) {
        this.logger.warn(`Attempt ${i + 1} failed: ${error.message}`);
        if (i === retries - 1) {
          this.logger.warn('Falling back to backup method');
          return fallbackFn();
        }
        await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY * (i + 1)));
      }
    }
    throw new Error('All retry attempts failed');
  }

  private async calculateRiskScoreFallback(data: any): Promise<number> {
    // CPU fallback实现简单风险评分
    this.logger.log('Using CPU fallback for risk calculation');
    const weights = {
      credit_score: 0.4,
      debt_ratio: 0.3,
      payment_history: 0.3
    };

    return (
      data.user.credit_score * weights.credit_score +
      (1 - data.loan.debt_to_income_ratio) * weights.debt_ratio +
      (data.behavior.payment_history_score || 0.5) * weights.payment_history
    ) * 100;
  }
  private async collectRiskData(application: LoanApplication): Promise<any> {
    // 扩展数据收集范围
    const [userFinancialData, creditHistory, behavioralData] = await Promise.all([
      this.getUserFinancialData(application.userId),
      this.getCreditHistory(application.userId),
      this.getBehavioralData(application.userId)
    ]);

    return {
      user: {
        credit_score: application.creditScore,
        financial_data: userFinancialData,
        credit_history: creditHistory
      },
      loan: {
        amount: application.amount,
        term_months: application.term, // 使用 term 而不是 term_months
        debt_to_income_ratio: application.debtToIncomeRatio,
        purpose: application.purpose
      },
      market: await this.getMarketRiskFactors(),
      behavior: behavioralData
    };
  }

  private async calculateRiskScore(data: any): Promise<number> {
    const startTime = Date.now();
    const cacheKey = `${this.CACHE_KEYS.RISK_MODEL}${data.application.id}`;
    
    try {
      // 检查缓存
      const cachedScore = await this.redisService.get(cacheKey);
      if (cachedScore) {
        this.monitoringService.recordMetric('risk_score_cache_hit', 1);
        return Number(cachedScore);
      }

      // 获取市场风险因子
      const marketRiskFactors = await this.getMarketRiskFactors();
      
      // 构建评分数据
      const scoringData = {
        ...data,
        marketFactors: marketRiskFactors,
        timestamp: Date.now()
      };

      // GPU加速风险评估
      const response = await axios.post(`${this.gpuServiceUrl}/risk-assessment`, {
        data: scoringData,
        use_gpu: true,
        batch_size: this.BATCH_SIZE,
        model_version: 'v2'
      });

      const riskScore = response.data.risk_score;
      
      // 缓存结果
      await this.redisService.set(cacheKey, riskScore.toString(), this.CACHE_TTL);
      
      this.monitoringService.recordMetric('gpu_risk_calculation_time', Date.now() - startTime);
      this.monitoringService.recordSuccess('risk_score_calculation');
      
      return riskScore;
    } catch (error) {
      this.monitoringService.recordError('risk_score_calculation', error);
      // 使用备用评分模型
      return this.calculateRiskScoreFallback(data);
    }
  }
  private makeDecision(riskScore: number): string {
    if (riskScore <= 20) {
      return 'APPROVED';
    } else if (riskScore <= 50) {
      return 'MANUAL_REVIEW';
    } else {
      return 'REJECTED';
    }
  }

  // 获取市场风险因子
  private async getMarketRiskFactors(): Promise<any> {
    const cacheKey = `${this.CACHE_KEYS.RISK_RULES}market_factors`;
    try {
      // 检查缓存
      const cached = await this.redisService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // 从外部API获取最新市场风险数据
      const response = await axios.get(`${this.gpuServiceUrl}/market-risk`);
      const marketFactors = response.data;

      // 缓存结果 (15分钟)
      await this.redisService.set(cacheKey, JSON.stringify(marketFactors), 900);

      return marketFactors;
    } catch (error) {
      this.logger.warn('获取市场风险因子失败,使用默认值', error);
      // 返回默认风险因子
      return {
        marketVolatility: 0.5,
        interestRateRisk: 0.3,
        industryRisk: 0.4,
        economicCycleRisk: 0.6
      };
    }
  }
  async getRiskAssessment(applicationId: number): Promise<RiskAssessment> {
    return this.riskRepository.findOne({ where: { applicationId: applicationId } });
  }

  async updateRiskFactors(assessmentId: number, newFactors: any): Promise<RiskAssessment> {
    const assessment = await this.riskRepository.findOne({ where: { id: assessmentId } });
    assessment.riskFactors = { ...assessment.riskFactors, ...newFactors };
    return this.riskRepository.save(assessment);
  }

  // 添加缺失的方法
  private async getUserFinancialData(userId: string): Promise<any> {
    try {
      // 这里应该从用户财务数据表或外部服务获取数据
      // 为了演示，返回模拟数据
      return {
        annualIncome: 500000,
        monthlyIncome: 41667,
        currentDebt: 50000,
        assets: 200000,
        employmentType: 'FULL_TIME',
        workExperience: 5
      };
    } catch (error: any) {
      this.logger.error(`获取用户财务数据失败: ${error.message}`);
      return {};
    }
  }

  private async getCreditHistory(userId: string): Promise<any> {
    try {
      // 这里应该从信用历史表或征信机构获取数据
      // 为了演示，返回模拟数据
      return {
        paymentHistory: 'GOOD',
        numberOfAccounts: 5,
        totalCreditLimit: 100000,
        creditUtilization: 0.3,
        defaultHistory: false,
        bankruptcyHistory: false
      };
    } catch (error: any) {
      this.logger.error(`获取信用历史失败: ${error.message}`);
      return {};
    }
  }

  private async getBehavioralData(userId: string): Promise<any> {
    try {
      // 这里应该从用户行为数据表获取数据
      // 为了演示，返回模拟数据
      return {
        loginFrequency: 15,
        applicationCompletionRate: 0.8,
        documentUploadSpeed: 'FAST',
        paymentHistoryScore: 0.85,
        socialMediaScore: 0.7
      };
    } catch (error: any) {
      this.logger.error(`获取行为数据失败: ${error.message}`);
      return {};
    }
  }
}
