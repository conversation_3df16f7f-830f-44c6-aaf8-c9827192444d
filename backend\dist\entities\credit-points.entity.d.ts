import { User } from './user.entity';
export declare class CreditPoints {
    id: number;
    userId: string;
    user: User;
    score: number;
    factors: {
        paymentHistory: number;
        creditUtilization: number;
        creditHistory: number;
        creditMix: number;
        newCredit: number;
    };
    history: Array<{
        date: Date;
        score: number;
        change: number;
        reason: string;
        details?: Record<string, any>;
    }>;
    metadata: {
        lastUpdated: Date;
        nextUpdate: Date;
        dataSource: string;
        version: string;
    };
    createdAt: Date;
    updatedAt: Date;
}
