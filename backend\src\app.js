﻿// src/app.js
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// 加载环境变量
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 8000;

// 中间件
app.use(helmet());
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourdomain.com'] 
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}));

app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// API路由
app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'SmartLoan API 运行正常',
    timestamp: new Date().toISOString()
  });
});

// 2025年最新金融产品数据
const financialProducts2025 = [
  {
    id: 1,
    name: '工商银行融e借2025版',
    provider: '中国工商银行',
    product_type: 'personal',
    interest_rate: 3.85,
    amount_min: 1000,
    amount_max: 800000,
    loan_term_min: 1,
    loan_term_max: 60,
    description: '2025年全新升级，AI智能审批，30秒放款，支持数字人民币',
    features: {
      fast_approval: true,
      online_application: true,
      digital_currency: true,
      ai_approval: true
    },
    requirements: {
      min_age: 18,
      max_age: 65,
      min_income: 3000,
      credit_score_min: 600
    }
  },
  {
    id: 2,
    name: '建设银行快贷Pro 2025',
    provider: '中国建设银行',
    product_type: 'personal',
    interest_rate: 3.95,
    amount_min: 1000,
    amount_max: 500000,
    loan_term_min: 1,
    loan_term_max: 60,
    description: '2025年全新升级，支持元宇宙场景，区块链征信，智能风控',
    features: {
      fast_approval: true,
      blockchain_credit: true,
      metaverse_support: true
    },
    requirements: {
      min_age: 18,
      max_age: 70,
      min_income: 2500,
      credit_score_min: 580
    }
  },
  {
    id: 3,
    name: '蚂蚁借呗升级版2025',
    provider: '蚂蚁金服',
    product_type: 'personal',
    interest_rate: 4.2,
    amount_min: 500,
    amount_max: 300000,
    loan_term_min: 1,
    loan_term_max: 12,
    description: '基于大数据风控，支持Web3.0身份认证，碳中和绿色金融',
    features: {
      fast_approval: true,
      web3_identity: true,
      green_finance: true
    },
    requirements: {
      min_age: 18,
      max_age: 65,
      min_income: 1500,
      credit_score_min: 520
    }
  }
];

// 智能产品匹配API
app.post('/api/products/match/smart', (req, res) => {
  const { amount, term_months, product_type, user_profile } = req.body;

  // AI智能匹配算法
  const matches = financialProducts2025
    .filter(product => product.product_type === product_type)
    .map(product => {
      let score = 0;
      let reasoning = [];

      // 金额匹配度 (30%)
      if (amount >= product.amount_min && amount <= product.amount_max) {
        score += 30;
        reasoning.push('贷款金额符合要求');
      }

      // 用户资质匹配度 (40%)
      if (user_profile.credit_score >= product.requirements.credit_score_min) {
        score += 20;
        reasoning.push('信用分数优秀');
      }
      if (user_profile.income >= product.requirements.min_income) {
        score += 20;
        reasoning.push('收入水平良好');
      }

      // 产品特性加分 (30%)
      if (product.features.fast_approval) {
        score += 10;
        reasoning.push('支持快速审批');
      }
      if (product.features.ai_approval) {
        score += 10;
        reasoning.push('AI智能审批');
      }
      if (product.features.digital_currency) {
        score += 10;
        reasoning.push('支持数字人民币');
      }

      // 计算推荐利率
      let recommended_rate = product.interest_rate;
      if (user_profile.credit_score >= 750) recommended_rate *= 0.95;
      else if (user_profile.credit_score >= 700) recommended_rate *= 0.98;

      return {
        product,
        match_score: score / 100,
        ai_reasoning: reasoning.join('，'),
        recommended_amount: Math.min(amount, product.amount_max),
        recommended_rate: Math.round(recommended_rate * 100) / 100
      };
    })
    .filter(match => match.match_score > 0.3)
    .sort((a, b) => b.match_score - a.match_score)
    .slice(0, 3);

  res.json({
    success: true,
    data: matches,
    message: '智能产品匹配成功',
    total: matches.length,
    timestamp: new Date().toISOString()
  });
});

// 传统产品匹配API (兼容)
app.get('/api/products/match/:userId', (req, res) => {
  const { userId } = req.params;
  res.json({
    userId: parseInt(userId),
    matches: financialProducts2025.slice(0, 3).map(product => ({
      id: product.id,
      name: product.name,
      provider: product.provider,
      interestRate: product.interest_rate,
      maxAmount: product.amount_max,
      matchScore: 85,
      aiReasoning: '基于2025年最新AI算法推荐'
    }))
  });
});

// 2025年AI风险评估API
app.post('/api/ai/risk-assessment', (req, res) => {
  const { user_profile, loan_application, financial_data } = req.body;

  // 2025年最新AI风控算法
  let riskScore = 50; // 基础分数
  let riskFactors = [];

  // 信用分数影响 (35%)
  if (financial_data.credit_score >= 750) {
    riskScore += 25;
  } else if (financial_data.credit_score >= 700) {
    riskScore += 15;
  } else if (financial_data.credit_score < 600) {
    riskScore -= 20;
    riskFactors.push('信用分数较低');
  }

  // 收入稳定性 (25%)
  if (user_profile.employment_type === 'full_time') {
    riskScore += 20;
  } else if (user_profile.employment_type === 'gig_economy') {
    riskScore += 10; // 2025年零工经济被认可
  } else if (user_profile.employment_type === 'freelance') {
    riskScore += 5;
  }

  // 债务收入比 (20%)
  const debtToIncomeRatio = financial_data.liabilities / financial_data.monthly_income;
  if (debtToIncomeRatio > 0.5) {
    riskScore -= 15;
    riskFactors.push('债务收入比过高');
  } else if (debtToIncomeRatio < 0.3) {
    riskScore += 15;
  }

  // 2025年新增：数字资产评估 (10%)
  if (financial_data.digital_assets) {
    riskScore += 10;
    riskFactors.push('拥有数字资产');
  }

  // 2025年新增：ESG评分 (10%)
  if (financial_data.esg_score && financial_data.esg_score > 70) {
    riskScore += 10;
    riskFactors.push('ESG评分优秀');
  }

  // 确定风险等级
  let riskLevel = 'medium';
  if (riskScore >= 80) riskLevel = 'low';
  else if (riskScore <= 40) riskLevel = 'high';

  const assessment = {
    risk_score: Math.max(0, Math.min(100, riskScore)),
    risk_level: riskLevel,
    risk_factors: riskFactors,
    recommendation: generateRiskRecommendation(riskScore, riskFactors),
    analysis_timestamp: new Date().toISOString(),
    ai_version: '2025.1.0',
    details: {
      credit_score_impact: financial_data.credit_score >= 700 ? 'positive' : 'negative',
      employment_stability: user_profile.employment_type === 'full_time' ? 'stable' : 'variable',
      debt_to_income_ratio: debtToIncomeRatio,
      digital_readiness: financial_data.digital_assets ? 'high' : 'medium'
    }
  };

  res.json({
    success: true,
    data: assessment,
    message: 'AI风险评估完成'
  });
});

// AI顾问对话API
app.post('/api/ai/advisor/chat', (req, res) => {
  const { query, context } = req.body;

  // 2025年Fin-R1大模型模拟响应
  const responses = {
    '贷款利率': '2025年央行基准利率下调后，个人信用贷款利率普遍在3.8%-6.5%之间，具体利率根据您的信用状况、数字资产和ESG评分确定。',
    '申请条件': '2025年贷款申请更加便民：年满18周岁、稳定收入（包括零工经济）、良好信用记录。支持Web3.0身份认证和数字人民币。',
    '还款方式': '支持传统还款方式外，新增数字人民币自动还款、智能合约还款、碳积分抵扣等创新方式。',
    '审批时间': '采用2025年最新AI技术，结合区块链征信，最快30秒完成审批，平均审批时间缩短至2小时。',
    '数字货币': '全面支持数字人民币(CBDC)，享受更低手续费和更快到账速度。',
    '元宇宙': '支持元宇宙场景消费贷款，包括虚拟房产、NFT抵押、虚拟世界创业贷款等。',
    '绿色金融': '提供碳中和贷款产品，支持新能源、环保项目，享受利率优惠和碳积分奖励。'
  };

  let response = '您好！我是SmartLoan 2025年AI金融顾问。我可以为您介绍最新的贷款利率、申请条件、数字货币支付、元宇宙金融、绿色金融等服务。请问您想了解什么？';

  for (const [keyword, answer] of Object.entries(responses)) {
    if (query.includes(keyword)) {
      response = answer;
      break;
    }
  }

  res.json({
    success: true,
    data: {
      response,
      confidence: 0.95,
      timestamp: new Date().toISOString(),
      ai_model: 'Fin-R1-2025',
      suggestions: [
        '了解2025年最新贷款产品',
        '数字人民币贷款服务',
        '元宇宙金融解决方案',
        '绿色金融产品',
        '联系人工专家'
      ]
    },
    message: 'AI顾问响应成功'
  });
});

// OCR识别API
app.post('/api/ai/ocr', (req, res) => {
  // 模拟2025年最新OCR识别结果
  const mockOcrResult = {
    type: 'ID_CARD',
    confidence: 0.98,
    extracted_data: {
      name: '张三',
      id_number: '110101199001011234',
      address: '北京市朝阳区xxx街道',
      issue_date: '2020-01-01',
      expiry_date: '2030-01-01'
    },
    ai_analysis: {
      document_quality: 'excellent',
      authenticity_score: 0.96,
      risk_indicators: []
    },
    processing_time: '0.3s',
    ai_version: '2025.1.0'
  };

  res.json({
    success: true,
    data: mockOcrResult,
    message: '证件识别成功'
  });
});

// 活体检测API
app.post('/api/ai/liveness', (req, res) => {
  // 模拟2025年最新活体检测结果
  const mockLivenessResult = {
    is_live: Math.random() > 0.1, // 90%通过率
    confidence: Math.random() * 0.3 + 0.7,
    face_detected: true,
    quality_score: Math.random() * 0.2 + 0.8,
    analysis: {
      blink_detected: true,
      head_movement: true,
      expression_change: true,
      depth_analysis: true // 2025年新增3D深度分析
    },
    ai_version: '2025.1.0',
    processing_time: '0.5s'
  };

  res.json({
    success: true,
    data: mockLivenessResult,
    message: '活体检测完成'
  });
});

// 风险推荐生成函数
function generateRiskRecommendation(riskScore, riskFactors) {
  if (riskScore >= 80) {
    return '风险较低，建议批准贷款申请。可享受优惠利率和数字人民币便利服务。';
  } else if (riskScore >= 60) {
    return '风险中等，建议进一步审核后决定。可考虑提供额外担保或降低贷款金额。';
  } else if (riskScore >= 40) {
    return '风险较高，建议谨慎审核。可通过提升ESG评分或增加数字资产来改善风险等级。';
  } else {
    return '风险很高，建议暂缓批准。建议客户先改善信用状况或寻求担保。';
  }
}

// 兼容旧版风险评估API
app.post('/api/evaluation/loan', (req, res) => {
  const { amount, term, income, creditScore } = req.body;

  const score = Math.min(100, Math.max(0,
    (creditScore / 850) * 40 +
    (income / 50000) * 30 +
    (1 - amount / 1000000) * 30
  ));

  res.json({
    score: Math.round(score),
    recommendation: score > 70 ? '建议批准' : score > 50 ? '谨慎批准' : '建议拒绝',
    riskLevel: score > 70 ? 'low' : score > 50 ? 'medium' : 'high',
    approvalProbability: score / 100,
    version: '2025.1.0'
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('Error:', err.stack);
  res.status(500).json({
    error: process.env.NODE_ENV === 'production' 
      ? '服务器内部错误' 
      : err.message
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({ error: '接口未找到' });
});

// 启动服务器
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(` SmartLoan Backend API 服务已启动`);
  console.log(` 服务地址: http://localhost:${PORT}`);
  console.log(` 环境: ${process.env.NODE_ENV || 'development'}`);
  console.log(` 时间: ${new Date().toLocaleString('zh-CN')}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

export default app;
