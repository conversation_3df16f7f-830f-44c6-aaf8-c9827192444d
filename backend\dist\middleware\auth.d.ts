/// <reference types="passport" />
import { Request, Response, NextFunction } from 'express';
export interface JwtPayload {
    sub: string;
    username: string;
    roles: string[];
}
declare global {
    namespace Express {
        interface Request {
            user?: User;
        }
    }
}
export declare const auth: (req: Request, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>>>;
