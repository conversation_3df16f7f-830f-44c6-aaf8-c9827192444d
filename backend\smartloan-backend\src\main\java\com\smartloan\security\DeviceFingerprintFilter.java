package com.smartloan.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeviceFingerprintFilter extends OncePerRequestFilter {

    private final DeviceFingerprintService deviceFingerprintService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        
        String deviceFingerprint = request.getHeader("X-Device-Fingerprint");
        
        if (deviceFingerprint != null) {
            try {
                deviceFingerprintService.validateDeviceFingerprint(deviceFingerprint, request);
                log.debug("Device fingerprint validation successful");
            } catch (Exception e) {
                log.error("Device fingerprint validation failed: {}", e.getMessage());
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return;
            }
        }

        filterChain.doFilter(request, response);
    }
}
