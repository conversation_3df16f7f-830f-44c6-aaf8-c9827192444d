export interface LoanCalculationParams {
    loanType: '公积金贷款' | '商业贷款' | '组合贷款';
    totalAmount?: number;
    commercialAmount?: number;
    providentAmount?: number;
    commercialRate?: number;
    providentRate?: number;
    loanTerm: number;
    repaymentMethod: '等额本息' | '等额本金' | '先息后本';
}
export interface MonthlyPayment {
    month: number;
    principal: number;
    interest: number;
    totalPayment: number;
    remainingPrincipal: number;
}
export interface LoanCalculationResult {
    monthlyPayments: MonthlyPayment[];
    totalInterest: number;
    totalPayment: number;
    monthlyPaymentAmount: number;
    summary: {
        loanAmount: number;
        interestRate: number;
        loanTerm: number;
        repaymentMethod: string;
    };
}
export declare class LoanCalculatorService {
    private readonly logger;
    private readonly interestRates;
    calculateLoan(params: LoanCalculationParams): Promise<LoanCalculationResult>;
    private calculateProvidentFundLoan;
    private calculateCommercialLoan;
    private calculateCombinedLoan;
    private performCalculation;
    private calculateEqualPayment;
    private calculateEqualPrincipal;
    private calculateInterestFirst;
    private getCommercialRate;
    private getProvidentRate;
}
