{"$schema": "https://json.schemastore.org/package.json", "name": "smartloan-backend", "version": "1.0.0", "description": "SmartLoan Backend API Service", "main": "src/app.js", "type": "module", "scripts": {"start": "node src/app.js", "dev": "nodemon --experimental-modules src/app.js", "build": "tsc", "test": "jest", "lint": "eslint src --ext .js,.ts", "migrate": "node src/migrations/migrate.js"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.2", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@types/multer": "^1.4.12", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "cache-manager": "^6.4.3", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^6.0.1", "ioredis": "^5.3.2", "joi": "^17.7.1", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "nodemailer": "^7.0.3", "pg": "^8.9.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "sharp": "^0.34.2", "tesseract.js": "^6.0.1", "typeorm": "^0.3.24", "uuid": "^9.0.0", "winston": "^3.8.2", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@testing-library/react": "^16.3.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.11", "@types/node": "^18.14.2", "jest": "^29.7.0", "jsdom": "^26.1.0", "nodemon": "^2.0.22", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "typescript": "^4.9.5", "vitest": "^3.1.4"}}