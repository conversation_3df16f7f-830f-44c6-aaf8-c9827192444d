# SmartLoan 后端服务启动脚本 (修复版)
Write-Host "🚀 启动 SmartLoan 后端服务..." -ForegroundColor Green

# 获取当前脚本目录
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$backendDir = Join-Path $scriptDir "backend"

Write-Host "📍 脚本目录: $scriptDir" -ForegroundColor Cyan
Write-Host "📍 后端目录: $backendDir" -ForegroundColor Cyan

# 检查后端目录是否存在
if (Test-Path $backendDir) {
    Write-Host "✅ 后端目录存在" -ForegroundColor Green
    
    # 进入后端目录
    Set-Location $backendDir
    Write-Host "📂 已进入后端目录: $(Get-Location)" -ForegroundColor Cyan
    
    # 检查Node.js
    try {
        $nodeVersion = node --version
        Write-Host "✅ Node.js 版本: $nodeVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ Node.js 未安装" -ForegroundColor Red
        exit 1
    }
    
    # 检查简化服务器文件
    if (Test-Path "simple-server.js") {
        Write-Host "✅ 发现简化服务器文件" -ForegroundColor Green
        
        Write-Host "🚀 启动简化后端服务器..." -ForegroundColor Yellow
        node simple-server.js
        
    } else {
        Write-Host "⚠️ 简化服务器文件不存在，创建中..." -ForegroundColor Yellow
        
        # 创建简化服务器
        $serverContent = @"
// SmartLoan 2025 简化版后端服务
const http = require('http');
const url = require('url');

const PORT = 3001;

// 2025年最新金融产品数据
const products2025 = [
  {
    id: 1,
    name: '工商银行融e借2025版',
    provider: '中国工商银行',
    interest_rate: 3.85,
    amount_max: 800000,
    description: '2025年全新升级，AI智能审批，支持数字人民币'
  },
  {
    id: 2,
    name: '建设银行快贷Pro 2025',
    provider: '中国建设银行',
    interest_rate: 3.95,
    amount_max: 500000,
    description: '支持元宇宙场景，区块链征信'
  },
  {
    id: 3,
    name: '蚂蚁借呗升级版2025',
    provider: '蚂蚁金服',
    interest_rate: 4.2,
    amount_max: 300000,
    description: '支持Web3.0身份认证，绿色金融'
  }
];

// 创建服务器
const server = http.createServer((req, res) => {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');

  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;

  console.log(`${req.method} ${path}`);

  // 健康检查
  if (path === '/api/health') {
    res.writeHead(200);
    res.end(JSON.stringify({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'SmartLoan Backend API 2025',
      version: '2025.1.0'
    }));
    return;
  }

  // 智能产品匹配
  if (path === '/api/products/match/smart' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        const matches = products2025.map((product, index) => ({
          product,
          match_score: 0.9 - index * 0.1,
          ai_reasoning: `基于2025年AI算法，${product.name}非常适合您的需求`,
          recommended_amount: Math.min(data.amount || 100000, product.amount_max),
          recommended_rate: product.interest_rate
        }));

        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: matches,
          message: '智能产品匹配成功',
          total: matches.length
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ error: '请求数据格式错误' }));
      }
    });
    return;
  }

  // 404处理
  res.writeHead(404);
  res.end(JSON.stringify({ error: 'API接口未找到' }));
});

// 启动服务器
server.listen(PORT, () => {
  console.log('🚀 SmartLoan 2025 Backend API 已启动');
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
  console.log('🎯 核心功能: AI智能匹配、多模态审核、实时风控');
});
"@
        
        $serverContent | Out-File -FilePath "simple-server.js" -Encoding UTF8
        Write-Host "✅ 简化服务器文件已创建" -ForegroundColor Green
        
        Write-Host "🚀 启动简化后端服务器..." -ForegroundColor Yellow
        node simple-server.js
    }
    
} else {
    Write-Host "❌ 后端目录不存在: $backendDir" -ForegroundColor Red
    Write-Host "📂 当前目录内容:" -ForegroundColor Yellow
    Get-ChildItem -Name
    exit 1
}
