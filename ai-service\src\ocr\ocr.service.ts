import { Injectable, Logger } from '@nestjs/common';
import { createWorker, Worker, WorkerOptions } from 'tesseract.js';
import sharp from 'sharp';
import { ConfigService } from '@nestjs/config';

interface OcrResult {
  type: 'ID_CARD' | 'BUSINESS_LICENSE';
  id_number?: string;
  name?: string;
  credit_code?: string;
  company_name?: string;
  raw_text: string;
}

@Injectable()
export class OcrService {
  private readonly gpuEnabled: boolean;
  private readonly logger = new Logger(OcrService.name);

  constructor(private configService: ConfigService) {
    this.gpuEnabled = this.configService.get('USE_GPU') === 'true';
  }
  async processImage(imageBuffer: Buffer, documentType: string): Promise<OcrResult> {
    this.logger.debug(`开始处理${documentType}类型的图片`);
    
    // 预处理图像
    const processedImage = await this.preprocessImage(imageBuffer);
    
    // 创建OCR worker
    const worker = await createWorker('chi_sim+eng');

    try {
      // @ts-ignore - tesseract.js 类型定义不完整
      await worker.loadLanguage('chi_sim+eng');
      // @ts-ignore - tesseract.js 类型定义不完整
      await worker.initialize('chi_sim+eng');
      
      // 根据文档类型设置识别参数
      if (documentType === 'ID_CARD') {
        // @ts-ignore - tesseract.js 类型定义不完整
        await worker.setParameters({
          tessedit_char_whitelist: '0123456789Xx',
        });
      }

      // 执行OCR
      const result = await worker.recognize(processedImage);
      await worker.terminate();

      // 解析结果
      const parsedResult = this.parseOcrResult(result.data.text, documentType);
      this.logger.debug(`成功识别${documentType}类型的图片`);
      return parsedResult;
    } catch (error: any) {
      console.error('OCR处理失败:', error);
      throw new Error(`OCR处理失败: ${error.message}`);
    }
  }

  private async preprocessImage(imageBuffer: Buffer): Promise<Buffer> {
    return sharp(imageBuffer)
      .resize(2048, null, { withoutEnlargement: true })
      .normalize()
      .sharpen()
      .toBuffer();
  }

  private parseOcrResult(text: string, documentType: string): OcrResult {    switch (documentType) {
      case 'ID_CARD':
        return this.parseIdCard(text);
      case 'BUSINESS_LICENSE':
        return this.parseBusinessLicense(text);
      default:
        return { type: 'ID_CARD', raw_text: text } as OcrResult;
    }
  }
  private parseIdCard(text: string): OcrResult {
    const idNumberMatch = text.match(/\d{17}[\dXx]/);
    const nameMatch = text.match(/姓名[\s\S]*?([\u4e00-\u9fa5]{2,4})/);

    return {
      type: 'ID_CARD',
      id_number: idNumberMatch ? idNumberMatch[0] : undefined,
      name: nameMatch ? nameMatch[1] : undefined,
      raw_text: text,
    };
  }
  private parseBusinessLicense(text: string): OcrResult {
    const creditCodeMatch = text.match(/统一社会信用代码[\s\S]*?([0-9A-Z]{18})/);
    const companyMatch = text.match(/名称[\s\S]*?([\u4e00-\u9fa5]{2,50})/);

    return {
      type: 'BUSINESS_LICENSE',
      credit_code: creditCodeMatch ? creditCodeMatch[1] : undefined,
      company_name: companyMatch ? companyMatch[1] : undefined,
      raw_text: text,
    };
  }
}
