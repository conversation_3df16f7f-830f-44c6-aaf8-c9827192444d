/// <reference types="node" />
/// <reference types="node" />
import { ConfigService } from '@nestjs/config';
import { LoggerService } from './logger.service';
import { ErrorHandler } from '../utils/error-handler';
import { MonitorService } from './monitor.service';
import { CacheService } from './cache.service';
export declare class FaceVerificationService {
    private readonly configService;
    private readonly logger;
    private readonly errorHandler;
    private readonly monitorService;
    private readonly cacheService;
    private modelsLoaded;
    constructor(configService: ConfigService, logger: LoggerService, errorHandler: ErrorHandler, monitorService: MonitorService, cacheService: CacheService);
    private loadModels;
    verifyFace(imageBuffer: Buffer): Promise<{
        detection: {
            faceFound: boolean;
            faceCount: number;
            confidence: number;
            landmarks: {};
        };
        liveness: {
            isLive: boolean;
            confidence: number;
        };
        comparison: {
            isMatch: boolean;
            confidence: number;
        };
        isVerified: boolean;
    }>;
    private detectFace;
    detectLiveness(imageBuffer: Buffer): Promise<{
        isLive: boolean;
        confidence: number;
    }>;
    private extractFeatures;
    private compareFaces;
    private preprocessImage;
    verifyFaceFromFile(filePath: string): Promise<{
        detection: {
            faceFound: boolean;
            faceCount: number;
            confidence: number;
            landmarks: {};
        };
        liveness: {
            isLive: boolean;
            confidence: number;
        };
        comparison: {
            isMatch: boolean;
            confidence: number;
        };
        isVerified: boolean;
    }>;
    processFile(filePath: string): Promise<void>;
    detectFaces(imageBuffer: Buffer): Promise<any>;
    private bufferToImage;
}
