import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from './logger.service';
import { <PERSON>rror<PERSON>and<PERSON> } from '../utils/error-handler';
import { MonitorService } from './monitor.service';
import { CacheService } from './cache.service';
import * as tf from '@tensorflow/tfjs';
import * as faceapi from 'face-api.js';
import * as fs from 'fs';

@Injectable()
export class FaceVerificationService {
  private modelsLoaded = false;

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
    private readonly errorHandler: <PERSON>rrorHandler,
    private readonly monitorService: MonitorService,
    private readonly cacheService: CacheService
  ) {
    this.loadModels();
  }

  private async loadModels() {
    try {
      await faceapi.nets.ssdMobilenetv1.loadFromDisk('models');
      await faceapi.nets.faceLandmark68Net.loadFromDisk('models');
      await faceapi.nets.faceRecognitionNet.loadFromDisk('models');
      this.modelsLoaded = true;
    } catch (error) {
      this.logger.error('加载人脸识别模型失败', error);
      throw error;
    }
  }

  async verifyFace(imageBuffer: Buffer) {
    try {
      this.monitorService.startOperation('face_verification');
      
      if (!this.modelsLoaded) {
        throw new Error('人脸识别模型未加载');
      }

      // 人脸检测
      const detection = await this.detectFace(imageBuffer);
      
      // 活体检测
      const liveness = await this.detectLiveness(imageBuffer);
      
      // 人脸特征提取
      const features = await this.extractFeatures(imageBuffer);
      
      // 人脸比对
      const comparison = await this.compareFaces(features);
      
      this.monitorService.endOperation('face_verification');
      
      return {
        detection,
        liveness,
        comparison,
        isVerified: liveness.isLive && comparison.isMatch
      };
    } catch (error) {
      this.logger.error('人脸验证失败', error);
      throw this.errorHandler.handle(error);
    }
  }

  private async detectFace(imageBuffer: Buffer) {
    try {
      const blob = new Blob([imageBuffer], { type: 'image/jpeg' });
      const image = await faceapi.bufferToImage(blob);
      const detections = await faceapi.detectAllFaces(image)
        .withFaceLandmarks()
        .withFaceDescriptors();

      if (detections.length === 0) {
        throw new Error('未检测到人脸');
      }

      if (detections.length > 1) {
        throw new Error('检测到多个人脸');
      }

      return {
        faceFound: true,
        faceCount: detections.length,
        confidence: detections[0].detection.score,
        landmarks: detections[0].landmarks
      };
    } catch (error) {
      this.logger.error('人脸检测失败', error);
      throw error;
    }
  }

  async detectLiveness(imageBuffer: Buffer) {
    try {
      // 使用TensorFlow.js进行活体检测
      const model = await tf.loadLayersModel('file://models/liveness/model.json');
      
      const blob = new Blob([imageBuffer], { type: 'image/jpeg' });
      const image = await this.preprocessImage(blob);
      const prediction = await model.predict(image) as tf.Tensor;
      const score = await prediction.data();
      
      return {
        isLive: score[0] > 0.5,
        confidence: score[0]
      };
    } catch (error) {
      this.logger.error('活体检测失败', error);
      throw error;
    }
  }

  private async extractFeatures(imageBuffer: Buffer) {
    try {
      const blob = new Blob([imageBuffer], { type: 'image/jpeg' });
      const image = await faceapi.bufferToImage(blob);
      const detection = await faceapi.detectSingleFace(image)
        .withFaceLandmarks()
        .withFaceDescriptor();

      if (!detection) {
        throw new Error('无法提取人脸特征');
      }

      return detection.descriptor;
    } catch (error) {
      this.logger.error('特征提取失败', error);
      throw error;
    }
  }

  private async compareFaces(features: Float32Array) {
    try {
      // 从缓存中获取已存储的人脸特征
      const storedFeatures = await this.cacheService.get('face_features') as Float32Array | null;

      if (!storedFeatures) {
        throw new Error('未找到已存储的人脸特征');
      }

      // 计算特征向量之间的欧氏距离
      const distance = faceapi.euclideanDistance(features, storedFeatures);

      return {
        isMatch: distance < 0.6,
        confidence: 1 - distance
      };
    } catch (error) {
      this.logger.error('人脸比对失败', error);
      throw error;
    }
  }

  private async preprocessImage(imageBuffer: Buffer) {
    try {
      // 图像预处理
      const image = await tf.node.decodeImage(imageBuffer);
      const resized = tf.image.resizeBilinear(image, [224, 224]);
      const normalized = resized.div(255.0);
      const batched = normalized.expandDims(0);

      return batched;
    } catch (error) {
      this.logger.error('图像预处理失败', error);
      throw error;
    }
  }

  async verifyFaceFromFile(filePath: string) {
    try {
      const buffer = await fs.promises.readFile(filePath);
      return await this.verifyFace(buffer);
    } catch (error) {
      this.logger.error('从文件进行人脸验证失败', error);
      throw this.errorHandler.handle(error);
    }
  }
  // 封装异步函数解决顶级 await 问题
  async processFile(filePath: string) {
    try {
      const buffer = await fs.promises.readFile(filePath);
      // 假设这里是获取特征向量的函数，需确保 getFeatures 函数存在
      const getFeatures = async () => {
        // 实现获取特征向量的逻辑
        return [1, 2, 3];
      };
      const unknownFeatures: unknown = await getFeatures();
      const features: number[] = Array.isArray(unknownFeatures) ? unknownFeatures : [];
    } catch (error) {
      this.logger.error('处理文件时出错', error);
    }
  }

  async detectFaces(imageBuffer: Buffer): Promise<any> {
    try {
      this.monitorService.startOperation('face_detection');
      
      if (!this.modelsLoaded) {
        await this.loadModels();
      }

      // Convert buffer to image
      const image = await this.bufferToImage(imageBuffer);
      
      // Detect faces with landmarks and descriptors
      const detections = await faceapi
        .detectAllFaces(image)
        .withFaceLandmarks()
        .withFaceDescriptors();

      this.monitorService.endOperation('face_detection');

      return {
        success: true,
        faceCount: detections.length,
        faces: detections.map((detection, index) => ({
          id: index,
          confidence: detection.detection.score,
          box: detection.detection.box,
          landmarks: detection.landmarks,
          descriptor: Array.from(detection.descriptor)
        }))
      };

    } catch (error) {
      this.logger.error('人脸检测失败', error);
      return {
        success: false,
        error: error.message,
        faceCount: 0,
        faces: []
      };
    }
  }
  private async bufferToImage(buffer: Buffer): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      try {
        // 在Node.js环境中，我们需要使用不同的方法
        // 这里使用简化的实现
        const canvas = require('canvas');
        const img = new canvas.Image();
        img.onload = () => resolve(img as any);
        img.onerror = reject;
        img.src = buffer;
        resolve(img as any);
      } catch (error) {
        reject(error);
      }
    });
  }
}