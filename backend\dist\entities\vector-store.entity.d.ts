export declare class VectorStore {
    id: number;
    documentId: string;
    content: string;
    vector: number[];
    metadata: {
        type: string;
        source: string;
        language?: string;
        tags?: string[];
        properties?: Record<string, any>;
    };
    embeddings: {
        model: string;
        version: string;
        dimension: number;
        lastUpdated: Date;
    };
    searchMetadata: {
        similarity?: number;
        lastSearched?: Date;
        searchCount?: number;
        ranking?: number;
    };
    createdAt: Date;
    updatedAt: Date;
}
