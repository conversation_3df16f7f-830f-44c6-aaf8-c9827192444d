/// <reference types="multer" />
/// <reference types="node" />
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
export declare class FileUploadService {
    private configService;
    private readonly uploadDir;
    constructor(configService: ConfigService);
    private ensureUploadDirExists;
    private generateUniqueFileName;
    saveFile(file: Express.Multer.File): Promise<string>;
    deleteFile(filePath: string): Promise<void>;
    getFileStream(filePath: string): Promise<fs.ReadStream>;
    getFileStats(filePath: string): Promise<fs.Stats>;
    validateFile(file: Express.Multer.File, options: {
        maxSize?: number;
        allowedMimeTypes?: string[];
    }): Promise<void>;
}
