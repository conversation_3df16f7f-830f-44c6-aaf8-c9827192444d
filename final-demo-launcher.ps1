# SmartLoan 2025 Final Demo Launcher
Write-Host "🚀 SmartLoan 2025 智能金融服务平台 - 最终演示启动器" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# 获取当前目录
$currentDir = Get-Location
Write-Host "📍 当前目录: $currentDir" -ForegroundColor Cyan

# 检查系统状态
Write-Host "`n🔍 检查系统状态..." -ForegroundColor Yellow

# 检查Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 未安装" -ForegroundColor Red
    exit 1
}

# 检查Docker
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Docker 未安装 (数据库功能可能受限)" -ForegroundColor Yellow
}

# 检查关键文件
$requiredFiles = @("demo.html", "quick-api.cjs")
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ 发现文件: $file" -ForegroundColor Green
    } else {
        Write-Host "❌ 缺少文件: $file" -ForegroundColor Red
    }
}

# 检查目录结构
$requiredDirs = @("frontend", "backend")
foreach ($dir in $requiredDirs) {
    if (Test-Path $dir) {
        Write-Host "✅ 发现目录: $dir" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 目录不存在: $dir" -ForegroundColor Yellow
    }
}

Write-Host "`n🎯 启动选项:" -ForegroundColor Cyan
Write-Host "1. 🌐 打开演示页面 (推荐)" -ForegroundColor White
Write-Host "2. 🚀 启动后端API服务" -ForegroundColor White
Write-Host "3. 📊 启动完整系统 (前端+后端)" -ForegroundColor White
Write-Host "4. 🔧 系统诊断" -ForegroundColor White
Write-Host "5. 📖 查看使用指南" -ForegroundColor White

$choice = Read-Host "`n请选择操作 (1-5)"

switch ($choice) {
    "1" {
        Write-Host "`n🌐 打开演示页面..." -ForegroundColor Green
        if (Test-Path "demo.html") {
            $demoPath = Join-Path $currentDir "demo.html"
            Start-Process $demoPath
            Write-Host "✅ 演示页面已打开！" -ForegroundColor Green
            Write-Host "💡 提示: 演示页面包含完整的功能展示和交互演示" -ForegroundColor Cyan
        } else {
            Write-Host "❌ demo.html 文件不存在" -ForegroundColor Red
        }
    }
    
    "2" {
        Write-Host "`n🚀 启动后端API服务..." -ForegroundColor Green
        if (Test-Path "quick-api.cjs") {
            Write-Host "📡 启动API服务器 (端口: 3001)..." -ForegroundColor Yellow
            Start-Process powershell -ArgumentList "-NoExit", "-Command", "node quick-api.cjs" -WindowStyle Normal
            Start-Sleep -Seconds 3
            
            # 测试API连接
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -UseBasicParsing -TimeoutSec 5
                if ($response.StatusCode -eq 200) {
                    Write-Host "✅ 后端API服务启动成功！" -ForegroundColor Green
                    Write-Host "📍 API地址: http://localhost:3001" -ForegroundColor Cyan
                    Write-Host "🔗 健康检查: http://localhost:3001/api/health" -ForegroundColor Cyan
                } else {
                    Write-Host "⚠️ API服务可能未完全启动" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "⚠️ 无法连接到API服务，请稍等片刻" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ quick-api.cjs 文件不存在" -ForegroundColor Red
        }
    }
    
    "3" {
        Write-Host "`n📊 启动完整系统..." -ForegroundColor Green
        
        # 启动后端
        if (Test-Path "quick-api.cjs") {
            Write-Host "🚀 启动后端服务..." -ForegroundColor Yellow
            Start-Process powershell -ArgumentList "-NoExit", "-Command", "node quick-api.cjs" -WindowStyle Normal
            Start-Sleep -Seconds 3
        }
        
        # 打开演示页面
        if (Test-Path "demo.html") {
            Write-Host "🌐 打开演示页面..." -ForegroundColor Yellow
            $demoPath = Join-Path $currentDir "demo.html"
            Start-Process $demoPath
        }
        
        Write-Host "✅ 完整系统启动完成！" -ForegroundColor Green
        Write-Host "📱 演示页面: 已在浏览器中打开" -ForegroundColor Cyan
        Write-Host "📡 后端API: http://localhost:3001" -ForegroundColor Cyan
    }
    
    "4" {
        Write-Host "`n🔧 系统诊断..." -ForegroundColor Green
        
        Write-Host "`n📋 环境检查:" -ForegroundColor Cyan
        Write-Host "  Node.js: $(if (Get-Command node -ErrorAction SilentlyContinue) { '✅ 已安装' } else { '❌ 未安装' })" -ForegroundColor White
        Write-Host "  Docker: $(if (Get-Command docker -ErrorAction SilentlyContinue) { '✅ 已安装' } else { '❌ 未安装' })" -ForegroundColor White
        Write-Host "  PowerShell: ✅ $($PSVersionTable.PSVersion)" -ForegroundColor White
        
        Write-Host "`n📁 文件检查:" -ForegroundColor Cyan
        $files = @("demo.html", "quick-api.cjs", "README.md", "使用指南.md")
        foreach ($file in $files) {
            $status = if (Test-Path $file) { "✅" } else { "❌" }
            Write-Host "  $file: $status" -ForegroundColor White
        }
        
        Write-Host "`n📂 目录检查:" -ForegroundColor Cyan
        $dirs = @("frontend", "backend", "ai-service")
        foreach ($dir in $dirs) {
            $status = if (Test-Path $dir) { "✅" } else { "❌" }
            Write-Host "  $dir: $status" -ForegroundColor White
        }
        
        # 端口检查
        Write-Host "`n🔌 端口检查:" -ForegroundColor Cyan
        $ports = @(3000, 3001, 3002, 5432, 6379)
        foreach ($port in $ports) {
            try {
                $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
                $status = if ($connection.TcpTestSucceeded) { "🔴 占用" } else { "✅ 可用" }
                Write-Host "  端口 $port : $status" -ForegroundColor White
            } catch {
                Write-Host "  端口 $port : ✅ 可用" -ForegroundColor White
            }
        }
    }
    
    "5" {
        Write-Host "`n📖 查看使用指南..." -ForegroundColor Green
        if (Test-Path "使用指南.md") {
            Start-Process "使用指南.md"
            Write-Host "✅ 使用指南已打开！" -ForegroundColor Green
        } elseif (Test-Path "README.md") {
            Start-Process "README.md"
            Write-Host "✅ README文档已打开！" -ForegroundColor Green
        } else {
            Write-Host "❌ 使用指南文件不存在" -ForegroundColor Red
        }
    }
    
    default {
        Write-Host "`n❌ 无效选择，请重新运行脚本" -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n🎉 操作完成！" -ForegroundColor Green
Write-Host "`n📋 快速访问链接:" -ForegroundColor Cyan
Write-Host "  🌐 演示页面: file:///$currentDir/demo.html" -ForegroundColor White
Write-Host "  📡 API健康检查: http://localhost:3001/api/health" -ForegroundColor White
Write-Host "  📖 项目文档: README.md" -ForegroundColor White

Write-Host "`n💡 提示:" -ForegroundColor Yellow
Write-Host "  - 演示页面包含完整的功能展示" -ForegroundColor White
Write-Host "  - 支持智能匹配、AI顾问等交互功能" -ForegroundColor White
Write-Host "  - 后端API提供2025年最新金融产品数据" -ForegroundColor White

Write-Host "`n🏆 SmartLoan - 让金融服务更智能！" -ForegroundColor Green

# 等待用户按键
Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
