import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisService } from '../services/redis.service';
import { LoggerService } from '../services/logger.service';

@Module({
  imports: [ConfigModule],
  providers: [
    LoggerService,
    {
      provide: RedisService,
      useFactory: (configService: ConfigService, loggerService: LoggerService) => {
        return new RedisService(configService, loggerService);
      },
      inject: [ConfigService, LoggerService],
    },
  ],
  exports: [RedisService],
})
export class RedisModule {} 