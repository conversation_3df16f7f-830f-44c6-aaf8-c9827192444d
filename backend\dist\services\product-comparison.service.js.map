{"version": 3, "file": "product-comparison.service.js", "sourceRoot": "", "sources": ["../../src/services/product-comparison.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAyC;AACzC,+DAAqD;AACrD,mDAA+C;AAC/C,qDAAiD;AAG1C,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IAGnC,YAEmB,iBAAsC,EACtC,YAA0B,EAC1B,MAAqB;QAFrB,sBAAiB,GAAjB,iBAAiB,CAAqB;QACtC,iBAAY,GAAZ,YAAY,CAAc;QAC1B,WAAM,GAAN,MAAM,CAAe;QANvB,cAAS,GAAG,IAAI,CAAC;IAO/B,CAAC;IAEJ,KAAK,CAAC,eAAe,CAAC,UAAoB;QACxC,IAAI;YACF,MAAM,QAAQ,GAAG,mBAAmB,UAAU,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAClE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE5D,IAAI,aAAa,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;gBACtD,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;aAClC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBACjD,KAAK,EAAE,EAAE,EAAE,EAAE,IAAA,YAAE,EAAC,UAAU,CAAC,EAAE;aAC9B,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;gBACtD,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;aAC5B;YAED,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBACtC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;gBACxC,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC;gBAChD,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;gBAClC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;aACzC,CAAC;YAEF,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAElF,OAAO,UAAU,CAAC;SACnB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEO,gBAAgB,CAAC,QAAmB;QAC1C,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC9B,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,QAAmB;QACzC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;QACtC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACnC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBACjC,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE;wBAC3B,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;qBAC/B;gBACH,CAAC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;YAC/C,MAAM,iBAAiB,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC/C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;oBAC/C,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAEtE,OAAO;oBACL,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,WAAW,EAAE,OAAO,CAAC,IAAI;oBACzB,UAAU,EAAE,CAAC,CAAC,OAAO;oBACrB,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,EAAE;oBACvC,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,EAAE;iBAC1B,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,IAAI,EAAE,WAAW;gBACjB,UAAU,EAAE,iBAAiB;aAC9B,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB,CAAC,QAAmB;QAC7C,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;QAC1C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBACvC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;oBACzC,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE;wBACnC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;qBACvC;gBACH,CAAC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE;YACvD,MAAM,qBAAqB,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBACnD,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;oBACvD,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAE9E,OAAO;oBACL,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,WAAW,EAAE,OAAO,CAAC,IAAI;oBACzB,cAAc,EAAE,CAAC,CAAC,WAAW;oBAC7B,WAAW,EAAE,WAAW,EAAE,WAAW,IAAI,EAAE;oBAC3C,KAAK,EAAE,WAAW,EAAE,KAAK,IAAI,EAAE;iBAChC,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,IAAI,EAAE,eAAe;gBACrB,UAAU,EAAE,qBAAqB;aAClC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,QAAmB;QACtC,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC9B,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,WAAW,EAAE,OAAO,CAAC,IAAI;YACzB,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,CAAC;YACzC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,CAAC;YAC7B,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,CAAC;SAClD,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,QAAmB;QACzC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;QACtC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACnC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBACjC,IAAI,OAAO,EAAE;wBACX,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;qBAC1B;gBACH,CAAC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAC3C,MAAM,iBAAiB,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACjD,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,WAAW,EAAE,OAAO,CAAC,IAAI;gBACzB,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;aAClF,CAAC,CAAC,CAAC;YAEJ,OAAO;gBACL,IAAI,EAAE,OAAO;gBACb,UAAU,EAAE,iBAAiB;aAC9B,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,UAAoB;QAC5C,IAAI;YACF,MAAM,QAAQ,GAAG,kBAAkB,UAAU,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACjE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE5D,IAAI,aAAa,EAAE;gBACjB,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;aAClC;YAAM,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBACxD,KAAK,EAAE,EAAE,EAAE,EAAE,IAAA,YAAE,EAAC,UAAU,CAAC,EAAE;aAC9B,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;gBACtD,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;aAC5B;YAED,MAAM,MAAM,GAAG;gBACb,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;gBACpC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;gBAC1C,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC;gBAClD,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;gBACpC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;aAC3C,CAAC;YAEF,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAE9E,OAAO,MAAM,CAAC;SACf;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACrC,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEO,cAAc,CAAC,QAAmB;QACxC,OAAO;YACL,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;YACrE,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI;gBACZ,OAAO,CAAC,IAAI;gBACZ,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACvB,OAAO,CAAC,SAAS;gBACjB,OAAO,CAAC,SAAS;gBACjB,OAAO,CAAC,OAAO;gBACf,OAAO,CAAC,OAAO;gBACf,OAAO,CAAC,YAAY;aACrB,CAAC;SACH,CAAC;IACJ,CAAC;IAEO,iBAAiB,CAAC,QAAmB;QAC3C,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;QACtC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACjC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC/C,WAAW;gBACX,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CACxB,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAC/D;aACF,CAAC;SACH,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,QAAmB;QAC/C,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;QAC1C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBACzC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC;gBACvD,eAAe;gBACf,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBACxB,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,CAAC;oBAC/E,OAAO,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBACtD,CAAC,CAAC;aACH,CAAC;SACH,CAAC;IACJ,CAAC;IAEO,cAAc,CAAC,QAAmB;QACxC,OAAO;YACL,OAAO,EAAE,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC/C,IAAI,EAAE;gBACJ,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC;gBAClD,CAAC,KAAK,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,aAAa,IAAI,CAAC,GAAG,CAAC,CAAC;gBACzD,CAAC,KAAK,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;gBACnD,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC;aAChE;SACF,CAAC;IACJ,CAAC;IAEO,iBAAiB,CAAC,QAAmB;QAC3C,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;QACtC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACjC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC3C,OAAO;gBACP,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CACxB,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAC/C;aACF,CAAC;SACH,CAAC;IACJ,CAAC;CACF,CAAA;AA1RY,wBAAwB;IADpC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,wBAAO,CAAC,CAAA;qCACU,oBAAU;QACf,4BAAY;QAClB,8BAAa;GAP7B,wBAAwB,CA0RpC;AA1RY,4DAAwB"}