import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { config } from '../config'
import { User } from '../entities/user.entity'

export interface JwtPayload {
  sub: string
  username: string
  roles: string[]
}

export interface RequestWithUser extends Request {
  user?: User
}

declare global {
  namespace Express {
    interface Request {
      user?: JwtPayload
    }
  }
}

export const auth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '')

    if (!token) {
      return res.status(401).json({
        code: 1,
        message: '请先登录'
      })
    }

    const decoded = jwt.verify(token, config.jwtSecret) as JwtPayload
    req.user = decoded
    next()
  } catch (error) {
    res.status(401).json({
      code: 1,
      message: '认证失败'
    })
  }
} 