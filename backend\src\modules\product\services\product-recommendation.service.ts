import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { Product } from '../entities/product.entity';
import { User } from '../../user/entities/user.entity';
import { CacheService } from '../../../cache/cache.service';
import { LoggerService } from '../../../logger/logger.service';{ Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { Product } from '../entities/product.entity';
import { User } from '../../user/entities/user.entity';
import { CacheService } from '../../../cache/cache.service';
import { LoggerService } from '../../../logger/logger.service';

@Injectable()
export class ProductRecommendationService {
  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    private readonly cacheService: CacheService,
    private readonly logger: LoggerService
  ) {}

  async getRecommendedProducts(user: User): Promise<Product[]> {
    const cacheKey = `recommendations:${user.id}`;
    
    try {
      // 尝试从缓存获取推荐
      const cachedRecommendations = await this.cacheService.get<Product[]>(cacheKey);
      if (cachedRecommendations) {
        return cachedRecommendations;
      }

      // 获取用户特征
      const userFeatures = await this.getUserFeatures(user);

      // 获取所有产品
      const products = await this.productRepository.find();

      // 计算产品得分
      const scoredProducts = products.map(product => ({
        product,
        score: this.calculateProductScore(product, userFeatures)
      }));

      // 按得分排序并获取前10个产品
      const recommendations = scoredProducts
        .sort((a, b) => b.score - a.score)
        .slice(0, 10)
        .map(item => item.product);

      // 缓存推荐结果（1小时）
      await this.cacheService.set(cacheKey, recommendations, 3600);

      return recommendations;
    } catch (error) {
      this.logger.error('获取产品推荐失败', error);
      throw error;
    }
  }

  private async getUserFeatures(user: User): Promise<Record<string, any>> {
    // 从用户元数据中获取特征
    const metadata = user.metadata || {};
    return {
      creditScore: metadata.creditScore || 0,
      income: metadata.monthlyIncome || 0,
      employmentStatus: metadata.employmentType || 'unknown',
      age: metadata.age || 0
    };
  }

  private calculateProductScore(product: Product, userFeatures: Record<string, any>): number {
    let score = 0;
    const requirements = product.requirements || {};

    // 基于用户收入的产品匹配
    if (userFeatures.income >= requirements.minIncome) {
      score += 2;
    }

    // 基于信用评分的产品匹配
    if (userFeatures.creditScore >= requirements.minCreditScore) {
      score += 2;
    }

    // 基于就业状态的产品匹配
    if (requirements.employmentTypes?.includes(userFeatures.employmentStatus)) {
      score += 1;
    }

    // 基于年龄的产品匹配
    if (userFeatures.age >= requirements.minAge && userFeatures.age <= requirements.maxAge) {
      score += 1;
    }

    return score;
  }

  async recommendProductsByUserFeatures(userId: string): Promise<Product[]> {
    try {
      // For now, return a simple implementation - can be enhanced with ML later
      const products = await this.productRepository.find({
        where: { isActive: true },
        order: { 'metadata.popularity': 'DESC' },
        take: 10
      });
      
      this.logger.debug(`为用户 ${userId} 推荐了 ${products.length} 个产品`);
      return products;
    } catch (error) {
      this.logger.error(`为用户 ${userId} 推荐产品失败: ${error.message || error}`);
      throw error;
    }
  }

  async getSimilarProducts(productId: string): Promise<Product[]> {
    try {
      const baseProduct = await this.productRepository.findOne({
        where: { id: productId }
      });

      if (!baseProduct) {
        return [];
      }

      // Find similar products by category and interest rate range
      const similarProducts = await this.productRepository.find({
        where: {
          category: baseProduct.category,
          isActive: true,
          id: Not(productId) // Exclude the base product
        },
        order: { interestRate: 'ASC' },
        take: 5
      });

      this.logger.debug(`为产品 ${productId} 找到了 ${similarProducts.length} 个相似产品`);
      return similarProducts;
    } catch (error) {
      this.logger.error(`获取相似产品失败: ${error.message || error}`);
      throw error;
    }
  }
}