<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartLoan 2025 - 资质审核系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1000px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .review-card { background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); margin-bottom: 20px; }
        .step-indicator { display: flex; justify-content: space-between; margin-bottom: 30px; }
        .step { flex: 1; text-align: center; position: relative; }
        .step::after { content: ''; position: absolute; top: 20px; right: -50%; width: 100%; height: 2px; background: #e0e0e0; z-index: 1; }
        .step:last-child::after { display: none; }
        .step.active::after { background: #4caf50; }
        .step-circle { width: 40px; height: 40px; border-radius: 50%; background: #e0e0e0; color: white; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px; position: relative; z-index: 2; }
        .step.active .step-circle { background: #4caf50; }
        .step.completed .step-circle { background: #4caf50; }
        .upload-area { border: 2px dashed #ccc; border-radius: 10px; padding: 40px; text-align: center; cursor: pointer; transition: all 0.3s; margin-bottom: 20px; }
        .upload-area:hover { border-color: #1890ff; background: #f0f8ff; }
        .upload-area.dragover { border-color: #4caf50; background: #e8f5e8; }
        .file-list { margin-top: 20px; }
        .file-item { display: flex; align-items: center; justify-content: between; padding: 10px; background: #f5f5f5; border-radius: 8px; margin-bottom: 10px; }
        .file-info { flex: 1; }
        .file-status { margin-left: 10px; }
        .btn { background: linear-gradient(45deg, #1890ff, #722ed1); color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-size: 16px; transition: all 0.3s; }
        .btn:hover { transform: scale(1.05); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .progress-bar { width: 100%; height: 8px; background: #e0e0e0; border-radius: 4px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(45deg, #4caf50, #8bc34a); transition: width 0.5s ease; }
        .result-card { background: #e8f5e8; border: 1px solid #4caf50; border-radius: 10px; padding: 20px; margin-top: 20px; }
        .result-item { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .status-badge { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }
        .status-success { background: #e8f5e8; color: #4caf50; }
        .status-warning { background: #fff3e0; color: #ff9800; }
        .status-error { background: #ffebee; color: #f44336; }
        .camera-preview { width: 100%; height: 300px; background: #000; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; margin-bottom: 20px; }
        .document-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .document-card { border: 2px solid #e0e0e0; border-radius: 10px; padding: 15px; text-align: center; cursor: pointer; transition: all 0.3s; }
        .document-card:hover { border-color: #1890ff; }
        .document-card.selected { border-color: #4caf50; background: #e8f5e8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 资质审核系统</h1>
            <p>多模态智能审核 - 支持15类证件识别</p>
        </div>

        <!-- 步骤指示器 -->
        <div class="review-card">
            <div class="step-indicator">
                <div class="step active" id="step1">
                    <div class="step-circle">1</div>
                    <div>选择证件</div>
                </div>
                <div class="step" id="step2">
                    <div class="step-circle">2</div>
                    <div>上传文件</div>
                </div>
                <div class="step" id="step3">
                    <div class="step-circle">3</div>
                    <div>活体检测</div>
                </div>
                <div class="step" id="step4">
                    <div class="step-circle">4</div>
                    <div>审核结果</div>
                </div>
            </div>
        </div>

        <!-- 步骤1: 选择证件类型 -->
        <div class="review-card" id="step1-content">
            <h3>📄 选择需要审核的证件类型</h3>
            <div class="document-grid">
                <div class="document-card" onclick="selectDocument('identity_card')">
                    <div style="font-size: 2rem;">🆔</div>
                    <div>身份证</div>
                </div>
                <div class="document-card" onclick="selectDocument('business_license')">
                    <div style="font-size: 2rem;">🏢</div>
                    <div>营业执照</div>
                </div>
                <div class="document-card" onclick="selectDocument('bank_card')">
                    <div style="font-size: 2rem;">💳</div>
                    <div>银行卡</div>
                </div>
                <div class="document-card" onclick="selectDocument('driving_license')">
                    <div style="font-size: 2rem;">🚗</div>
                    <div>驾驶证</div>
                </div>
                <div class="document-card" onclick="selectDocument('passport')">
                    <div style="font-size: 2rem;">📘</div>
                    <div>护照</div>
                </div>
                <div class="document-card" onclick="selectDocument('property_certificate')">
                    <div style="font-size: 2rem;">🏠</div>
                    <div>房产证</div>
                </div>
            </div>
            <button class="btn" onclick="nextStep(2)" id="next-btn-1" disabled>下一步</button>
        </div>

        <!-- 步骤2: 文件上传 -->
        <div class="review-card" id="step2-content" style="display:none;">
            <h3>📤 上传证件照片</h3>
            <div class="upload-area" onclick="triggerFileUpload()" ondrop="handleDrop(event)" ondragover="handleDragOver(event)">
                <div style="font-size: 3rem; margin-bottom: 10px;">📷</div>
                <div>点击上传或拖拽文件到此处</div>
                <small>支持 JPG、PNG、PDF 格式，最大 10MB</small>
            </div>
            <input type="file" id="fileInput" style="display:none;" accept="image/*,.pdf" onchange="handleFileSelect(event)">
            <div class="file-list" id="fileList"></div>
            <button class="btn" onclick="processOCR()" id="process-btn" disabled>开始识别</button>
        </div>

        <!-- 步骤3: 活体检测 -->
        <div class="review-card" id="step3-content" style="display:none;">
            <h3>👤 活体检测验证</h3>
            <div class="camera-preview" id="cameraPreview">
                <div style="text-align: center;">
                    <div style="font-size: 3rem; margin-bottom: 10px;">📹</div>
                    <div>请点击开始按钮进行活体检测</div>
                </div>
            </div>
            <div style="text-align: center;">
                <button class="btn" onclick="startLivenessDetection()" id="liveness-btn">开始活体检测</button>
            </div>
            <div id="liveness-instructions" style="display:none; margin-top: 20px; text-align: center;">
                <p>请按照提示完成以下动作：</p>
                <div id="action-list">
                    <div>👁️ 请眨眼</div>
                    <div>↔️ 请左右转头</div>
                    <div>😊 请微笑</div>
                </div>
            </div>
        </div>

        <!-- 步骤4: 审核结果 -->
        <div class="review-card" id="step4-content" style="display:none;">
            <h3>✅ 审核结果</h3>
            <div class="result-card">
                <h4>🎉 审核通过</h4>
                <div class="result-item">
                    <span>证件类型:</span>
                    <span id="result-doc-type">身份证</span>
                </div>
                <div class="result-item">
                    <span>识别准确度:</span>
                    <span class="status-badge status-success">95%</span>
                </div>
                <div class="result-item">
                    <span>活体检测:</span>
                    <span class="status-badge status-success">通过</span>
                </div>
                <div class="result-item">
                    <span>资质评分:</span>
                    <span id="qualification-score">85分</span>
                </div>
                <div class="result-item">
                    <span>审核决策:</span>
                    <span class="status-badge status-success" id="decision">批准</span>
                </div>
                <div class="result-item">
                    <span>处理时间:</span>
                    <span>2.3秒</span>
                </div>
            </div>
            <button class="btn" onclick="restartReview()">重新审核</button>
        </div>
    </div>

    <script>
        let selectedDocument = '';
        let uploadedFiles = [];
        let currentStep = 1;

        function selectDocument(docType) {
            selectedDocument = docType;
            document.querySelectorAll('.document-card').forEach(card => card.classList.remove('selected'));
            event.target.closest('.document-card').classList.add('selected');
            document.getElementById('next-btn-1').disabled = false;
        }

        function nextStep(step) {
            document.getElementById(`step${currentStep}-content`).style.display = 'none';
            document.getElementById(`step${currentStep}`).classList.remove('active');
            document.getElementById(`step${currentStep}`).classList.add('completed');
            
            currentStep = step;
            document.getElementById(`step${step}-content`).style.display = 'block';
            document.getElementById(`step${step}`).classList.add('active');
        }

        function triggerFileUpload() {
            document.getElementById('fileInput').click();
        }

        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            addFiles(files);
        }

        function handleDrop(event) {
            event.preventDefault();
            const files = Array.from(event.dataTransfer.files);
            addFiles(files);
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.target.closest('.upload-area').classList.add('dragover');
        }

        function addFiles(files) {
            uploadedFiles = [...uploadedFiles, ...files];
            updateFileList();
            document.getElementById('process-btn').disabled = false;
        }

        function updateFileList() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = uploadedFiles.map((file, index) => `
                <div class="file-item">
                    <div class="file-info">
                        <div><strong>${file.name}</strong></div>
                        <div style="font-size: 12px; color: #666;">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                    </div>
                    <div class="file-status">
                        <span class="status-badge status-success">就绪</span>
                    </div>
                </div>
            `).join('');
        }

        async function processOCR() {
            document.getElementById('process-btn').disabled = true;
            document.getElementById('process-btn').textContent = '识别中...';
            
            // 模拟OCR处理
            setTimeout(() => {
                nextStep(3);
            }, 2000);
        }

        function startLivenessDetection() {
            document.getElementById('liveness-btn').disabled = true;
            document.getElementById('liveness-instructions').style.display = 'block';
            document.getElementById('cameraPreview').innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 3rem; margin-bottom: 10px;">📹</div>
                    <div>检测中...</div>
                    <div class="progress-bar" style="width: 200px; margin: 20px auto;">
                        <div class="progress-fill" style="width: 0%; animation: progress 3s ease-in-out forwards;"></div>
                    </div>
                </div>
            `;
            
            setTimeout(() => {
                nextStep(4);
            }, 3000);
        }

        function restartReview() {
            currentStep = 1;
            selectedDocument = '';
            uploadedFiles = [];
            
            document.querySelectorAll('.step').forEach(step => {
                step.classList.remove('active', 'completed');
            });
            document.querySelectorAll('[id$="-content"]').forEach(content => {
                content.style.display = 'none';
            });
            
            document.getElementById('step1').classList.add('active');
            document.getElementById('step1-content').style.display = 'block';
            document.getElementById('next-btn-1').disabled = true;
            document.getElementById('process-btn').disabled = true;
            document.getElementById('liveness-btn').disabled = false;
        }
    </script>

    <style>
        @keyframes progress {
            from { width: 0%; }
            to { width: 100%; }
        }
    </style>
</body>
</html>
