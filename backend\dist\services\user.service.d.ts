import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';
import { MonitorService } from './monitor.service';
import { CacheService } from './cache.service';
export declare class UserService {
    private readonly userRepository;
    private readonly monitorService;
    private readonly cacheService;
    constructor(userRepository: Repository<User>, monitorService: MonitorService, cacheService: CacheService);
    create(createUserDto: CreateUserDto): Promise<User>;
    findAll(): Promise<User[]>;
    findById(id: string): Promise<User>;
    findByUsername(username: string): Promise<User>;
    findByEmail(email: string): Promise<User>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<User>;
    updateLastLogin(id: string): Promise<void>;
    updatePassword(id: string, hashedPassword: string): Promise<void>;
    remove(id: string): Promise<void>;
    sanitizeUser(user: User): Partial<User>;
}
