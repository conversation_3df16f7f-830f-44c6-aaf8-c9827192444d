export declare class KnowledgeGraph {
    id: number;
    nodeId: string;
    nodeType: string;
    properties: {
        name: string;
        description?: string;
        attributes?: Record<string, any>;
        metadata?: Record<string, any>;
    };
    relationships: Array<{
        targetNodeId: string;
        type: string;
        properties?: Record<string, any>;
        weight?: number;
    }>;
    embeddings: {
        vector?: number[];
        model?: string;
        version?: string;
        lastUpdated?: Date;
    };
    metadata: {
        source?: string;
        confidence?: number;
        lastVerified?: Date;
        tags?: string[];
    };
    createdAt: Date;
    updatedAt: Date;
}
