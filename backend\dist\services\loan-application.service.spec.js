"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const loan_application_service_1 = require("./loan-application.service");
const loan_application_entity_1 = require("../entities/loan-application.entity");
const loan_type_enum_1 = require("../enums/loan-type.enum");
const employment_status_enum_1 = require("../enums/employment-status.enum");
const collateral_type_enum_1 = require("../enums/collateral-type.enum");
const loan_purpose_enum_1 = require("../enums/loan-purpose.enum");
const loan_application_entity_2 = require("../entities/loan-application.entity");
const common_1 = require("@nestjs/common");
describe('LoanApplicationService', () => {
    let service;
    let repository;
    const mockRepository = {
        create: jest.fn(),
        save: jest.fn(),
        find: jest.fn(),
        findOne: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                loan_application_service_1.LoanApplicationService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(loan_application_entity_1.LoanApplication),
                    useValue: mockRepository,
                },
            ],
        }).compile();
        service = module.get(loan_application_service_1.LoanApplicationService);
        repository = module.get((0, typeorm_1.getRepositoryToken)(loan_application_entity_1.LoanApplication));
    });
    it('should be defined', () => {
        expect(service).toBeDefined();
    });
    describe('create', () => {
        it('should create a new loan application', async () => {
            const dto = {
                amount: 10000,
                term: 12,
                type: loan_type_enum_1.LoanType.PERSONAL,
                employmentStatus: employment_status_enum_1.EmploymentStatus.EMPLOYED,
                collateral: collateral_type_enum_1.CollateralType.NONE,
                purpose: loan_purpose_enum_1.LoanPurpose.PERSONAL,
                annualIncome: 50000,
                debtToIncomeRatio: 0.3
            };
            const expectedResult = {
                id: '1',
                ...dto,
                status: loan_application_entity_2.LoanStatus.PENDING,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockRepository.create.mockReturnValue(expectedResult);
            mockRepository.save.mockResolvedValue(expectedResult);
            const result = await service.create(dto, '1');
            expect(result).toBeDefined();
            expect(mockRepository.create).toHaveBeenCalled();
            expect(mockRepository.save).toHaveBeenCalled();
        });
        it('should throw error for invalid data', async () => {
            const dto = {
                amount: 10000,
                term: 12,
                type: loan_type_enum_1.LoanType.PERSONAL,
                employmentStatus: employment_status_enum_1.EmploymentStatus.EMPLOYED,
                collateral: collateral_type_enum_1.CollateralType.NONE,
                purpose: loan_purpose_enum_1.LoanPurpose.PERSONAL,
                annualIncome: 50000,
                debtToIncomeRatio: 0.3,
            };
            mockRepository.create.mockImplementation(() => {
                throw new common_1.BadRequestException('Invalid data');
            });
            await expect(service.create(dto, '1')).rejects.toThrow(common_1.BadRequestException);
        });
    });
    describe('updateStatus', () => {
        it('should update application status', async () => {
            const expectedResult = {
                id: '1',
                status: loan_application_entity_2.LoanStatus.APPROVED,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockRepository.findOne.mockResolvedValue(expectedResult);
            mockRepository.save.mockResolvedValue(expectedResult);
            const result = await service.updateStatus('1', loan_application_entity_2.LoanStatus.APPROVED);
            expect(result).toBeDefined();
            expect(mockRepository.findOne).toHaveBeenCalledWith({ where: { id: '1' } });
        });
        it('should throw error for non-existent application', async () => {
            mockRepository.findOne.mockResolvedValue(null);
            await expect(service.updateStatus('1', loan_application_entity_2.LoanStatus.APPROVED)).rejects.toThrow(common_1.NotFoundException);
        });
    });
});
//# sourceMappingURL=loan-application.service.spec.js.map