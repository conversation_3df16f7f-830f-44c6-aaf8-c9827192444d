export declare class User {
    id: number;
    username: string;
    email: string;
    phone: string;
    password_hash: string;
    full_name: string;
    birth_date: Date;
    gender: string;
    id_number: string;
    employment_info: {
        company_name?: string;
        position?: string;
        employment_type?: string;
        monthly_income?: number;
        work_years?: number;
        industry?: string;
    };
    financial_info: {
        monthly_income?: number;
        other_income?: number;
        monthly_expenses?: number;
        assets?: number;
        liabilities?: number;
        credit_score?: number;
        bank_accounts?: string[];
    };
    status: string;
    is_verified: boolean;
    kyc_completed: boolean;
    last_login: Date;
    created_at: Date;
    updated_at: Date;
    get age(): number;
    get creditScore(): number;
    get monthlyIncome(): number;
    get employmentType(): string;
}
