import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('financial_products')
export class FinancialProduct {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: '产品ID' })
  id: number;

  @Column()
  @ApiProperty({ description: '产品名称' })
  name: string;

  @Column()
  @ApiProperty({ description: '金融机构名称' })
  institution_name: string;

  @Column()
  @ApiProperty({ description: '产品类型' })
  product_type: string;

  @Column('decimal', { precision: 5, scale: 2 })
  @ApiProperty({ description: '年利率' })
  interest_rate: number;

  @Column()
  @ApiProperty({ description: '最短贷款期限(月)' })
  loan_term_min: number;

  @Column()
  @ApiProperty({ description: '最长贷款期限(月)' })
  loan_term_max: number;

  @Column('decimal', { precision: 12, scale: 2 })
  @ApiProperty({ description: '最小贷款金额' })
  amount_min: number;

  @Column('decimal', { precision: 12, scale: 2 })
  @ApiProperty({ description: '最大贷款金额' })
  amount_max: number;
  @Column('text', { nullable: true })
  @ApiProperty({ description: '产品描述' })
  description: string;

  @Column('text', { nullable: true })
  @ApiProperty({ description: '产品特性' })
  features: string;

  @Column('text', { nullable: true })
  @ApiProperty({ description: '申请要求' })
  requirements: string;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  @ApiProperty({ description: '创建时间' })
  created_at: Date;
}
