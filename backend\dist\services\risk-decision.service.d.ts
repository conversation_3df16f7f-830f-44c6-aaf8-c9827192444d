import { Repository } from 'typeorm';
import { LoanApplication } from '../entities/loan-application.entity';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../services/logger.service';
import { <PERSON>rror<PERSON>and<PERSON> } from '../utils/error-handler';
import { MonitorService } from '../services/monitor.service';
import { CacheService } from '../services/cache.service';
import { RiskAssessment } from '../interfaces/risk-assessment.interface';
export declare class RiskDecisionService {
    private loanApplicationRepository;
    private readonly configService;
    private readonly logger;
    private readonly errorHandler;
    private readonly monitorService;
    private readonly cacheService;
    private readonly baseRate;
    private readonly maxDTI;
    private readonly minCreditScore;
    private readonly minScore;
    constructor(loanApplicationRepository: Repository<LoanApplication>, configService: ConfigService, logger: LoggerService, errorHandler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, monitorService: MonitorService, cacheService: CacheService);
    evaluate(application: LoanApplication): Promise<RiskAssessment>;
    assessRisk(application: LoanApplication): Promise<RiskAssessment>;
    private checkFraud;
    private calculateRiskScore;
    private evaluateApproval;
    private performStressTest;
    private determineRiskLevel;
    private calculateInterestRate;
    private calculateMaxLoanAmount;
    private calculateSuggestedTerm;
}
