import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { RiskService } from '../services/risk.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiQuery } from '@nestjs/swagger';

@ApiTags('风控管理')
@Controller('risk')
@UseGuards(JwtAuthGuard)
export class RiskController {
  constructor(private readonly riskService: RiskService) {}

  @Get('data')
  @ApiOperation({ summary: '获取风控数据' })
  @ApiQuery({ name: 'timeRange', enum: ['today', 'week', 'month', 'year'] })
  async getRiskData(@Query('timeRange') timeRange: string) {
    return this.riskService.getRiskData(timeRange);
  }

  @Get('alerts')
  @ApiOperation({ summary: '获取实时预警' })
  async getAlerts() {
    return this.riskService.getAlerts();
  }

  @Get('subscribe')
  @ApiOperation({ summary: '订阅实时预警' })
  async subscribeAlerts() {
    return this.riskService.subscribeAlerts();
  }
} 