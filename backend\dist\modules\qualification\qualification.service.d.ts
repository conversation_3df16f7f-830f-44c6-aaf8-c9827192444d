/// <reference types="multer" />
import { Repository } from 'typeorm';
import { QualificationReview } from './entities/qualification-review.entity';
import { LoanApplication } from '../loan/entities/loan-application.entity';
import { OcrService } from '../ocr/ocr.service';
import { GpuService } from '../../services/gpu.service';
import { RedisService } from '../../services/redis.service';
export declare class QualificationService {
    private reviewRepository;
    private applicationRepository;
    private ocrService;
    private gpuService;
    private redisService;
    private readonly logger;
    private readonly UPLOAD_DIR;
    private readonly CACHE_TTL;
    constructor(reviewRepository: Repository<QualificationReview>, applicationRepository: Repository<LoanApplication>, ocrService: OcrService, gpuService: GpuService, redisService: RedisService);
    processDocument(applicationId: number, documentType: string, file: Express.Multer.File): Promise<QualificationReview>;
    private verifyOcrResultAsync;
    private ensureUploadDirExists;
    private uploadFile;
    private validateOcrResult;
    getApplicationReviews(applicationId: number): Promise<QualificationReview[]>;
}
