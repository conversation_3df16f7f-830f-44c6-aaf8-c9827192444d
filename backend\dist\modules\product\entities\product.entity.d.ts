import { Institution } from './institution.entity';
export declare class Product {
    id: number;
    institution: Institution;
    name: string;
    type: string;
    code: string;
    description: string;
    min_amount: number;
    max_amount: number;
    get minAmount(): number;
    set minAmount(value: number);
    get maxAmount(): number;
    set maxAmount(value: number);
    interest_rate: number;
    get interestRate(): number;
    set interestRate(value: number);
    minTerm: number;
    maxTerm: number;
    processingFee: number;
    earlyRepaymentFee: number;
    isActive: boolean;
    isFeatured: boolean;
    category: string;
    sortOrder: number;
    term_range: number[];
    requirements: any;
    features: any;
    created_at: Date;
    get createdAt(): Date;
    set createdAt(value: Date);
    updated_at: Date;
    get updatedAt(): Date;
    set updatedAt(value: Date);
}
