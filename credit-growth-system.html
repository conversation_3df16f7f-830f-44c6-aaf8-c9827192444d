<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartLoan 2025 - 信用积分成长体系</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .credit-dashboard { background: white; border-radius: 20px; padding: 30px; margin-bottom: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .credit-score-circle { width: 200px; height: 200px; border-radius: 50%; background: conic-gradient(#4caf50 0deg 306deg, #e0e0e0 306deg 360deg); display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; position: relative; }
        .credit-score-inner { width: 160px; height: 160px; border-radius: 50%; background: white; display: flex; flex-direction: column; align-items: center; justify-content: center; }
        .score-value { font-size: 3rem; font-weight: bold; color: #4caf50; }
        .score-label { font-size: 0.9rem; color: #666; }
        .level-info { text-align: center; margin-bottom: 30px; }
        .level-badge { display: inline-block; padding: 8px 20px; background: linear-gradient(45deg, #4caf50, #8bc34a); color: white; border-radius: 25px; font-weight: bold; margin-bottom: 10px; }
        .level-progress { width: 100%; height: 8px; background: #e0e0e0; border-radius: 4px; overflow: hidden; margin-bottom: 10px; }
        .progress-fill { height: 100%; background: linear-gradient(45deg, #4caf50, #8bc34a); transition: width 2s ease; }
        .benefits-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .benefit-card { background: #f8f9fa; border-radius: 15px; padding: 20px; text-align: center; }
        .benefit-icon { font-size: 2.5rem; margin-bottom: 10px; }
        .benefit-title { font-size: 1.1rem; font-weight: bold; margin-bottom: 8px; }
        .benefit-desc { font-size: 0.9rem; color: #666; }
        .growth-tasks { background: white; border-radius: 20px; padding: 30px; margin-bottom: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .task-item { display: flex; align-items: center; padding: 15px; background: #f8f9fa; border-radius: 10px; margin-bottom: 15px; }
        .task-icon { width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(45deg, #1890ff, #722ed1); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2rem; margin-right: 15px; }
        .task-info { flex: 1; }
        .task-title { font-weight: bold; margin-bottom: 5px; }
        .task-desc { font-size: 0.8rem; color: #666; }
        .task-reward { text-align: right; }
        .reward-points { font-size: 1.2rem; font-weight: bold; color: #4caf50; }
        .reward-label { font-size: 0.7rem; color: #666; }
        .task-completed { opacity: 0.6; }
        .task-completed .task-icon { background: #4caf50; }
        .history-section { background: white; border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .history-item { display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0; }
        .history-item:last-child { border-bottom: none; }
        .history-date { font-size: 0.8rem; color: #666; }
        .history-action { font-weight: bold; }
        .history-points { color: #4caf50; font-weight: bold; }
        .level-roadmap { display: flex; justify-content: space-between; margin: 30px 0; }
        .level-step { text-align: center; flex: 1; position: relative; }
        .level-step::after { content: ''; position: absolute; top: 25px; right: -50%; width: 100%; height: 2px; background: #e0e0e0; z-index: 1; }
        .level-step:last-child::after { display: none; }
        .level-step.completed::after { background: #4caf50; }
        .step-circle { width: 50px; height: 50px; border-radius: 50%; background: #e0e0e0; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px; position: relative; z-index: 2; }
        .level-step.completed .step-circle { background: #4caf50; color: white; }
        .level-step.current .step-circle { background: #1890ff; color: white; animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.1); } }
        .step-label { font-size: 0.8rem; font-weight: bold; }
        .step-score { font-size: 0.7rem; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🏆 信用积分成长体系</h1>
            <p>通过完成任务提升信用等级，享受更多金融服务特权</p>
        </div>

        <!-- 信用分数仪表盘 -->
        <div class="credit-dashboard">
            <div class="credit-score-circle">
                <div class="credit-score-inner">
                    <div class="score-value">850</div>
                    <div class="score-label">信用分数</div>
                </div>
            </div>
            
            <div class="level-info">
                <div class="level-badge">🥇 钻石会员</div>
                <div>距离下一等级还需 50 分</div>
                <div class="level-progress">
                    <div class="progress-fill" style="width: 85%;"></div>
                </div>
                <div style="font-size: 0.8rem; color: #666;">当前等级进度: 85%</div>
            </div>

            <!-- 等级路线图 -->
            <div class="level-roadmap">
                <div class="level-step completed">
                    <div class="step-circle">🥉</div>
                    <div class="step-label">青铜</div>
                    <div class="step-score">0-599分</div>
                </div>
                <div class="level-step completed">
                    <div class="step-circle">🥈</div>
                    <div class="step-label">白银</div>
                    <div class="step-score">600-699分</div>
                </div>
                <div class="level-step completed">
                    <div class="step-circle">🥇</div>
                    <div class="step-label">黄金</div>
                    <div class="step-score">700-799分</div>
                </div>
                <div class="level-step current">
                    <div class="step-circle">💎</div>
                    <div class="step-label">钻石</div>
                    <div class="step-score">800-899分</div>
                </div>
                <div class="level-step">
                    <div class="step-circle">👑</div>
                    <div class="step-label">王者</div>
                    <div class="step-score">900-1000分</div>
                </div>
            </div>

            <!-- 会员特权 -->
            <h3 style="margin-bottom: 20px; color: #333;">🎁 钻石会员特权</h3>
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">⚡</div>
                    <div class="benefit-title">极速审批</div>
                    <div class="benefit-desc">10秒内完成贷款审批</div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">💰</div>
                    <div class="benefit-title">专属利率</div>
                    <div class="benefit-desc">享受最低3.5%年化利率</div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">🎯</div>
                    <div class="benefit-title">额度提升</div>
                    <div class="benefit-desc">最高可申请200万额度</div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">🛡️</div>
                    <div class="benefit-title">风险保障</div>
                    <div class="benefit-desc">免费信用保险服务</div>
                </div>
            </div>
        </div>

        <!-- 成长任务 -->
        <div class="growth-tasks">
            <h3 style="margin-bottom: 20px; color: #333;">📋 成长任务</h3>
            
            <div class="task-item task-completed">
                <div class="task-icon">✓</div>
                <div class="task-info">
                    <div class="task-title">完善个人信息</div>
                    <div class="task-desc">上传身份证、绑定银行卡</div>
                </div>
                <div class="task-reward">
                    <div class="reward-points">+50</div>
                    <div class="reward-label">积分</div>
                </div>
            </div>

            <div class="task-item task-completed">
                <div class="task-icon">✓</div>
                <div class="task-info">
                    <div class="task-title">首次贷款申请</div>
                    <div class="task-desc">成功申请并放款</div>
                </div>
                <div class="task-reward">
                    <div class="reward-points">+100</div>
                    <div class="reward-label">积分</div>
                </div>
            </div>

            <div class="task-item">
                <div class="task-icon">📱</div>
                <div class="task-info">
                    <div class="task-title">邀请好友注册</div>
                    <div class="task-desc">邀请3位好友注册并完成认证</div>
                </div>
                <div class="task-reward">
                    <div class="reward-points">+75</div>
                    <div class="reward-label">积分</div>
                </div>
            </div>

            <div class="task-item">
                <div class="task-icon">💳</div>
                <div class="task-info">
                    <div class="task-title">按时还款</div>
                    <div class="task-desc">连续6个月按时还款</div>
                </div>
                <div class="task-reward">
                    <div class="reward-points">+200</div>
                    <div class="reward-label">积分</div>
                </div>
            </div>

            <div class="task-item">
                <div class="task-icon">🎓</div>
                <div class="task-info">
                    <div class="task-title">金融知识学习</div>
                    <div class="task-desc">完成5个金融知识测试</div>
                </div>
                <div class="task-reward">
                    <div class="reward-points">+30</div>
                    <div class="reward-label">积分</div>
                </div>
            </div>

            <div class="task-item">
                <div class="task-icon">🌟</div>
                <div class="task-info">
                    <div class="task-title">产品评价</div>
                    <div class="task-desc">对使用的金融产品进行评价</div>
                </div>
                <div class="task-reward">
                    <div class="reward-points">+25</div>
                    <div class="reward-label">积分</div>
                </div>
            </div>
        </div>

        <!-- 积分历史 -->
        <div class="history-section">
            <h3 style="margin-bottom: 20px; color: #333;">📈 积分历史</h3>
            
            <div class="history-item">
                <div>
                    <div class="history-action">完成身份认证</div>
                    <div class="history-date">2025-01-20 14:30</div>
                </div>
                <div class="history-points">+50</div>
            </div>

            <div class="history-item">
                <div>
                    <div class="history-action">首次贷款申请成功</div>
                    <div class="history-date">2025-01-18 10:15</div>
                </div>
                <div class="history-points">+100</div>
            </div>

            <div class="history-item">
                <div>
                    <div class="history-action">按时还款</div>
                    <div class="history-date">2025-01-15 09:00</div>
                </div>
                <div class="history-points">+30</div>
            </div>

            <div class="history-item">
                <div>
                    <div class="history-action">邀请好友注册</div>
                    <div class="history-date">2025-01-12 16:45</div>
                </div>
                <div class="history-points">+25</div>
            </div>

            <div class="history-item">
                <div>
                    <div class="history-action">完善个人资料</div>
                    <div class="history-date">2025-01-10 11:20</div>
                </div>
                <div class="history-points">+20</div>
            </div>
        </div>
    </div>

    <script>
        // 模拟积分动画
        window.onload = function() {
            // 动态更新积分圆环
            const circle = document.querySelector('.credit-score-circle');
            const score = 850;
            const percentage = (score / 1000) * 360;
            circle.style.background = `conic-gradient(#4caf50 0deg ${percentage}deg, #e0e0e0 ${percentage}deg 360deg)`;
            
            console.log('🏆 信用积分成长体系已加载');
            console.log('✅ 当前信用分数: 850分');
            console.log('✅ 当前等级: 钻石会员');
            console.log('✅ 可用特权: 4项');
        };
    </script>
</body>
</html>
