"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductComparisonService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const product_entity_1 = require("../entities/product.entity");
const cache_service_1 = require("./cache.service");
const logger_service_1 = require("./logger.service");
let ProductComparisonService = class ProductComparisonService {
    constructor(productRepository, cacheService, logger) {
        this.productRepository = productRepository;
        this.cacheService = cacheService;
        this.logger = logger;
        this.CACHE_TTL = 3600;
    }
    async compareProducts(productIds) {
        try {
            const cacheKey = `product:compare:${productIds.sort().join(',')}`;
            const cachedResults = await this.cacheService.get(cacheKey);
            if (cachedResults && typeof cachedResults === 'string') {
                return JSON.parse(cachedResults);
            }
            const products = await this.productRepository.find({
                where: { id: (0, typeorm_2.In)(productIds) }
            });
            if (!products || products.length !== productIds.length) {
                throw new Error('部分产品未找到');
            }
            const comparison = {
                basic: this.compareBasicInfo(products),
                features: this.compareFeatures(products),
                requirements: this.compareRequirements(products),
                costs: this.compareCosts(products),
                benefits: this.compareBenefits(products)
            };
            await this.cacheService.set(cacheKey, JSON.stringify(comparison), this.CACHE_TTL);
            return comparison;
        }
        catch (error) {
            this.logger.error('比较产品失败', error);
            throw error;
        }
    }
    compareBasicInfo(products) {
        return products.map(product => ({
            id: product.id,
            name: product.name,
            code: product.code,
            description: product.description,
            type: product.type,
            minAmount: product.minAmount,
            maxAmount: product.maxAmount,
            minTerm: product.minTerm,
            maxTerm: product.maxTerm,
            interestRate: product.interestRate,
            isActive: product.isActive,
            isFeatured: product.isFeatured,
            metadata: product.metadata
        }));
    }
    compareFeatures(products) {
        const allFeatures = new Set();
        products.forEach(product => {
            if (Array.isArray(product.features)) {
                product.features.forEach(feature => {
                    if (feature && feature.name) {
                        allFeatures.add(feature.name);
                    }
                });
            }
        });
        return Array.from(allFeatures).map(featureName => {
            const featureComparison = products.map(product => {
                const feature = Array.isArray(product.features) ?
                    product.features.find(f => f && f.name === featureName) : undefined;
                return {
                    productId: product.id,
                    productName: product.name,
                    hasFeature: !!feature,
                    description: feature?.description || '',
                    icon: feature?.icon || ''
                };
            });
            return {
                name: featureName,
                comparison: featureComparison
            };
        });
    }
    compareRequirements(products) {
        const allRequirements = new Set();
        products.forEach(product => {
            if (Array.isArray(product.requirements)) {
                product.requirements.forEach(requirement => {
                    if (requirement && requirement.name) {
                        allRequirements.add(requirement.name);
                    }
                });
            }
        });
        return Array.from(allRequirements).map(requirementName => {
            const requirementComparison = products.map(product => {
                const requirement = Array.isArray(product.requirements) ?
                    product.requirements.find(r => r && r.name === requirementName) : undefined;
                return {
                    productId: product.id,
                    productName: product.name,
                    hasRequirement: !!requirement,
                    description: requirement?.description || '',
                    value: requirement?.value || ''
                };
            });
            return {
                name: requirementName,
                comparison: requirementComparison
            };
        });
    }
    compareCosts(products) {
        return products.map(product => ({
            productId: product.id,
            productName: product.name,
            interestRate: product.interestRate,
            processingFee: product.processingFee || 0,
            lateFee: product.lateFee || 0,
            earlyRepaymentFee: product.earlyRepaymentFee || 0
        }));
    }
    compareBenefits(products) {
        const allBenefits = new Set();
        products.forEach(product => {
            if (Array.isArray(product.benefits)) {
                product.benefits.forEach(benefit => {
                    if (benefit) {
                        allBenefits.add(benefit);
                    }
                });
            }
        });
        return Array.from(allBenefits).map(benefit => {
            const benefitComparison = products.map(product => ({
                productId: product.id,
                productName: product.name,
                hasBenefit: Array.isArray(product.benefits) && product.benefits.includes(benefit)
            }));
            return {
                name: benefit,
                comparison: benefitComparison
            };
        });
    }
    async getComparisonMatrix(productIds) {
        try {
            const cacheKey = `product:matrix:${productIds.sort().join(',')}`;
            const cachedResults = await this.cacheService.get(cacheKey);
            if (cachedResults) {
                return JSON.parse(cachedResults);
            }
            const products = await this.productRepository.find({
                where: { id: (0, typeorm_2.In)(productIds) }
            });
            if (!products || products.length !== productIds.length) {
                throw new Error('部分产品未找到');
            }
            const matrix = {
                basic: this.getBasicMatrix(products),
                features: this.getFeaturesMatrix(products),
                requirements: this.getRequirementsMatrix(products),
                costs: this.getCostsMatrix(products),
                benefits: this.getBenefitsMatrix(products)
            };
            await this.cacheService.set(cacheKey, JSON.stringify(matrix), this.CACHE_TTL);
            return matrix;
        }
        catch (error) {
            this.logger.error('获取比较矩阵失败', error);
            throw error;
        }
    }
    getBasicMatrix(products) {
        return {
            headers: ['产品名称', '产品代码', '类型', '最小金额', '最大金额', '最小期限', '最大期限', '利率'],
            rows: products.map(product => [
                product.name,
                product.code,
                product.type.toString(),
                product.minAmount,
                product.maxAmount,
                product.minTerm,
                product.maxTerm,
                product.interestRate
            ])
        };
    }
    getFeaturesMatrix(products) {
        const allFeatures = new Set();
        products.forEach(product => {
            product.features.forEach(feature => {
                allFeatures.add(feature.name);
            });
        });
        return {
            headers: ['特性', ...products.map(p => p.name)],
            rows: Array.from(allFeatures).map(featureName => [
                featureName,
                ...products.map(product => product.features.some(f => f.name === featureName) ? '✓' : '✗')
            ])
        };
    }
    getRequirementsMatrix(products) {
        const allRequirements = new Set();
        products.forEach(product => {
            product.requirements.forEach(requirement => {
                allRequirements.add(requirement.name);
            });
        });
        return {
            headers: ['要求', ...products.map(p => p.name)],
            rows: Array.from(allRequirements).map(requirementType => [
                requirementType,
                ...products.map(product => {
                    const requirement = product.requirements.find(r => r.name === requirementType);
                    return requirement ? requirement.value || '✓' : '✗';
                })
            ])
        };
    }
    getCostsMatrix(products) {
        return {
            headers: ['费用类型', ...products.map(p => p.name)],
            rows: [
                ['利率', ...products.map(p => `${p.interestRate}%`)],
                ['手续费', ...products.map(p => `${p.processingFee || 0}%`)],
                ['逾期费', ...products.map(p => `${p.lateFee || 0}%`)],
                ['提前还款费', ...products.map(p => `${p.earlyRepaymentFee || 0}%`)]
            ]
        };
    }
    getBenefitsMatrix(products) {
        const allBenefits = new Set();
        products.forEach(product => {
            product.benefits.forEach(benefit => {
                allBenefits.add(benefit);
            });
        });
        return {
            headers: ['优势', ...products.map(p => p.name)],
            rows: Array.from(allBenefits).map(benefit => [
                benefit,
                ...products.map(product => product.benefits.includes(benefit) ? '✓' : '✗')
            ])
        };
    }
};
ProductComparisonService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        cache_service_1.CacheService,
        logger_service_1.LoggerService])
], ProductComparisonService);
exports.ProductComparisonService = ProductComparisonService;
//# sourceMappingURL=product-comparison.service.js.map