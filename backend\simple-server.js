// SmartLoan 2025 简化版后端服务
const http = require('http');
const url = require('url');

const PORT = 3001;

// 2025年最新金融产品数据
const products2025 = [
  {
    id: 1,
    name: '工商银行融e借2025版',
    provider: '中国工商银行',
    interest_rate: 3.85,
    amount_max: 800000,
    description: '2025年全新升级，AI智能审批，支持数字人民币'
  },
  {
    id: 2,
    name: '建设银行快贷Pro 2025',
    provider: '中国建设银行',
    interest_rate: 3.95,
    amount_max: 500000,
    description: '支持元宇宙场景，区块链征信'
  },
  {
    id: 3,
    name: '蚂蚁借呗升级版2025',
    provider: '蚂蚁金服',
    interest_rate: 4.2,
    amount_max: 300000,
    description: '支持Web3.0身份认证，绿色金融'
  }
];

// 创建服务器
const server = http.createServer((req, res) => {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');

  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;

  console.log(`${req.method} ${path}`);

  // 健康检查
  if (path === '/api/health') {
    res.writeHead(200);
    res.end(JSON.stringify({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'SmartLoan Backend API 2025',
      version: '2025.1.0'
    }));
    return;
  }

  // 智能产品匹配
  if (path === '/api/products/match/smart' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        const matches = products2025.map((product, index) => ({
          product,
          match_score: 0.9 - index * 0.1,
          ai_reasoning: `基于2025年AI算法，${product.name}非常适合您的需求`,
          recommended_amount: Math.min(data.amount || 100000, product.amount_max),
          recommended_rate: product.interest_rate
        }));

        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: matches,
          message: '智能产品匹配成功',
          total: matches.length
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ error: '请求数据格式错误' }));
      }
    });
    return;
  }

  // AI风险评估
  if (path === '/api/ai/risk-assessment' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      const riskScore = Math.floor(Math.random() * 40) + 60; // 60-100分
      const assessment = {
        risk_score: riskScore,
        risk_level: riskScore >= 80 ? 'low' : riskScore >= 60 ? 'medium' : 'high',
        recommendation: riskScore >= 80 ? '建议批准' : '建议进一步审核',
        ai_version: '2025.1.0'
      };

      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: assessment,
        message: 'AI风险评估完成'
      }));
    });
    return;
  }

  // AI顾问对话
  if (path === '/api/ai/advisor/chat' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      const response = {
        response: '您好！我是SmartLoan 2025年AI金融顾问。基于最新的AI技术，我可以为您提供个性化的金融服务建议。',
        confidence: 0.95,
        ai_model: 'Fin-R1-2025'
      };

      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: response,
        message: 'AI顾问响应成功'
      }));
    });
    return;
  }

  // OCR识别
  if (path === '/api/ai/ocr' && req.method === 'POST') {
    const ocrResult = {
      type: 'ID_CARD',
      confidence: 0.98,
      name: '张三',
      id_number: '110101199001011234',
      ai_version: '2025.1.0'
    };

    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: ocrResult,
      message: '证件识别成功'
    }));
    return;
  }

  // 活体检测
  if (path === '/api/ai/liveness' && req.method === 'POST') {
    const livenessResult = {
      is_live: true,
      confidence: 0.95,
      face_detected: true,
      quality_score: 0.92,
      ai_version: '2025.1.0'
    };

    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: livenessResult,
      message: '活体检测完成'
    }));
    return;
  }

  // 404处理
  res.writeHead(404);
  res.end(JSON.stringify({ error: 'API接口未找到' }));
});

// 启动服务器
server.listen(PORT, () => {
  console.log('🚀 SmartLoan 2025 Backend API 已启动');
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
  console.log('🎯 核心功能: AI智能匹配、多模态审核、实时风控');
  console.log('');
  console.log('📡 可用接口:');
  console.log('  GET  /api/health - 健康检查');
  console.log('  POST /api/products/match/smart - 智能产品匹配');
  console.log('  POST /api/ai/risk-assessment - AI风险评估');
  console.log('  POST /api/ai/advisor/chat - AI顾问对话');
  console.log('  POST /api/ai/ocr - OCR识别');
  console.log('  POST /api/ai/liveness - 活体检测');
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
