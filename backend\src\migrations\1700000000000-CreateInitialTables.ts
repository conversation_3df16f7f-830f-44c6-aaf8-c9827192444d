import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInitialTables1700000000000 implements MigrationInterface {
  name = 'CreateInitialTables1700000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建用户表
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" SERIAL PRIMARY KEY,
        "username" VARCHAR NOT NULL UNIQUE,
        "password" VARCHAR NOT NULL,
        "email" VARCHAR NOT NULL UNIQUE,
        "phone" VARCHAR,
        "avatar" VARCHAR,
        "roles" VARCHAR[] NOT NULL DEFAULT '{}',
        "permissions" VARCHAR[] NOT NULL DEFAULT '{}',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // 创建贷款申请表
    await queryRunner.query(`
      CREATE TYPE "loan_status" AS ENUM ('pending', 'approved', 'rejected', 'processing', 'completed')
    `);

    await queryRunner.query(`
      CREATE TABLE "loan_applications" (
        "id" SERIAL PRIMARY KEY,
        "user_id" INTEGER NOT NULL REFERENCES "users"("id"),
        "amount" DECIMAL(10,2) NOT NULL,
        "interest_rate" DECIMAL(5,2) NOT NULL,
        "term" INTEGER NOT NULL,
        "status" loan_status NOT NULL DEFAULT 'pending',
        "risk_assessment" JSONB,
        "documents" JSONB,
        "rejection_reason" VARCHAR,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "loan_applications"`);
    await queryRunner.query(`DROP TYPE "loan_status"`);
    await queryRunner.query(`DROP TABLE "users"`);
  }
} 