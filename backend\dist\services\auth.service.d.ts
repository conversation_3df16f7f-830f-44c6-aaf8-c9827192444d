import { JwtService } from '@nestjs/jwt';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
export declare class AuthService {
    private userRepository;
    private jwtService;
    constructor(userRepository: Repository<User>, jwtService: JwtService);
    register(phone: string, password: string, email?: string): Promise<any>;
    login(phone: string, password: string): Promise<any>;
    getUserById(userId: number): Promise<User>;
    updateUserProfile(userId: number, updateData: Partial<User>): Promise<User>;
    validateUser(email: string, password: string): Promise<any>;
    changePassword(userId: string, oldPassword: string, newPassword: string): Promise<void>;
    resetPassword(email: string): Promise<void>;
}
