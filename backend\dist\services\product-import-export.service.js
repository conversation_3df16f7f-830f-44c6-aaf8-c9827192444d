"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductImportExportService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const product_entity_1 = require("../entities/product.entity");
const logger_service_1 = require("./logger.service");
const XLSX = __importStar(require("xlsx"));
let ProductImportExportService = class ProductImportExportService {
    constructor(productRepository, logger) {
        this.productRepository = productRepository;
        this.logger = logger;
    }
    async importProducts(file) {
        try {
            const workbook = XLSX.read(file.buffer, { type: 'buffer' });
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            const data = XLSX.utils.sheet_to_json(worksheet);
            const products = data.map(row => ({
                name: row['产品名称'],
                code: row['产品代码'],
                description: row['产品描述'],
                category: row['产品类别'],
                minAmount: Number(row['最小金额']),
                maxAmount: Number(row['最大金额']),
                minTerm: Number(row['最小期限']),
                maxTerm: Number(row['最大期限']),
                interestRate: Number(row['利率']),
                processingFee: Number(row['手续费率'] || 0),
                lateFee: Number(row['逾期费率'] || 0),
                earlyRepaymentFee: Number(row['提前还款费率'] || 0),
                features: JSON.parse(row['产品特点'] || '[]'),
                benefits: (row['产品优势'] || '').split(','),
                requirements: JSON.parse(row['申请要求'] || '[]'),
                metadata: JSON.parse(row['元数据'] || '{}'),
                isActive: row['是否激活'] === '是',
                isFeatured: row['是否推荐'] === '是',
                sortOrder: Number(row['排序'] || 0)
            }));
            const results = await Promise.all(products.map(async (product) => {
                try {
                    const existingProduct = await this.productRepository.findOne({
                        where: { code: product.code }
                    });
                    if (existingProduct) {
                        await this.productRepository.update(existingProduct.id, product);
                        return { code: product.code, status: 'updated' };
                    }
                    else {
                        await this.productRepository.save(product);
                        return { code: product.code, status: 'created' };
                    }
                }
                catch (error) {
                    this.logger.error(`导入产品失败: ${product.code}`, error);
                    return { code: product.code, status: 'failed', error: error.message };
                }
            }));
            return {
                total: results.length,
                created: results.filter(r => r.status === 'created').length,
                updated: results.filter(r => r.status === 'updated').length,
                failed: results.filter(r => r.status === 'failed').length,
                details: results
            };
        }
        catch (error) {
            this.logger.error('导入产品失败', error);
            throw error;
        }
    }
    async exportProducts() {
        try {
            const products = await this.productRepository.find({
                order: {
                    category: 'ASC',
                    sortOrder: 'ASC'
                }
            });
            const data = products.map(product => ({
                '产品名称': product.name,
                '产品代码': product.code,
                '产品描述': product.description,
                '产品类别': product.category,
                '最小金额': product.minAmount,
                '最大金额': product.maxAmount,
                '最小期限': product.minTerm,
                '最大期限': product.maxTerm,
                '利率': product.interestRate,
                '手续费率': product.processingFee || 0,
                '逾期费率': product.lateFee || 0,
                '提前还款费率': product.earlyRepaymentFee || 0,
                '产品特点': JSON.stringify(product.features),
                '产品优势': product.benefits.join(','),
                '申请要求': JSON.stringify(product.requirements),
                '元数据': JSON.stringify(product.metadata),
                '是否激活': product.isActive ? '是' : '否',
                '是否推荐': product.isFeatured ? '是' : '否',
                '排序': product.sortOrder
            }));
            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Products');
            const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
            return {
                buffer,
                filename: `products_${new Date().toISOString().split('T')[0]}.xlsx`,
                contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            };
        }
        catch (error) {
            this.logger.error('导出产品失败', error);
            throw error;
        }
    }
    async exportTemplate() {
        try {
            const template = [{
                    '产品名称': '',
                    '产品代码': '',
                    '产品描述': '',
                    '产品类别': 'PERSONAL_LOAN',
                    '最小金额': 0,
                    '最大金额': 0,
                    '最小期限': 0,
                    '最大期限': 0,
                    '利率': 0,
                    '手续费率': 0,
                    '逾期费率': 0,
                    '提前还款费率': 0,
                    '产品特点': '[]',
                    '产品优势': '',
                    '申请要求': '[]',
                    '元数据': '{}',
                    '是否激活': '是',
                    '是否推荐': '否',
                    '排序': 0
                }];
            const worksheet = XLSX.utils.json_to_sheet(template);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Template');
            const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
            return {
                buffer,
                filename: 'product_import_template.xlsx',
                contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            };
        }
        catch (error) {
            this.logger.error('导出模板失败', error);
            throw error;
        }
    }
};
ProductImportExportService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        logger_service_1.LoggerService])
], ProductImportExportService);
exports.ProductImportExportService = ProductImportExportService;
//# sourceMappingURL=product-import-export.service.js.map