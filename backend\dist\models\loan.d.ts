import mongoose, { Document } from 'mongoose';
export interface ILoan extends Document {
    userId: mongoose.Types.ObjectId;
    loanId: string;
    amount: number;
    term: number;
    purpose: string;
    status: 'pending' | 'approved' | 'rejected';
    creditScore: number;
    monthlyIncome: number;
    houseStatus: 'none' | 'owned' | 'mortgaged';
    carStatus: 'none' | 'owned' | 'loaned';
    createdAt: Date;
    updatedAt: Date;
}
export declare const Loan: mongoose.Model<ILoan, {}, {}, {}, mongoose.Document<unknown, {}, ILoan, {}> & ILoan & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
