import { LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
export declare class LoggerService implements NestLoggerService {
    private logger;
    private readonly logDir;
    private readonly context?;
    constructor(configServiceOrContext?: ConfigService | string);
    private formatMessage;
    log(message: any, context?: string): void;
    error(message: any, traceOrMeta?: string | any, context?: string): void;
    warn(message: any, metaOrContext?: any | string): void;
    debug(message: any, metaOrContext?: any | string): void;
    verbose(message: any, context?: string): void;
    info(message: any, meta?: any): void;
    http(message: string, meta?: any): void;
    performance(operation: string, duration: number, meta?: any): void;
    security(message: string, meta?: any): void;
    business(operation: string, userId: string, meta?: any): void;
    system(message: string, meta?: any): void;
    analyzeLogs(options: {
        startDate?: Date;
        endDate?: Date;
        level?: string;
        searchText?: string;
    }): Promise<{
        totalLogs: number;
        levelDistribution: {};
        errorTypes: {};
        timeDistribution: {};
        topErrors: any[];
        performanceMetrics: {
            averageResponseTime: number;
            slowestEndpoints: any[];
        };
    }>;
    private getLogFiles;
    private generateLogAnalysis;
    cleanOldLogs(daysToKeep?: number): Promise<void>;
    exportLogAnalysis(options: any): Promise<{
        filename: string;
        path: string;
    }>;
    private formatAnalysisReport;
    private readFileAsync;
    private writeFileAsync;
}
