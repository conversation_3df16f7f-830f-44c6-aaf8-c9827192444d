import { LoanApplicationService } from '../services/loan-application.service';
import { CreateLoanApplicationDto } from '../dto/create-loan-application.dto';
import { UpdateLoanApplicationDto } from '../dto/update-loan-application.dto';
export declare class LoanController {
    private readonly loanApplicationService;
    constructor(loanApplicationService: LoanApplicationService);
    createLoan(createDto: CreateLoanApplicationDto, req: any): Promise<{
        code: number;
        message: string;
        data: import("../entities/loan-application.entity").LoanApplication;
    }>;
    getLoans(req: any, query: any): Promise<{
        code: number;
        data: import("../entities/loan-application.entity").LoanApplication[];
    }>;
    getLoanDetail(loanId: string, req: any): Promise<{
        code: number;
        data: import("../entities/loan-application.entity").LoanApplication;
    }>;
    updateLoan(loanId: string, updateDto: UpdateLoanApplicationDto, req: any): Promise<{
        code: number;
        message: string;
        data: import("../entities/loan-application.entity").LoanApplication;
    }>;
}
export declare const createLoan: (req: any, res: any) => Promise<void>;
export declare const getLoans: (req: any, res: any) => Promise<void>;
export declare const getLoanDetail: (req: any, res: any) => Promise<void>;
