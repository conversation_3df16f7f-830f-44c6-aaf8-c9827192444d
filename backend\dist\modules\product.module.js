"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const product_entity_1 = require("../entities/product.entity");
const product_controller_1 = require("../controllers/product.controller");
const product_service_1 = require("../services/product.service");
const product_search_service_1 = require("../services/product-search.service");
const product_import_export_service_1 = require("../services/product-import-export.service");
const product_comparison_service_1 = require("../services/product-comparison.service");
const product_recommendation_service_1 = require("../services/product-recommendation.service");
const product_search_controller_1 = require("../controllers/product-search.controller");
const product_import_export_controller_1 = require("../controllers/product-import-export.controller");
const product_comparison_controller_1 = require("../controllers/product-comparison.controller");
const product_recommendation_controller_1 = require("../controllers/product-recommendation.controller");
const cache_manager_1 = require("@nestjs/cache-manager");
const redis_module_1 = require("../modules/redis.module");
let ProductModule = class ProductModule {
};
ProductModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([product_entity_1.Product]),
            cache_manager_1.CacheModule.register(),
            redis_module_1.RedisModule
        ],
        controllers: [
            product_controller_1.ProductController,
            product_search_controller_1.ProductSearchController,
            product_import_export_controller_1.ProductImportExportController,
            product_comparison_controller_1.ProductComparisonController,
            product_recommendation_controller_1.ProductRecommendationController
        ],
        providers: [
            product_service_1.ProductService,
            product_search_service_1.ProductSearchService,
            product_import_export_service_1.ProductImportExportService,
            product_comparison_service_1.ProductComparisonService,
            product_recommendation_service_1.ProductRecommendationService
        ],
        exports: [
            product_service_1.ProductService,
            product_search_service_1.ProductSearchService,
            product_import_export_service_1.ProductImportExportService,
            product_comparison_service_1.ProductComparisonService,
            product_recommendation_service_1.ProductRecommendationService
        ]
    })
], ProductModule);
exports.ProductModule = ProductModule;
//# sourceMappingURL=product.module.js.map