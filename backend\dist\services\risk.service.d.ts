import { OnModuleInit } from '@nestjs/common';
import { Repository } from 'typeorm';
import { RiskData } from '../entities/risk-data.entity';
import { Alert } from '../entities/alert.entity';
import { Server } from 'socket.io';
import { MuxiGPUService } from './muxi-gpu.service';
import { MonitoringService } from './monitoring.service';
export declare class RiskService implements OnModuleInit {
    private riskDataRepository;
    private alertRepository;
    private readonly muxiGPUService;
    private monitoringService;
    server: Server;
    private alertSubject;
    constructor(riskDataRepository: Repository<RiskData>, alertRepository: Repository<Alert>, muxiGPUService: MuxiGPUService, monitoringService: MonitoringService);
    onModuleInit(): void;
    getRiskData(timeRange: string): Promise<{
        radar: {
            indicators: {
                name: string;
                max: number;
            }[];
            values: any[];
        };
        trend: {
            dates: any;
            values: any;
        };
        heatmap: {
            cities: any;
            riskTypes: any;
            data: any;
        };
    }>;
    getAlerts(): Promise<Alert[]>;
    subscribeAlerts(): import("rxjs").Observable<Alert>;
    private calculateRiskData;
    private startAlertMonitoring;
    createAlert(alert: Partial<Alert>): Promise<Alert>;
}
