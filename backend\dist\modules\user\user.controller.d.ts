import { UserService } from './user.service';
import { User } from './entities/user.entity';
export declare class UserController {
    private readonly userService;
    constructor(userService: UserService);
    create(createUserDto: any): Promise<User>;
    getProfile(req: any): Promise<User>;
    updateProfile(req: any, updateUserDto: any): Promise<User>;
    updatePassword(req: any, oldPassword: string, newPassword: string): Promise<void>;
    requestPasswordReset(email: string): Promise<void>;
    resetPassword(token: string, newPassword: string): Promise<void>;
    findAll(): Promise<User[]>;
    findOne(id: string): Promise<User>;
    update(id: string, updateUserDto: any): Promise<User>;
    assignRoles(id: string, roleIds: number[]): Promise<User>;
    remove(id: string): Promise<void>;
}
