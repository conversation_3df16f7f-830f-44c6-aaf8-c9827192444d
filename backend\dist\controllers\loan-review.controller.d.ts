import { LoanReviewService } from '../services/loan-review.service';
import { CreateLoanReviewDto } from '../dto/create-loan-review.dto';
export declare class LoanReviewController {
    private readonly loanReviewService;
    constructor(loanReviewService: LoanReviewService);
    create(createDto: CreateLoanReviewDto, req: any): Promise<import("../entities/loan-review.entity").LoanReview>;
    findAll(applicationId: string): Promise<import("../entities/loan-review.entity").LoanReview[]>;
    findOne(id: string): Promise<import("../entities/loan-review.entity").LoanReview>;
    getReviewStatistics(applicationId: string): Promise<any>;
}
