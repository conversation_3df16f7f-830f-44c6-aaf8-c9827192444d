import { LoanStatus } from '../enums/loan-status.enum';
import { LoanType } from '../enums/loan-type.enum';
import { EmploymentStatus } from '../enums/employment-status.enum';
import { CollateralType } from '../enums/collateral-type.enum';
import { LoanApplication } from '../entities/loan-application.entity';
export interface ILoanApplication {
    id: number;
    userId: number;
    amount: number;
    term: number;
    type: LoanType;
    status: LoanStatus;
    interestRate?: number;
    monthlyPayment?: number;
    totalPayment?: number;
    riskScore?: number;
    rejectionReason?: string;
    approvedBy?: number;
    rejectedBy?: number;
    employmentStatus: EmploymentStatus;
    collateral: CollateralType;
    metadata?: {
        creditScore?: number;
        monthlyIncome?: number;
        annualIncome?: number;
        debtToIncomeRatio?: number;
        employmentDuration?: number;
        employmentType?: string;
        houseStatus?: string;
        carStatus?: string;
        existingLoans?: number;
        deviceScore?: number;
        ipRiskScore?: number;
        behaviorScore?: number;
    };
    documentMetadata?: {
        id: string;
        type: string;
        url: string;
        verified: boolean;
        verifiedAt?: Date;
        verifiedBy?: number;
    }[];
    riskAssessment?: {
        score: number;
        factors: string[];
        decision: string;
        timestamp: Date;
    };
    approvedAt?: Date;
    rejectedAt?: Date;
    cancelledAt?: Date;
    cancelledBy?: number;
    cancellationReason?: string;
    purpose: string;
    annualIncome: number;
    debtToIncomeRatio: number;
    notes?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface ICreateLoanApplication {
    userId: string;
    loanType: LoanType;
    loanAmount: number;
    loanTerm: number;
    monthlyIncome: number;
    employmentType: string;
    employmentDuration: number;
    education: string;
    maritalStatus: string;
    houseStatus: string;
    carStatus: string;
    creditScore: number;
    debtToIncomeRatio: number;
    loanPurpose: string;
    collateral?: string;
    guarantor?: string;
    documents: string[];
}
export interface IUpdateLoanApplication {
    loanAmount?: number;
    loanTerm?: number;
    monthlyIncome?: number;
    employmentType?: string;
    employmentDuration?: number;
    education?: string;
    maritalStatus?: string;
    houseStatus?: string;
    carStatus?: string;
    creditScore?: number;
    debtToIncomeRatio?: number;
    loanPurpose?: string;
    collateral?: string;
    guarantor?: string;
    documents?: string[];
}
export interface ILoanApplicationStatistics {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    cancelled: number;
    review: number;
}
export interface ILoanApplicationTrends {
    daily: {
        date: string;
        count: number;
    }[];
    weekly: {
        week: string;
        count: number;
    }[];
    monthly: {
        month: string;
        count: number;
    }[];
}
export interface ILoanApplicationDecision {
    decision: 'approve' | 'reject' | 'review';
    reason: string;
    details: {
        fraudCheck: any;
        riskScore: any;
        stressTest: any;
    };
}
export interface ILoanApplicationRecommendation {
    product: {
        id: string;
        name: string;
        type: LoanType;
        minAmount: number;
        maxAmount: number;
        minTerm: number;
        maxTerm: number;
        interestRate: number;
        requirements: {
            minCreditScore: number;
            minIncome: number;
            employmentTypes: string[];
            educationLevels: string[];
        };
    };
    score: number;
    reasons: string[];
    advantages: string[];
    disadvantages: string[];
}
export interface ILoanApplicationResponse {
    application: ILoanApplication;
    recommendations: ILoanApplicationRecommendation[];
    decision: ILoanApplicationDecision;
}
export interface LoanApplicationWithAmount extends LoanApplication {
    totalAmount: number;
    monthlyPayment: number;
    interestAmount: number;
}
export interface LoanApplicationStatistics {
    totalApplications: number;
    approvedApplications: number;
    rejectedApplications: number;
    pendingApplications: number;
    averageAmount: number;
    averageTerm: number;
    approvalRate: number;
    rejectionRate: number;
    totalAmount: number;
    totalInterest: number;
}
export interface LoanApplicationTrend {
    date: string;
    count: number;
    amount: number;
}
export interface LoanApplicationDistribution {
    type: string;
    count: number;
    percentage: number;
}
