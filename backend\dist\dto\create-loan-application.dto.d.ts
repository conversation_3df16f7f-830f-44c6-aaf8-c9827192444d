import { LoanType } from '../enums/loan-type.enum';
import { EmploymentStatus } from '../enums/employment-status.enum';
import { CollateralType } from '../enums/collateral-type.enum';
import { LoanPurpose } from '../enums/loan-purpose.enum';
export declare class CreateLoanApplicationDto {
    amount: number;
    term: number;
    type: LoanType;
    employmentStatus: EmploymentStatus;
    collateral: CollateralType;
    purpose: LoanPurpose;
    annualIncome: number;
    debtToIncomeRatio: number;
    notes?: string;
    metadata?: LoanApplicationMetadataDto;
}
export declare class LoanApplicationMetadataDto {
    creditScore?: number;
    monthlyIncome?: number;
    annualIncome?: number;
    debtToIncomeRatio?: number;
    employmentDuration?: number;
    employmentType?: string;
    houseStatus?: string;
    carStatus?: string;
    existingLoans?: number;
    deviceScore?: number;
    ipRiskScore?: number;
    behaviorScore?: number;
}
