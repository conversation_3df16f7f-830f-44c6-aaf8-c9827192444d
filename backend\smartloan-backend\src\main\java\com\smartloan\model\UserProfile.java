package com.smartloan.model;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "user_profiles")
public class UserProfile {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @OneToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(nullable = false)
    private String realName;
    
    @Column(nullable = false)
    private String idNumber;
    
    @Column(nullable = false)
    private LocalDate birthDate;
    
    @Column(nullable = false)
    private String gender;
    
    @Column(nullable = false)
    private String phoneNumber;
    
    private String email;
    
    @Column(nullable = false)
    private String address;
    
    @Column(nullable = false)
    private String education;
    
    @Column(nullable = false)
    private String occupation;
    
    @Column(nullable = false)
    private Double monthlyIncome;
    
    private String creditScore;
    
    private Boolean isVerified = false;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
