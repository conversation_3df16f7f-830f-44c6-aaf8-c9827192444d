import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsBoolean, IsArray, Min, Max, IsEnum, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { ProductCategory } from '../entities/product.entity';

export class ProductFeatureDto {
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  icon?: string;
}

export class ProductRequirementDto {
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  value?: string;
}

export class ProductMetadataDto {
  @IsOptional()
  @IsNumber()
  popularity?: number;

  @IsOptional()
  @IsNumber()
  conversionRate?: number;

  @IsOptional()
  @IsNumber()
  averageRating?: number;

  @IsOptional()
  @IsNumber()
  reviewCount?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

export class CreateProductDto {
  @IsString()
  name: string;

  @IsString()
  code: string;

  @IsString()
  description: string;

  @IsEnum(ProductCategory)
  category: ProductCategory;

  @IsNumber()
  @Min(0)
  minAmount: number;

  @IsNumber()
  @Min(0)
  maxAmount: number;

  @IsNumber()
  @Min(1)
  minTerm: number;

  @IsNumber()
  @Min(1)
  maxTerm: number;

  @IsNumber()
  @Min(0)
  @Max(100)
  interestRate: number;

  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  processingFee?: number;

  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  lateFee?: number;

  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  earlyRepaymentFee?: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductFeatureDto)
  features: ProductFeatureDto[];

  @IsArray()
  @IsString({ each: true })
  benefits: string[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductRequirementDto)
  requirements: ProductRequirementDto[];

  @IsObject()
  @ValidateNested()
  @Type(() => ProductMetadataDto)
  metadata: ProductMetadataDto;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsBoolean()
  @IsOptional()
  isFeatured?: boolean;

  @IsNumber()
  @IsOptional()
  sortOrder?: number;
}

export class UpdateProductDto extends CreateProductDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  code?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsEnum(ProductCategory)
  @IsOptional()
  category?: ProductCategory;

  @IsNumber()
  @Min(0)
  @IsOptional()
  minAmount?: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  maxAmount?: number;

  @IsNumber()
  @Min(1)
  @IsOptional()
  minTerm?: number;

  @IsNumber()
  @Min(1)
  @IsOptional()
  maxTerm?: number;

  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  interestRate?: number;
}

export class ProductFilterDto {
  @IsString()
  @IsOptional()
  search?: string;

  @IsEnum(ProductCategory)
  @IsOptional()
  category?: ProductCategory;

  @IsNumber()
  @Min(0)
  @IsOptional()
  minAmount?: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  maxAmount?: number;

  @IsNumber()
  @Min(1)
  @IsOptional()
  minTerm?: number;

  @IsNumber()
  @Min(1)
  @IsOptional()
  maxTerm?: number;

  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  maxInterestRate?: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsBoolean()
  @IsOptional()
  isFeatured?: boolean;

  @IsString()
  @IsOptional()
  sortBy?: string;

  @IsString()
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC';

  @IsNumber()
  @Min(1)
  @IsOptional()
  page?: number;

  @IsNumber()
  @Min(1)
  @IsOptional()
  limit?: number;
} 