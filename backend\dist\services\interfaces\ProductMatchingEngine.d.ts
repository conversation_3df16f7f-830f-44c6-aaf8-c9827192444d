import { Product } from '../../entities/product.entity';
export interface UserProfile {
    age: number;
    monthlyIncome: number;
    creditScore: number;
    employmentType: string;
    employmentDuration: number;
    debtToIncomeRatio: number;
    requestedAmount: number;
    preferredTerm: number;
    location: string;
    purpose: string;
}
export interface ProductMatchWeights {
    amount: number;
    term: number;
    interestRate: number;
    creditScore: number;
    income: number;
    purpose: number;
}
export interface ProductMatchingEngine {
    findBestMatches(userProfile: UserProfile, filters?: Partial<Product>, weights?: ProductMatchWeights): Promise<Product[]>;
    calculateMatchScore(userProfile: UserProfile, product: Product, weights?: ProductMatchWeights): number;
    calculateApprovalProbability(userProfile: UserProfile, product: Product): number;
    calculateTotalCost(amount: number, product: Product, term: number): {
        totalAmount: number;
        monthlyPayment: number;
        totalInterest: number;
        fees: Record<string, number>;
    };
}
