import { Repository } from 'typeorm';
import { LoanReview } from '../entities/loan-review.entity';
import { CreateLoanReviewDto } from '../dto/create-loan-review.dto';
import { LoanApplication } from '../entities/loan-application.entity';
import { LoggerService } from '../logger/logger.service';
export declare class LoanReviewService {
    private readonly loanReviewRepository;
    private readonly loanApplicationRepository;
    private readonly logger;
    constructor(loanReviewRepository: Repository<LoanReview>, loanApplicationRepository: Repository<LoanApplication>, logger: LoggerService);
    create(createDto: CreateLoanReviewDto, reviewerId: number): Promise<LoanReview>;
    findAll(loanApplicationId: number): Promise<LoanReview[]>;
    findOne(id: number): Promise<LoanReview>;
    getReviewStatistics(loanApplicationId: number): Promise<any>;
}
