{"version": "2.0.0", "tasks": [{"label": "启动后端服务", "type": "shell", "command": "npm run dev", "options": {"cwd": "${workspaceFolder}/backend"}, "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "启动前端服务", "type": "shell", "command": "npm run dev", "options": {"cwd": "${workspaceFolder}/frontend"}, "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "启动AI服务", "type": "shell", "command": "python app.py", "options": {"cwd": "${workspaceFolder}/ai-service"}, "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "安装所有依赖", "type": "shell", "command": "npm install", "options": {"cwd": "${workspaceFolder}"}, "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "dependsOrder": "sequence", "dependsOn": ["安装后端依赖", "安装前端依赖"]}, {"label": "安装后端依赖", "type": "shell", "command": "npm install", "options": {"cwd": "${workspaceFolder}/backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "安装前端依赖", "type": "shell", "command": "npm install", "options": {"cwd": "${workspaceFolder}/frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "启动所有服务", "dependsOrder": "parallel", "dependsOn": ["启动后端服务", "启动前端服务"], "group": {"kind": "build", "isDefault": true}}]}