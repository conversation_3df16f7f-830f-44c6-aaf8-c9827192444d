{"sqltools.autoConnectTo": "SmartLoan-SQLTools-0603-054846", "postgresql.showExplorer": true, "sqltools.connections": [{"database": "smartloan", "username": "postgres", "port": 5432, "name": "SmartLoan-SQLTools-0603-054846", "driver": "PostgreSQL", "connectionTimeout": 30, "password": "smartloan123", "server": "localhost"}], "postgresql.connections": [{"database": "smartloan", "username": "postgres", "port": 5432, "host": "localhost", "connectOnStartup": true, "password": "smartloan123", "ssl": false, "name": "SmartLoan-Clean-0603-054846"}], "postgresql.defaultConnection": "SmartLoan-Clean-0603-054846", "java.compile.nullAnalysis.mode": "disabled", "java.server.launchMode": "LightWeight", "java.import.gradle.enabled": false, "java.import.maven.enabled": false, "java.autobuild.enabled": false, "java.maxConcurrentBuilds": 1, "java.jdt.ls.vmargs": "-XX:+UseParallelGC -Xmx1G -Xms100m", "redhat.telemetry.enabled": false, "java.configuration.runtimes": [{"name": "JavaSE-17", "path": "C:\\Program Files\\Microsoft\\jdk-*********-hotspot", "default": true}], "java.home": "C:\\Program Files\\Microsoft\\jdk-*********-hotspot", "java.errors.incompleteClasspath.severity": "ignore", "java.configuration.checkProjectSettingsExclusions": false, "typescript.preferences.noSemicolons": "off", "typescript.validate.enable": true, "typescript.suggest.autoImports": false, "typescript.preferences.includePackageJsonAutoImports": "off", "eslint.enable": false, "typescript.reportStyleChecksAsWarnings": false, "problems.decorations.enabled": false}