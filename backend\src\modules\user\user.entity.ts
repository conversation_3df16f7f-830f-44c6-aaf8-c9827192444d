import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Exclude } from 'class-transformer';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true, length: 50 })
  username: string;

  @Column({ unique: true, length: 100 })
  email: string;

  @Column({ unique: true, length: 20 })
  phone: string;

  @Column()
  @Exclude()
  password_hash: string;

  @Column({ length: 50, nullable: true })
  full_name: string;

  @Column({ type: 'date', nullable: true })
  birth_date: Date;

  @Column({ length: 10, nullable: true })
  gender: string;

  @Column({ length: 18, nullable: true })
  id_number: string;

  @Column('json', { nullable: true })
  employment_info: {
    company_name?: string;
    position?: string;
    employment_type?: string; // 'full_time', 'part_time', 'self_employed', 'unemployed'
    monthly_income?: number;
    work_years?: number;
    industry?: string;
  };

  @Column('json', { nullable: true })
  financial_info: {
    monthly_income?: number;
    other_income?: number;
    monthly_expenses?: number;
    assets?: number;
    liabilities?: number;
    credit_score?: number;
    bank_accounts?: string[];
  };

  @Column({ default: 'active' })
  status: string; // 'active', 'inactive', 'suspended'

  @Column({ default: false })
  is_verified: boolean;

  @Column({ default: false })
  kyc_completed: boolean;

  @Column({ type: 'timestamp', nullable: true })
  last_login: Date;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;

  // 计算年龄
  get age(): number {
    if (!this.birth_date) return 0;
    const today = new Date();
    const birthDate = new Date(this.birth_date);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }

  // 获取信用分数
  get creditScore(): number {
    return this.financial_info?.credit_score || 0;
  }

  // 获取月收入
  get monthlyIncome(): number {
    return this.employment_info?.monthly_income || this.financial_info?.monthly_income || 0;
  }

  // 获取就业类型
  get employmentType(): string {
    return this.employment_info?.employment_type || 'unemployed';
  }
}
