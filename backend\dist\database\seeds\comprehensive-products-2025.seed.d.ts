export declare const comprehensiveProducts2025: ({
    id: string;
    name: string;
    provider: string;
    institution_type: string;
    product_category: string;
    interest_rate_min: number;
    interest_rate_max: number;
    amount_min: number;
    amount_max: number;
    loan_term_min: number;
    loan_term_max: number;
    description: string;
    features: {
        fast_approval: boolean;
        online_application: boolean;
        digital_currency: boolean;
        ai_approval: boolean;
        gpu_acceleration: boolean;
        blockchain_credit?: undefined;
        metaverse_support?: undefined;
        green_finance?: undefined;
        rural_finance?: undefined;
        green_priority?: undefined;
        carbon_neutral?: undefined;
        multi_currency?: undefined;
        cross_border?: undefined;
        belt_road_support?: undefined;
        ai_risk_control?: undefined;
        instant_approval?: undefined;
        fintech_leader?: undefined;
        ai_credit_management?: undefined;
        cbdc_support?: undefined;
        smart_limit?: undefined;
        tech_innovation?: undefined;
        beijing_local?: undefined;
        startup_support?: undefined;
        big_data_risk?: undefined;
        web3_identity?: undefined;
        ecosystem_integration?: undefined;
        nft_collateral?: undefined;
        metaverse_consumption?: undefined;
        ecommerce_integration?: undefined;
        property_valuation_ai?: undefined;
        high_amount?: undefined;
        low_interest?: undefined;
        new_energy_discount?: undefined;
        iot_finance?: undefined;
        auto_insurance_bundle?: undefined;
    };
    requirements: {
        min_age: number;
        max_age: number;
        min_income: number;
        credit_score_min: number;
        employment_types: string[];
        collateral_required: boolean;
        collateral_type?: undefined;
    };
    priority_score: number;
    approval_rate: number;
    avg_approval_time: string;
} | {
    id: string;
    name: string;
    provider: string;
    institution_type: string;
    product_category: string;
    interest_rate_min: number;
    interest_rate_max: number;
    amount_min: number;
    amount_max: number;
    loan_term_min: number;
    loan_term_max: number;
    description: string;
    features: {
        fast_approval: boolean;
        blockchain_credit: boolean;
        metaverse_support: boolean;
        green_finance: boolean;
        online_application?: undefined;
        digital_currency?: undefined;
        ai_approval?: undefined;
        gpu_acceleration?: undefined;
        rural_finance?: undefined;
        green_priority?: undefined;
        carbon_neutral?: undefined;
        multi_currency?: undefined;
        cross_border?: undefined;
        belt_road_support?: undefined;
        ai_risk_control?: undefined;
        instant_approval?: undefined;
        fintech_leader?: undefined;
        ai_credit_management?: undefined;
        cbdc_support?: undefined;
        smart_limit?: undefined;
        tech_innovation?: undefined;
        beijing_local?: undefined;
        startup_support?: undefined;
        big_data_risk?: undefined;
        web3_identity?: undefined;
        ecosystem_integration?: undefined;
        nft_collateral?: undefined;
        metaverse_consumption?: undefined;
        ecommerce_integration?: undefined;
        property_valuation_ai?: undefined;
        high_amount?: undefined;
        low_interest?: undefined;
        new_energy_discount?: undefined;
        iot_finance?: undefined;
        auto_insurance_bundle?: undefined;
    };
    requirements: {
        min_age: number;
        max_age: number;
        min_income: number;
        credit_score_min: number;
        employment_types: string[];
        collateral_required: boolean;
        collateral_type?: undefined;
    };
    priority_score: number;
    approval_rate: number;
    avg_approval_time: string;
} | {
    id: string;
    name: string;
    provider: string;
    institution_type: string;
    product_category: string;
    interest_rate_min: number;
    interest_rate_max: number;
    amount_min: number;
    amount_max: number;
    loan_term_min: number;
    loan_term_max: number;
    description: string;
    features: {
        rural_finance: boolean;
        green_priority: boolean;
        carbon_neutral: boolean;
        fast_approval?: undefined;
        online_application?: undefined;
        digital_currency?: undefined;
        ai_approval?: undefined;
        gpu_acceleration?: undefined;
        blockchain_credit?: undefined;
        metaverse_support?: undefined;
        green_finance?: undefined;
        multi_currency?: undefined;
        cross_border?: undefined;
        belt_road_support?: undefined;
        ai_risk_control?: undefined;
        instant_approval?: undefined;
        fintech_leader?: undefined;
        ai_credit_management?: undefined;
        cbdc_support?: undefined;
        smart_limit?: undefined;
        tech_innovation?: undefined;
        beijing_local?: undefined;
        startup_support?: undefined;
        big_data_risk?: undefined;
        web3_identity?: undefined;
        ecosystem_integration?: undefined;
        nft_collateral?: undefined;
        metaverse_consumption?: undefined;
        ecommerce_integration?: undefined;
        property_valuation_ai?: undefined;
        high_amount?: undefined;
        low_interest?: undefined;
        new_energy_discount?: undefined;
        iot_finance?: undefined;
        auto_insurance_bundle?: undefined;
    };
    requirements: {
        min_age: number;
        max_age: number;
        min_income: number;
        credit_score_min: number;
        employment_types: string[];
        collateral_required: boolean;
        collateral_type?: undefined;
    };
    priority_score: number;
    approval_rate: number;
    avg_approval_time: string;
} | {
    id: string;
    name: string;
    provider: string;
    institution_type: string;
    product_category: string;
    interest_rate_min: number;
    interest_rate_max: number;
    amount_min: number;
    amount_max: number;
    loan_term_min: number;
    loan_term_max: number;
    description: string;
    features: {
        multi_currency: boolean;
        cross_border: boolean;
        belt_road_support: boolean;
        fast_approval?: undefined;
        online_application?: undefined;
        digital_currency?: undefined;
        ai_approval?: undefined;
        gpu_acceleration?: undefined;
        blockchain_credit?: undefined;
        metaverse_support?: undefined;
        green_finance?: undefined;
        rural_finance?: undefined;
        green_priority?: undefined;
        carbon_neutral?: undefined;
        ai_risk_control?: undefined;
        instant_approval?: undefined;
        fintech_leader?: undefined;
        ai_credit_management?: undefined;
        cbdc_support?: undefined;
        smart_limit?: undefined;
        tech_innovation?: undefined;
        beijing_local?: undefined;
        startup_support?: undefined;
        big_data_risk?: undefined;
        web3_identity?: undefined;
        ecosystem_integration?: undefined;
        nft_collateral?: undefined;
        metaverse_consumption?: undefined;
        ecommerce_integration?: undefined;
        property_valuation_ai?: undefined;
        high_amount?: undefined;
        low_interest?: undefined;
        new_energy_discount?: undefined;
        iot_finance?: undefined;
        auto_insurance_bundle?: undefined;
    };
    requirements: {
        min_age: number;
        max_age: number;
        min_income: number;
        credit_score_min: number;
        employment_types: string[];
        collateral_required: boolean;
        collateral_type?: undefined;
    };
    priority_score: number;
    approval_rate: number;
    avg_approval_time: string;
} | {
    id: string;
    name: string;
    provider: string;
    institution_type: string;
    product_category: string;
    interest_rate_min: number;
    interest_rate_max: number;
    amount_min: number;
    amount_max: number;
    loan_term_min: number;
    loan_term_max: number;
    description: string;
    features: {
        ai_risk_control: boolean;
        instant_approval: boolean;
        fintech_leader: boolean;
        fast_approval?: undefined;
        online_application?: undefined;
        digital_currency?: undefined;
        ai_approval?: undefined;
        gpu_acceleration?: undefined;
        blockchain_credit?: undefined;
        metaverse_support?: undefined;
        green_finance?: undefined;
        rural_finance?: undefined;
        green_priority?: undefined;
        carbon_neutral?: undefined;
        multi_currency?: undefined;
        cross_border?: undefined;
        belt_road_support?: undefined;
        ai_credit_management?: undefined;
        cbdc_support?: undefined;
        smart_limit?: undefined;
        tech_innovation?: undefined;
        beijing_local?: undefined;
        startup_support?: undefined;
        big_data_risk?: undefined;
        web3_identity?: undefined;
        ecosystem_integration?: undefined;
        nft_collateral?: undefined;
        metaverse_consumption?: undefined;
        ecommerce_integration?: undefined;
        property_valuation_ai?: undefined;
        high_amount?: undefined;
        low_interest?: undefined;
        new_energy_discount?: undefined;
        iot_finance?: undefined;
        auto_insurance_bundle?: undefined;
    };
    requirements: {
        min_age: number;
        max_age: number;
        min_income: number;
        credit_score_min: number;
        employment_types: string[];
        collateral_required: boolean;
        collateral_type?: undefined;
    };
    priority_score: number;
    approval_rate: number;
    avg_approval_time: string;
} | {
    id: string;
    name: string;
    provider: string;
    institution_type: string;
    product_category: string;
    interest_rate_min: number;
    interest_rate_max: number;
    amount_min: number;
    amount_max: number;
    loan_term_min: number;
    loan_term_max: number;
    description: string;
    features: {
        ai_credit_management: boolean;
        cbdc_support: boolean;
        smart_limit: boolean;
        fast_approval?: undefined;
        online_application?: undefined;
        digital_currency?: undefined;
        ai_approval?: undefined;
        gpu_acceleration?: undefined;
        blockchain_credit?: undefined;
        metaverse_support?: undefined;
        green_finance?: undefined;
        rural_finance?: undefined;
        green_priority?: undefined;
        carbon_neutral?: undefined;
        multi_currency?: undefined;
        cross_border?: undefined;
        belt_road_support?: undefined;
        ai_risk_control?: undefined;
        instant_approval?: undefined;
        fintech_leader?: undefined;
        tech_innovation?: undefined;
        beijing_local?: undefined;
        startup_support?: undefined;
        big_data_risk?: undefined;
        web3_identity?: undefined;
        ecosystem_integration?: undefined;
        nft_collateral?: undefined;
        metaverse_consumption?: undefined;
        ecommerce_integration?: undefined;
        property_valuation_ai?: undefined;
        high_amount?: undefined;
        low_interest?: undefined;
        new_energy_discount?: undefined;
        iot_finance?: undefined;
        auto_insurance_bundle?: undefined;
    };
    requirements: {
        min_age: number;
        max_age: number;
        min_income: number;
        credit_score_min: number;
        employment_types: string[];
        collateral_required: boolean;
        collateral_type?: undefined;
    };
    priority_score: number;
    approval_rate: number;
    avg_approval_time: string;
} | {
    id: string;
    name: string;
    provider: string;
    institution_type: string;
    product_category: string;
    interest_rate_min: number;
    interest_rate_max: number;
    amount_min: number;
    amount_max: number;
    loan_term_min: number;
    loan_term_max: number;
    description: string;
    features: {
        tech_innovation: boolean;
        beijing_local: boolean;
        startup_support: boolean;
        fast_approval?: undefined;
        online_application?: undefined;
        digital_currency?: undefined;
        ai_approval?: undefined;
        gpu_acceleration?: undefined;
        blockchain_credit?: undefined;
        metaverse_support?: undefined;
        green_finance?: undefined;
        rural_finance?: undefined;
        green_priority?: undefined;
        carbon_neutral?: undefined;
        multi_currency?: undefined;
        cross_border?: undefined;
        belt_road_support?: undefined;
        ai_risk_control?: undefined;
        instant_approval?: undefined;
        fintech_leader?: undefined;
        ai_credit_management?: undefined;
        cbdc_support?: undefined;
        smart_limit?: undefined;
        big_data_risk?: undefined;
        web3_identity?: undefined;
        ecosystem_integration?: undefined;
        nft_collateral?: undefined;
        metaverse_consumption?: undefined;
        ecommerce_integration?: undefined;
        property_valuation_ai?: undefined;
        high_amount?: undefined;
        low_interest?: undefined;
        new_energy_discount?: undefined;
        iot_finance?: undefined;
        auto_insurance_bundle?: undefined;
    };
    requirements: {
        min_age: number;
        max_age: number;
        min_income: number;
        credit_score_min: number;
        employment_types: string[];
        collateral_required: boolean;
        collateral_type?: undefined;
    };
    priority_score: number;
    approval_rate: number;
    avg_approval_time: string;
} | {
    id: string;
    name: string;
    provider: string;
    institution_type: string;
    product_category: string;
    interest_rate_min: number;
    interest_rate_max: number;
    amount_min: number;
    amount_max: number;
    loan_term_min: number;
    loan_term_max: number;
    description: string;
    features: {
        big_data_risk: boolean;
        web3_identity: boolean;
        green_finance: boolean;
        ecosystem_integration: boolean;
        fast_approval?: undefined;
        online_application?: undefined;
        digital_currency?: undefined;
        ai_approval?: undefined;
        gpu_acceleration?: undefined;
        blockchain_credit?: undefined;
        metaverse_support?: undefined;
        rural_finance?: undefined;
        green_priority?: undefined;
        carbon_neutral?: undefined;
        multi_currency?: undefined;
        cross_border?: undefined;
        belt_road_support?: undefined;
        ai_risk_control?: undefined;
        instant_approval?: undefined;
        fintech_leader?: undefined;
        ai_credit_management?: undefined;
        cbdc_support?: undefined;
        smart_limit?: undefined;
        tech_innovation?: undefined;
        beijing_local?: undefined;
        startup_support?: undefined;
        nft_collateral?: undefined;
        metaverse_consumption?: undefined;
        ecommerce_integration?: undefined;
        property_valuation_ai?: undefined;
        high_amount?: undefined;
        low_interest?: undefined;
        new_energy_discount?: undefined;
        iot_finance?: undefined;
        auto_insurance_bundle?: undefined;
    };
    requirements: {
        min_age: number;
        max_age: number;
        min_income: number;
        credit_score_min: number;
        employment_types: string[];
        collateral_required: boolean;
        collateral_type?: undefined;
    };
    priority_score: number;
    approval_rate: number;
    avg_approval_time: string;
} | {
    id: string;
    name: string;
    provider: string;
    institution_type: string;
    product_category: string;
    interest_rate_min: number;
    interest_rate_max: number;
    amount_min: number;
    amount_max: number;
    loan_term_min: number;
    loan_term_max: number;
    description: string;
    features: {
        nft_collateral: boolean;
        metaverse_consumption: boolean;
        ecommerce_integration: boolean;
        fast_approval?: undefined;
        online_application?: undefined;
        digital_currency?: undefined;
        ai_approval?: undefined;
        gpu_acceleration?: undefined;
        blockchain_credit?: undefined;
        metaverse_support?: undefined;
        green_finance?: undefined;
        rural_finance?: undefined;
        green_priority?: undefined;
        carbon_neutral?: undefined;
        multi_currency?: undefined;
        cross_border?: undefined;
        belt_road_support?: undefined;
        ai_risk_control?: undefined;
        instant_approval?: undefined;
        fintech_leader?: undefined;
        ai_credit_management?: undefined;
        cbdc_support?: undefined;
        smart_limit?: undefined;
        tech_innovation?: undefined;
        beijing_local?: undefined;
        startup_support?: undefined;
        big_data_risk?: undefined;
        web3_identity?: undefined;
        ecosystem_integration?: undefined;
        property_valuation_ai?: undefined;
        high_amount?: undefined;
        low_interest?: undefined;
        new_energy_discount?: undefined;
        iot_finance?: undefined;
        auto_insurance_bundle?: undefined;
    };
    requirements: {
        min_age: number;
        max_age: number;
        min_income: number;
        credit_score_min: number;
        employment_types: string[];
        collateral_required: boolean;
        collateral_type?: undefined;
    };
    priority_score: number;
    approval_rate: number;
    avg_approval_time: string;
} | {
    id: string;
    name: string;
    provider: string;
    institution_type: string;
    product_category: string;
    interest_rate_min: number;
    interest_rate_max: number;
    amount_min: number;
    amount_max: number;
    loan_term_min: number;
    loan_term_max: number;
    description: string;
    features: {
        property_valuation_ai: boolean;
        high_amount: boolean;
        low_interest: boolean;
        fast_approval?: undefined;
        online_application?: undefined;
        digital_currency?: undefined;
        ai_approval?: undefined;
        gpu_acceleration?: undefined;
        blockchain_credit?: undefined;
        metaverse_support?: undefined;
        green_finance?: undefined;
        rural_finance?: undefined;
        green_priority?: undefined;
        carbon_neutral?: undefined;
        multi_currency?: undefined;
        cross_border?: undefined;
        belt_road_support?: undefined;
        ai_risk_control?: undefined;
        instant_approval?: undefined;
        fintech_leader?: undefined;
        ai_credit_management?: undefined;
        cbdc_support?: undefined;
        smart_limit?: undefined;
        tech_innovation?: undefined;
        beijing_local?: undefined;
        startup_support?: undefined;
        big_data_risk?: undefined;
        web3_identity?: undefined;
        ecosystem_integration?: undefined;
        nft_collateral?: undefined;
        metaverse_consumption?: undefined;
        ecommerce_integration?: undefined;
        new_energy_discount?: undefined;
        iot_finance?: undefined;
        auto_insurance_bundle?: undefined;
    };
    requirements: {
        min_age: number;
        max_age: number;
        min_income: number;
        credit_score_min: number;
        employment_types: string[];
        collateral_required: boolean;
        collateral_type: string;
    };
    priority_score: number;
    approval_rate: number;
    avg_approval_time: string;
} | {
    id: string;
    name: string;
    provider: string;
    institution_type: string;
    product_category: string;
    interest_rate_min: number;
    interest_rate_max: number;
    amount_min: number;
    amount_max: number;
    loan_term_min: number;
    loan_term_max: number;
    description: string;
    features: {
        new_energy_discount: boolean;
        iot_finance: boolean;
        auto_insurance_bundle: boolean;
        fast_approval?: undefined;
        online_application?: undefined;
        digital_currency?: undefined;
        ai_approval?: undefined;
        gpu_acceleration?: undefined;
        blockchain_credit?: undefined;
        metaverse_support?: undefined;
        green_finance?: undefined;
        rural_finance?: undefined;
        green_priority?: undefined;
        carbon_neutral?: undefined;
        multi_currency?: undefined;
        cross_border?: undefined;
        belt_road_support?: undefined;
        ai_risk_control?: undefined;
        instant_approval?: undefined;
        fintech_leader?: undefined;
        ai_credit_management?: undefined;
        cbdc_support?: undefined;
        smart_limit?: undefined;
        tech_innovation?: undefined;
        beijing_local?: undefined;
        startup_support?: undefined;
        big_data_risk?: undefined;
        web3_identity?: undefined;
        ecosystem_integration?: undefined;
        nft_collateral?: undefined;
        metaverse_consumption?: undefined;
        ecommerce_integration?: undefined;
        property_valuation_ai?: undefined;
        high_amount?: undefined;
        low_interest?: undefined;
    };
    requirements: {
        min_age: number;
        max_age: number;
        min_income: number;
        credit_score_min: number;
        employment_types: string[];
        collateral_required: boolean;
        collateral_type: string;
    };
    priority_score: number;
    approval_rate: number;
    avg_approval_time: string;
})[];
export declare const loanCalculatorConfig: {
    types: string[];
    repayment_methods: string[];
    interest_rates: {
        provident_fund: {
            five_years_below: number;
            five_years_above: number;
        };
        commercial: {
            one_year_below: number;
            one_to_five_years: number;
            five_years_above: number;
        };
    };
};
export default comprehensiveProducts2025;
