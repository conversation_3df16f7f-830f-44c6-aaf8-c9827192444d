import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RiskFactorDto } from '../dto/risk-factor.dto';
import { VerificationResultDto } from '../dto/verification-result.dto';
import { DecisionFactorDto } from '../dto/decision-factor.dto';
import { LoanReview } from '../entities/loan-review.entity';
import { CreateLoanReviewDto } from '../dto/create-loan-review.dto';
import { LoanApplication } from '../entities/loan-application.entity';
import { LoggerService } from '../logger/logger.service';
import { ReviewType } from '../entities/loan-review.entity';

@Injectable()
export class LoanReviewService {
  constructor(
    @InjectRepository(LoanReview)
    private readonly loanReviewRepository: Repository<LoanReview>,
    @InjectRepository(LoanApplication)
    private readonly loanApplicationRepository: Repository<LoanApplication>,
    private readonly logger: LoggerService
  ) {}
  async create(createDto: CreateLoanReviewDto, reviewerId: number): Promise<LoanReview> {
    try {
      const application = await this.loanApplicationRepository.findOne({
        where: { id: createDto.loanApplicationId.toString() }
      });

      if (!application) {
        throw new NotFoundException(`Loan application ${createDto.loanApplicationId} not found`);
      }      const reviewData = {
        loanApplicationId: createDto.loanApplicationId,
        reviewerId: reviewerId,
        status: createDto.status,
        type: createDto.type,
        comments: createDto.comments,
        riskFactors: createDto.riskFactors,
        verificationResults: createDto.verificationResults,
        decisionFactors: createDto.decisionFactors,
      };
      
      const review = this.loanReviewRepository.create(reviewData);
      const saved = await this.loanReviewRepository.save(review);
      const result = Array.isArray(saved) ? saved[0] : saved;
      this.logger.log(`Created loan review ${result.id} for application ${createDto.loanApplicationId}`);
      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to create loan review: ${error.message}`);
      throw new BadRequestException('Failed to create loan review');
    }
  }

  async findAll(loanApplicationId: number): Promise<LoanReview[]> {
    try {
      return await this.loanReviewRepository.find({
        where: { loanApplicationId },
        order: { createdAt: 'DESC' }
      });
    } catch (error) {
      this.logger.error(`Failed to fetch loan reviews: ${error.message}`);
      throw new BadRequestException('Failed to fetch loan reviews');
    }
  }

  async findOne(id: number): Promise<LoanReview> {
    try {
      const review = await this.loanReviewRepository.findOne({
        where: { id }
      });

      if (!review) {
        throw new NotFoundException(`Loan review ${id} not found`);
      }

      return review;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch loan review ${id}: ${error.message}`);
      throw new BadRequestException('Failed to fetch loan review');
    }
  }

  async getReviewStatistics(loanApplicationId: number): Promise<any> {
    try {
      const reviews = await this.findAll(loanApplicationId);
      
      const statistics = {
        total: reviews.length,
        byType: {},
        byStatus: {},
        averageRiskScore: 0
      };

      reviews.forEach(review => {
        // Count by type
        statistics.byType[review.type] = (statistics.byType[review.type] || 0) + 1;
        
        // Count by status
        statistics.byStatus[review.status] = (statistics.byStatus[review.status] || 0) + 1;
        
        // Calculate average risk score
        if (review.riskFactors) {
          const riskScore = review.riskFactors.reduce((sum, factor) => sum + factor.score * factor.weight, 0);
          statistics.averageRiskScore += riskScore;
        }
      });

      if (reviews.length > 0) {
        statistics.averageRiskScore /= reviews.length;
      }

      return statistics;
    } catch (error) {
      this.logger.error(`Failed to get review statistics: ${error.message}`);
      throw new BadRequestException('Failed to get review statistics');
    }
  }
} 