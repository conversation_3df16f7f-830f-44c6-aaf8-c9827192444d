/// <reference types="node" />
/// <reference types="node" />
/// <reference types="multer" />
import { Repository } from 'typeorm';
import { Product } from '../modules/product/entities/product.entity';
import { CreateProductDto, UpdateProductDto, ProductFilterDto } from '../dto/product.dto';
import { RedisService } from './redis.service';
import { CacheService } from './cache.service';
import { LoggerService } from './logger.service';
export declare class ProductService {
    private readonly productRepository;
    private readonly redisService;
    private readonly cacheService;
    private readonly logger;
    private readonly CACHE_TTL;
    constructor(productRepository: Repository<Product>, redisService: RedisService, cacheService: CacheService, logger: LoggerService);
    private getCacheKey;
    create(createProductDto: CreateProductDto): Promise<Product>;
    update(id: number, updateProductDto: UpdateProductDto): Promise<{
        name: string;
        code: string;
        description: string;
        category: any;
        minAmount?: number;
        maxAmount?: number;
        minTerm: number;
        maxTerm: number;
        interestRate?: number;
        processingFee: number;
        lateFee?: number;
        earlyRepaymentFee: number;
        features: any;
        benefits?: string[];
        requirements: any;
        metadata?: import("../dto/product.dto").ProductMetadataDto;
        isActive: boolean;
        isFeatured: boolean;
        sortOrder: number;
        id: number;
        institution: import("../modules/product/entities/institution.entity").Institution;
        type: string;
        min_amount: number;
        max_amount: number;
        interest_rate: number;
        term_range: number[];
        created_at: Date;
        updated_at: Date;
    } & Product>;
    delete(id: number): Promise<{
        message: string;
    }>;
    findAll(filterDto: ProductFilterDto): Promise<any>;
    findById(id: number): Promise<any>;
    findByCode(code: string): Promise<any>;
    private clearProductCache;
    getProductStatistics(): Promise<any>;
    exportToExcel(): Promise<Buffer>;
    importFromExcel(file: Express.Multer.File): Promise<void>;
    findAllProducts(params: {
        page?: number;
        pageSize?: number;
        category?: string;
        isActive?: boolean;
        isFeatured?: boolean;
    }): Promise<{
        products: Product[];
        total: number;
        page: number;
        pageSize: number;
    }>;
    findProductById(id: number): Promise<Product>;
    findProductByCode(code: string): Promise<Product>;
    updateProductStatus(id: number, isActive: boolean): Promise<Product>;
    updateProductSortOrder(id: number, sortOrder: number): Promise<Product>;
    updateProductFeatured(id: number, isFeatured: boolean): Promise<Product>;
    uploadProducts(file: Express.Multer.File): Promise<{
        products: Product[];
        errors: any[];
    }>;
    updateProduct(id: number, updateProductDto: UpdateProductDto): Promise<Product>;
    private mapHeaders;
    private parseRow;
    private transformProductData;
}
