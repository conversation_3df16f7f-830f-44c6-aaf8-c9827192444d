# SmartLoan 智能金融服务平台启动脚本
# PowerShell 脚本用于启动整个系统

Write-Host "🚀 启动 SmartLoan 智能金融服务平台..." -ForegroundColor Green

# 检查 Docker 是否运行
Write-Host "📋 检查 Docker 状态..." -ForegroundColor Yellow
try {
    docker --version | Out-Null
    Write-Host "✅ Docker 已安装" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker 未安装或未运行，请先安装 Docker" -ForegroundColor Red
    exit 1
}

# 检查 Node.js 是否安装
Write-Host "📋 检查 Node.js 状态..." -ForegroundColor Yellow
try {
    node --version | Out-Null
    Write-Host "✅ Node.js 已安装" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 未安装，请先安装 Node.js" -ForegroundColor Red
    exit 1
}

# 启动数据库服务
Write-Host "🗄️ 启动数据库服务..." -ForegroundColor Yellow
docker-compose up -d postgres redis

# 等待数据库启动
Write-Host "⏳ 等待数据库启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 检查数据库连接
Write-Host "🔍 检查数据库连接..." -ForegroundColor Yellow
$dbReady = $false
$maxRetries = 30
$retryCount = 0

while (-not $dbReady -and $retryCount -lt $maxRetries) {
    try {
        $result = docker exec smartloan-postgres pg_isready -U smartloan_user -d smartloan_db
        if ($result -match "accepting connections") {
            $dbReady = $true
            Write-Host "✅ 数据库连接成功" -ForegroundColor Green
        }
    } catch {
        $retryCount++
        Write-Host "⏳ 等待数据库连接... ($retryCount/$maxRetries)" -ForegroundColor Yellow
        Start-Sleep -Seconds 2
    }
}

if (-not $dbReady) {
    Write-Host "❌ 数据库连接失败" -ForegroundColor Red
    exit 1
}

# 安装后端依赖
Write-Host "📦 安装后端依赖..." -ForegroundColor Yellow
Set-Location backend
if (-not (Test-Path "node_modules")) {
    npm install
}

# 运行数据库迁移和种子数据
Write-Host "🌱 初始化数据库..." -ForegroundColor Yellow
npm run migration:run 2>$null
npm run seed 2>$null

# 启动后端服务
Write-Host "🔧 启动后端服务..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-Command", "cd '$PWD'; npm run start:dev" -WindowStyle Minimized

# 返回根目录
Set-Location ..

# 安装前端依赖
Write-Host "📦 安装前端依赖..." -ForegroundColor Yellow
Set-Location frontend
if (-not (Test-Path "node_modules")) {
    npm install
}

# 启动前端服务
Write-Host "🎨 启动前端服务..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-Command", "cd '$PWD'; npm run dev" -WindowStyle Minimized

# 返回根目录
Set-Location ..

# 安装AI服务依赖
Write-Host "📦 安装AI服务依赖..." -ForegroundColor Yellow
Set-Location ai-service
if (-not (Test-Path "node_modules")) {
    npm install
}

# 启动AI服务
Write-Host "🤖 启动AI服务..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-Command", "cd '$PWD'; npm run start:dev" -WindowStyle Minimized

# 返回根目录
Set-Location ..

# 等待服务启动
Write-Host "⏳ 等待所有服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# 检查服务状态
Write-Host "🔍 检查服务状态..." -ForegroundColor Yellow

# 检查后端服务
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -TimeoutSec 5 -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 后端服务运行正常 (http://localhost:3001)" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ 后端服务可能还在启动中..." -ForegroundColor Yellow
}

# 检查前端服务
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5173" -TimeoutSec 5 -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 前端服务运行正常 (http://localhost:5173)" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ 前端服务可能还在启动中..." -ForegroundColor Yellow
}

# 检查AI服务
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3002/health" -TimeoutSec 5 -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ AI服务运行正常 (http://localhost:3002)" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ AI服务可能还在启动中..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 SmartLoan 智能金融服务平台启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📱 访问地址:" -ForegroundColor Cyan
Write-Host "   前端应用: http://localhost:5173" -ForegroundColor White
Write-Host "   后端API: http://localhost:3001" -ForegroundColor White
Write-Host "   AI服务:  http://localhost:3002" -ForegroundColor White
Write-Host ""
Write-Host "🔧 管理命令:" -ForegroundColor Cyan
Write-Host "   查看日志: docker-compose logs -f" -ForegroundColor White
Write-Host "   停止服务: docker-compose down" -ForegroundColor White
Write-Host "   重启数据库: docker-compose restart postgres redis" -ForegroundColor White
Write-Host ""
Write-Host "📊 核心功能:" -ForegroundColor Cyan
Write-Host "   ✨ 智能产品匹配 - AI驱动的个性化推荐" -ForegroundColor White
Write-Host "   🔍 多模态资质审核 - OCR识别 + 活体检测" -ForegroundColor White
Write-Host "   📈 实时风控看板 - 动态风险监控" -ForegroundColor White
Write-Host "   🤖 AI虚拟顾问 - 7×24小时智能服务" -ForegroundColor White
Write-Host ""

# 自动打开浏览器
Write-Host "🌐 正在打开浏览器..." -ForegroundColor Yellow
Start-Sleep -Seconds 3
Start-Process "http://localhost:5173"

Write-Host "✅ 系统启动完成，请在浏览器中查看应用！" -ForegroundColor Green
