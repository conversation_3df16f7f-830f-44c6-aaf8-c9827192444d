// SmartLoan 2025年智能金融服务后端API
const express = require('express');
const cors = require('cors');
const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 2025年最新金融产品数据
const financialProducts2025 = [
  {
    id: 1,
    name: '工商银行融e借2025版',
    provider: '中国工商银行',
    product_type: 'personal',
    interest_rate: 3.85,
    amount_min: 1000,
    amount_max: 800000,
    loan_term_min: 1,
    loan_term_max: 60,
    description: '2025年全新升级，AI智能审批，30秒放款，支持数字人民币',
    features: {
      fast_approval: true,
      online_application: true,
      digital_currency: true,
      ai_approval: true
    },
    requirements: {
      min_age: 18,
      max_age: 65,
      min_income: 3000,
      credit_score_min: 600
    }
  },
  {
    id: 2,
    name: '建设银行快贷Pro 2025',
    provider: '中国建设银行',
    product_type: 'personal',
    interest_rate: 3.95,
    amount_min: 1000,
    amount_max: 500000,
    loan_term_min: 1,
    loan_term_max: 60,
    description: '2025年全新升级，支持元宇宙场景，区块链征信，智能风控',
    features: {
      fast_approval: true,
      blockchain_credit: true,
      metaverse_support: true
    },
    requirements: {
      min_age: 18,
      max_age: 70,
      min_income: 2500,
      credit_score_min: 580
    }
  },
  {
    id: 3,
    name: '蚂蚁借呗升级版2025',
    provider: '蚂蚁金服',
    product_type: 'personal',
    interest_rate: 4.2,
    amount_min: 500,
    amount_max: 300000,
    loan_term_min: 1,
    loan_term_max: 12,
    description: '基于大数据风控，支持Web3.0身份认证，碳中和绿色金融',
    features: {
      fast_approval: true,
      web3_identity: true,
      green_finance: true
    },
    requirements: {
      min_age: 18,
      max_age: 65,
      min_income: 1500,
      credit_score_min: 520
    }
  }
];

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'SmartLoan Backend API 2025',
    version: '2025.1.0',
    features: ['AI智能匹配', '多模态审核', '实时风控', 'AI虚拟顾问']
  });
});

// 智能产品匹配API
app.post('/api/products/match/smart', (req, res) => {
  const { amount, term_months, product_type, user_profile } = req.body;
  
  console.log('收到智能匹配请求:', { amount, term_months, product_type, user_profile });
  
  // AI智能匹配算法
  const matches = financialProducts2025
    .filter(product => product.product_type === (product_type || 'personal'))
    .map(product => {
      let score = 0;
      let reasoning = [];
      
      // 金额匹配度 (30%)
      if (amount >= product.amount_min && amount <= product.amount_max) {
        score += 30;
        reasoning.push('贷款金额符合要求');
      }
      
      // 用户资质匹配度 (40%)
      if (user_profile && user_profile.credit_score >= product.requirements.credit_score_min) {
        score += 20;
        reasoning.push('信用分数优秀');
      }
      if (user_profile && user_profile.income >= product.requirements.min_income) {
        score += 20;
        reasoning.push('收入水平良好');
      }
      
      // 产品特性加分 (30%)
      if (product.features.fast_approval) {
        score += 10;
        reasoning.push('支持快速审批');
      }
      if (product.features.ai_approval) {
        score += 10;
        reasoning.push('AI智能审批');
      }
      if (product.features.digital_currency) {
        score += 10;
        reasoning.push('支持数字人民币');
      }
      
      // 计算推荐利率
      let recommended_rate = product.interest_rate;
      if (user_profile && user_profile.credit_score >= 750) recommended_rate *= 0.95;
      else if (user_profile && user_profile.credit_score >= 700) recommended_rate *= 0.98;
      
      return {
        product,
        match_score: score / 100,
        ai_reasoning: reasoning.join('，') || '基于2025年AI算法推荐',
        recommended_amount: Math.min(amount, product.amount_max),
        recommended_rate: Math.round(recommended_rate * 100) / 100
      };
    })
    .filter(match => match.match_score > 0.3)
    .sort((a, b) => b.match_score - a.match_score)
    .slice(0, 3);
  
  res.json({
    success: true,
    data: matches,
    message: '智能产品匹配成功',
    total: matches.length,
    timestamp: new Date().toISOString()
  });
});

// AI风险评估API
app.post('/api/ai/risk-assessment', (req, res) => {
  const { user_profile, loan_application, financial_data } = req.body;
  
  console.log('收到风险评估请求:', { user_profile, loan_application, financial_data });
  
  // 2025年最新AI风控算法
  let riskScore = 50;
  let riskFactors = [];
  
  // 信用分数影响
  if (financial_data && financial_data.credit_score >= 750) {
    riskScore += 25;
  } else if (financial_data && financial_data.credit_score >= 700) {
    riskScore += 15;
  } else if (financial_data && financial_data.credit_score < 600) {
    riskScore -= 20;
    riskFactors.push('信用分数较低');
  }
  
  // 收入稳定性
  if (user_profile && user_profile.employment_type === 'full_time') {
    riskScore += 20;
  } else if (user_profile && user_profile.employment_type === 'gig_economy') {
    riskScore += 10; // 2025年零工经济被认可
  }
  
  // 确定风险等级
  let riskLevel = 'medium';
  if (riskScore >= 80) riskLevel = 'low';
  else if (riskScore <= 40) riskLevel = 'high';
  
  const assessment = {
    risk_score: Math.max(0, Math.min(100, riskScore)),
    risk_level: riskLevel,
    risk_factors: riskFactors,
    recommendation: generateRiskRecommendation(riskScore),
    analysis_timestamp: new Date().toISOString(),
    ai_version: '2025.1.0'
  };
  
  res.json({
    success: true,
    data: assessment,
    message: 'AI风险评估完成'
  });
});

// AI顾问对话API
app.post('/api/ai/advisor/chat', (req, res) => {
  const { query } = req.body;
  
  console.log('收到AI顾问请求:', query);
  
  const responses = {
    '贷款利率': '2025年央行基准利率下调后，个人信用贷款利率普遍在3.8%-6.5%之间，具体利率根据您的信用状况确定。',
    '申请条件': '2025年贷款申请更加便民：年满18周岁、稳定收入、良好信用记录。支持数字人民币。',
    '审批时间': '采用2025年最新AI技术，最快30秒完成审批。',
    '数字货币': '全面支持数字人民币(CBDC)，享受更低手续费。'
  };
  
  let response = '您好！我是SmartLoan 2025年AI金融顾问。请问您想了解什么？';
  
  for (const [keyword, answer] of Object.entries(responses)) {
    if (query && query.includes(keyword)) {
      response = answer;
      break;
    }
  }
  
  res.json({
    success: true,
    data: {
      response,
      confidence: 0.95,
      timestamp: new Date().toISOString(),
      ai_model: 'Fin-R1-2025'
    },
    message: 'AI顾问响应成功'
  });
});

// OCR识别API
app.post('/api/ai/ocr', (req, res) => {
  console.log('收到OCR识别请求');
  
  const mockOcrResult = {
    type: 'ID_CARD',
    confidence: 0.98,
    name: '张三',
    id_number: '110101199001011234',
    raw_text: '身份证识别成功',
    ai_version: '2025.1.0'
  };
  
  res.json({
    success: true,
    data: mockOcrResult,
    message: '证件识别成功'
  });
});

// 活体检测API
app.post('/api/ai/liveness', (req, res) => {
  console.log('收到活体检测请求');
  
  const mockLivenessResult = {
    is_live: Math.random() > 0.1,
    confidence: Math.random() * 0.3 + 0.7,
    face_detected: true,
    quality_score: Math.random() * 0.2 + 0.8,
    ai_version: '2025.1.0'
  };
  
  res.json({
    success: true,
    data: mockLivenessResult,
    message: '活体检测完成'
  });
});

// 风险推荐生成函数
function generateRiskRecommendation(riskScore) {
  if (riskScore >= 80) {
    return '风险较低，建议批准贷款申请。可享受优惠利率。';
  } else if (riskScore >= 60) {
    return '风险中等，建议进一步审核后决定。';
  } else {
    return '风险较高，建议谨慎审核或降低贷款金额。';
  }
}

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 SmartLoan 2025 Backend API 已启动`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
  console.log(`🎯 核心功能: AI智能匹配、多模态审核、实时风控`);
});

module.exports = app;
