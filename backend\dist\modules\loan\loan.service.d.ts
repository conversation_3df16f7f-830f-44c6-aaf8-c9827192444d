import { Repository } from 'typeorm';
import { LoanApplication } from './loan.entity';
import { User } from '../user/user.entity';
import { FinancialProduct } from '../product/entities/financial-product.entity';
export interface CreateLoanApplicationDto {
    user_id: number;
    product_id: number;
    amount: number;
    purpose?: string;
    employment_info?: any;
    financial_info?: any;
    collateral_info?: any;
}
export interface LoanApplicationWithDetails extends LoanApplication {
    user: User;
    product: FinancialProduct;
}
export declare class LoanService {
    private loanRepository;
    private userRepository;
    private productRepository;
    private readonly logger;
    constructor(loanRepository: Repository<LoanApplication>, userRepository: Repository<User>, productRepository: Repository<FinancialProduct>);
    createApplication(createDto: CreateLoanApplicationDto): Promise<LoanApplication>;
    getApplicationById(id: number): Promise<LoanApplicationWithDetails>;
    getApplicationsByUser(userId: number): Promise<LoanApplication[]>;
    getAllApplications(page?: number, limit?: number): Promise<{
        applications: LoanApplicationWithDetails[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    updateApplicationStatus(id: number, status: string, officerId?: number, comment?: string): Promise<LoanApplication>;
    approveApplication(id: number, approvalData: {
        approval_amount: number;
        approved_rate: number;
        approved_term: number;
        officer_id: number;
        comment?: string;
    }): Promise<LoanApplication>;
    rejectApplication(id: number, rejectionData: {
        rejection_reason: string;
        officer_id: number;
    }): Promise<LoanApplication>;
    disburseApplication(id: number, officerId: number): Promise<LoanApplication>;
    updateAIAssessment(id: number, aiData: {
        ai_score: number;
        ai_recommendation: string;
        risk_assessment?: any;
    }): Promise<LoanApplication>;
    getApplicationStatistics(): Promise<{
        total: number;
        pending: number;
        under_review: number;
        approved: number;
        rejected: number;
        disbursed: number;
        total_amount: number;
        approved_amount: number;
    }>;
}
