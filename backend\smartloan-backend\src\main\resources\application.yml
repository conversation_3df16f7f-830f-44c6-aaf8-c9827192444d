server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: smartloan-backend
  cloud:
    gateway:
      routes:
        - id: api_route
          uri: lb://smartloan-backend
          predicates:
            - Path=/api/**
          filters:
            - StripPrefix=1
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                redis-rate-limiter.requestedTokens: 1
    
  # 数据库配置
  datasource:
    url: *******************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # JPA配置
  jpa:
    database-platform: org.hibernate.dialect.MySQLDialect
    hibernate:
      ddl-auto: update
    show-sql: true
    
  # 安全配置
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${JWT_JWK_SET_URI:http://localhost:9000/oauth2/jwks}
          issuer-uri: ${JWT_ISSUER_URI:http://localhost:9000}
          
# JWT配置
jwt:
  secret: ${JWT_SECRET:a123456789012345678901234567890b}
  expiration: ${JWT_EXPIRATION:86400} # 24小时
  
# MetaX GPU配置
metax:
  gpu:
    enabled: true
    url: ${METAX_GPU_URL:http://localhost:5000}
    apiKey: ${METAX_GPU_KEY:your-api-key}
    
# Gitee AI配置
gitee:
  ai:
    enabled: true
    url: ${GITEE_AI_URL:https://ai.gitee.com}
    token: ${GITEE_AI_TOKEN:your-token}
    
# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    tags:
      application: ${spring.application.name}
    distribution:
      percentiles-histogram:
        http.server.requests: true
      slo:
        http.server.requests: 50ms, 100ms, 200ms

# 日志配置
logging:
  level:
    root: INFO
    com.smartloan: DEBUG
    org.springframework.security: INFO
    org.springframework.web: INFO
    org.springframework.cloud.gateway: DEBUG
