import { Repository } from 'typeorm';
import { Product } from './entities/product.entity';
import { Institution } from './entities/institution.entity';
import { GpuService } from '../../services/gpu.service';
export declare class ProductMatchingService {
    private readonly productRepository;
    private readonly institutionRepository;
    private cacheManager;
    private gpuService;
    private readonly logger;
    private readonly CACHE_TTL;
    constructor(productRepository: Repository<Product>, institutionRepository: Repository<Institution>, cacheManager: any, gpuService: GpuService);
    matchProducts(userProfile: any, requirements: any): Promise<Product[]>;
    private calculateMatchScore;
    private calculateAmountScore;
    private calculateTermScore;
    private calculateRateScore;
    private calculateProfileScore;
}
