import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LoanApplication } from './loan.entity';
import { User } from '../user/user.entity';
import { FinancialProduct } from '../product/entities/financial-product.entity';

export interface CreateLoanApplicationDto {
  user_id: number;
  product_id: number;
  amount: number;
  purpose?: string;
  employment_info?: any;
  financial_info?: any;
  collateral_info?: any;
}

export interface LoanApplicationWithDetails extends LoanApplication {
  user: User;
  product: FinancialProduct;
}

@Injectable()
export class LoanService {
  private readonly logger = new Logger(LoanService.name);

  constructor(
    @InjectRepository(LoanApplication)
    private loanRepository: Repository<LoanApplication>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(FinancialProduct)
    private productRepository: Repository<FinancialProduct>,
  ) {}

  async createApplication(createDto: CreateLoanApplicationDto): Promise<LoanApplication> {
    this.logger.debug('创建贷款申请', createDto);

    // 验证用户存在
    const user = await this.userRepository.findOne({ where: { id: createDto.user_id } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 验证产品存在
    const product = await this.productRepository.findOne({ where: { id: createDto.product_id } });
    if (!product) {
      throw new NotFoundException('产品不存在');
    }

    // 验证贷款金额
    if (createDto.amount < (product.amount_min || 0) || createDto.amount > (product.amount_max || 1000000)) {
      throw new BadRequestException('贷款金额超出产品限制');
    }

    // 生成申请编号
    const applicationNumber = LoanApplication.generateApplicationNumber();

    // 创建申请
    const application = this.loanRepository.create({
      ...createDto,
      application_number: applicationNumber,
      status: 'pending',
    });

    const savedApplication = await this.loanRepository.save(application);
    this.logger.debug(`贷款申请创建成功: ${applicationNumber}`);

    return savedApplication;
  }

  async getApplicationById(id: number): Promise<LoanApplicationWithDetails> {
    const application = await this.loanRepository.findOne({
      where: { id },
      relations: ['user', 'product']
    });

    if (!application) {
      throw new NotFoundException('贷款申请不存在');
    }

    return application as LoanApplicationWithDetails;
  }

  async getApplicationsByUser(userId: number): Promise<LoanApplication[]> {
    return this.loanRepository.find({
      where: { user_id: userId },
      relations: ['product'],
      order: { created_at: 'DESC' }
    });
  }

  async getAllApplications(page: number = 1, limit: number = 10): Promise<{
    applications: LoanApplicationWithDetails[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const [applications, total] = await this.loanRepository.findAndCount({
      relations: ['user', 'product'],
      order: { created_at: 'DESC' },
      skip: (page - 1) * limit,
      take: limit
    });

    return {
      applications: applications as LoanApplicationWithDetails[],
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  async updateApplicationStatus(
    id: number, 
    status: string, 
    officerId?: number, 
    comment?: string
  ): Promise<LoanApplication> {
    const application = await this.getApplicationById(id);

    // 更新状态
    application.status = status;
    if (officerId) {
      application.officer_id = officerId;
    }

    // 添加审批历史
    if (!application.approval_history) {
      application.approval_history = [];
    }
    application.approval_history.push({
      action: status,
      officer_id: officerId || 0,
      timestamp: new Date(),
      comment
    });

    return this.loanRepository.save(application);
  }

  async approveApplication(
    id: number,
    approvalData: {
      approval_amount: number;
      approved_rate: number;
      approved_term: number;
      officer_id: number;
      comment?: string;
    }
  ): Promise<LoanApplication> {
    const application = await this.getApplicationById(id);

    if (application.status !== 'under_review' && application.status !== 'pending') {
      throw new BadRequestException('申请状态不允许批准操作');
    }

    // 更新批准信息
    application.status = 'approved';
    application.approval_amount = approvalData.approval_amount;
    application.approved_rate = approvalData.approved_rate;
    application.approved_term = approvalData.approved_term;
    application.officer_id = approvalData.officer_id;

    // 添加审批历史
    if (!application.approval_history) {
      application.approval_history = [];
    }
    application.approval_history.push({
      action: 'approved',
      officer_id: approvalData.officer_id,
      timestamp: new Date(),
      comment: approvalData.comment
    });

    this.logger.debug(`贷款申请已批准: ${application.application_number}`);
    return this.loanRepository.save(application);
  }

  async rejectApplication(
    id: number,
    rejectionData: {
      rejection_reason: string;
      officer_id: number;
    }
  ): Promise<LoanApplication> {
    const application = await this.getApplicationById(id);

    if (application.status !== 'under_review' && application.status !== 'pending') {
      throw new BadRequestException('申请状态不允许拒绝操作');
    }

    // 更新拒绝信息
    application.status = 'rejected';
    application.rejection_reason = rejectionData.rejection_reason;
    application.officer_id = rejectionData.officer_id;

    // 添加审批历史
    if (!application.approval_history) {
      application.approval_history = [];
    }
    application.approval_history.push({
      action: 'rejected',
      officer_id: rejectionData.officer_id,
      timestamp: new Date(),
      comment: rejectionData.rejection_reason
    });

    this.logger.debug(`贷款申请已拒绝: ${application.application_number}`);
    return this.loanRepository.save(application);
  }

  async disburseApplication(id: number, officerId: number): Promise<LoanApplication> {
    const application = await this.getApplicationById(id);

    if (application.status !== 'approved') {
      throw new BadRequestException('只有已批准的申请才能放款');
    }

    if (application.disbursement_date) {
      throw new BadRequestException('该申请已经放款');
    }

    // 更新放款信息
    application.status = 'disbursed';
    application.disbursement_date = new Date();
    application.officer_id = officerId;

    // 添加审批历史
    if (!application.approval_history) {
      application.approval_history = [];
    }
    application.approval_history.push({
      action: 'disbursed',
      officer_id: officerId,
      timestamp: new Date(),
      comment: '放款成功'
    });

    this.logger.debug(`贷款申请已放款: ${application.application_number}`);
    return this.loanRepository.save(application);
  }

  async updateAIAssessment(
    id: number,
    aiData: {
      ai_score: number;
      ai_recommendation: string;
      risk_assessment?: any;
    }
  ): Promise<LoanApplication> {
    const application = await this.getApplicationById(id);

    application.ai_score = aiData.ai_score;
    application.ai_recommendation = aiData.ai_recommendation;
    if (aiData.risk_assessment) {
      application.risk_assessment = aiData.risk_assessment;
    }

    return this.loanRepository.save(application);
  }

  async getApplicationStatistics(): Promise<{
    total: number;
    pending: number;
    under_review: number;
    approved: number;
    rejected: number;
    disbursed: number;
    total_amount: number;
    approved_amount: number;
  }> {
    const [total, pending, underReview, approved, rejected, disbursed] = await Promise.all([
      this.loanRepository.count(),
      this.loanRepository.count({ where: { status: 'pending' } }),
      this.loanRepository.count({ where: { status: 'under_review' } }),
      this.loanRepository.count({ where: { status: 'approved' } }),
      this.loanRepository.count({ where: { status: 'rejected' } }),
      this.loanRepository.count({ where: { status: 'disbursed' } })
    ]);

    // 计算总申请金额和批准金额
    const totalAmountResult = await this.loanRepository
      .createQueryBuilder('loan')
      .select('SUM(loan.amount)', 'total')
      .getRawOne();

    const approvedAmountResult = await this.loanRepository
      .createQueryBuilder('loan')
      .select('SUM(loan.approval_amount)', 'total')
      .where('loan.status IN (:...statuses)', { statuses: ['approved', 'disbursed'] })
      .getRawOne();

    return {
      total,
      pending,
      under_review: underReview,
      approved,
      rejected,
      disbursed,
      total_amount: parseFloat(totalAmountResult?.total || '0'),
      approved_amount: parseFloat(approvedAmountResult?.total || '0')
    };
  }
}
