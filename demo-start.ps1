# SmartLoan Demo Launcher
Write-Host "Starting SmartLoan Demo..." -ForegroundColor Green

# Get current directory
$currentDir = Get-Location
Write-Host "Current directory: $currentDir" -ForegroundColor Cyan

# Check if demo file exists
if (Test-Path "demo.html") {
    Write-Host "Demo file found!" -ForegroundColor Green
    
    # Open demo page
    Write-Host "Opening demo page..." -ForegroundColor Yellow
    $demoPath = Join-Path $currentDir "demo.html"
    Start-Process $demoPath
    
    Write-Host ""
    Write-Host "SmartLoan AI Financial Platform Demo" -ForegroundColor Green
    Write-Host "====================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Core Features:" -ForegroundColor Cyan
    Write-Host "  * Smart Product Matching - AI-driven recommendations" -ForegroundColor White
    Write-Host "  * Multi-modal Qualification Review - OCR + Liveness Detection" -ForegroundColor White
    Write-Host "  * Real-time Risk Dashboard - Dynamic monitoring" -ForegroundColor White
    Write-Host "  * AI Virtual Advisor - 24/7 intelligent service" -ForegroundColor White
    Write-Host ""
    Write-Host "Technical Highlights:" -ForegroundColor Cyan
    Write-Host "  * Powered by MuXi MetaX GPU" -ForegroundColor White
    Write-Host "  * Integrated with Gitee AI Platform" -ForegroundColor White
    Write-Host "  * Credit analysis <= 1s, 5000+ concurrent users" -ForegroundColor White
    Write-Host "  * 300% efficiency improvement, 65% cost reduction" -ForegroundColor White
    Write-Host ""
    Write-Host "Demo opened successfully! Please check your browser." -ForegroundColor Green
    
} else {
    Write-Host "Demo file not found!" -ForegroundColor Red
    Write-Host "Please make sure demo.html exists in the current directory." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
