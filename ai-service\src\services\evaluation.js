"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EvaluationService = void 0;
var EvaluationService = /** @class */ (function () {
    function EvaluationService() {
        this.MIN_SCORE = 0;
        this.MAX_SCORE = 100;
        this.APPROVAL_THRESHOLD = 70;
        this.PENDING_THRESHOLD = 60;
    }
    EvaluationService.prototype.calculateBaseScore = function (application) {
        var score = 50; // 基础分
        // 收入评分 (0-20分)
        var incomeScore = this.evaluateIncome(application.monthlyIncome);
        score += incomeScore;
        // 资产评分 (0-15分)
        var assetScore = this.evaluateAssets(application.houseStatus, application.carStatus);
        score += assetScore;
        // 贷款金额和期限评分 (0-15分)
        var loanScore = this.evaluateLoanAmountAndTerm(application.loanAmount, application.loanTerm, application.monthlyIncome);
        score += loanScore;
        return Math.min(Math.max(score, this.MIN_SCORE), this.MAX_SCORE);
    };
    EvaluationService.prototype.evaluateIncome = function (monthlyIncome) {
        if (monthlyIncome >= 20000)
            return 20;
        if (monthlyIncome >= 15000)
            return 18;
        if (monthlyIncome >= 10000)
            return 15;
        if (monthlyIncome >= 8000)
            return 12;
        if (monthlyIncome >= 5000)
            return 8;
        return 5;
    };
    EvaluationService.prototype.evaluateAssets = function (houseStatus, carStatus) {
        var score = 0;
        // 房产评分
        switch (houseStatus) {
            case 'owned':
                score += 10;
                break;
            case 'mortgaged':
                score += 5;
                break;
            case 'none':
                score += 0;
                break;
        }
        // 车产评分
        switch (carStatus) {
            case 'owned':
                score += 5;
                break;
            case 'loaned':
                score += 3;
                break;
            case 'none':
                score += 0;
                break;
        }
        return score;
    };
    EvaluationService.prototype.evaluateLoanAmountAndTerm = function (loanAmount, loanTerm, monthlyIncome) {
        var score = 0;
        // 贷款金额与收入比例评分
        var incomeRatio = loanAmount / (monthlyIncome * loanTerm);
        if (incomeRatio <= 0.3)
            score += 8;
        else if (incomeRatio <= 0.5)
            score += 6;
        else if (incomeRatio <= 0.7)
            score += 4;
        else
            score += 2;
        // 贷款期限评分
        if (loanTerm <= 24)
            score += 7;
        else if (loanTerm <= 36)
            score += 5;
        else if (loanTerm <= 48)
            score += 3;
        else
            score += 1;
        return score;
    };
    EvaluationService.prototype.generateReason = function (score, application, suggestedAmount, suggestedTerm) {
        var reasons = [];
        // 收入相关说明
        if (application.monthlyIncome < 5000) {
            reasons.push('您的月收入较低');
        }
        else if (application.monthlyIncome >= 15000) {
            reasons.push('您的月收入较高，还款能力良好');
        }
        // 资产相关说明
        if (application.houseStatus === 'owned') {
            reasons.push('您拥有房产，信用资质良好');
        }
        else if (application.houseStatus === 'none') {
            reasons.push('建议您考虑增加资产配置');
        }
        // 贷款金额和期限相关说明
        var incomeRatio = application.loanAmount / (application.monthlyIncome * application.loanTerm);
        if (incomeRatio > 0.7) {
            reasons.push('贷款金额与收入比例较高，建议适当降低贷款金额或延长贷款期限');
        }
        // 建议金额和期限说明
        if (suggestedAmount && suggestedAmount !== application.loanAmount) {
            reasons.push("\u5EFA\u8BAE\u8D37\u6B3E\u91D1\u989D\u8C03\u6574\u4E3A".concat(suggestedAmount, "\u5143"));
        }
        if (suggestedTerm && suggestedTerm !== application.loanTerm) {
            reasons.push("\u5EFA\u8BAE\u8D37\u6B3E\u671F\u9650\u8C03\u6574\u4E3A".concat(suggestedTerm, "\u4E2A\u6708"));
        }
        return reasons.join('；');
    };
    EvaluationService.prototype.calculateSuggestedAmount = function (application, score) {
        if (score < this.APPROVAL_THRESHOLD)
            return undefined;
        var maxAmount = application.monthlyIncome * application.loanTerm * 0.7;
        if (application.loanAmount <= maxAmount)
            return undefined;
        return Math.floor(maxAmount / 1000) * 1000;
    };
    EvaluationService.prototype.calculateSuggestedTerm = function (application, score) {
        if (score < this.APPROVAL_THRESHOLD)
            return undefined;
        var incomeRatio = application.loanAmount / application.monthlyIncome;
        if (incomeRatio <= 12)
            return undefined;
        return Math.min(Math.ceil(incomeRatio), 60);
    };
    EvaluationService.prototype.evaluate = function (application) {
        var score = this.calculateBaseScore(application);
        var suggestedAmount = this.calculateSuggestedAmount(application, score);
        var suggestedTerm = this.calculateSuggestedTerm(application, score);
        var status;
        if (score >= this.APPROVAL_THRESHOLD) {
            status = 'approved';
        }
        else if (score >= this.PENDING_THRESHOLD) {
            status = 'pending';
        }
        else {
            status = 'rejected';
        }
        var reason = this.generateReason(score, application, suggestedAmount, suggestedTerm);
        return {
            score: score,
            status: status,
            reason: reason,
            suggestedAmount: suggestedAmount,
            suggestedTerm: suggestedTerm
        };
    };
    return EvaluationService;
}());
exports.EvaluationService = EvaluationService;
