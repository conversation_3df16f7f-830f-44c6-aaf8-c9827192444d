export interface ValidationOptions {
    skipMissingProperties?: boolean;
    strictGroups?: boolean;
    groups?: string[];
}
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings?: string[];
}
export declare class Validator {
    validateObject(obj: any, options?: ValidationOptions): Promise<ValidationResult>;
    validateEmail(email: string): ValidationResult;
    validatePhone(phone: string): ValidationResult;
    validateIdCard(idCard: string): ValidationResult;
    validateBankCard(cardNumber: string): ValidationResult;
    validateAmount(amount: number, min?: number, max?: number): ValidationResult;
    validateDateRange(startDate: Date, endDate: Date): ValidationResult;
    validatePurpose(purpose: string): ValidationResult;
    validateIncome(income: number): ValidationResult;
    validateTerm(term: number): ValidationResult;
    validateBatch<T>(objects: T[], options?: ValidationOptions): Promise<ValidationResult[]>;
    validateConditional(value: any, condition: boolean, validator: (val: any) => ValidationResult): ValidationResult;
    private formatValidationErrors;
    addCustomValidator(name: string, validator: (value: any, args?: any) => boolean, message?: string): void;
}
