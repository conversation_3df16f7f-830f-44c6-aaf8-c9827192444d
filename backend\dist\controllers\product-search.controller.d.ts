import { ProductSearchService, SearchResult } from '../services/product-search.service';
export declare class ProductSearchController {
    private readonly productSearchService;
    constructor(productSearchService: ProductSearchService);
    searchProducts(query: string): Promise<SearchResult>;
    searchByCategory(category: string): Promise<any>;
    searchByAmountRange(minAmount: number, maxAmount: number): Promise<any>;
    searchByTermRange(minTerm: number, maxTerm: number): Promise<any>;
    searchByInterestRate(maxInterestRate: number): Promise<any>;
}
