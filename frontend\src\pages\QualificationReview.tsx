import React, { useState } from 'react';
import {
  Card,
  Steps,
  Button,
  Upload,
  Form,
  Input,
  Row,
  Col,
  Typography,
  Alert,
  message,
  Result,
  Statistic
} from 'antd';
import {
  UploadOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  IdcardOutlined
} from '@ant-design/icons';
import type { UploadFile } from 'antd';

const { Title, Paragraph } = Typography;
const { Step } = Steps;

interface QualificationData {
  name: string;
  idNumber: string;
  phone: string;
  employment: string;
  income: number;
  documents: UploadFile[];
}

const QualificationReview: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [reviewResult, setReviewResult] = useState<{
    score: number;
    status: 'approved' | 'rejected' | 'pending';
    message: string;
  } | null>(null);

  const steps = [
    {
      title: '基本信息',
      content: (
        <>
          <Form.Item label="姓名" name="name" rules={[{ required: true, message: '请输入姓名' }]}>
            <Input placeholder="请输入姓名" size="large" />
          </Form.Item>
          <Form.Item label="身份证号" name="idNumber" rules={[{ required: true, message: '请输入身份证号' }]}>
            <Input placeholder="请输入身份证号" size="large" />
          </Form.Item>
        </>
      ),
    },
    {
      title: '联系信息',
      content: (
        <>
          <Form.Item label="手机号" name="phone" rules={[{ required: true, message: '请输入手机号' }]}>
            <Input placeholder="请输入手机号" size="large" />
          </Form.Item>
          <Form.Item label="工作单位" name="employment" rules={[{ required: true, message: '请输入工作单位' }]}>
            <Input placeholder="请输入工作单位" size="large" />
          </Form.Item>
        </>
      ),
    },
    {
      title: '财务信息',
      content: (
        <>
          <Form.Item label="月收入" name="income" rules={[{ required: true, message: '请输入月收入' }]}>
            <Input type="number" placeholder="请输入月收入" size="large" suffix="元" />
          </Form.Item>
        </>
      ),
    },
    {
      title: '材料上传',
      content: (
        <Form.Item label="证明材料" name="documents" rules={[{ required: true, message: '请上传证明材料' }]}>
          <Upload
            listType="picture-card"
            maxCount={5}
            beforeUpload={() => false}
            accept="image/*"
          >
            <div>
              <UploadOutlined />
              <div style={{ marginTop: 8 }}>上传文件</div>
            </div>
          </Upload>
        </Form.Item>
      ),
    },
  ];

  const handleSubmit = async (values: QualificationData) => {
    setLoading(true);
    try {
      console.log('提交的数据:', values);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 模拟审核结果
      const score = Math.floor(Math.random() * 40) + 60; // 60-100分
      const status = score >= 70 ? 'approved' : 'rejected';
      const reviewMessage = status === 'approved'
        ? '恭喜您通过资质审核！您可以继续申请贷款。'
        : '很抱歉，您未通过资质审核。请完善资料后重新申请。';

      setReviewResult({ score, status, message: reviewMessage });
      message.success('资质审核提交成功！');
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败，请重试！');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <Title level={2}>
            <CheckCircleOutlined style={{ color: '#1890ff', marginRight: '8px' }} />
            资质审核
          </Title>
          <Paragraph type="secondary">
            请按步骤完成资质审核，我们将在1-3个工作日内完成审核
          </Paragraph>
        </div>

        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card>
              <Steps current={currentStep} style={{ marginBottom: 32 }}>
                {steps.map(item => (
                  <Step key={item.title} title={item.title} />
                ))}
              </Steps>

              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                size="large"
              >
                <div style={{ minHeight: '200px' }}>
                  {steps[currentStep].content}
                </div>

                <div style={{ marginTop: 32, textAlign: 'center' }}>
                  {currentStep > 0 && (
                    <Button
                      size="large"
                      style={{ marginRight: 16 }}
                      onClick={() => setCurrentStep(currentStep - 1)}
                    >
                      上一步
                    </Button>
                  )}
                  {currentStep < steps.length - 1 && (
                    <Button
                      type="primary"
                      size="large"
                      onClick={() => setCurrentStep(currentStep + 1)}
                    >
                      下一步
                    </Button>
                  )}
                  {currentStep === steps.length - 1 && (
                    <Button
                      type="primary"
                      size="large"
                      htmlType="submit"
                      loading={loading}
                    >
                      提交审核
                    </Button>
                  )}
                </div>
              </Form>
            </Card>
          </Col>

          {reviewResult && (
            <Col span={24}>
              <Card title="审核结果">
                <Row gutter={16}>
                  <Col xs={24} sm={8}>
                    <Statistic
                      title="审核分数"
                      value={reviewResult.score}
                      suffix="/100"
                      valueStyle={{ color: reviewResult.score >= 70 ? '#3f8600' : '#cf1322' }}
                    />
                  </Col>
                  <Col xs={24} sm={8}>
                    <Statistic
                      title="审核状态"
                      value={reviewResult.status === 'approved' ? '通过' : '未通过'}
                      valueStyle={{
                        color: reviewResult.status === 'approved' ? '#3f8600' : '#cf1322',
                      }}
                      prefix={
                        reviewResult.status === 'approved' ? (
                          <CheckCircleOutlined />
                        ) : (
                          <CloseCircleOutlined />
                        )
                      }
                    />
                  </Col>
                  <Col xs={24} sm={8}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>
                        审核结果
                      </div>
                      <div style={{ fontSize: '16px', fontWeight: 'bold' }}>
                        {reviewResult.message}
                      </div>
                    </div>
                  </Col>
                </Row>

                {reviewResult.status === 'approved' && (
                  <div style={{ marginTop: '24px', textAlign: 'center' }}>
                    <Button type="primary" size="large">
                      继续申请贷款
                    </Button>
                  </div>
                )}
              </Card>
            </Col>
          )}
        </Row>
      </div>
    </div>
  );
};

export default QualificationReview; 