import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../user/user.entity';
import { FinancialProduct } from '../product/entities/financial-product.entity';

@Entity('loan_applications')
export class LoanApplication {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  user_id: number;

  @Column()
  product_id: number;

  @Column({ unique: true, length: 50 })
  application_number: string;

  @Column('decimal', { precision: 15, scale: 2 })
  amount: number;

  @Column('text', { nullable: true })
  purpose: string;

  @Column('json', { nullable: true })
  employment_info: {
    company_name?: string;
    position?: string;
    employment_type?: string;
    monthly_income?: number;
    work_years?: number;
    industry?: string;
  };

  @Column('json', { nullable: true })
  financial_info: {
    monthly_income?: number;
    other_income?: number;
    monthly_expenses?: number;
    assets?: number;
    liabilities?: number;
    credit_score?: number;
  };

  @Column('json', { nullable: true })
  collateral_info: {
    type?: string;
    value?: number;
    description?: string;
  };

  @Column({ default: 'pending' })
  status: string; // 'pending', 'under_review', 'approved', 'rejected', 'disbursed', 'closed'

  @Column('decimal', { precision: 5, scale: 2, nullable: true })
  ai_score: number;

  @Column('text', { nullable: true })
  ai_recommendation: string;

  @Column('decimal', { precision: 15, scale: 2, nullable: true })
  approval_amount: number;

  @Column('decimal', { precision: 5, scale: 2, nullable: true })
  approved_rate: number;

  @Column({ nullable: true })
  approved_term: number;

  @Column('text', { nullable: true })
  rejection_reason: string;

  @Column({ type: 'date', nullable: true })
  disbursement_date: Date;

  @Column({ nullable: true })
  officer_id: number;

  @Column('json', { nullable: true })
  documents: {
    id_card?: string;
    income_proof?: string;
    bank_statements?: string[];
    collateral_docs?: string[];
    other_docs?: string[];
  };

  @Column('json', { nullable: true })
  risk_assessment: {
    score?: number;
    level?: string; // 'low', 'medium', 'high'
    factors?: string[];
    recommendation?: string;
  };

  @Column('json', { nullable: true })
  approval_history: Array<{
    action: string;
    officer_id: number;
    timestamp: Date;
    comment?: string;
  }>;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => FinancialProduct)
  @JoinColumn({ name: 'product_id' })
  product: FinancialProduct;

  // 生成申请编号
  static generateApplicationNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `SL${timestamp.slice(-8)}${random}`;
  }

  // 计算月还款额
  calculateMonthlyPayment(): number {
    if (!this.approval_amount || !this.approved_rate || !this.approved_term) {
      return 0;
    }
    
    const monthlyRate = this.approved_rate / 12 / 100;
    const monthlyPayment =
      (this.approval_amount * monthlyRate * Math.pow(1 + monthlyRate, this.approved_term)) /
      (Math.pow(1 + monthlyRate, this.approved_term) - 1);
    
    return Math.round(monthlyPayment * 100) / 100;
  }

  // 获取申请状态描述
  get statusDescription(): string {
    const statusMap = {
      'pending': '待审核',
      'under_review': '审核中',
      'approved': '已批准',
      'rejected': '已拒绝',
      'disbursed': '已放款',
      'closed': '已结清'
    };
    return statusMap[this.status] || '未知状态';
  }

  // 获取风险等级
  get riskLevel(): string {
    return this.risk_assessment?.level || 'unknown';
  }

  // 检查是否可以放款
  get canDisburse(): boolean {
    return this.status === 'approved' && !this.disbursement_date;
  }
}
