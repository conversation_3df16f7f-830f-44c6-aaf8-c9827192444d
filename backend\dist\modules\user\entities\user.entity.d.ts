import { Role } from './role.entity';
import { LoanApplication } from '../../loan/entities/loan-application.entity';
import { UserLog } from './user-log.entity';
export declare class User {
    id: number;
    username: string;
    email: string;
    phone: string;
    password: string;
    salt: string;
    avatar: string;
    status: string;
    last_login_at: Date;
    reset_token: string;
    reset_token_expires: Date;
    is_active: boolean;
    roles: Role[];
    loan_applications: LoanApplication[];
    logs: UserLog[];
    metadata: any;
    created_at: Date;
    updated_at: Date;
}
