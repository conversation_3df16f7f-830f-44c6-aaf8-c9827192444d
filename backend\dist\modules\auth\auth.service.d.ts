import { JwtService } from '@nestjs/jwt';
import { UserService } from '../user/user.service';
import { User } from '../user/entities/user.entity';
import { LoggerService } from '../../logger/logger.service';
export declare class AuthService {
    private readonly userService;
    private readonly jwtService;
    private readonly logger;
    constructor(userService: UserService, jwtService: JwtService, logger: LoggerService);
    validateUser(username: string, password: string): Promise<any>;
    login(user: User): Promise<{
        access_token: string;
        user: {
            id: number;
            username: string;
            email: string;
            roles: import("../user/entities/role.entity").Role[];
        };
    }>;
}
