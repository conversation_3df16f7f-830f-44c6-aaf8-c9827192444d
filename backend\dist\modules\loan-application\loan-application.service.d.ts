import { Repository } from 'typeorm';
import { LoanApplication, LoanStatus } from './entities/loan-application.entity';
import { LoggerService } from '../../logger/logger.service';
import { CacheService } from '../../cache/cache.service';
import { MonitorService } from '../../monitor/monitor.service';
export declare class LoanApplicationService {
    private readonly loanApplicationRepository;
    private readonly logger;
    private readonly cache;
    private readonly monitor;
    constructor(loanApplicationRepository: Repository<LoanApplication>, logger: LoggerService, cache: CacheService, monitor: MonitorService);
    create(applicationData: Partial<LoanApplication>): Promise<LoanApplication>;
    findById(id: number): Promise<LoanApplication | null>;
    updateStatus(id: number, status: LoanStatus, rejectionReason?: string): Promise<LoanApplication | null>;
    findByUserId(userId: number): Promise<LoanApplication[]>;
    findAll(): Promise<LoanApplication[]>;
}
