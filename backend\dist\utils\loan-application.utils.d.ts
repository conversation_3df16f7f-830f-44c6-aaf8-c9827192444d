import { LoanType, LoanStatus } from '../entities/loan-application.entity';
export declare class LoanApplicationUtils {
    static calculateMonthlyPayment(amount: number, term: number, interestRate: number): number;
    static calculateTotalPayment(monthlyPayment: number, term: number): number;
    static calculateInterestRate(amount: number, term: number, monthlyPayment: number): number;
    static validateLoanAmount(amount: number): boolean;
    static validateLoanTerm(term: number): boolean;
    static validateMonthlyIncome(income: number): boolean;
    static validateCreditScore(score: number): boolean;
    static validateDebtToIncomeRatio(ratio: number): boolean;
    static validateFileSize(size: number): boolean;
    static validateFileType(type: string): boolean;
    static validateStatusTransition(currentStatus: LoanStatus, newStatus: LoanStatus): boolean;
    static getRiskLevel(score: number): 'low' | 'medium' | 'high';
    static calculateLoanAmount(monthlyIncome: number, creditScore: number, debtToIncomeRatio: number): number;
    static calculateLoanTerm(amount: number, monthlyIncome: number, creditScore: number): number;
    static formatLoanType(type: LoanType): string;
    static formatLoanStatus(status: LoanStatus): string;
    static formatAmount(amount: number): string;
    static formatDate(date: Date): string;
}
