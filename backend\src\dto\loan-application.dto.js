"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoanApplicationDto = exports.UpdateLoanApplicationDto = exports.CreateLoanApplicationDto = void 0;
var class_validator_1 = require("class-validator");
var class_transformer_1 = require("class-transformer");
var swagger_1 = require("@nestjs/swagger");
var loan_type_enum_1 = require("../enums/loan-type.enum");
var loan_status_enum_1 = require("../enums/loan-status.enum");
var LoanMetadataDto = function () {
    var _a;
    var _monthlyIncome_decorators;
    var _monthlyIncome_initializers = [];
    var _monthlyIncome_extraInitializers = [];
    var _employmentDuration_decorators;
    var _employmentDuration_initializers = [];
    var _employmentDuration_extraInitializers = [];
    var _creditScore_decorators;
    var _creditScore_initializers = [];
    var _creditScore_extraInitializers = [];
    var _debtToIncomeRatio_decorators;
    var _debtToIncomeRatio_initializers = [];
    var _debtToIncomeRatio_extraInitializers = [];
    return _a = /** @class */ (function () {
            function LoanMetadataDto() {
                this.monthlyIncome = __runInitializers(this, _monthlyIncome_initializers, void 0);
                this.employmentDuration = (__runInitializers(this, _monthlyIncome_extraInitializers), __runInitializers(this, _employmentDuration_initializers, void 0));
                this.creditScore = (__runInitializers(this, _employmentDuration_extraInitializers), __runInitializers(this, _creditScore_initializers, void 0));
                this.debtToIncomeRatio = (__runInitializers(this, _creditScore_extraInitializers), __runInitializers(this, _debtToIncomeRatio_initializers, void 0));
                __runInitializers(this, _debtToIncomeRatio_extraInitializers);
            }
            return LoanMetadataDto;
        }()),
        (function () {
            var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            _monthlyIncome_decorators = [(0, swagger_1.ApiProperty)({ description: '月收入' }), (0, class_validator_1.IsNumber)(), (0, class_validator_1.Min)(0)];
            _employmentDuration_decorators = [(0, swagger_1.ApiProperty)({ description: '就业时长（月）' }), (0, class_validator_1.IsNumber)(), (0, class_validator_1.Min)(0)];
            _creditScore_decorators = [(0, swagger_1.ApiProperty)({ description: '信用分数' }), (0, class_validator_1.IsNumber)(), (0, class_validator_1.Min)(300), (0, class_validator_1.Max)(850)];
            _debtToIncomeRatio_decorators = [(0, swagger_1.ApiProperty)({ description: '负债收入比' }), (0, class_validator_1.IsNumber)(), (0, class_validator_1.Min)(0), (0, class_validator_1.Max)(1)];
            __esDecorate(null, null, _monthlyIncome_decorators, { kind: "field", name: "monthlyIncome", static: false, private: false, access: { has: function (obj) { return "monthlyIncome" in obj; }, get: function (obj) { return obj.monthlyIncome; }, set: function (obj, value) { obj.monthlyIncome = value; } }, metadata: _metadata }, _monthlyIncome_initializers, _monthlyIncome_extraInitializers);
            __esDecorate(null, null, _employmentDuration_decorators, { kind: "field", name: "employmentDuration", static: false, private: false, access: { has: function (obj) { return "employmentDuration" in obj; }, get: function (obj) { return obj.employmentDuration; }, set: function (obj, value) { obj.employmentDuration = value; } }, metadata: _metadata }, _employmentDuration_initializers, _employmentDuration_extraInitializers);
            __esDecorate(null, null, _creditScore_decorators, { kind: "field", name: "creditScore", static: false, private: false, access: { has: function (obj) { return "creditScore" in obj; }, get: function (obj) { return obj.creditScore; }, set: function (obj, value) { obj.creditScore = value; } }, metadata: _metadata }, _creditScore_initializers, _creditScore_extraInitializers);
            __esDecorate(null, null, _debtToIncomeRatio_decorators, { kind: "field", name: "debtToIncomeRatio", static: false, private: false, access: { has: function (obj) { return "debtToIncomeRatio" in obj; }, get: function (obj) { return obj.debtToIncomeRatio; }, set: function (obj, value) { obj.debtToIncomeRatio = value; } }, metadata: _metadata }, _debtToIncomeRatio_initializers, _debtToIncomeRatio_extraInitializers);
            if (_metadata) Object.defineProperty(_a, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        })(),
        _a;
}();
var CreateLoanApplicationDto = function () {
    var _a;
    var _loanType_decorators;
    var _loanType_initializers = [];
    var _loanType_extraInitializers = [];
    var _loanAmount_decorators;
    var _loanAmount_initializers = [];
    var _loanAmount_extraInitializers = [];
    var _loanTerm_decorators;
    var _loanTerm_initializers = [];
    var _loanTerm_extraInitializers = [];
    var _loanPurpose_decorators;
    var _loanPurpose_initializers = [];
    var _loanPurpose_extraInitializers = [];
    var _metadata_decorators;
    var _metadata_initializers = [];
    var _metadata_extraInitializers = [];
    return _a = /** @class */ (function () {
            function CreateLoanApplicationDto() {
                this.loanType = __runInitializers(this, _loanType_initializers, void 0);
                this.loanAmount = (__runInitializers(this, _loanType_extraInitializers), __runInitializers(this, _loanAmount_initializers, void 0));
                this.loanTerm = (__runInitializers(this, _loanAmount_extraInitializers), __runInitializers(this, _loanTerm_initializers, void 0));
                this.loanPurpose = (__runInitializers(this, _loanTerm_extraInitializers), __runInitializers(this, _loanPurpose_initializers, void 0));
                this.metadata = (__runInitializers(this, _loanPurpose_extraInitializers), __runInitializers(this, _metadata_initializers, void 0));
                __runInitializers(this, _metadata_extraInitializers);
            }
            return CreateLoanApplicationDto;
        }()),
        (function () {
            var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            _loanType_decorators = [(0, swagger_1.ApiProperty)({ enum: loan_type_enum_1.LoanType, description: '贷款类型' }), (0, class_validator_1.IsEnum)(loan_type_enum_1.LoanType)];
            _loanAmount_decorators = [(0, swagger_1.ApiProperty)({ description: '贷款金额', minimum: 1000, maximum: 1000000 }), (0, class_validator_1.IsNumber)(), (0, class_validator_1.Min)(1000), (0, class_validator_1.Max)(1000000)];
            _loanTerm_decorators = [(0, swagger_1.ApiProperty)({ description: '贷款期限(月)', minimum: 3, maximum: 360 }), (0, class_validator_1.IsNumber)(), (0, class_validator_1.Min)(3), (0, class_validator_1.Max)(360)];
            _loanPurpose_decorators = [(0, swagger_1.ApiProperty)({ description: '贷款用途' }), (0, class_validator_1.IsString)()];
            _metadata_decorators = [(0, swagger_1.ApiProperty)({ type: LoanMetadataDto, description: '贷款元数据' }), (0, class_validator_1.IsObject)(), (0, class_validator_1.ValidateNested)(), (0, class_transformer_1.Type)(function () { return LoanMetadataDto; })];
            __esDecorate(null, null, _loanType_decorators, { kind: "field", name: "loanType", static: false, private: false, access: { has: function (obj) { return "loanType" in obj; }, get: function (obj) { return obj.loanType; }, set: function (obj, value) { obj.loanType = value; } }, metadata: _metadata }, _loanType_initializers, _loanType_extraInitializers);
            __esDecorate(null, null, _loanAmount_decorators, { kind: "field", name: "loanAmount", static: false, private: false, access: { has: function (obj) { return "loanAmount" in obj; }, get: function (obj) { return obj.loanAmount; }, set: function (obj, value) { obj.loanAmount = value; } }, metadata: _metadata }, _loanAmount_initializers, _loanAmount_extraInitializers);
            __esDecorate(null, null, _loanTerm_decorators, { kind: "field", name: "loanTerm", static: false, private: false, access: { has: function (obj) { return "loanTerm" in obj; }, get: function (obj) { return obj.loanTerm; }, set: function (obj, value) { obj.loanTerm = value; } }, metadata: _metadata }, _loanTerm_initializers, _loanTerm_extraInitializers);
            __esDecorate(null, null, _loanPurpose_decorators, { kind: "field", name: "loanPurpose", static: false, private: false, access: { has: function (obj) { return "loanPurpose" in obj; }, get: function (obj) { return obj.loanPurpose; }, set: function (obj, value) { obj.loanPurpose = value; } }, metadata: _metadata }, _loanPurpose_initializers, _loanPurpose_extraInitializers);
            __esDecorate(null, null, _metadata_decorators, { kind: "field", name: "metadata", static: false, private: false, access: { has: function (obj) { return "metadata" in obj; }, get: function (obj) { return obj.metadata; }, set: function (obj, value) { obj.metadata = value; } }, metadata: _metadata }, _metadata_initializers, _metadata_extraInitializers);
            if (_metadata) Object.defineProperty(_a, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        })(),
        _a;
}();
exports.CreateLoanApplicationDto = CreateLoanApplicationDto;
var UpdateLoanApplicationDto = function () {
    var _a;
    var _loanAmount_decorators;
    var _loanAmount_initializers = [];
    var _loanAmount_extraInitializers = [];
    var _loanTerm_decorators;
    var _loanTerm_initializers = [];
    var _loanTerm_extraInitializers = [];
    var _monthlyIncome_decorators;
    var _monthlyIncome_initializers = [];
    var _monthlyIncome_extraInitializers = [];
    var _employmentType_decorators;
    var _employmentType_initializers = [];
    var _employmentType_extraInitializers = [];
    var _employmentDuration_decorators;
    var _employmentDuration_initializers = [];
    var _employmentDuration_extraInitializers = [];
    var _education_decorators;
    var _education_initializers = [];
    var _education_extraInitializers = [];
    var _maritalStatus_decorators;
    var _maritalStatus_initializers = [];
    var _maritalStatus_extraInitializers = [];
    var _houseStatus_decorators;
    var _houseStatus_initializers = [];
    var _houseStatus_extraInitializers = [];
    var _carStatus_decorators;
    var _carStatus_initializers = [];
    var _carStatus_extraInitializers = [];
    var _creditScore_decorators;
    var _creditScore_initializers = [];
    var _creditScore_extraInitializers = [];
    var _debtToIncomeRatio_decorators;
    var _debtToIncomeRatio_initializers = [];
    var _debtToIncomeRatio_extraInitializers = [];
    var _loanPurpose_decorators;
    var _loanPurpose_initializers = [];
    var _loanPurpose_extraInitializers = [];
    var _collateral_decorators;
    var _collateral_initializers = [];
    var _collateral_extraInitializers = [];
    var _guarantor_decorators;
    var _guarantor_initializers = [];
    var _guarantor_extraInitializers = [];
    var _documents_decorators;
    var _documents_initializers = [];
    var _documents_extraInitializers = [];
    return _a = /** @class */ (function () {
            function UpdateLoanApplicationDto() {
                this.loanAmount = __runInitializers(this, _loanAmount_initializers, void 0);
                this.loanTerm = (__runInitializers(this, _loanAmount_extraInitializers), __runInitializers(this, _loanTerm_initializers, void 0));
                this.monthlyIncome = (__runInitializers(this, _loanTerm_extraInitializers), __runInitializers(this, _monthlyIncome_initializers, void 0));
                this.employmentType = (__runInitializers(this, _monthlyIncome_extraInitializers), __runInitializers(this, _employmentType_initializers, void 0));
                this.employmentDuration = (__runInitializers(this, _employmentType_extraInitializers), __runInitializers(this, _employmentDuration_initializers, void 0));
                this.education = (__runInitializers(this, _employmentDuration_extraInitializers), __runInitializers(this, _education_initializers, void 0));
                this.maritalStatus = (__runInitializers(this, _education_extraInitializers), __runInitializers(this, _maritalStatus_initializers, void 0));
                this.houseStatus = (__runInitializers(this, _maritalStatus_extraInitializers), __runInitializers(this, _houseStatus_initializers, void 0));
                this.carStatus = (__runInitializers(this, _houseStatus_extraInitializers), __runInitializers(this, _carStatus_initializers, void 0));
                this.creditScore = (__runInitializers(this, _carStatus_extraInitializers), __runInitializers(this, _creditScore_initializers, void 0));
                this.debtToIncomeRatio = (__runInitializers(this, _creditScore_extraInitializers), __runInitializers(this, _debtToIncomeRatio_initializers, void 0));
                this.loanPurpose = (__runInitializers(this, _debtToIncomeRatio_extraInitializers), __runInitializers(this, _loanPurpose_initializers, void 0));
                this.collateral = (__runInitializers(this, _loanPurpose_extraInitializers), __runInitializers(this, _collateral_initializers, void 0));
                this.guarantor = (__runInitializers(this, _collateral_extraInitializers), __runInitializers(this, _guarantor_initializers, void 0));
                this.documents = (__runInitializers(this, _guarantor_extraInitializers), __runInitializers(this, _documents_initializers, void 0));
                __runInitializers(this, _documents_extraInitializers);
            }
            return UpdateLoanApplicationDto;
        }()),
        (function () {
            var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            _loanAmount_decorators = [(0, swagger_1.ApiProperty)({ description: '贷款金额', required: false }), (0, class_validator_1.IsNumber)(), (0, class_validator_1.Min)(1000), (0, class_validator_1.Max)(1000000), (0, class_validator_1.IsOptional)()];
            _loanTerm_decorators = [(0, swagger_1.ApiProperty)({ description: '贷款期限(月)', required: false }), (0, class_validator_1.IsNumber)(), (0, class_validator_1.Min)(3), (0, class_validator_1.Max)(360), (0, class_validator_1.IsOptional)()];
            _monthlyIncome_decorators = [(0, swagger_1.ApiProperty)({ description: '月收入', required: false }), (0, class_validator_1.IsNumber)(), (0, class_validator_1.Min)(0), (0, class_validator_1.IsOptional)()];
            _employmentType_decorators = [(0, swagger_1.ApiProperty)({ description: '就业类型', required: false }), (0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _employmentDuration_decorators = [(0, swagger_1.ApiProperty)({ description: '就业时长(月)', required: false }), (0, class_validator_1.IsNumber)(), (0, class_validator_1.Min)(0), (0, class_validator_1.IsOptional)()];
            _education_decorators = [(0, swagger_1.ApiProperty)({ description: '教育程度', required: false }), (0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _maritalStatus_decorators = [(0, swagger_1.ApiProperty)({ description: '婚姻状况', required: false }), (0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _houseStatus_decorators = [(0, swagger_1.ApiProperty)({ description: '房产状况', required: false }), (0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _carStatus_decorators = [(0, swagger_1.ApiProperty)({ description: '车辆状况', required: false }), (0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _creditScore_decorators = [(0, swagger_1.ApiProperty)({ description: '信用分数', required: false }), (0, class_validator_1.IsNumber)(), (0, class_validator_1.Min)(300), (0, class_validator_1.Max)(850), (0, class_validator_1.IsOptional)()];
            _debtToIncomeRatio_decorators = [(0, swagger_1.ApiProperty)({ description: '负债收入比', required: false }), (0, class_validator_1.IsNumber)(), (0, class_validator_1.Min)(0), (0, class_validator_1.Max)(1), (0, class_validator_1.IsOptional)()];
            _loanPurpose_decorators = [(0, swagger_1.ApiProperty)({ description: '贷款用途', required: false }), (0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _collateral_decorators = [(0, swagger_1.ApiProperty)({ description: '抵押物', required: false }), (0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _guarantor_decorators = [(0, swagger_1.ApiProperty)({ description: '担保人', required: false }), (0, class_validator_1.IsString)(), (0, class_validator_1.IsOptional)()];
            _documents_decorators = [(0, swagger_1.ApiProperty)({ description: '文档列表', type: [String], required: false }), (0, class_validator_1.IsArray)(), (0, class_validator_1.IsString)({ each: true }), (0, class_validator_1.IsOptional)()];
            __esDecorate(null, null, _loanAmount_decorators, { kind: "field", name: "loanAmount", static: false, private: false, access: { has: function (obj) { return "loanAmount" in obj; }, get: function (obj) { return obj.loanAmount; }, set: function (obj, value) { obj.loanAmount = value; } }, metadata: _metadata }, _loanAmount_initializers, _loanAmount_extraInitializers);
            __esDecorate(null, null, _loanTerm_decorators, { kind: "field", name: "loanTerm", static: false, private: false, access: { has: function (obj) { return "loanTerm" in obj; }, get: function (obj) { return obj.loanTerm; }, set: function (obj, value) { obj.loanTerm = value; } }, metadata: _metadata }, _loanTerm_initializers, _loanTerm_extraInitializers);
            __esDecorate(null, null, _monthlyIncome_decorators, { kind: "field", name: "monthlyIncome", static: false, private: false, access: { has: function (obj) { return "monthlyIncome" in obj; }, get: function (obj) { return obj.monthlyIncome; }, set: function (obj, value) { obj.monthlyIncome = value; } }, metadata: _metadata }, _monthlyIncome_initializers, _monthlyIncome_extraInitializers);
            __esDecorate(null, null, _employmentType_decorators, { kind: "field", name: "employmentType", static: false, private: false, access: { has: function (obj) { return "employmentType" in obj; }, get: function (obj) { return obj.employmentType; }, set: function (obj, value) { obj.employmentType = value; } }, metadata: _metadata }, _employmentType_initializers, _employmentType_extraInitializers);
            __esDecorate(null, null, _employmentDuration_decorators, { kind: "field", name: "employmentDuration", static: false, private: false, access: { has: function (obj) { return "employmentDuration" in obj; }, get: function (obj) { return obj.employmentDuration; }, set: function (obj, value) { obj.employmentDuration = value; } }, metadata: _metadata }, _employmentDuration_initializers, _employmentDuration_extraInitializers);
            __esDecorate(null, null, _education_decorators, { kind: "field", name: "education", static: false, private: false, access: { has: function (obj) { return "education" in obj; }, get: function (obj) { return obj.education; }, set: function (obj, value) { obj.education = value; } }, metadata: _metadata }, _education_initializers, _education_extraInitializers);
            __esDecorate(null, null, _maritalStatus_decorators, { kind: "field", name: "maritalStatus", static: false, private: false, access: { has: function (obj) { return "maritalStatus" in obj; }, get: function (obj) { return obj.maritalStatus; }, set: function (obj, value) { obj.maritalStatus = value; } }, metadata: _metadata }, _maritalStatus_initializers, _maritalStatus_extraInitializers);
            __esDecorate(null, null, _houseStatus_decorators, { kind: "field", name: "houseStatus", static: false, private: false, access: { has: function (obj) { return "houseStatus" in obj; }, get: function (obj) { return obj.houseStatus; }, set: function (obj, value) { obj.houseStatus = value; } }, metadata: _metadata }, _houseStatus_initializers, _houseStatus_extraInitializers);
            __esDecorate(null, null, _carStatus_decorators, { kind: "field", name: "carStatus", static: false, private: false, access: { has: function (obj) { return "carStatus" in obj; }, get: function (obj) { return obj.carStatus; }, set: function (obj, value) { obj.carStatus = value; } }, metadata: _metadata }, _carStatus_initializers, _carStatus_extraInitializers);
            __esDecorate(null, null, _creditScore_decorators, { kind: "field", name: "creditScore", static: false, private: false, access: { has: function (obj) { return "creditScore" in obj; }, get: function (obj) { return obj.creditScore; }, set: function (obj, value) { obj.creditScore = value; } }, metadata: _metadata }, _creditScore_initializers, _creditScore_extraInitializers);
            __esDecorate(null, null, _debtToIncomeRatio_decorators, { kind: "field", name: "debtToIncomeRatio", static: false, private: false, access: { has: function (obj) { return "debtToIncomeRatio" in obj; }, get: function (obj) { return obj.debtToIncomeRatio; }, set: function (obj, value) { obj.debtToIncomeRatio = value; } }, metadata: _metadata }, _debtToIncomeRatio_initializers, _debtToIncomeRatio_extraInitializers);
            __esDecorate(null, null, _loanPurpose_decorators, { kind: "field", name: "loanPurpose", static: false, private: false, access: { has: function (obj) { return "loanPurpose" in obj; }, get: function (obj) { return obj.loanPurpose; }, set: function (obj, value) { obj.loanPurpose = value; } }, metadata: _metadata }, _loanPurpose_initializers, _loanPurpose_extraInitializers);
            __esDecorate(null, null, _collateral_decorators, { kind: "field", name: "collateral", static: false, private: false, access: { has: function (obj) { return "collateral" in obj; }, get: function (obj) { return obj.collateral; }, set: function (obj, value) { obj.collateral = value; } }, metadata: _metadata }, _collateral_initializers, _collateral_extraInitializers);
            __esDecorate(null, null, _guarantor_decorators, { kind: "field", name: "guarantor", static: false, private: false, access: { has: function (obj) { return "guarantor" in obj; }, get: function (obj) { return obj.guarantor; }, set: function (obj, value) { obj.guarantor = value; } }, metadata: _metadata }, _guarantor_initializers, _guarantor_extraInitializers);
            __esDecorate(null, null, _documents_decorators, { kind: "field", name: "documents", static: false, private: false, access: { has: function (obj) { return "documents" in obj; }, get: function (obj) { return obj.documents; }, set: function (obj, value) { obj.documents = value; } }, metadata: _metadata }, _documents_initializers, _documents_extraInitializers);
            if (_metadata) Object.defineProperty(_a, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        })(),
        _a;
}();
exports.UpdateLoanApplicationDto = UpdateLoanApplicationDto;
var LoanApplicationDto = function () {
    var _a;
    var _id_decorators;
    var _id_initializers = [];
    var _id_extraInitializers = [];
    var _userId_decorators;
    var _userId_initializers = [];
    var _userId_extraInitializers = [];
    var _amount_decorators;
    var _amount_initializers = [];
    var _amount_extraInitializers = [];
    var _term_decorators;
    var _term_initializers = [];
    var _term_extraInitializers = [];
    var _purpose_decorators;
    var _purpose_initializers = [];
    var _purpose_extraInitializers = [];
    var _status_decorators;
    var _status_initializers = [];
    var _status_extraInitializers = [];
    var _createdAt_decorators;
    var _createdAt_initializers = [];
    var _createdAt_extraInitializers = [];
    var _updatedAt_decorators;
    var _updatedAt_initializers = [];
    var _updatedAt_extraInitializers = [];
    var _loanType_decorators;
    var _loanType_initializers = [];
    var _loanType_extraInitializers = [];
    var _monthlyPayment_decorators;
    var _monthlyPayment_initializers = [];
    var _monthlyPayment_extraInitializers = [];
    var _totalPayment_decorators;
    var _totalPayment_initializers = [];
    var _totalPayment_extraInitializers = [];
    return _a = /** @class */ (function () {
            function LoanApplicationDto() {
                this.id = __runInitializers(this, _id_initializers, void 0);
                this.userId = (__runInitializers(this, _id_extraInitializers), __runInitializers(this, _userId_initializers, void 0));
                this.amount = (__runInitializers(this, _userId_extraInitializers), __runInitializers(this, _amount_initializers, void 0));
                this.term = (__runInitializers(this, _amount_extraInitializers), __runInitializers(this, _term_initializers, void 0));
                this.purpose = (__runInitializers(this, _term_extraInitializers), __runInitializers(this, _purpose_initializers, void 0));
                this.status = (__runInitializers(this, _purpose_extraInitializers), __runInitializers(this, _status_initializers, void 0));
                this.createdAt = (__runInitializers(this, _status_extraInitializers), __runInitializers(this, _createdAt_initializers, void 0));
                this.updatedAt = (__runInitializers(this, _createdAt_extraInitializers), __runInitializers(this, _updatedAt_initializers, void 0));
                this.loanType = (__runInitializers(this, _updatedAt_extraInitializers), __runInitializers(this, _loanType_initializers, void 0));
                this.monthlyPayment = (__runInitializers(this, _loanType_extraInitializers), __runInitializers(this, _monthlyPayment_initializers, void 0));
                this.totalPayment = (__runInitializers(this, _monthlyPayment_extraInitializers), __runInitializers(this, _totalPayment_initializers, void 0));
                __runInitializers(this, _totalPayment_extraInitializers);
            }
            return LoanApplicationDto;
        }()),
        (function () {
            var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            _id_decorators = [(0, swagger_1.ApiProperty)()];
            _userId_decorators = [(0, swagger_1.ApiProperty)()];
            _amount_decorators = [(0, swagger_1.ApiProperty)()];
            _term_decorators = [(0, swagger_1.ApiProperty)()];
            _purpose_decorators = [(0, swagger_1.ApiProperty)()];
            _status_decorators = [(0, swagger_1.ApiProperty)({ enum: loan_status_enum_1.LoanStatus })];
            _createdAt_decorators = [(0, swagger_1.ApiProperty)()];
            _updatedAt_decorators = [(0, swagger_1.ApiProperty)()];
            _loanType_decorators = [(0, swagger_1.ApiProperty)({ enum: loan_type_enum_1.LoanType })];
            _monthlyPayment_decorators = [(0, swagger_1.ApiProperty)()];
            _totalPayment_decorators = [(0, swagger_1.ApiProperty)()];
            __esDecorate(null, null, _id_decorators, { kind: "field", name: "id", static: false, private: false, access: { has: function (obj) { return "id" in obj; }, get: function (obj) { return obj.id; }, set: function (obj, value) { obj.id = value; } }, metadata: _metadata }, _id_initializers, _id_extraInitializers);
            __esDecorate(null, null, _userId_decorators, { kind: "field", name: "userId", static: false, private: false, access: { has: function (obj) { return "userId" in obj; }, get: function (obj) { return obj.userId; }, set: function (obj, value) { obj.userId = value; } }, metadata: _metadata }, _userId_initializers, _userId_extraInitializers);
            __esDecorate(null, null, _amount_decorators, { kind: "field", name: "amount", static: false, private: false, access: { has: function (obj) { return "amount" in obj; }, get: function (obj) { return obj.amount; }, set: function (obj, value) { obj.amount = value; } }, metadata: _metadata }, _amount_initializers, _amount_extraInitializers);
            __esDecorate(null, null, _term_decorators, { kind: "field", name: "term", static: false, private: false, access: { has: function (obj) { return "term" in obj; }, get: function (obj) { return obj.term; }, set: function (obj, value) { obj.term = value; } }, metadata: _metadata }, _term_initializers, _term_extraInitializers);
            __esDecorate(null, null, _purpose_decorators, { kind: "field", name: "purpose", static: false, private: false, access: { has: function (obj) { return "purpose" in obj; }, get: function (obj) { return obj.purpose; }, set: function (obj, value) { obj.purpose = value; } }, metadata: _metadata }, _purpose_initializers, _purpose_extraInitializers);
            __esDecorate(null, null, _status_decorators, { kind: "field", name: "status", static: false, private: false, access: { has: function (obj) { return "status" in obj; }, get: function (obj) { return obj.status; }, set: function (obj, value) { obj.status = value; } }, metadata: _metadata }, _status_initializers, _status_extraInitializers);
            __esDecorate(null, null, _createdAt_decorators, { kind: "field", name: "createdAt", static: false, private: false, access: { has: function (obj) { return "createdAt" in obj; }, get: function (obj) { return obj.createdAt; }, set: function (obj, value) { obj.createdAt = value; } }, metadata: _metadata }, _createdAt_initializers, _createdAt_extraInitializers);
            __esDecorate(null, null, _updatedAt_decorators, { kind: "field", name: "updatedAt", static: false, private: false, access: { has: function (obj) { return "updatedAt" in obj; }, get: function (obj) { return obj.updatedAt; }, set: function (obj, value) { obj.updatedAt = value; } }, metadata: _metadata }, _updatedAt_initializers, _updatedAt_extraInitializers);
            __esDecorate(null, null, _loanType_decorators, { kind: "field", name: "loanType", static: false, private: false, access: { has: function (obj) { return "loanType" in obj; }, get: function (obj) { return obj.loanType; }, set: function (obj, value) { obj.loanType = value; } }, metadata: _metadata }, _loanType_initializers, _loanType_extraInitializers);
            __esDecorate(null, null, _monthlyPayment_decorators, { kind: "field", name: "monthlyPayment", static: false, private: false, access: { has: function (obj) { return "monthlyPayment" in obj; }, get: function (obj) { return obj.monthlyPayment; }, set: function (obj, value) { obj.monthlyPayment = value; } }, metadata: _metadata }, _monthlyPayment_initializers, _monthlyPayment_extraInitializers);
            __esDecorate(null, null, _totalPayment_decorators, { kind: "field", name: "totalPayment", static: false, private: false, access: { has: function (obj) { return "totalPayment" in obj; }, get: function (obj) { return obj.totalPayment; }, set: function (obj, value) { obj.totalPayment = value; } }, metadata: _metadata }, _totalPayment_initializers, _totalPayment_extraInitializers);
            if (_metadata) Object.defineProperty(_a, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        })(),
        _a;
}();
exports.LoanApplicationDto = LoanApplicationDto;
