import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateLoanTables1710000000000 implements MigrationInterface {
  name = 'CreateLoanTables1710000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enums
    await queryRunner.query(`
      CREATE TYPE "public"."loan_applications_purpose_enum" AS ENUM (
        'personal',
        'education',
        'housing',
        'car',
        'business',
        'medical',
        'renovation',
        'other'
      )
    `);

    // Create loan_applications table
    await queryRunner.query(`
      CREATE TABLE "loan_applications" (
        "id" SERIAL NOT NULL,
        "userId" integer NOT NULL,
        "amount" decimal(10,2) NOT NULL,
        "term" integer NOT NULL,
        "type" "public"."loan_applications_type_enum" NOT NULL DEFAULT 'personal',
        "status" "public"."loan_applications_status_enum" NOT NULL DEFAULT 'pending',
        "interestRate" decimal(5,2),
        "monthlyPayment" decimal(10,2),
        "totalPayment" decimal(10,2),
        "riskScore" decimal(5,2),
        "rejectionReason" character varying,
        "approvedBy" integer,
        "rejectedBy" integer,
        "employmentStatus" "public"."loan_applications_employment_status_enum" NOT NULL DEFAULT 'employed',
        "collateral" "public"."loan_applications_collateral_enum" NOT NULL DEFAULT 'none',
        "metadata" jsonb,
        "documentMetadata" jsonb,
        "riskAssessment" jsonb,
        "approvedAt" TIMESTAMP,
        "rejectedAt" TIMESTAMP,
        "cancelledAt" TIMESTAMP,
        "cancelledBy" integer,
        "cancellationReason" character varying,
        "purpose" "public"."loan_applications_purpose_enum" NOT NULL DEFAULT 'other',
        "annualIncome" decimal(10,2) NOT NULL,
        "debtToIncomeRatio" decimal(5,2) NOT NULL,
        "notes" text,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_loan_applications" PRIMARY KEY ("id")
      )
    `);

    // Create loan_reviews table
    await queryRunner.query(`
      CREATE TABLE "loan_reviews" (
        "id" SERIAL NOT NULL,
        "loanApplicationId" integer NOT NULL,
        "reviewerId" integer NOT NULL,
        "status" "public"."loan_reviews_status_enum" NOT NULL DEFAULT 'pending',
        "type" "public"."loan_reviews_type_enum" NOT NULL DEFAULT 'initial',
        "comments" text,
        "riskFactors" jsonb,
        "verificationResults" jsonb,
        "decisionFactors" jsonb,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_loan_reviews" PRIMARY KEY ("id")
      )
    `);

    // Create foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "loan_applications"
      ADD CONSTRAINT "FK_loan_applications_user"
      FOREIGN KEY ("userId")
      REFERENCES "users"("id")
      ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "loan_reviews"
      ADD CONSTRAINT "FK_loan_reviews_application"
      FOREIGN KEY ("loanApplicationId")
      REFERENCES "loan_applications"("id")
      ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "loan_reviews"
      ADD CONSTRAINT "FK_loan_reviews_reviewer"
      FOREIGN KEY ("reviewerId")
      REFERENCES "users"("id")
      ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "loan_reviews" DROP CONSTRAINT "FK_loan_reviews_reviewer"`);
    await queryRunner.query(`ALTER TABLE "loan_reviews" DROP CONSTRAINT "FK_loan_reviews_application"`);
    await queryRunner.query(`ALTER TABLE "loan_applications" DROP CONSTRAINT "FK_loan_applications_user"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "loan_reviews"`);
    await queryRunner.query(`DROP TABLE "loan_applications"`);    // Drop enums
    await queryRunner.query(`DROP TYPE "public"."loan_reviews_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."loan_reviews_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."loan_applications_collateral_enum"`);
    await queryRunner.query(`DROP TYPE "public"."loan_applications_employment_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."loan_applications_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."loan_applications_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."loan_applications_purpose_enum"`);
    await queryRunner.query(`DROP TYPE "public"."loan_applications_purpose_enum"`);
  }
}