"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserAndAuthTables1700000000002 = void 0;
var CreateUserAndAuthTables1700000000002 = /** @class */ (function () {
    function CreateUserAndAuthTables1700000000002() {
        this.name = 'CreateUserAndAuthTables1700000000002';
    }
    CreateUserAndAuthTables1700000000002.prototype.up = function (queryRunner) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: 
                    // 创建角色表
                    return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"roles\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"name\" VARCHAR NOT NULL UNIQUE,\n        \"description\" VARCHAR,\n        \"permissions\" JSONB NOT NULL DEFAULT '[]',\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now(),\n        \"updated_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 1:
                        // 创建角色表
                        _a.sent();
                        // 创建用户表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"users\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"username\" VARCHAR NOT NULL UNIQUE,\n        \"email\" VARCHAR NOT NULL UNIQUE,\n        \"phone\" VARCHAR,\n        \"password\" VARCHAR NOT NULL,\n        \"salt\" VARCHAR NOT NULL,\n        \"avatar\" VARCHAR,\n        \"status\" VARCHAR NOT NULL DEFAULT 'active',\n        \"last_login_at\" TIMESTAMP,\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now(),\n        \"updated_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 2:
                        // 创建用户表
                        _a.sent();
                        // 创建用户角色关联表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"user_roles\" (\n        \"user_id\" INTEGER NOT NULL REFERENCES \"users\"(\"id\") ON DELETE CASCADE,\n        \"role_id\" INTEGER NOT NULL REFERENCES \"roles\"(\"id\") ON DELETE CASCADE,\n        PRIMARY KEY (\"user_id\", \"role_id\")\n      )\n    ")];
                    case 3:
                        // 创建用户角色关联表
                        _a.sent();
                        // 创建用户认证令牌表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"auth_tokens\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"user_id\" INTEGER NOT NULL REFERENCES \"users\"(\"id\") ON DELETE CASCADE,\n        \"token\" VARCHAR NOT NULL UNIQUE,\n        \"type\" VARCHAR NOT NULL,\n        \"expires_at\" TIMESTAMP NOT NULL,\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 4:
                        // 创建用户认证令牌表
                        _a.sent();
                        // 创建密码重置表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"password_resets\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"user_id\" INTEGER NOT NULL REFERENCES \"users\"(\"id\") ON DELETE CASCADE,\n        \"token\" VARCHAR NOT NULL UNIQUE,\n        \"expires_at\" TIMESTAMP NOT NULL,\n        \"used_at\" TIMESTAMP,\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 5:
                        // 创建密码重置表
                        _a.sent();
                        // 插入基础角色
                        return [4 /*yield*/, queryRunner.query("\n      INSERT INTO \"roles\" (\"name\", \"description\", \"permissions\") VALUES\n      ('admin', '\u7CFB\u7EDF\u7BA1\u7406\u5458', '[\"*\"]'),\n      ('user', '\u666E\u901A\u7528\u6237', '[\"read:own\", \"write:own\"]'),\n      ('auditor', '\u5BA1\u6838\u5458', '[\"read:all\", \"write:audit\"]')\n    ")];
                    case 6:
                        // 插入基础角色
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    CreateUserAndAuthTables1700000000002.prototype.down = function (queryRunner) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, queryRunner.query("DROP TABLE \"password_resets\"")];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"auth_tokens\"")];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"user_roles\"")];
                    case 3:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"users\"")];
                    case 4:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"roles\"")];
                    case 5:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    return CreateUserAndAuthTables1700000000002;
}());
exports.CreateUserAndAuthTables1700000000002 = CreateUserAndAuthTables1700000000002;
