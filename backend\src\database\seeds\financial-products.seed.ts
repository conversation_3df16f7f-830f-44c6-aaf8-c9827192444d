import { DataSource } from 'typeorm';
import { FinancialProduct } from '../../modules/product/entities/financial-product.entity';

export default async function seedFinancialProducts(dataSource: DataSource) {
  const productRepository = dataSource.getRepository(FinancialProduct);

  // 清空现有数据
  await productRepository.clear();

  const products = [
    {
      name: '工商银行融e借2025版',
      institution_name: '中国工商银行',
      product_type: 'personal',
      interest_rate: 3.85,
      amount_min: 1000,
      amount_max: 800000,
      loan_term_min: 1,
      loan_term_max: 60,
      description: '2025年全新升级，支持AI智能审批，区块链征信，元宇宙场景应用',
      features: JSON.stringify({
        online_application: true,
        flexible_repayment: true,
        early_repayment: true,
        blockchain_credit: true,
        metaverse_support: true
      }),
      requirements: JSON.stringify({
        min_age: 18,
        max_age: 70,
        min_income: 2500,
        credit_score_min: 580,
        employment_type: ['full_time', 'part_time', 'gig_economy'],
        collateral_required: false
      })
    },
    {
      name: '招商银行闪电贷',
      institution_name: '招商银行',
      product_type: 'personal',
      interest_rate: 5.25,
      amount_min: 1000,
      amount_max: 300000,
      loan_term_min: 3,
      loan_term_max: 24,
      description: '手机银行一键申请，最快60秒放款',
      features: JSON.stringify({
        instant_approval: true,
        online_application: true,
        flexible_repayment: false,
        early_repayment: true
      }),
      requirements: JSON.stringify({
        min_age: 22,
        max_age: 60,
        min_income: 4000,
        credit_score_min: 680,
        employment_type: ['full_time'],
        collateral_required: false
      })
    },
    {
      name: '平安银行薪金贷',
      institution_name: '平安银行',
      product_type: 'personal',
      interest_rate: 6.8,
      amount_min: 30000,
      amount_max: 500000,
      loan_term_min: 12,
      loan_term_max: 48,
      description: '无需抵押担保，凭工资流水即可申请',
      features: JSON.stringify({
        salary_based: true,
        online_application: true,
        flexible_repayment: true,
        early_repayment: true
      }),
      requirements: JSON.stringify({
        min_age: 25,
        max_age: 55,
        min_income: 8000,
        credit_score_min: 700,
        employment_type: ['full_time'],
        collateral_required: false
      })
    },
    {
      name: '中国银行中小企业贷',
      institution_name: '中国银行',
      product_type: 'business',
      interest_rate: 5.5,
      amount_min: 100000,
      amount_max: 5000000,
      loan_term_min: 12,
      loan_term_max: 120,
      description: '专为中小企业设计，支持企业经营发展',
      features: JSON.stringify({
        business_focused: true,
        online_application: false,
        flexible_repayment: true,
        early_repayment: true
      }),
      requirements: JSON.stringify({
        min_age: 25,
        max_age: 65,
        min_income: 20000,
        credit_score_min: 650,
        employment_type: ['self_employed'],
        collateral_required: true
      })
    },
    {
      name: '农业银行房屋抵押贷',
      institution_name: '农业银行',
      product_type: 'mortgage',
      interest_rate: 4.9,
      amount_min: 500000,
      amount_max: 10000000,
      loan_term_min: 60,
      loan_term_max: 360,
      description: '以房产作为抵押，利率低，额度高',
      features: JSON.stringify({
        property_secured: true,
        online_application: false,
        flexible_repayment: false,
        early_repayment: true
      }),
      requirements: JSON.stringify({
        min_age: 25,
        max_age: 65,
        min_income: 10000,
        credit_score_min: 650,
        employment_type: ['full_time', 'self_employed'],
        collateral_required: true
      })
    },
    {
      name: '交通银行车贷通',
      institution_name: '交通银行',
      product_type: 'auto',
      interest_rate: 5.8,
      amount_min: 50000,
      amount_max: 1000000,
      loan_term_min: 12,
      loan_term_max: 60,
      description: '购车专用贷款，手续简便，放款快速',
      features: JSON.stringify({
        auto_focused: true,
        online_application: true,
        flexible_repayment: false,
        early_repayment: true
      }),
      requirements: JSON.stringify({
        min_age: 22,
        max_age: 60,
        min_income: 6000,
        credit_score_min: 600,
        employment_type: ['full_time', 'part_time'],
        collateral_required: true
      })
    },
    {
      name: '微众银行微粒贷',
      institution_name: '微众银行',
      product_type: 'personal',
      interest_rate: 7.2,
      amount_min: 500,
      amount_max: 200000,
      loan_term_min: 1,
      loan_term_max: 20,
      description: '微信银行产品，随借随还，按日计息',
      features: JSON.stringify({
        daily_interest: true,
        online_application: true,
        flexible_repayment: true,
        early_repayment: false
      }),
      requirements: JSON.stringify({
        min_age: 20,
        max_age: 60,
        min_income: 2000,
        credit_score_min: 550,
        employment_type: ['full_time', 'part_time', 'self_employed'],
        collateral_required: false
      })
    },
    {
      name: '网商银行经营贷',
      institution_name: '网商银行',
      product_type: 'business',
      interest_rate: 6.5,
      amount_min: 1000,
      amount_max: 1000000,
      loan_term_min: 1,
      loan_term_max: 12,
      description: '专为小微企业和个体工商户设计',
      features: JSON.stringify({
        sme_focused: true,
        online_application: true,
        flexible_repayment: true,
        early_repayment: false
      }),
      requirements: JSON.stringify({
        min_age: 22,
        max_age: 65,
        min_income: 5000,
        credit_score_min: 600,
        employment_type: ['self_employed'],
        collateral_required: false
      })
    },
    {
      name: '浦发银行AI智贷2025',
      institution_name: '浦发银行',
      product_type: 'personal',
      interest_rate: 4.8,
      amount_min: 1000,
      amount_max: 600000,
      loan_term_min: 1,
      loan_term_max: 60,
      description: '2025年AI驱动，智能额度管理，支持CBDC数字人民币',
      features: JSON.stringify({
        ai_driven: true,
        online_application: true,
        flexible_repayment: true,
        early_repayment: true,
        ai_credit_management: true,
        cbdc_support: true
      }),
      requirements: JSON.stringify({
        min_age: 18,
        max_age: 70,
        min_income: 2000,
        credit_score_min: 550,
        employment_type: ['full_time', 'part_time', 'freelance'],
        collateral_required: false
      })
    },
    {
      name: '蚂蚁借呗Web3版',
      institution_name: '蚂蚁金服',
      product_type: 'personal',
      interest_rate: 4.2,
      amount_min: 500,
      amount_max: 300000,
      loan_term_min: 1,
      loan_term_max: 12,
      description: '基于大数据风控，支持Web3.0身份认证，碳中和绿色金融',
      features: JSON.stringify({
        big_data_risk: true,
        online_application: true,
        flexible_repayment: true,
        early_repayment: true,
        web3_identity: true,
        green_finance: true
      }),
      requirements: JSON.stringify({
        min_age: 18,
        max_age: 65,
        min_income: 1500,
        credit_score_min: 520,
        employment_type: ['full_time', 'part_time', 'gig_economy', 'student'],
        collateral_required: false
      })
    },
    {
      name: '京东金条元宇宙版',
      institution_name: '京东数科',
      product_type: 'personal',
      interest_rate: 4.5,
      amount_min: 1000,
      amount_max: 200000,
      loan_term_min: 1,
      loan_term_max: 24,
      description: '2025年全新升级，支持NFT抵押，元宇宙消费场景',
      features: JSON.stringify({
        nft_support: true,
        online_application: true,
        flexible_repayment: true,
        early_repayment: true,
        nft_collateral: true,
        metaverse_consumption: true
      }),
      requirements: JSON.stringify({
        min_age: 20,
        max_age: 60,
        min_income: 2000,
        credit_score_min: 580,
        employment_type: ['full_time', 'part_time', 'freelance'],
        collateral_required: false
      })
    }
  ];

  await productRepository.save(products);
  console.log('Financial products seeded successfully');
}
