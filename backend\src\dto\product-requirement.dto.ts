import { Is<PERSON>tring, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsBoolean, IsArray, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class ProductRequirementDto {
  @IsOptional()
  @IsUUID()
  id?: string;

  @IsOptional()
  @IsUUID()
  productId?: string;

  @IsOptional()
  @IsNumber()
  @Min(18)
  @Max(100)
  minAge?: number;

  @IsOptional()
  @IsNumber()
  @Min(18)
  @Max(100)
  maxAge?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  minIncome?: number;

  @IsOptional()
  @IsNumber()
  @Min(300)
  @Max(850)
  minCreditScore?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  maxDebtToIncomeRatio?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  employmentTypes?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requiredDocuments?: string[];

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(50)
  workExperienceYears?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  additionalFactors?: string[];

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class CreateProductRequirementDto extends ProductRequirementDto {
  @IsUUID()
  productId: string;
}

export class UpdateProductRequirementDto extends ProductRequirementDto {}
