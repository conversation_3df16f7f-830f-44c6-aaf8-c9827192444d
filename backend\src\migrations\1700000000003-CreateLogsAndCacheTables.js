"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateLogsAndCacheTables1700000000003 = void 0;
var CreateLogsAndCacheTables1700000000003 = /** @class */ (function () {
    function CreateLogsAndCacheTables1700000000003() {
        this.name = 'CreateLogsAndCacheTables1700000000003';
    }
    CreateLogsAndCacheTables1700000000003.prototype.up = function (queryRunner) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: 
                    // 创建用户日志表
                    return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"user_logs\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"user_id\" INTEGER NOT NULL REFERENCES \"users\"(\"id\") ON DELETE CASCADE,\n        \"action\" VARCHAR NOT NULL,\n        \"details\" JSONB,\n        \"ip_address\" VARCHAR,\n        \"user_agent\" VARCHAR,\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 1:
                        // 创建用户日志表
                        _a.sent();
                        // 创建系统日志表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"system_logs\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"level\" VARCHAR NOT NULL,\n        \"message\" TEXT NOT NULL,\n        \"context\" JSONB,\n        \"timestamp\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 2:
                        // 创建系统日志表
                        _a.sent();
                        // 创建缓存表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"cache\" (\n        \"key\" VARCHAR PRIMARY KEY,\n        \"value\" TEXT NOT NULL,\n        \"expires_at\" TIMESTAMP,\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 3:
                        // 创建缓存表
                        _a.sent();
                        // 创建索引
                        return [4 /*yield*/, queryRunner.query("\n      CREATE INDEX \"idx_user_logs_user_id\" ON \"user_logs\"(\"user_id\");\n      CREATE INDEX \"idx_user_logs_action\" ON \"user_logs\"(\"action\");\n      CREATE INDEX \"idx_system_logs_level\" ON \"system_logs\"(\"level\");\n      CREATE INDEX \"idx_system_logs_timestamp\" ON \"system_logs\"(\"timestamp\");\n      CREATE INDEX \"idx_cache_expires_at\" ON \"cache\"(\"expires_at\");\n    ")];
                    case 4:
                        // 创建索引
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    CreateLogsAndCacheTables1700000000003.prototype.down = function (queryRunner) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, queryRunner.query("DROP TABLE \"user_logs\"")];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"system_logs\"")];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"cache\"")];
                    case 3:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    return CreateLogsAndCacheTables1700000000003;
}());
exports.CreateLogsAndCacheTables1700000000003 = CreateLogsAndCacheTables1700000000003;
