import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, Between, In, MoreThanOrEqual, LessThanOrEqual } from 'typeorm';
import { Product } from '../entities/product.entity';
import { CacheService } from './cache.service';
import { LoggerService } from './logger.service';

export interface SearchResult {
  products: Product[];
  total: number;
  page: number;
  pageSize: number;
}

@Injectable()
export class ProductSearchService {
  private readonly CACHE_TTL = 3600; // 1小时缓存

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    private readonly cacheService: CacheService,
    private readonly logger: LoggerService
  ) {}

  // 搜索产品
  async searchProducts(params: {
    keyword?: string;
    category?: string;
    minAmount?: number;
    maxAmount?: number;
    minTerm?: number;
    maxTerm?: number;
    minInterestRate?: number;
    maxInterestRate?: number;
    isActive?: boolean;
    isFeatured?: boolean;
    page?: number;
    pageSize?: number;
    sortBy?: keyof Product;
    sortOrder?: 'ASC' | 'DESC';
  }): Promise<SearchResult> {
    try {
      const {
        keyword,
        category,
        minAmount,
        maxAmount,
        minTerm,
        maxTerm,
        minInterestRate,
        maxInterestRate,
        isActive,
        isFeatured,
        page = 1,
        pageSize = 10,
        sortBy,
        sortOrder = 'DESC'
      } = params;

      // 构建缓存键
      const cacheKey = `search:${JSON.stringify(params)}`;
      const cachedResult = await this.cacheService.get<SearchResult>(cacheKey);
      if (cachedResult) {
        return cachedResult;
      }

      // 构建查询条件
      const where: any = {};

      if (keyword) {
        where.name = Like(`%${keyword}%`);
      }

      if (category) {
        where.category = category;
      }

      if (minAmount !== undefined || maxAmount !== undefined) {
        where.amount = {};
        if (minAmount !== undefined) {
          where.amount.minAmount = MoreThanOrEqual(minAmount);
        }
        if (maxAmount !== undefined) {
          where.amount.maxAmount = LessThanOrEqual(maxAmount);
        }
      }

      if (minTerm !== undefined || maxTerm !== undefined) {
        where.term = {};
        if (minTerm !== undefined) {
          where.term.minTerm = Between(minTerm, maxTerm || 999);
        }
        if (maxTerm !== undefined) {
          where.term.maxTerm = Between(minTerm || 0, maxTerm);
        }
      }

      if (minInterestRate !== undefined || maxInterestRate !== undefined) {
        where.interestRate = {};
        if (minInterestRate !== undefined) {
          where.interestRate = Between(
            minInterestRate,
            maxInterestRate || 999
          );
        }
        if (maxInterestRate !== undefined) {
          where.interestRate = Between(
            minInterestRate || 0,
            maxInterestRate
          );
        }
      }

      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      if (isFeatured !== undefined) {
        where.isFeatured = isFeatured;
      }

      // 构建排序条件
      const order: { [key in keyof Product]?: "ASC" | "DESC" | 1 | -1 } = {};
      if (sortBy) {
        order[sortBy as keyof Product] = sortOrder as "ASC" | "DESC";
      }

      // 执行查询
      const [products, total] = await this.productRepository.findAndCount({
        where,
        order,
        skip: (page - 1) * pageSize,
        take: pageSize,
      });

      const result: SearchResult = {
        products,
        total,
        page,
        pageSize
      };

      // 缓存结果
      await this.cacheService.set(cacheKey, result, this.CACHE_TTL);

      this.logger.debug(`Found ${products.length} products out of ${total}`);

      return result;
    } catch (error) {
      this.logger.error(`Error searching products: ${String(error)}`, error instanceof Error ? error.stack : undefined);
      return {
        products: [],
        total: 0,
        page: 1,
        pageSize: 10
      };
    }
  }

  // 获取产品分类列表
  async getCategories(): Promise<string[]> {
    try {
      const cacheKey = 'product:categories';
      const cachedCategories = await this.cacheService.get<string[]>(cacheKey);
      if (cachedCategories) {
        return cachedCategories;
      }

      const products = await this.productRepository.find({
        select: ['category']
      });

      const categories = [...new Set(products.map(p => p.category))].filter(
        Boolean
      );

      await this.cacheService.set(cacheKey, categories, this.CACHE_TTL);

      return categories;
    } catch (error) {
      this.logger.error('获取产品分类失败', error);
      return [];
    }
  }

  // 获取产品标签列表
  async getTags(): Promise<string[]> {
    try {
      const cacheKey = 'product:tags';
      const cachedTags = await this.cacheService.get<string[]>(cacheKey);
      if (cachedTags) {
        return cachedTags;
      }

      const products = await this.productRepository.find({
        select: ['tags']
      });

      const tags = [...new Set(products.flatMap(p => p.tags || []))].filter(
        Boolean
      );

      await this.cacheService.set(cacheKey, tags, this.CACHE_TTL);

      return tags;
    } catch (error) {
      this.logger.error('获取产品标签失败', error);
      return [];
    }
  }

  // 获取热门搜索关键词
  async getHotKeywords(limit: number = 10): Promise<string[]> {
    try {
      const cacheKey = `product:hot:keywords:${limit}`;
      const cachedKeywords = await this.cacheService.get<string[]>(cacheKey);
      if (cachedKeywords) {
        return cachedKeywords;
      }

      // 这里可以从搜索日志或统计表中获取热门关键词
      // 示例实现
      const keywords = [
        '个人贷款',
        '房贷',
        '车贷',
        '信用贷款',
        '经营贷款',
        '消费贷款',
        '抵押贷款',
        '无抵押贷款',
        '小额贷款',
        '大额贷款'
      ].slice(0, limit);

      await this.cacheService.set(cacheKey, keywords, 1800); // 30分钟过期

      return keywords;
    } catch (error) {
      this.logger.error('获取热门搜索关键词失败', error);
      return [];
    }
  }

  async searchByCategory(category: string) {
    try {
      const cacheKey = `product:category:${category}`;
      const cachedResults = await this.cacheService.get<string>(cacheKey);
      
      if (cachedResults) {
        return JSON.parse(cachedResults);
      }

      const products = await this.productRepository.find({
        where: { category },
        order: {
          isFeatured: 'DESC',
          sortOrder: 'ASC',
          createdAt: 'DESC'
        }
      });

      const results = products.map(product => ({
        id: product.id,
        name: product.name,
        code: product.code,
        description: product.description,
        category: product.category,
        minAmount: product.minAmount,
        maxAmount: product.maxAmount,
        minTerm: product.minTerm,
        maxTerm: product.maxTerm,
        interestRate: product.interestRate,
        isActive: product.isActive,
        isFeatured: product.isFeatured,
        metadata: product.metadata
      }));

      await this.cacheService.set(cacheKey, JSON.stringify(results), this.CACHE_TTL);
      
      return results;
    } catch (error) {
      this.logger.error('按类别搜索产品失败', error);
      throw error;
    }
  }

  async searchByAmountRange(minAmount: number, maxAmount: number) {
    try {
      const cacheKey = `product:amount:${minAmount}:${maxAmount}`;
      const cachedResults = await this.cacheService.get<string>(cacheKey);
      
      if (cachedResults) {
        return JSON.parse(cachedResults);
      }

      const products = await this.productRepository.find({
        where: {
          minAmount: minAmount,
          maxAmount: maxAmount
        },
        order: {
          isFeatured: 'DESC',
          sortOrder: 'ASC',
          createdAt: 'DESC'
        }
      });

      const results = products.map(product => ({
        id: product.id,
        name: product.name,
        code: product.code,
        description: product.description,
        category: product.category,
        minAmount: product.minAmount,
        maxAmount: product.maxAmount,
        minTerm: product.minTerm,
        maxTerm: product.maxTerm,
        interestRate: product.interestRate,
        isActive: product.isActive,
        isFeatured: product.isFeatured,
        metadata: product.metadata
      }));

      await this.cacheService.set(cacheKey, JSON.stringify(results), this.CACHE_TTL);
      
      return results;
    } catch (error) {
      this.logger.error('按金额范围搜索产品失败', error);
      throw error;
    }
  }

  async searchByTermRange(minTerm: number, maxTerm: number) {
    try {
      const cacheKey = `product:term:${minTerm}:${maxTerm}`;
      const cachedResults = await this.cacheService.get<string>(cacheKey);
      
      if (cachedResults) {
        return JSON.parse(cachedResults);
      }

      const products = await this.productRepository.find({
        where: {
          minTerm: minTerm,
          maxTerm: maxTerm
        },
        order: {
          isFeatured: 'DESC',
          sortOrder: 'ASC',
          createdAt: 'DESC'
        }
      });

      const results = products.map(product => ({
        id: product.id,
        name: product.name,
        code: product.code,
        description: product.description,
        category: product.category,
        minAmount: product.minAmount,
        maxAmount: product.maxAmount,
        minTerm: product.minTerm,
        maxTerm: product.maxTerm,
        interestRate: product.interestRate,
        isActive: product.isActive,
        isFeatured: product.isFeatured,
        metadata: product.metadata
      }));

      await this.cacheService.set(cacheKey, JSON.stringify(results), this.CACHE_TTL);
      
      return results;
    } catch (error) {
      this.logger.error('按期限范围搜索产品失败', error);
      throw error;
    }
  }

  async searchByInterestRate(maxInterestRate: number) {
    try {
      const cacheKey = `product:interest:${maxInterestRate}`;
      const cachedResults = await this.cacheService.get<string>(cacheKey);
      
      if (cachedResults) {
        return JSON.parse(cachedResults);
      }

      const products = await this.productRepository.find({
        where: {
          interestRate: maxInterestRate
        },
        order: {
          isFeatured: 'DESC',
          sortOrder: 'ASC',
          createdAt: 'DESC'
        }
      });

      const results = products.map(product => ({
        id: product.id,
        name: product.name,
        code: product.code,
        description: product.description,
        category: product.category,
        minAmount: product.minAmount,
        maxAmount: product.maxAmount,
        minTerm: product.minTerm,
        maxTerm: product.maxTerm,
        interestRate: product.interestRate,
        isActive: product.isActive,
        isFeatured: product.isFeatured,
        metadata: product.metadata
      }));

      await this.cacheService.set(cacheKey, JSON.stringify(results), this.CACHE_TTL);
      
      return results;
    } catch (error) {
      this.logger.error('按利率搜索产品失败', error);
      throw error;
    }
  }

  private processData(data: any): any {
    this.logger.debug(`Processing data: ${String(data)}`);
    return data;
  }

  private mapHeaders(headers: any): any {
    this.logger.debug(`Mapping headers: ${String(headers)}`);
    return headers;
  }

  private parseRow(row: any): any {
    this.logger.debug(`Parsing row: ${String(row)}`);
    return row;
  }

  private transformProductData(data: any): any {
    this.logger.debug(`Transforming product data: ${String(data)}`);
    return data;
  }
} 