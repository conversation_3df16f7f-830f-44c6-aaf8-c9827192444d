import { IsEnum, IsNumber, IsString, IsObject, IsOptional, Min, Max, IsArray, ValidateNested, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { LoanType } from '../enums/loan-type.enum';
import { LoanStatus } from '../enums/loan-status.enum';
import { LoanPurpose } from '../enums/loan-purpose.enum';
import { CollateralType } from '../enums/collateral-type.enum';
import { LoanDocument } from '../entities/loan-document.entity';

class LoanMetadataDto {
  @ApiProperty({ description: '月收入' })
  @IsNumber()
  @Min(0)
  monthlyIncome: number;

  @ApiProperty({ description: '就业时长（月）' })
  @IsNumber()
  @Min(0)
  employmentDuration: number;

  @ApiProperty({ description: '信用分数' })
  @IsNumber()
  @Min(300)
  @Max(850)
  creditScore: number;

  @ApiProperty({ description: '负债收入比' })
  @IsNumber()
  @Min(0)
  @Max(1)
  debtToIncomeRatio: number;
}

export class CreateLoanApplicationDto {
  @ApiProperty({ description: '贷款金额' })
  @IsNotEmpty()
  @IsNumber()
  @Min(1000)
  @Max(1000000)
  amount: number;

  @ApiProperty({ description: '贷款期限（月）' })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(360)
  term: number;

  @ApiProperty({ description: '贷款类型', enum: LoanType })
  @IsNotEmpty()
  @IsEnum(LoanType)
  type: LoanType;
  @ApiProperty({ description: '贷款用途', enum: LoanPurpose })
  @IsNotEmpty()
  @IsEnum(LoanPurpose)
  purpose: LoanPurpose;

  @ApiProperty({ description: '年收入' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  annualIncome: number;

  @ApiProperty({ description: '债务收入比' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(1)
  debtToIncomeRatio: number;

  @ApiProperty({ description: '就业状态' })
  @IsNotEmpty()
  @IsString()
  employmentStatus: string;

  @ApiProperty({ description: '工作年限' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  workExperience: number;
  @ApiProperty({ description: '信用评分' })
  @IsOptional()
  @IsNumber()
  @Min(300)
  @Max(850)
  creditScore?: number;

  @ApiProperty({ description: '贷款元数据' })
  @IsObject()
  @ValidateNested()
  @Type(() => LoanMetadataDto)
  metadata: LoanMetadataDto;
}

export class UpdateLoanApplicationDto {
  @ApiProperty({ description: '贷款金额' })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(1000000)
  amount?: number;

  @ApiProperty({ description: '贷款期限（月）' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(360)
  term?: number;
  @ApiProperty({ description: '贷款用途', enum: LoanPurpose })
  @IsOptional()
  @IsEnum(LoanPurpose)
  purpose?: LoanPurpose;

  @ApiProperty({ description: '年收入' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  annualIncome?: number;

  @ApiProperty({ description: '债务收入比' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  debtToIncomeRatio?: number;

  @ApiProperty({ description: '就业状态' })
  @IsOptional()
  @IsString()
  employmentStatus?: string;

  @ApiProperty({ description: '工作年限' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  workExperience?: number;

  @ApiProperty({ description: '信用评分' })
  @IsOptional()
  @IsNumber()
  @Min(300)
  @Max(850)
  creditScore?: number;

  @ApiProperty({ description: '月收入', required: false })
  @IsNumber()
  @Min(0)
  @IsOptional()
  monthlyIncome?: number;

  @ApiProperty({ description: '就业类型', required: false })
  @IsString()
  @IsOptional()
  employmentType?: string;

  @ApiProperty({ description: '就业时长(月)', required: false })
  @IsNumber()
  @Min(0)
  @IsOptional()
  employmentDuration?: number;

  @ApiProperty({ description: '教育程度', required: false })
  @IsString()
  @IsOptional()
  education?: string;

  @ApiProperty({ description: '婚姻状况', required: false })
  @IsString()
  @IsOptional()
  maritalStatus?: string;

  @ApiProperty({ description: '房产状况', required: false })
  @IsString()
  @IsOptional()
  houseStatus?: string;

  @ApiProperty({ description: '车辆状况', required: false })
  @IsString()
  @IsOptional()
  carStatus?: string;

  @ApiProperty({ description: '贷款用途', required: false })
  @IsString()
  @IsOptional()
  loanPurpose?: string;
  @ApiProperty({ description: '抵押物', enum: CollateralType, required: false })
  @IsEnum(CollateralType)
  @IsOptional()
  collateral?: CollateralType;

  @ApiProperty({ description: '担保人', required: false })
  @IsString()
  @IsOptional()
  guarantor?: string;

  @ApiProperty({ description: '文档列表', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  documents?: string[];
}

export class LoanApplicationDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  amount: number;

  @ApiProperty()
  term: number;

  @ApiProperty()
  purpose: string;

  @ApiProperty({ enum: LoanStatus })
  status: LoanStatus;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ enum: LoanType })
  loanType: LoanType;

  @ApiProperty()
  monthlyPayment: number;

  @ApiProperty()
  totalPayment: number;
} 