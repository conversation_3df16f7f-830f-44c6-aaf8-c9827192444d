import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuditRecord } from '../entities/audit-record.entity';
import { User } from '../entities/user.entity';
import { OcrService } from './ocr.service';
import { FaceVerificationService } from './face-verification.service';
import { LoggerService } from './logger.service';

export interface MultiModalAuditResult {
  success: boolean;
  data?: any;
  message?: string;
  errors?: string[];
}

export interface AuditHistoryQuery {
  userId?: string;
  startDate?: Date;
  endDate?: Date;
  auditType?: string;
  page?: number;
  limit?: number;
}

@Injectable()
export class MultiModalAuditService {
  constructor(
    @InjectRepository(AuditRecord)
    private readonly auditRecordRepository: Repository<AuditRecord>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly ocrService: OcrService,
    private readonly faceVerificationService: FaceVerificationService,
    private readonly logger: LoggerService
  ) {}
  async createAuditRecord(
    userId: string,
    auditType: string,
    data: any,
    result: MultiModalAuditResult
  ): Promise<AuditRecord> {
    try {      const auditRecord = this.auditRecordRepository.create({
        userId,
        applicationId: `app_${Date.now()}`, // Generate applicationId
        status: result.success ? 'approved' : 'rejected',
        reviewNotes: result.message,
        reviewDetails: {
          riskScore: result.data?.overallScore || 0,
          documentVerification: result.success,
          additionalChecks: {
            auditType,
            auditData: data,
            result: result.success ? 'success' : 'failed',
            details: result.data || result.message,
            errors: result.errors
          }
        },
        metadata: {
          reviewTime: Date.now(),
          reviewSteps: [auditType],
          reviewHistory: [{
            status: result.success ? 'approved' : 'rejected',
            timestamp: new Date(),
            notes: result.message
          }]
        }
      });

      return await this.auditRecordRepository.save(auditRecord);
    } catch (error) {
      this.logger.error('Failed to create audit record', error);
      throw error;
    }
  }
  async getAuditHistory(query: AuditHistoryQuery): Promise<{
    records: AuditRecord[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const {
        userId,
        startDate,
        endDate,
        auditType,
        page = 1,
        limit = 10
      } = query;

      const queryBuilder = this.auditRecordRepository.createQueryBuilder('audit');

      if (userId) {
        queryBuilder.andWhere('audit.userId = :userId', { userId });
      }

      if (startDate) {
        queryBuilder.andWhere('audit.createdAt >= :startDate', { startDate });
      }

      if (endDate) {
        queryBuilder.andWhere('audit.createdAt <= :endDate', { endDate });
      }      if (auditType) {
        queryBuilder.andWhere("audit.reviewDetails -> 'additionalChecks' ->> 'auditType' = :auditType", { 
          auditType 
        });
      }

      queryBuilder
        .orderBy('audit.createdAt', 'DESC')
        .skip((page - 1) * limit)
        .take(limit);

      const [records, total] = await queryBuilder.getManyAndCount();

      return {
        records,
        total,
        page,
        limit
      };
    } catch (error) {
      this.logger.error('Failed to get audit history', error);
      throw error;
    }
  }

  async performComprehensiveAudit(
    userId: string,
    documents: any[]
  ): Promise<MultiModalAuditResult> {
    try {
      const results = {
        identityCard: null,
        bankCard: null,
        incomeProof: null,
        faceVerification: null,
        overallScore: 0,
        riskLevel: 'LOW'
      };

      let totalScore = 0;
      let validDocuments = 0;

      // Process each document
      for (const doc of documents) {
        switch (doc.type) {
          case 'identity_card':
            try {
              results.identityCard = await this.ocrService.recognizeText(doc.buffer);
              if (results.identityCard.success) {
                totalScore += 25;
                validDocuments++;
              }
            } catch (error) {
              this.logger.error('Identity card recognition failed', error);
              results.identityCard = { success: false, error: error.message };
            }
            break;

          case 'bank_card':
            try {
              results.bankCard = await this.ocrService.recognizeText(doc.buffer);
              if (results.bankCard.success) {
                totalScore += 20;
                validDocuments++;
              }
            } catch (error) {
              this.logger.error('Bank card recognition failed', error);
              results.bankCard = { success: false, error: error.message };
            }
            break;

          case 'income_proof':
            try {
              results.incomeProof = await this.ocrService.recognizeText(doc.buffer);
              if (results.incomeProof.success) {
                totalScore += 30;
                validDocuments++;
              }
            } catch (error) {
              this.logger.error('Income proof recognition failed', error);
              results.incomeProof = { success: false, error: error.message };
            }
            break;

          case 'face_photo':
            try {
              results.faceVerification = await this.faceVerificationService.detectFaces(doc.buffer);
              if (results.faceVerification.success) {
                totalScore += 25;
                validDocuments++;
              }
            } catch (error) {
              this.logger.error('Face verification failed', error);
              results.faceVerification = { success: false, error: error.message };
            }
            break;
        }
      }

      // Calculate overall score and risk level
      results.overallScore = validDocuments > 0 ? totalScore / validDocuments : 0;

      if (results.overallScore >= 80) {
        results.riskLevel = 'LOW';
      } else if (results.overallScore >= 60) {
        results.riskLevel = 'MEDIUM';
      } else {
        results.riskLevel = 'HIGH';
      }

      // Save audit record
      await this.createAuditRecord(
        userId,
        'comprehensive_audit',
        { documentCount: documents.length, validDocuments },
        {
          success: true,
          data: results
        }
      );

      return {
        success: true,
        data: results
      };

    } catch (error) {
      this.logger.error('Comprehensive audit failed', error);
      
      await this.createAuditRecord(
        userId,
        'comprehensive_audit',
        { documentCount: documents.length },
        {
          success: false,
          message: error.message,
          errors: [error.message]
        }
      );

      throw new BadRequestException('审核失败，请重试');
    }
  }
  async getAuditStatistics(userId?: string): Promise<any> {
    try {
      const queryBuilder = this.auditRecordRepository.createQueryBuilder('audit');

      if (userId) {
        queryBuilder.where('audit.userId = :userId', { userId });
      }

      const totalRecords = await queryBuilder.getCount();

      const successRecords = await queryBuilder
        .andWhere('audit.status = :status', { status: 'approved' })
        .getCount();

      const failedRecords = totalRecords - successRecords;

      const successRate = totalRecords > 0 ? (successRecords / totalRecords) * 100 : 0;

      // Get audit type distribution from reviewDetails
      const typeDistribution = await queryBuilder
        .select('audit.reviewDetails', 'details')
        .getRawMany();      // Process type distribution
      const typeCount: Record<string, number> = {};
      typeDistribution.forEach(record => {
        const auditType = record.details?.additionalChecks?.auditType || 'unknown';
        typeCount[auditType] = (typeCount[auditType] || 0) + 1;
      });

      const formattedTypeDistribution = Object.entries(typeCount).map(([type, count]) => ({
        type,
        count
      }));

      return {
        totalRecords,
        successRecords,
        failedRecords,
        successRate: Math.round(successRate * 100) / 100,
        typeDistribution: formattedTypeDistribution
      };

    } catch (error) {
      this.logger.error('Failed to get audit statistics', error);
      throw error;
    }
  }
  async getAuditResult(auditId: string, userId: string): Promise<any> {
    try {
      const auditRecord = await this.auditRecordRepository.findOne({
        where: { 
          id: parseInt(auditId),
          userId: userId 
        }
      });

      if (!auditRecord) {
        throw new Error('审核记录不存在');
      }      return {
        id: auditRecord.id,
        auditType: (auditRecord.reviewDetails as any)?.additionalChecks?.auditType || 'unknown',
        result: (auditRecord.reviewDetails as any)?.additionalChecks?.result || 'unknown',
        details: (auditRecord.reviewDetails as any)?.additionalChecks?.details || auditRecord.reviewNotes,
        createdAt: auditRecord.createdAt,
        status: auditRecord.status
      };
    } catch (error) {
      this.logger.error('Failed to get audit result', error);
      throw error;
    }
  }
}
