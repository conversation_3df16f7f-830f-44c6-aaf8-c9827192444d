"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductMatchingController = exports.ProductMatchingRequestDto = exports.ProductMatchWeightsDto = exports.ProductFilterDto = exports.UserProfileDto = exports.ProductCategory = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const product_service_1 = require("../services/product.service");
const jwt_auth_guard_1 = require("../guards/jwt-auth.guard");
const loan_purpose_enum_1 = require("../enums/loan-purpose.enum");
var ProductCategory;
(function (ProductCategory) {
    ProductCategory["PERSONAL"] = "personal";
    ProductCategory["BUSINESS"] = "business";
    ProductCategory["MORTGAGE"] = "mortgage";
    ProductCategory["AUTO"] = "auto";
    ProductCategory["EDUCATION"] = "education";
})(ProductCategory = exports.ProductCategory || (exports.ProductCategory = {}));
class UserProfileDto {
}
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(18),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "age", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "monthlyIncome", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(300),
    (0, class_validator_1.Max)(850),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "creditScore", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserProfileDto.prototype, "employmentType", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "employmentDuration", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "debtToIncomeRatio", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "requestedAmount", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(360),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "preferredTerm", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserProfileDto.prototype, "location", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(loan_purpose_enum_1.LoanPurpose),
    __metadata("design:type", String)
], UserProfileDto.prototype, "purpose", void 0);
exports.UserProfileDto = UserProfileDto;
class ProductFilterDto {
}
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ProductCategory),
    __metadata("design:type", String)
], ProductFilterDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(loan_purpose_enum_1.LoanPurpose),
    __metadata("design:type", String)
], ProductFilterDto.prototype, "purpose", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ProductFilterDto.prototype, "minAmount", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], ProductFilterDto.prototype, "maxInterestRate", void 0);
exports.ProductFilterDto = ProductFilterDto;
class ProductMatchWeightsDto {
    constructor() {
        this.amount = 0.25;
        this.term = 0.15;
        this.interestRate = 0.3;
        this.creditScore = 0.1;
        this.income = 0.1;
        this.purpose = 0.1;
    }
}
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], ProductMatchWeightsDto.prototype, "amount", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], ProductMatchWeightsDto.prototype, "term", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], ProductMatchWeightsDto.prototype, "interestRate", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], ProductMatchWeightsDto.prototype, "creditScore", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], ProductMatchWeightsDto.prototype, "income", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], ProductMatchWeightsDto.prototype, "purpose", void 0);
exports.ProductMatchWeightsDto = ProductMatchWeightsDto;
class ProductMatchingRequestDto {
}
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => UserProfileDto),
    __metadata("design:type", UserProfileDto)
], ProductMatchingRequestDto.prototype, "userProfile", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => ProductFilterDto),
    __metadata("design:type", ProductFilterDto)
], ProductMatchingRequestDto.prototype, "filters", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => ProductMatchWeightsDto),
    __metadata("design:type", ProductMatchWeightsDto)
], ProductMatchingRequestDto.prototype, "weights", void 0);
exports.ProductMatchingRequestDto = ProductMatchingRequestDto;
let ProductMatchingController = class ProductMatchingController {
    constructor(productMatchingEngine, productService) {
        this.productMatchingEngine = productMatchingEngine;
        this.productService = productService;
    }
    async matchProducts(request) {
        try {
            const matchedProducts = await this.productMatchingEngine.findBestMatches(request.userProfile, request.filters, request.weights);
            return {
                success: true,
                data: {
                    recommendations: matchedProducts,
                    totalMatched: matchedProducts.length,
                    userProfile: request.userProfile
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }
    async getDetailedAnalysis(request) {
        try {
            const allProducts = await this.productService.findAll({});
            const detailedAnalysis = await Promise.all(allProducts.map(async (product) => {
                const matchScore = this.productMatchingEngine.calculateMatchScore(request.userProfile, product, request.weights);
                const approvalProbability = this.productMatchingEngine.calculateApprovalProbability(request.userProfile, product);
                const costAnalysis = this.productMatchingEngine.calculateTotalCost(request.userProfile.requestedAmount, product, request.userProfile.preferredTerm);
                return {
                    product,
                    matchScore,
                    approvalProbability,
                    costAnalysis,
                    recommendations: this.generateProductRecommendations(request.userProfile, product)
                };
            }));
            detailedAnalysis.sort((a, b) => b.matchScore - a.matchScore);
            return {
                success: true,
                data: {
                    analysis: detailedAnalysis,
                    userProfile: request.userProfile,
                    marketAnalysis: this.generateMarketAnalysis(allProducts)
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }
    async quickMatch(userId) {
        try {
            const userProfile = await this.getUserProfileFromDb(userId);
            if (!userProfile) {
                throw new Error('用户信息不存在');
            }
            const matchedProducts = await this.productMatchingEngine.findBestMatches(userProfile, {}, this.getDefaultWeights());
            return {
                success: true,
                data: {
                    recommendations: matchedProducts,
                    userProfile
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }
    async compareProducts(request) {
        try {
            const products = await Promise.all(request.productIds.map(id => this.productService.findById(parseInt(id))));
            const comparison = products.map(product => {
                const matchScore = this.productMatchingEngine.calculateMatchScore(request.userProfile, product);
                const approvalProbability = this.productMatchingEngine.calculateApprovalProbability(request.userProfile, product);
                const costAnalysis = this.productMatchingEngine.calculateTotalCost(request.userProfile.requestedAmount, product, request.userProfile.preferredTerm);
                return {
                    product,
                    matchScore,
                    approvalProbability,
                    costAnalysis,
                    recommendations: this.generateProductRecommendations(request.userProfile, product)
                };
            });
            return {
                success: true,
                data: {
                    comparison,
                    bestMatch: comparison.reduce((best, current) => current.matchScore > best.matchScore ? current : best)
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }
    generateProductRecommendations(userProfile, product) {
        const recommendations = [];
        if (product.requirementsData) {
            if (userProfile.creditScore < product.minCreditScore) {
                recommendations.push('建议提高信用评分后再申请');
            }
            if (userProfile.monthlyIncome < product.minIncome) {
                recommendations.push('建议增加收入证明或考虑担保');
            }
        }
        if (product.interestRate > 0.15) {
            recommendations.push('利率较高，建议对比其他产品');
        }
        return recommendations;
    }
    getDefaultWeights() {
        return {
            amount: 0.25,
            term: 0.15,
            interestRate: 0.3,
            creditScore: 0.1,
            income: 0.1,
            purpose: 0.1
        };
    }
    generateMarketAnalysis(products) {
        return {
            categoryDistribution: this.calculateCategoryDistribution(products),
            interestRateRange: this.calculateInterestRateRange(products),
            averageApprovalRate: this.calculateAverageApprovalRate(products),
            popularFeatures: this.analyzePopularFeatures(products),
            marketTrends: this.analyzeMarketTrends(products)
        };
    }
    calculateCategoryDistribution(products) {
        const distribution = {};
        products.forEach(product => {
            const category = product.category || 'other';
            distribution[category] = (distribution[category] || 0) + 1;
        });
        return distribution;
    }
    calculateInterestRateRange(products) {
        const rates = products.map(p => p.interestRate);
        return {
            min: Math.min(...rates),
            max: Math.max(...rates),
            average: rates.reduce((sum, rate) => sum + rate, 0) / rates.length
        };
    }
    calculateAverageApprovalRate(products) {
        const approvalRates = products
            .filter(p => p.metadata?.approvalRate !== undefined)
            .map(p => p.metadata.approvalRate);
        return approvalRates.length > 0
            ? approvalRates.reduce((sum, rate) => sum + rate, 0) / approvalRates.length
            : 0;
    }
    analyzePopularFeatures(products) {
        const featureCount = {};
        products.forEach(product => {
            if (product.features) {
                product.features.forEach(feature => {
                    featureCount[feature.name] = (featureCount[feature.name] || 0) + 1;
                });
            }
        });
        return Object.entries(featureCount)
            .sort(([, countA], [, countB]) => Number(countB) - Number(countA))
            .slice(0, 5)
            .map(([name, count]) => ({
            name,
            count: Number(count),
            percentage: Number((Number(count) / products.length) * 100)
        }));
    }
    analyzeMarketTrends(products) {
        return {
            totalProducts: products.length,
            averageInterestRate: this.calculateInterestRateRange(products).average,
            averageApprovalRate: this.calculateAverageApprovalRate(products),
            mostPopularCategories: Object.entries(this.calculateCategoryDistribution(products))
                .sort(([, countA], [, countB]) => Number(countB) - Number(countA))
                .slice(0, 3)
                .map(([category, count]) => ({
                category,
                count: Number(count),
                percentage: Number((Number(count) / products.length) * 100)
            }))
        };
    }
    async getUserProfileFromDb(userId) {
        return {
            age: 30,
            monthlyIncome: 10000,
            creditScore: 700,
            employmentType: 'employed',
            employmentDuration: 5,
            debtToIncomeRatio: 0.3,
            requestedAmount: 100000,
            preferredTerm: 12,
            location: '北京',
            purpose: loan_purpose_enum_1.LoanPurpose.PERSONAL
        };
    }
};
__decorate([
    (0, common_1.Post)('match'),
    (0, swagger_1.ApiOperation)({ summary: '智能产品匹配' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '返回匹配的产品推荐列表' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ProductMatchingRequestDto]),
    __metadata("design:returntype", Promise)
], ProductMatchingController.prototype, "matchProducts", null);
__decorate([
    (0, common_1.Post)('detailed-analysis'),
    (0, swagger_1.ApiOperation)({ summary: '详细产品分析' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '返回用户画像与产品的详细匹配分析' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ProductMatchingRequestDto]),
    __metadata("design:returntype", Promise)
], ProductMatchingController.prototype, "getDetailedAnalysis", null);
__decorate([
    (0, common_1.Get)('quick-match/:userId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({ summary: '基于用户ID的快速匹配' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '返回用户的快速产品推荐' }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProductMatchingController.prototype, "quickMatch", null);
__decorate([
    (0, common_1.Post)('compare-products'),
    (0, swagger_1.ApiOperation)({ summary: '对比多个产品' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '返回产品对比结果' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProductMatchingController.prototype, "compareProducts", null);
ProductMatchingController = __decorate([
    (0, swagger_1.ApiTags)('智能产品匹配'),
    (0, common_1.Controller)('product-matching'),
    __metadata("design:paramtypes", [Object, product_service_1.ProductService])
], ProductMatchingController);
exports.ProductMatchingController = ProductMatchingController;
//# sourceMappingURL=product-matching.controller.js.map