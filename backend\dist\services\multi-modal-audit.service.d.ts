import { Repository } from 'typeorm';
import { AuditRecord } from '../entities/audit-record.entity';
import { User } from '../entities/user.entity';
import { OcrService } from './ocr.service';
import { FaceVerificationService } from './face-verification.service';
import { LoggerService } from './logger.service';
export interface MultiModalAuditResult {
    success: boolean;
    data?: any;
    message?: string;
    errors?: string[];
}
export interface AuditHistoryQuery {
    userId?: string;
    startDate?: Date;
    endDate?: Date;
    auditType?: string;
    page?: number;
    limit?: number;
}
export declare class MultiModalAuditService {
    private readonly auditRecordRepository;
    private readonly userRepository;
    private readonly ocrService;
    private readonly faceVerificationService;
    private readonly logger;
    constructor(auditRecordRepository: Repository<AuditRecord>, userRepository: Repository<User>, ocrService: OcrService, faceVerificationService: FaceVerificationService, logger: LoggerService);
    createAuditRecord(userId: string, auditType: string, data: any, result: MultiModalAuditResult): Promise<AuditRecord>;
    getAuditHistory(query: AuditHistoryQuery): Promise<{
        records: AuditRecord[];
        total: number;
        page: number;
        limit: number;
    }>;
    performComprehensiveAudit(userId: string, documents: any[]): Promise<MultiModalAuditResult>;
    getAuditStatistics(userId?: string): Promise<any>;
    getAuditResult(auditId: string, userId: string): Promise<any>;
}
