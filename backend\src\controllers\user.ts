import { Request, Response } from 'express'
import jwt from 'jsonwebtoken'
import { User } from '../models/user'

// 生成JWT令牌
const generateToken = (userId: string | unknown) => {
  if (typeof userId !== 'string') {
    throw new Error('Invalid user ID');
  }
  return jwt.sign({ id: userId }, process.env.JWT_SECRET || 'default_secret', {
    expiresIn: '24h'
  });
}

// 用户注册
export const register = async (req: Request, res: Response) => {
  try {
    const { username, password, email, phone } = req.body

    // 检查用户是否已存在
    const existingUser = await User.findOne({
      $or: [{ username }, { email }, { phone }]
    })

    if (existingUser) {
      return res.status(400).json({
        code: 1,
        message: '用户名、邮箱或手机号已存在'
      })
    }

    // 创建新用户
    const user = new User({
      username,
      password,
      email,
      phone
    })

    await user.save()

    res.status(201).json({
      code: 0,
      message: '注册成功'
    })
  } catch (error) {
    console.error('注册失败：', error)
    res.status(500).json({
      code: 1,
      message: '注册失败'
    })
  }
}

// 用户登录
export const login = async (req: Request, res: Response) => {
  try {
    const { username, password } = req.body

    // 查找用户
    const user = await User.findOne({ username })
    if (!user) {
      return res.status(401).json({
        code: 1,
        message: '用户名或密码错误'
      })
    }

    // 验证密码
    const isMatch = await user.comparePassword(password)
    if (!isMatch) {
      return res.status(401).json({
        code: 1,
        message: '用户名或密码错误'
      })
    }

    // 生成令牌
    const token = generateToken(user._id)

    res.json({
      code: 0,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          phone: user.phone,
          creditScore: user.creditScore,
          loanCount: user.loanCount
        }
      }
    })
  } catch (error) {
    console.error('登录失败：', error)
    res.status(500).json({
      code: 1,
      message: '登录失败'
    })
  }
}

// 获取用户信息
export const getUserInfo = async (req: Request, res: Response) => {
  try {
    const user = await User.findById(req.user?.userId)
    if (!user) {
      return res.status(404).json({
        code: 1,
        message: '用户不存在'
      })
    }

    res.json({
      code: 0,
      data: {
        id: user._id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        creditScore: user.creditScore,
        loanCount: user.loanCount
      }
    })
  } catch (error) {
    console.error('获取用户信息失败：', error)
    res.status(500).json({
      code: 1,
      message: '获取用户信息失败'
    })
  }
}

// 更新用户信息
export const updateUserInfo = async (req: Request, res: Response) => {
  try {
    const { email, phone, newPassword } = req.body
    const user = await User.findById(req.user?.userId)

    if (!user) {
      return res.status(404).json({
        code: 1,
        message: '用户不存在'
      })
    }

    // 更新基本信息
    if (email) user.email = email
    if (phone) user.phone = phone
    if (newPassword) user.password = newPassword

    await user.save()

    res.json({
      code: 0,
      message: '更新成功'
    })
  } catch (error) {
    console.error('更新用户信息失败：', error)
    res.status(500).json({
      code: 1,
      message: '更新用户信息失败'
    })
  }
} 