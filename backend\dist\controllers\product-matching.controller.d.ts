import { ProductMatchingEngine, UserProfile, ProductMatchWeights } from '../services/interfaces/ProductMatchingEngine';
import { ProductService } from '../services/product.service';
import { Product } from '../entities/product.entity';
import { LoanPurpose } from '../enums/loan-purpose.enum';
export declare enum ProductCategory {
    PERSONAL = "personal",
    BUSINESS = "business",
    MORTGAGE = "mortgage",
    AUTO = "auto",
    EDUCATION = "education"
}
export interface ProductRequirements {
    minAge: number;
    maxAge: number;
    minIncome: number;
    minCreditScore: number;
    maxDebtToIncomeRatio: number;
    employmentTypes: string[];
    workExperienceYears: number;
    requiredDocuments: string[];
}
export declare class UserProfileDto implements UserProfile {
    age: number;
    monthlyIncome: number;
    creditScore: number;
    employmentType: string;
    employmentDuration: number;
    debtToIncomeRatio: number;
    requestedAmount: number;
    preferredTerm: number;
    location: string;
    purpose: string;
}
export declare class ProductFilterDto {
    category?: ProductCategory;
    purpose?: LoanPurpose;
    minAmount?: number;
    maxInterestRate?: number;
}
export declare class ProductMatchWeightsDto implements ProductMatchWeights {
    amount: number;
    term: number;
    interestRate: number;
    creditScore: number;
    income: number;
    purpose: number;
}
export declare class ProductMatchingRequestDto {
    userProfile: UserProfileDto;
    filters?: ProductFilterDto;
    weights?: ProductMatchWeightsDto;
}
export declare class ProductMatchingController {
    private readonly productMatchingEngine;
    private readonly productService;
    constructor(productMatchingEngine: ProductMatchingEngine, productService: ProductService);
    matchProducts(request: ProductMatchingRequestDto): Promise<{
        success: boolean;
        data: {
            recommendations: Product[];
            totalMatched: number;
            userProfile: UserProfileDto;
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: any;
    }>;
    getDetailedAnalysis(request: ProductMatchingRequestDto): Promise<{
        success: boolean;
        data: {
            analysis: any[];
            userProfile: UserProfileDto;
            marketAnalysis: {
                categoryDistribution: Record<string, number>;
                interestRateRange: {
                    min: number;
                    max: number;
                    average: number;
                };
                averageApprovalRate: number;
                popularFeatures: {
                    name: string;
                    count: number;
                    percentage: number;
                }[];
                marketTrends: {
                    totalProducts: number;
                    averageInterestRate: number;
                    averageApprovalRate: number;
                    mostPopularCategories: {
                        category: string;
                        count: number;
                        percentage: number;
                    }[];
                };
            };
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: any;
    }>;
    quickMatch(userId: string): Promise<{
        success: boolean;
        data: {
            recommendations: Product[];
            userProfile: UserProfileDto;
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: any;
    }>;
    compareProducts(request: {
        userProfile: UserProfileDto;
        productIds: string[];
    }): Promise<{
        success: boolean;
        data: {
            comparison: {
                product: any;
                matchScore: number;
                approvalProbability: number;
                costAnalysis: {
                    totalAmount: number;
                    monthlyPayment: number;
                    totalInterest: number;
                    fees: Record<string, number>;
                };
                recommendations: string[];
            }[];
            bestMatch: {
                product: any;
                matchScore: number;
                approvalProbability: number;
                costAnalysis: {
                    totalAmount: number;
                    monthlyPayment: number;
                    totalInterest: number;
                    fees: Record<string, number>;
                };
                recommendations: string[];
            };
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: any;
    }>;
    private generateProductRecommendations;
    private getDefaultWeights;
    private generateMarketAnalysis;
    private calculateCategoryDistribution;
    private calculateInterestRateRange;
    private calculateAverageApprovalRate;
    private analyzePopularFeatures;
    private analyzeMarketTrends;
    private getUserProfileFromDb;
}
