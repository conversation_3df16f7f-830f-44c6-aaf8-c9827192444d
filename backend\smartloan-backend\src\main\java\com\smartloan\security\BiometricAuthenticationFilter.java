package com.smartloan.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

@Slf4j
@Component
@RequiredArgsConstructor
public class BiometricAuthenticationFilter extends OncePerRequestFilter {

    private final BiometricService biometricService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        
        String biometricToken = request.getHeader("X-Biometric-Token");
        
        if (biometricToken != null) {
            try {
                if (biometricService.validateBiometricToken(biometricToken)) {
                    // 生物识别验证通过，增强当前认证
                    var authentication = SecurityContextHolder.getContext().getAuthentication();
                    if (authentication != null) {
                        var enhancedAuth = new UsernamePasswordAuthenticationToken(
                            authentication.getPrincipal(),
                            authentication.getCredentials(),
                            authentication.getAuthorities()
                        );
                        SecurityContextHolder.getContext().setAuthentication(enhancedAuth);
                        log.debug("Enhanced authentication with biometric validation for user: {}", 
                                authentication.getName());
                    }
                }
            } catch (Exception e) {
                log.error("Biometric token validation failed: {}", e.getMessage());
            }
        }

        filterChain.doFilter(request, response);
    }
}
