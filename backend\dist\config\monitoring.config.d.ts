import { Histogram } from 'prom-client';
import { WinstonModuleOptions } from 'nest-winston';
import 'winston-daily-rotate-file';
import React from 'react';
export declare const prometheusConfig: {
    defaultMetrics: {
        enabled: boolean;
        config: {
            prefix: string;
            timeout: number;
            labels: {
                application: string;
            };
        };
    };
    customMetrics: {
        httpRequestDuration: Histogram<"route" | "method" | "status_code">;
        riskCalculationDuration: Histogram<"type">;
        alertProcessingDuration: Histogram<"level">;
    };
};
export declare const winstonConfig: WinstonModuleOptions;
export declare const alertConfig: {
    thresholds: {
        responseTime: number;
        riskCalculation: number;
        alertProcessing: number;
    };
    email: {
        enabled: boolean;
        from: string;
        to: string;
        smtp: {
            host: string;
            port: number;
            secure: boolean;
            auth: {
                user: string;
                pass: string;
            };
        };
    };
    slack: {
        enabled: boolean;
        webhookUrl: string;
    };
};
export declare const formConfig: {
    items: ({
        name: string;
        label: string;
        component: React.FunctionComponentElement<import("antd").InputProps & React.RefAttributes<import("antd").InputRef>>;
        required: boolean;
        rules?: undefined;
        span?: undefined;
    } | {
        name: string;
        label: string;
        component: React.FunctionComponentElement<import("antd").InputProps & React.RefAttributes<import("antd").InputRef>>;
        required: boolean;
        rules: {
            type: string;
            message: string;
        }[];
        span?: undefined;
    } | {
        name: string;
        label: string;
        component: React.FunctionComponentElement<import("antd").SelectProps<unknown, import("rc-select/lib/Select").DefaultOptionType | import("rc-select/lib/Select").BaseOptionType> & {
            children?: React.ReactNode;
        } & React.RefAttributes<import("antd").RefSelectProps>>;
        span: number;
        required?: undefined;
        rules?: undefined;
    })[];
};
