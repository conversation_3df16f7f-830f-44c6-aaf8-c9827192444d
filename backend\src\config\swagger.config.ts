import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';

export function setupSwagger(app: INestApplication) {
  const options = new DocumentBuilder()
    .setTitle('SmartLoan API')
    .setDescription('智能贷款申请系统API文档')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('认证')
    .addTag('用户')
    .addTag('贷款申请')
    .addTag('文件上传')
    .build();

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('api/docs', app, document);
} 