/**
 * 智能产品匹配服务
 * 基于沐曦MetaX GPU加速的向量相似度计算
 * 支持500+金融机构产品智能推荐
 */

package com.smartloan.service;

import com.smartloan.model.Product;
import com.smartloan.model.UserProfile;
import com.smartloan.model.LoanRequirement;
import com.smartloan.model.MatchResult;
import com.smartloan.annotation.GPUAccelerated;
import com.smartloan.repository.ProductRepository;
import com.smartloan.gpu.MetaXGPUService;

import org.springframework.stereotype.Service;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;
import java.math.BigDecimal;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ProductMatchingService {

    private final ProductRepository productRepository;
    private final MetaXGPUService metaXGPUService;
    private final UserProfileService userProfileService;
    private final RiskAssessmentService riskAssessmentService;

    /**
     * 智能产品匹配主入口
     */
    @Cacheable(value = "productMatches", key = "#userProfile.userId + '_' + #requirement.hashCode()")
    public List<MatchResult> matchProducts(UserProfile userProfile, LoanRequirement requirement) {
        log.info("🎯 开始智能产品匹配 | userId={} | amount={} | term={}", 
            userProfile.getUserId(), requirement.getAmount(), requirement.getTerm());
        
        try {
            // 1. 基础筛选
            List<Product> candidateProducts = performBasicFiltering(requirement);
            log.info("📋 基础筛选完成，候选产品数量: {}", candidateProducts.size());
            
            // 2. GPU加速向量匹配
            List<Product> vectorMatchedProducts = performVectorMatching(userProfile, candidateProducts);
            log.info("🎮 GPU向量匹配完成，匹配产品数量: {}", vectorMatchedProducts.size());
            
            // 3. 动态权重评分
            List<MatchResult> scoredResults = calculateMatchScores(userProfile, requirement, vectorMatchedProducts);
            log.info("📊 动态权重评分完成");
            
            // 4. 风险评估过滤
            List<MatchResult> riskFilteredResults = applyRiskFiltering(userProfile, scoredResults);
            log.info("🛡️ 风险评估过滤完成");
            
            // 5. 排序和截取TOP结果
            List<MatchResult> finalResults = scoredResults.stream()
                .sorted((a, b) -> Double.compare(b.getMatchScore(), a.getMatchScore()))
                .limit(10)
                .collect(Collectors.toList());
            
            log.info("✅ 智能产品匹配完成，返回{}个推荐产品", finalResults.size());
            return finalResults;
            
        } catch (Exception e) {
            log.error("❌ 产品匹配失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 基础筛选 - 根据金额、期限、产品类型等基础条件筛选
     */
    private List<Product> performBasicFiltering(LoanRequirement requirement) {
        return productRepository.findByBasicCriteria(
            requirement.getAmount(),
            requirement.getTerm(),
            requirement.getProductType(),
            requirement.getPurpose()
        );
    }

    /**
     * GPU加速向量匹配
     */
    @GPUAccelerated(cluster = "metax-matching-cluster")
    private List<Product> performVectorMatching(UserProfile userProfile, List<Product> candidates) {
        try {
            // 生成用户特征向量
            float[] userVector = generateUserVector(userProfile);
            log.debug("🧮 用户特征向量生成完成，维度: {}", userVector.length);
            
            // 批量计算相似度
            Map<String, Float> similarities = metaXGPUService.batchSimilarityCalculation(
                userVector, 
                candidates.stream()
                    .collect(Collectors.toMap(
                        Product::getId,
                        Product::getEmbedding
                    ))
            );
            
            // 筛选相似度阈值以上的产品
            return candidates.stream()
                .filter(product -> similarities.getOrDefault(product.getId(), 0.0f) > 0.7f)
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            log.error("GPU向量匹配失败: {}", e.getMessage(), e);
            return candidates; // 降级到非GPU匹配
        }
    }

    /**
     * 生成用户特征向量
     */
    private float[] generateUserVector(UserProfile userProfile) {
        // 768维特征向量
        float[] vector = new float[768];
        
        // 基础信息特征 (0-99)
        vector[0] = normalizeAge(userProfile.getAge());
        vector[1] = normalizeIncome(userProfile.getIncome());
        vector[2] = normalizeCreditScore(userProfile.getCreditScore());
        vector[3] = normalizeEmploymentYears(userProfile.getEmploymentYears());
        
        // 金融行为特征 (100-199)
        vector[100] = userProfile.getDebtToIncomeRatio();
        vector[101] = userProfile.getSavingsRatio();
        vector[102] = userProfile.getInvestmentExperience();
        
        // 风险偏好特征 (200-299)
        vector[200] = userProfile.getRiskTolerance();
        vector[201] = userProfile.getLiquidityPreference();
        
        // 产品偏好特征 (300-399)
        vector[300] = userProfile.getDigitalPreference();
        vector[301] = userProfile.getGreenFinancePreference();
        
        // 其余维度使用随机初始化或预训练嵌入
        Random random = new Random(userProfile.getUserId().hashCode());
        for (int i = 400; i < 768; i++) {
            vector[i] = random.nextGaussian() * 0.1f;
        }
        
        return vector;
    }

    /**
     * 计算匹配分数
     */
    private List<MatchResult> calculateMatchScores(UserProfile userProfile, LoanRequirement requirement, 
                                                  List<Product> products) {
        return products.stream()
            .map(product -> {
                double score = calculateIndividualScore(userProfile, requirement, product);
                String reasoning = generateAIReasoning(userProfile, product, score);
                
                return MatchResult.builder()
                    .product(product)
                    .matchScore(score)
                    .aiReasoning(reasoning)
                    .approvalProbability(estimateApprovalProbability(userProfile, product))
                    .build();
            })
            .collect(Collectors.toList());
    }

    /**
     * 计算单个产品匹配分数
     */
    private double calculateIndividualScore(UserProfile userProfile, LoanRequirement requirement, Product product) {
        double score = 0.0;
        
        // 利率匹配度 (权重: 30%)
        double rateScore = calculateRateScore(requirement, product);
        score += rateScore * 0.3;
        
        // 额度匹配度 (权重: 25%)
        double amountScore = calculateAmountScore(requirement, product);
        score += amountScore * 0.25;
        
        // 期限匹配度 (权重: 20%)
        double termScore = calculateTermScore(requirement, product);
        score += termScore * 0.2;
        
        // 用户画像匹配度 (权重: 15%)
        double profileScore = calculateProfileScore(userProfile, product);
        score += profileScore * 0.15;
        
        // 机构偏好度 (权重: 10%)
        double institutionScore = calculateInstitutionScore(userProfile, product);
        score += institutionScore * 0.1;
        
        return Math.min(score, 1.0);
    }

    /**
     * 生成AI推荐理由
     */
    private String generateAIReasoning(UserProfile userProfile, Product product, double score) {
        List<String> reasons = new ArrayList<>();
        
        if (score > 0.9) {
            reasons.add("该产品与您的需求高度匹配");
        }
        
        if (product.getInterestRateMin().compareTo(BigDecimal.valueOf(4.0)) < 0) {
            reasons.add("利率优惠，低于市场平均水平");
        }
        
        if (product.isDigitalCurrencySupport()) {
            reasons.add("支持数字人民币，享受便捷支付体验");
        }
        
        if (product.isEsgCertified()) {
            reasons.add("ESG认证产品，符合绿色金融理念");
        }
        
        if (userProfile.getCreditScore() > 750 && product.getTargetCreditScore() <= 750) {
            reasons.add("您的信用评分优秀，可享受优惠条件");
        }
        
        return reasons.isEmpty() ? "基于AI算法综合评估推荐" : String.join("；", reasons);
    }

    /**
     * 估算审批通过概率
     */
    private double estimateApprovalProbability(UserProfile userProfile, Product product) {
        double probability = 0.5; // 基础概率
        
        // 信用评分影响
        if (userProfile.getCreditScore() > product.getTargetCreditScore()) {
            probability += 0.3;
        }
        
        // 收入稳定性影响
        if (userProfile.getEmploymentYears() > 2) {
            probability += 0.1;
        }
        
        // 负债率影响
        if (userProfile.getDebtToIncomeRatio() < 0.3) {
            probability += 0.1;
        }
        
        return Math.min(probability, 0.95); // 最高95%
    }

    /**
     * 风险评估过滤
     */
    private List<MatchResult> applyRiskFiltering(UserProfile userProfile, List<MatchResult> results) {
        String userRiskLevel = riskAssessmentService.assessUserRiskLevel(userProfile);
        
        return results.stream()
            .filter(result -> isRiskCompatible(userRiskLevel, result.getProduct().getRiskLevel()))
            .collect(Collectors.toList());
    }

    /**
     * 检查风险兼容性
     */
    private boolean isRiskCompatible(String userRiskLevel, String productRiskLevel) {
        // 风险等级匹配逻辑
        Map<String, Integer> riskLevels = Map.of(
            "LOW", 1,
            "MEDIUM", 2,
            "HIGH", 3
        );
        
        int userLevel = riskLevels.getOrDefault(userRiskLevel, 2);
        int productLevel = riskLevels.getOrDefault(productRiskLevel, 2);
        
        return Math.abs(userLevel - productLevel) <= 1;
    }

    // 辅助计算方法
    private double calculateRateScore(LoanRequirement requirement, Product product) {
        // 利率评分逻辑
        return 0.8; // 简化实现
    }

    private double calculateAmountScore(LoanRequirement requirement, Product product) {
        // 额度评分逻辑
        return 0.9; // 简化实现
    }

    private double calculateTermScore(LoanRequirement requirement, Product product) {
        // 期限评分逻辑
        return 0.85; // 简化实现
    }

    private double calculateProfileScore(UserProfile userProfile, Product product) {
        // 画像评分逻辑
        return 0.75; // 简化实现
    }

    private double calculateInstitutionScore(UserProfile userProfile, Product product) {
        // 机构评分逻辑
        return 0.8; // 简化实现
    }

    // 归一化方法
    private float normalizeAge(int age) {
        return (age - 18.0f) / (65.0f - 18.0f);
    }

    private float normalizeIncome(BigDecimal income) {
        return income.floatValue() / 1000000.0f; // 假设最高收入100万
    }

    private float normalizeCreditScore(int score) {
        return (score - 300.0f) / (850.0f - 300.0f);
    }

    private float normalizeEmploymentYears(int years) {
        return Math.min(years / 10.0f, 1.0f);
    }
}
