import { Repository } from 'typeorm';
import { LoanApplication } from '../entities/loan-application.entity';
import { LoanReview } from '../entities/loan-review.entity';
import { User } from '../entities/user.entity';
export declare class LoanStatisticsService {
    private readonly loanApplicationRepository;
    private readonly loanReviewRepository;
    constructor(loanApplicationRepository: Repository<LoanApplication>, loanReviewRepository: Repository<LoanReview>);
    getApplicationStatistics(user: User): Promise<any>;
    getApplicationTrends(user: User, days?: number): Promise<any>;
    getRiskStatistics(user: User): Promise<any>;
    getReviewStatistics(user: User): Promise<any>;
}
