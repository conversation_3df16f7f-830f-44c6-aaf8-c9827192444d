import { Repository } from 'typeorm';
import { Contract } from '../entities/contract.entity';
import { SignContractDto } from '../dto/sign-contract.dto';
export declare class ContractService {
    private contractRepository;
    constructor(contractRepository: Repository<Contract>);
    getContract(id: string): Promise<{
        contract: {
            contractNumber: string;
            applicationNumber: string;
            loanType: string;
            loanAmount: number;
            loanTerm: number;
            interestRate: number;
            repaymentMethod: string;
            guaranteeMethod: string;
            disbursementDate: string;
            maturityDate: string;
            loanPurpose: string;
            repaymentDay: number;
            guarantor: string;
        };
        repaymentPlan: import("../entities/repayment-plan.entity").RepaymentPlan[];
    }>;
    generateContractFile(id: string): Promise<string>;
    signContract(id: string, signContractDto: SignContractDto): Promise<{
        message: string;
        contract: {
            id: string;
            status: string;
            signedAt: Date;
        };
    }>;
    private validateSignature;
}
