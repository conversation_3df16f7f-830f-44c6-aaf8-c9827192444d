import { ConfigService } from '@nestjs/config';
import { CacheService } from './cache.service';
import { MonitorService } from './monitor.service';
import { Repository } from 'typeorm';
import { Product } from '../entities/product.entity';
import { LoanApplication } from '../entities/loan-application.entity';
import { User } from '../entities/user.entity';
import { RedisService } from './redis.service';
export interface LoanProduct {
    id: string;
    name: string;
    type: string;
    minAmount: number;
    maxAmount: number;
    minTerm: number;
    maxTerm: number;
    baseInterestRate: number;
    description: string;
    features: string[];
    requirements: string[];
}
export declare class ProductRecommendationService {
    private readonly productRepository;
    private readonly loanApplicationRepository;
    private readonly userRepository;
    private readonly redisService;
    private readonly cacheService;
    private readonly monitorService;
    private readonly configService;
    private readonly logger;
    private readonly CACHE_TTL;
    private readonly products;
    constructor(productRepository: Repository<Product>, loanApplicationRepository: Repository<LoanApplication>, userRepository: Repository<User>, redisService: RedisService, cacheService: CacheService, monitorService: MonitorService, configService: ConfigService);
    getRecommendedProducts(userId: string, amount: number, term: number): Promise<Product[]>;
    getPopularProducts(): Promise<Product[]>;
    adjustInterestRate(productId: string, riskScore: number): Promise<number>;
    getProductDetails(productId: string): Promise<LoanProduct | null>;
    getAllProducts(): Promise<LoanProduct[]>;
    getProductsByType(type: string): Promise<LoanProduct[]>;
    recommendProductsByUserFeatures(userId: string, features: any): Promise<Product[]>;
    getSimilarProducts(productId: string): Promise<Product[]>;
}
