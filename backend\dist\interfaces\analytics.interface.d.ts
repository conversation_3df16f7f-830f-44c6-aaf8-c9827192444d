import { LoanApplication } from '../entities/loan-application.entity';
import { LoanStatus } from '../enums/loan-status.enum';
import { LoanType } from '../enums/loan-type.enum';
export interface LoanApplicationWithAmount extends LoanApplication {
    amount: number;
    type: LoanType;
    status: LoanStatus;
}
export interface DashboardQuery {
    startDate?: string;
    endDate?: string;
    type?: string;
}
export interface AnalyticsQuery extends DashboardQuery {
    groupBy?: string;
    filter?: Record<string, any>;
}
export interface ExportQuery extends AnalyticsQuery {
    format?: string;
    fields?: string[];
}
export interface DashboardData {
    total: number;
    applications: LoanApplicationWithAmount[];
    statistics: {
        totalAmount: number;
        averageAmount: number;
        approvalRate: number;
        averageScore: number;
    };
}
export interface AnalyticsData extends DashboardData {
    trends: {
        date: string;
        applications: number;
        amount: number;
    }[];
    distributions: {
        byType: Record<LoanType, number>;
        byStatus: Record<LoanStatus, number>;
    };
}
export interface ExportData {
    id: string;
    data: any;
    format: 'csv' | 'excel' | 'pdf';
    status: 'pending' | 'completed' | 'failed';
    createdAt: Date;
    completedAt?: Date;
    error?: string;
}
export interface TrendPrediction {
    date: string;
    predictedApplications: number;
    predictedAmount: number;
    confidence: number;
}
export interface Anomaly {
    id: string;
    type: 'unusual' | 'suspicious' | 'error';
    severity: 'low' | 'medium' | 'high';
    description: string;
    timestamp: Date;
    data: Record<string, any>;
}
export interface Recommendation {
    id: string;
    type: 'risk' | 'opportunity' | 'optimization';
    priority: 'low' | 'medium' | 'high';
    description: string;
    action: string;
    impact: {
        score: number;
        metrics: string[];
    };
}
