import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class GpuService {
  private readonly logger = new Logger(GpuService.name);
  private readonly gpuEnabled: boolean;
  private readonly cpuFallback: boolean;

  constructor(private configService: ConfigService) {
    this.gpuEnabled = this.configService.get('USE_GPU') === 'true';
    this.cpuFallback = this.configService.get('CPU_FALLBACK', 'true') === 'true';
  }

  async prepareProductTensor(products: any[]): Promise<any> {
    try {
      this.logger.debug(`准备产品张量，产品数量: ${products.length}`);
      
      if (this.gpuEnabled) {
        try {
          const response = await axios.post('http://localhost:3001/prepare-product-tensor', {
            products: products,
            use_gpu: true
          });
          return response.data;
        } catch (error: any) {
          this.logger.warn('GPU产品张量准备失败，回退到CPU处理', error.message);
          if (!this.cpuFallback) throw error;
        }
      }

      // CPU fallback implementation
      return this.prepareProductTensorCpu(products);
    } catch (error: any) {
      this.logger.error('产品张量准备失败:', error.message);
      throw new Error(`产品张量准备失败: ${error.message}`);
    }
  }

  async prepareCriteriaTensor(criteria: any): Promise<any> {
    try {
      this.logger.debug('准备条件张量');
      
      if (this.gpuEnabled) {
        try {
          const response = await axios.post('http://localhost:3001/prepare-criteria-tensor', {
            criteria: criteria,
            use_gpu: true
          });
          return response.data;
        } catch (error: any) {
          this.logger.warn('GPU条件张量准备失败，回退到CPU处理', error.message);
          if (!this.cpuFallback) throw error;
        }
      }

      // CPU fallback implementation
      return this.prepareCriteriaTensorCpu(criteria);
    } catch (error: any) {
      this.logger.error('条件张量准备失败:', error.message);
      throw new Error(`条件张量准备失败: ${error.message}`);
    }
  }

  async processOcrBatch(images: Buffer[]): Promise<any[]> {
    try {
      this.logger.debug(`批量处理OCR，图片数量: ${images.length}`);
      
      if (this.gpuEnabled && images.length > 1) {
        try {
          const response = await axios.post('http://localhost:3001/process-ocr-batch', {
            images: images.map(img => img.toString('base64')),
            use_gpu: true,
            batch_size: Math.min(images.length, 32)
          });
          return response.data.results;
        } catch (error: any) {
          this.logger.warn('GPU批量OCR处理失败，回退到CPU处理', error.message);
          if (!this.cpuFallback) throw error;
        }
      }

      // CPU fallback - process sequentially
      const results = [];
      for (const image of images) {
        const result = await this.processOcrSingle(image);
        results.push(result);
      }
      return results;
    } catch (error: any) {
      this.logger.error('批量OCR处理失败:', error.message);
      throw new Error(`批量OCR处理失败: ${error.message}`);
    }
  }

  async processRiskModelBatch(applications: any[]): Promise<any[]> {
    try {
      this.logger.debug(`批量风险评估，申请数量: ${applications.length}`);
      
      if (this.gpuEnabled && applications.length > 1) {
        try {
          const response = await axios.post('http://localhost:3001/process-risk-batch', {
            applications: applications,
            use_gpu: true,
            batch_size: Math.min(applications.length, 32)
          });
          return response.data.results;
        } catch (error: any) {
          this.logger.warn('GPU批量风险评估失败，回退到CPU处理', error.message);
          if (!this.cpuFallback) throw error;
        }
      }

      // CPU fallback - process sequentially
      const results = [];
      for (const application of applications) {
        const result = await this.processRiskModelSingle(application);
        results.push(result);
      }
      return results;
    } catch (error: any) {
      this.logger.error('批量风险评估失败:', error.message);
      throw new Error(`批量风险评估失败: ${error.message}`);
    }
  }

  async processMatchingBatch(requests: any[]): Promise<any[]> {
    try {
      this.logger.debug(`批量产品匹配，请求数量: ${requests.length}`);
      
      if (this.gpuEnabled && requests.length > 1) {
        try {
          const response = await axios.post('http://localhost:3001/process-matching-batch', {
            requests: requests,
            use_gpu: true,
            batch_size: Math.min(requests.length, 32)
          });
          return response.data.results;
        } catch (error: any) {
          this.logger.warn('GPU批量产品匹配失败，回退到CPU处理', error.message);
          if (!this.cpuFallback) throw error;
        }
      }

      // CPU fallback - process sequentially
      const results = [];
      for (const request of requests) {
        const result = await this.processMatchingSingle(request);
        results.push(result);
      }
      return results;
    } catch (error: any) {
      this.logger.error('批量产品匹配失败:', error.message);
      throw new Error(`批量产品匹配失败: ${error.message}`);
    }
  }

  // Document processing method
  async processDocument(imageData: string, options: { documentType: string }): Promise<{ data: any; confidence: number }> {
    try {
      this.logger.debug(`处理文档，类型: ${options.documentType}`);
      
      if (this.gpuEnabled) {
        try {
          const response = await axios.post('http://localhost:3001/process-document', {
            image: imageData,
            document_type: options.documentType,
            use_gpu: true
          });
          return {
            data: response.data.extracted_data,
            confidence: response.data.confidence || 0.85
          };
        } catch (error: any) {
          this.logger.warn('GPU文档处理失败，回退到CPU处理', error.message);
          if (!this.cpuFallback) throw error;
        }
      }

      // CPU fallback implementation
      return this.processDocumentCpu(imageData, options);
    } catch (error: any) {
      this.logger.error('文档处理失败:', error.message);
      throw new Error(`文档处理失败: ${error.message}`);
    }
  }

  private processDocumentCpu(imageData: string, options: { documentType: string }): { data: any; confidence: number } {
    this.logger.debug('使用CPU处理文档');
    
    // 根据文档类型返回模拟数据
    let mockData: any = {};
    
    switch (options.documentType) {
      case 'idCard':
        mockData = {
          name: '张三',
          idNumber: '110101199001011234',
          address: '北京市朝阳区',
          issueDate: '2020-01-01',
          expiryDate: '2030-01-01'
        };
        break;
      case 'incomeProof':
        mockData = {
          monthlyIncome: 10000,
          employer: '某某公司',
          position: '软件工程师',
          issueDate: new Date().toISOString().split('T')[0]
        };
        break;
      case 'bankStatement':
        mockData = {
          bankName: '中国银行',
          accountNumber: '1234****5678',
          balance: 50000,
          monthlyAverage: 30000,
          transactionCount: 45
        };
        break;
      default:
        mockData = {
          documentType: options.documentType,
          extractedText: '模拟提取的文档内容',
          fields: {}
        };
    }

    return {
      data: mockData,
      confidence: 0.85
    };
  }

  // CPU fallback implementations
  private prepareProductTensorCpu(products: any[]): any {
    this.logger.debug('使用CPU准备产品张量');
    return {
      tensor: products.map(p => [
        p.interestRate || 0,
        p.maxAmount || 0,
        p.minAmount || 0,
        p.maxTerm || 0,
        p.minTerm || 0,
        p.riskLevel || 0
      ]),
      shape: [products.length, 6],
      dtype: 'float32'
    };
  }

  private prepareCriteriaTensorCpu(criteria: any): any {
    this.logger.debug('使用CPU准备条件张量');
    return {
      tensor: [
        criteria.amount || 0,
        criteria.term || 0,
        criteria.creditScore || 0,
        criteria.income || 0,
        criteria.debtRatio || 0
      ],
      shape: [5],
      dtype: 'float32'
    };
  }

  private async processOcrSingle(image: Buffer): Promise<any> {
    // 模拟OCR处理
    return {
      confidence: 0.85,
      text: "模拟OCR文本结果",
      processed_by: 'cpu'
    };
  }

  private async processRiskModelSingle(application: any): Promise<any> {
    // 简单的CPU风险评估
    const score = Math.random() * 100;
    return {
      riskScore: score,
      decision: score < 30 ? 'APPROVED' : score < 70 ? 'MANUAL_REVIEW' : 'REJECTED',
      processed_by: 'cpu'
    };
  }

  private async processMatchingSingle(request: any): Promise<any> {
    // 简单的CPU产品匹配
    return {
      matchScore: Math.random(),
      recommended: true,
      processed_by: 'cpu'
    };
  }

  // 人脸识别加速方法
  async accelerateFaceRecognition(imageData: Buffer | any): Promise<any> {
    try {
      this.logger.debug('GPU加速人脸识别处理');
      
      if (this.gpuEnabled) {
        try {
          const response = await axios.post('http://localhost:3001/face-recognition', {
            image_data: imageData,
            use_gpu: true,
            return_features: true
          });
          return response.data;
        } catch (error: any) {
          this.logger.warn('GPU人脸识别失败，回退到CPU处理', error.message);
          if (!this.cpuFallback) throw error;
        }
      }

      // CPU fallback implementation
      return this.processFaceRecognitionCpu(imageData);
    } catch (error: any) {
      this.logger.error('人脸识别处理失败:', error.message);
      throw new Error(`人脸识别失败: ${error.message}`);
    }
  }

  // 产品匹配加速方法
  async accelerateProductMatching(userProfile: any, products: any[]): Promise<any> {
    try {
      this.logger.debug(`GPU加速产品匹配，产品数量: ${products.length}`);
      
      if (this.gpuEnabled) {
        try {
          const response = await axios.post('http://localhost:3001/product-matching', {
            user_profile: userProfile,
            products: products,
            use_gpu: true,
            algorithm: 'cosine_similarity'
          });
          return response.data;
        } catch (error: any) {
          this.logger.warn('GPU产品匹配失败，回退到CPU处理', error.message);
          if (!this.cpuFallback) throw error;
        }
      }

      // CPU fallback implementation
      return this.processProductMatchingCpu(userProfile, products);
    } catch (error: any) {
      this.logger.error('产品匹配处理失败:', error.message);
      throw new Error(`产品匹配失败: ${error.message}`);
    }
  }

  // 风险评估加速方法
  async accelerateRiskAssessment(riskData: any): Promise<any> {
    try {
      this.logger.debug('GPU加速风险评估处理');
      
      if (this.gpuEnabled) {
        try {
          const response = await axios.post('http://localhost:3001/risk-assessment', {
            risk_data: riskData,
            use_gpu: true,
            model_type: 'ensemble'
          });
          return response.data;
        } catch (error: any) {
          this.logger.warn('GPU风险评估失败，回退到CPU处理', error.message);
          if (!this.cpuFallback) throw error;
        }
      }

      // CPU fallback implementation
      return this.processRiskAssessmentCpu(riskData);
    } catch (error: any) {
      this.logger.error('风险评估处理失败:', error.message);
      throw new Error(`风险评估失败: ${error.message}`);
    }
  }

  private async processFaceRecognitionCpu(imageData: any): Promise<any> {
    this.logger.debug('使用CPU处理人脸识别');
    // 模拟人脸识别处理
    return {
      confidence: 0.82,
      face_detected: true,
      liveness_score: 0.78,
      match_score: 0.85,
      features: new Array(128).fill(0).map(() => Math.random()),
      processed_by: 'cpu'
    };
  }

  private async processProductMatchingCpu(userProfile: any, products: any[]): Promise<any> {
    this.logger.debug('使用CPU处理产品匹配');
    // 简单的CPU产品匹配算法
    const matches = products.map(product => ({
      product_id: product.id,
      match_score: Math.random() * 0.8 + 0.2, // 0.2-1.0之间的匹配分数
      reasons: ['利率匹配', '金额匹配', '期限匹配'],
      recommendation: Math.random() > 0.3
    }));

    return {
      matches: matches.sort((a, b) => b.match_score - a.match_score),
      total_matches: matches.length,
      processing_time: Math.random() * 100,
      processed_by: 'cpu'
    };
  }

  private async processRiskAssessmentCpu(riskData: any): Promise<any> {
    this.logger.debug('使用CPU处理风险评估');
    // 简单的CPU风险评估算法
    const baseScore = Math.random() * 100;
    const factors = [
      { name: '信用记录', score: Math.random() * 100, weight: 0.3 },
      { name: '收入水平', score: Math.random() * 100, weight: 0.25 },
      { name: '债务比率', score: Math.random() * 100, weight: 0.2 },
      { name: '就业状况', score: Math.random() * 100, weight: 0.15 },
      { name: '历史违约', score: Math.random() * 100, weight: 0.1 }
    ];

    const weightedScore = factors.reduce((sum, factor) => 
      sum + (factor.score * factor.weight), 0);

    return {
      risk_score: weightedScore,
      risk_level: weightedScore < 30 ? 'LOW' : weightedScore < 70 ? 'MEDIUM' : 'HIGH',
      decision: weightedScore < 30 ? 'APPROVED' : weightedScore < 70 ? 'MANUAL_REVIEW' : 'REJECTED',
      factors: factors,
      confidence: 0.75,
      processed_by: 'cpu'
    };
  }

  // Health check method
  async checkGpuAvailability(): Promise<boolean> {
    if (!this.gpuEnabled) return false;
    
    try {
      const response = await axios.get('http://localhost:3001/health', { timeout: 5000 });
      return response.status === 200 && response.data.gpu_available;
    } catch (error: any) {
      this.logger.warn('GPU服务健康检查失败', error.message);
      return false;
    }
  }
}