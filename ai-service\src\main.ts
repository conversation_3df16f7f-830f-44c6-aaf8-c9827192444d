import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // 启用全局验证管道
  app.useGlobalPipes(new ValidationPipe());
  
  // 配置跨域
  app.enableCors();
  
  // 配置Swagger文档
  const config = new DocumentBuilder()
    .setTitle('SmartLoan AI Service')
    .setDescription('智能金融服务平台AI服务API文档')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  // 启动服务器
  await app.listen(3001);
  console.log(`AI服务已启动: http://localhost:3001/api`);
}

bootstrap();
