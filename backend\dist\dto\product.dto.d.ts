import { ProductCategory } from '../entities/product.entity';
export declare class ProductFeatureDto {
    name: string;
    description: string;
    icon?: string;
}
export declare class ProductRequirementDto {
    name: string;
    description: string;
    value?: string;
}
export declare class ProductMetadataDto {
    popularity?: number;
    conversionRate?: number;
    averageRating?: number;
    reviewCount?: number;
    tags?: string[];
}
export declare class CreateProductDto {
    name: string;
    code: string;
    description: string;
    category: ProductCategory;
    minAmount: number;
    maxAmount: number;
    minTerm: number;
    maxTerm: number;
    interestRate: number;
    processingFee?: number;
    lateFee?: number;
    earlyRepaymentFee?: number;
    features: ProductFeatureDto[];
    benefits: string[];
    requirements: ProductRequirementDto[];
    metadata: ProductMetadataDto;
    isActive?: boolean;
    isFeatured?: boolean;
    sortOrder?: number;
}
export declare class UpdateProductDto {
    name?: string;
    code?: string;
    description?: string;
    category?: ProductCategory;
    minAmount?: number;
    maxAmount?: number;
    minTerm?: number;
    maxTerm?: number;
    interestRate?: number;
    processingFee?: number;
    lateFee?: number;
    earlyRepaymentFee?: number;
    features?: ProductFeatureDto[];
    benefits?: string[];
    requirements?: ProductRequirementDto[];
    metadata?: ProductMetadataDto;
    isActive?: boolean;
    isFeatured?: boolean;
    sortOrder?: number;
}
export declare class ProductFilterDto {
    search?: string;
    category?: ProductCategory;
    minAmount?: number;
    maxAmount?: number;
    minTerm?: number;
    maxTerm?: number;
    maxInterestRate?: number;
    isActive?: boolean;
    isFeatured?: boolean;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    page?: number;
    limit?: number;
}
