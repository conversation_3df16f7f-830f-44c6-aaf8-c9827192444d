export interface ProductFeature {
    id: string;
    type: string;
    value: string;
    name: string;
    description?: string;
}
export interface ProductRequirements {
    minAmount: number;
    maxAmount: number;
    minTerm: number;
    maxTerm: number;
    minIncome: number;
    minCreditScore: number;
    location: LocationRequirement;
}
export interface LocationRequirement {
    exact?: string[];
    city?: string[];
    province?: string[];
}
export interface LocationMatchRule {
    type: 'exact' | 'city' | 'province';
    locations: string[];
    score: number;
}
export interface UserLocation {
    province: string;
    city: string;
    district: string;
}
export interface Product {
    id: string;
    name: string;
    description?: string;
    interestRate: number;
    features: ProductFeature[];
    requirements: ProductRequirements;
    popularityScore?: number;
    status: 'active' | 'inactive';
    createdAt: Date;
    updatedAt: Date;
}
