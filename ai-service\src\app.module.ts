import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HealthController } from './health/health.controller';
// 注释掉缺失的导入，使用基础模块
// import { OcrService } from './ocr/ocr.service';
// import { RiskModelService } from './risk/risk-model.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
  ],
  controllers: [HealthController],
  providers: [], // 暂时移除缺失的服务
  exports: [],
})
export class AppModule {}
