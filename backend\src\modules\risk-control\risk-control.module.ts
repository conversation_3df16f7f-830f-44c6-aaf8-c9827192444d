import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RiskControlService } from './risk-control.service';
import { RiskRuleService } from '../../modules/risk-control/risk-rule.service';
import { RiskAssessment } from '../../entities/risk-assessment.entity';
import { RiskRule } from '../../entities/risk-rule.entity';
import { RedisService } from '../../services/redis.service';
import { GpuService } from '../../services/gpu.service';
import { MonitoringService } from '../../services/monitoring.service';
import { ConfigService } from '@nestjs/config';

@Module({
  imports: [
    TypeOrmModule.forFeature([RiskAssessment, RiskRule])
  ],
  providers: [
    RiskControlService,
    RiskRuleService,
    RedisService,
    GpuService,
    MonitoringService,
    ConfigService
  ],
  exports: [RiskControlService, RiskRuleService]
})
export class RiskControlModule {}
