"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cache = void 0;
const common_1 = require("@nestjs/common");
let Cache = class Cache {
    constructor() {
        this.cache = new Map();
        this.defaultTtl = 3600;
        this.maxSize = 1000;
    }
    set(key, value, ttl) {
        const expiresAt = Date.now() + (ttl || this.defaultTtl) * 1000;
        if (this.cache.size >= this.maxSize) {
            this.cleanup();
        }
        this.cache.set(key, { value, expires: expiresAt });
    }
    get(key) {
        const item = this.cache.get(key);
        if (!item) {
            return undefined;
        }
        if (Date.now() > item.expires) {
            this.cache.delete(key);
            return undefined;
        }
        return item.value;
    }
    has(key) {
        const item = this.cache.get(key);
        if (!item) {
            return false;
        }
        if (Date.now() > item.expires) {
            this.cache.delete(key);
            return false;
        }
        return true;
    }
    delete(key) {
        return this.cache.delete(key);
    }
    clear() {
        this.cache.clear();
    }
    size() {
        this.cleanup();
        return this.cache.size;
    }
    keys() {
        this.cleanup();
        return Array.from(this.cache.keys());
    }
    cleanup() {
        const now = Date.now();
        for (const [key, item] of this.cache.entries()) {
            if (now > item.expires) {
                this.cache.delete(key);
            }
        }
    }
    async getOrSet(key, factory, ttl) {
        const cached = this.get(key);
        if (cached !== undefined) {
            return cached;
        }
        const value = await factory();
        this.set(key, value, ttl);
        return value;
    }
    mget(keys) {
        return keys
            .map(key => ({ key, value: this.get(key) }))
            .filter(item => item.value !== undefined);
    }
    mset(items) {
        items.forEach(({ key, value, ttl }) => {
            this.set(key, value, ttl);
        });
    }
};
Cache = __decorate([
    (0, common_1.Injectable)()
], Cache);
exports.Cache = Cache;
//# sourceMappingURL=cache.js.map