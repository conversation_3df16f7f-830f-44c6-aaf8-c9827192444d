import { Repository } from 'typeorm';
import { FinancialProduct } from './entities/financial-product.entity';
export interface ProductMatchCriteria {
    amount: number;
    term_months: number;
    product_type: string;
    user_profile: {
        age: number;
        income: number;
        credit_score: number;
        employment_type: string;
    };
}
export interface ProductMatch {
    product: FinancialProduct;
    match_score: number;
    ai_reasoning: string;
    recommended_amount: number;
    recommended_rate: number;
}
export declare class ProductService {
    private productRepository;
    private readonly logger;
    constructor(productRepository: Repository<FinancialProduct>);
    findAll(): Promise<FinancialProduct[]>;
    findByType(type: string): Promise<FinancialProduct[]>;
    findById(id: number): Promise<FinancialProduct>;
    matchProducts(criteria: {
        amount: number;
        term_months: number;
        product_type?: string;
    }): Promise<FinancialProduct[]>;
    findMatchingProducts(criteria: ProductMatchCriteria): Promise<ProductMatch[]>;
    private calculateMatchScore;
    private calculateAmountScore;
    private calculateTermScore;
    private calculateQualificationScore;
    private calculateRecommendedRate;
    calculateMonthlyPayment(amount: number, term_months: number, annual_rate: number): Promise<{
        monthlyPayment: number;
        totalInterest: number;
        totalPayment: number;
    }>;
}
