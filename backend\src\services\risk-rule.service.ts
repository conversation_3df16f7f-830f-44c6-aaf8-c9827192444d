import { Injectable, Logger } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { RiskRule } from '../entities/risk-rule.entity';
import { RedisService } from './redis.service';
import { GpuService } from './gpu.service';
import { MonitoringService } from './monitoring.service';

@Injectable()
export class RiskRuleService {
  private readonly logger = new Logger(RiskRuleService.name);
  private readonly CACHE_TTL = 3600; // 1小时
  private readonly CACHE_KEY_PREFIX = 'risk:rules:';
  
  constructor(
    @InjectRepository(RiskRule)
    private readonly riskRuleRepository: Repository<RiskRule>,
    private readonly redisService: RedisService,
    private readonly gpuService: GpuService,
    private readonly monitoringService: MonitoringService
  ) {}

  // 获取活跃的风控规则
  async getActiveRules(): Promise<RiskRule[]> {
    const cacheKey = `${this.CACHE_KEY_PREFIX}active`;
    try {
      // 尝试从缓存获取
      const cached = await this.redisService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // 从数据库获取
      const rules = await this.riskRuleRepository.find({
        where: { isActive: true },
        order: { priority: 'DESC' }
      });

      // 缓存结果
      await this.redisService.set(cacheKey, JSON.stringify(rules), this.CACHE_TTL);

      return rules;
    } catch (error) {
      this.logger.error('获取风控规则失败', error);
      this.monitoringService.recordError('get_risk_rules', error);
      throw error;
    }
  }

  // 并行执行风控规则
  async executeRules(data: any, rules: RiskRule[]): Promise<any[]> {
    try {
      // 使用GPU并行处理
      const results = await this.gpuService.processRiskModelBatch(
        rules.map(rule => ({
          ruleId: rule.id,
          data,
          conditions: rule.conditions
        }))
      );

      // 记录性能指标
      this.monitoringService.recordMetric('rules_execution_count', rules.length);

      return results;
    } catch (error) {
      this.logger.error('执行风控规则失败', error);
      // 降级到串行CPU处理
      return this.executeRulesSerially(data, rules);
    }
  }

  // 串行执行规则(CPU降级方案)
  private async executeRulesSerially(data: any, rules: RiskRule[]): Promise<any[]> {
    const results = [];
    for (const rule of rules) {
      try {
        const result = await this.evaluateRule(data, rule);
        results.push(result);
      } catch (error) {
        this.logger.error(`规则执行失败: ${rule.id}`, error);
        results.push({
          ruleId: rule.id,
          executed: false,
          error: error.message
        });
      }
    }
    return results;
  }

  // 评估单个规则
  private async evaluateRule(data: any, rule: RiskRule): Promise<any> {
    const startTime = Date.now();
    try {
      // 解析和评估规则条件
      const conditions = this.parseConditions(rule.conditions);
      const result = this.evaluateConditions(data, conditions);

      // 记录执行时间
      this.monitoringService.recordMetric(
        'rule_execution_time',
        Date.now() - startTime
      );

      return {
        ruleId: rule.id,
        executed: true,
        triggered: result.triggered,
        score: result.score,
        factors: result.factors
      };
    } catch (error) {
      throw new Error(`规则评估失败: ${error.message}`);
    }
  }

  // 解析规则条件
  private parseConditions(conditions: any): any[] {
    // 解析规则条件逻辑
    return conditions.map((condition: any) => ({
      field: condition.field,
      operator: condition.operator,
      value: condition.value
    }));
  }

  // 评估条件
  private evaluateConditions(data: any, conditions: any[]): any {
    let triggered = false;
    let score = 0;
    const factors = [];

    conditions.forEach(condition => {
      const fieldValue = this.getFieldValue(data, condition.field);
      if (this.compareValues(fieldValue, condition.operator, condition.value)) {
        triggered = true;
        score += condition.weight || 1;
        factors.push({
          field: condition.field,
          actual: fieldValue,
          expected: condition.value,
          impact: condition.weight || 1
        });
      }
    });

    return { triggered, score, factors };
  }

  // 获取字段值
  private getFieldValue(data: any, field: string): any {
    return field.split('.').reduce((obj, key) => obj?.[key], data);
  }

  // 比较值
  private compareValues(actual: any, operator: string, expected: any): boolean {
    switch (operator) {
      case 'eq':
        return actual === expected;
      case 'neq':
        return actual !== expected;
      case 'gt':
        return actual > expected;
      case 'gte':
        return actual >= expected;
      case 'lt':
        return actual < expected;
      case 'lte':
        return actual <= expected;
      case 'in':
        return Array.isArray(expected) && expected.includes(actual);
      case 'nin':
        return Array.isArray(expected) && !expected.includes(actual);
      case 'contains':
        return typeof actual === 'string' && actual.includes(expected);
      case 'startsWith':
        return typeof actual === 'string' && actual.startsWith(expected);
      case 'endsWith':
        return typeof actual === 'string' && actual.endsWith(expected);
      default:
        throw new Error(`不支持的操作符: ${operator}`);
    }
  }

  // 更新规则
  async updateRule(ruleId: number, updates: Partial<RiskRule>): Promise<RiskRule> {
    try {
      await this.riskRuleRepository.update(ruleId, updates);
      
      // 更新后清除缓存
      await this.redisService.del(`${this.CACHE_KEY_PREFIX}active`);
      
      return this.riskRuleRepository.findOne({ where: { id: ruleId } });
    } catch (error) {
      this.logger.error(`更新规则失败: ${ruleId}`, error);
      throw error;
    }
  }

  // 创建新规则
  async createRule(rule: Partial<RiskRule>): Promise<RiskRule> {
    try {
      const newRule = await this.riskRuleRepository.save(rule);
      
      // 清除缓存
      await this.redisService.del(`${this.CACHE_KEY_PREFIX}active`);
      
      return newRule;
    } catch (error) {
      this.logger.error('创建规则失败', error);
      throw error;
    }
  }

  // 删除规则
  async deleteRule(ruleId: number): Promise<void> {
    try {
      await this.riskRuleRepository.delete(ruleId);
      
      // 清除缓存
      await this.redisService.del(`${this.CACHE_KEY_PREFIX}active`);
    } catch (error) {
      this.logger.error(`删除规则失败: ${ruleId}`, error);
      throw error;
    }
  }
}
