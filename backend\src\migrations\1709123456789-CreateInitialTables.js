"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateInitialTables1709123456789 = void 0;
var CreateInitialTables1709123456789 = /** @class */ (function () {
    function CreateInitialTables1709123456789() {
        this.name = 'CreateInitialTables1709123456789';
    }
    CreateInitialTables1709123456789.prototype.up = function (queryRunner) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: 
                    // 创建用户表
                    return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"user\" (\n        \"id\" uuid NOT NULL DEFAULT uuid_generate_v4(),\n        \"username\" character varying NOT NULL,\n        \"email\" character varying NOT NULL,\n        \"password\" character varying NOT NULL,\n        \"role\" character varying NOT NULL DEFAULT 'user',\n        \"isActive\" boolean NOT NULL DEFAULT true,\n        \"lastLoginAt\" TIMESTAMP,\n        \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(),\n        \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(),\n        CONSTRAINT \"UQ_78a916df40e02a9deb1c4b75edb\" UNIQUE (\"username\"),\n        CONSTRAINT \"UQ_e12875dfb3b1d92d7d7c5377e22\" UNIQUE (\"email\"),\n        CONSTRAINT \"PK_cace4a159ff9f2512dd42373760\" PRIMARY KEY (\"id\")\n      )\n    ")];
                    case 1:
                        // 创建用户表
                        _a.sent();
                        // 创建贷款申请表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"loan_application\" (\n        \"id\" uuid NOT NULL DEFAULT uuid_generate_v4(),\n        \"userId\" uuid NOT NULL,\n        \"loanType\" character varying NOT NULL,\n        \"loanAmount\" numeric NOT NULL,\n        \"loanTerm\" integer NOT NULL,\n        \"purpose\" character varying NOT NULL,\n        \"status\" character varying NOT NULL DEFAULT 'pending',\n        \"riskScore\" integer,\n        \"rejectionReason\" character varying,\n        \"approvedAt\" TIMESTAMP,\n        \"rejectedAt\" TIMESTAMP,\n        \"cancelledAt\" TIMESTAMP,\n        \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(),\n        \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(),\n        CONSTRAINT \"PK_loan_application\" PRIMARY KEY (\"id\"),\n        CONSTRAINT \"FK_loan_application_user\" FOREIGN KEY (\"userId\") REFERENCES \"user\"(\"id\") ON DELETE CASCADE\n      )\n    ")];
                    case 2:
                        // 创建贷款申请表
                        _a.sent();
                        // 创建贷款文档表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"loan_document\" (\n        \"id\" uuid NOT NULL DEFAULT uuid_generate_v4(),\n        \"loanApplicationId\" uuid NOT NULL,\n        \"type\" character varying NOT NULL,\n        \"fileName\" character varying NOT NULL,\n        \"filePath\" character varying NOT NULL,\n        \"fileSize\" integer NOT NULL,\n        \"mimeType\" character varying NOT NULL,\n        \"isVerified\" boolean NOT NULL DEFAULT false,\n        \"verificationNotes\" character varying,\n        \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(),\n        \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(),\n        CONSTRAINT \"PK_loan_document\" PRIMARY KEY (\"id\"),\n        CONSTRAINT \"FK_loan_document_loan_application\" FOREIGN KEY (\"loanApplicationId\") REFERENCES \"loan_application\"(\"id\") ON DELETE CASCADE\n      )\n    ")];
                    case 3:
                        // 创建贷款文档表
                        _a.sent();
                        // 创建审计日志表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"audit_log\" (\n        \"id\" uuid NOT NULL DEFAULT uuid_generate_v4(),\n        \"userId\" uuid,\n        \"action\" character varying NOT NULL,\n        \"entityType\" character varying NOT NULL,\n        \"entityId\" uuid,\n        \"details\" jsonb,\n        \"ipAddress\" character varying,\n        \"userAgent\" character varying,\n        \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(),\n        CONSTRAINT \"PK_audit_log\" PRIMARY KEY (\"id\"),\n        CONSTRAINT \"FK_audit_log_user\" FOREIGN KEY (\"userId\") REFERENCES \"user\"(\"id\") ON DELETE SET NULL\n      )\n    ")];
                    case 4:
                        // 创建审计日志表
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    CreateInitialTables1709123456789.prototype.down = function (queryRunner) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, queryRunner.query("DROP TABLE \"audit_log\"")];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"loan_document\"")];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"loan_application\"")];
                    case 3:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"user\"")];
                    case 4:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    return CreateInitialTables1709123456789;
}());
exports.CreateInitialTables1709123456789 = CreateInitialTables1709123456789;
