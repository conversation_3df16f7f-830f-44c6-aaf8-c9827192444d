import { Repository } from 'typeorm';
import { AuditLog } from '../entities/audit-log.entity';
import { MonitorService } from './monitor.service';
export declare class AuditService {
    private auditLogRepository;
    private readonly monitorService;
    private readonly logger;
    constructor(auditLogRepository: Repository<AuditLog>, monitorService: MonitorService);
    log(operation: string, details: any, options?: {
        userId?: string;
        ipAddress?: string;
        userAgent?: string;
        module?: string;
        action?: string;
        resource?: string;
    }): Promise<AuditLog>;
    logError(operation: string, error: Error, options?: {
        userId?: string;
        ipAddress?: string;
        userAgent?: string;
        module?: string;
        action?: string;
        resource?: string;
    }): Promise<AuditLog>;
    getLogs(options?: {
        startDate?: Date;
        endDate?: Date;
        userId?: string;
        operation?: string;
        module?: string;
        status?: string;
        skip?: number;
        take?: number;
    }): Promise<{
        logs: AuditLog[];
        total: number;
    }>;
    getLogById(id: string): Promise<AuditLog>;
    getLogsByUserId(userId: string, options?: {
        startDate?: Date;
        endDate?: Date;
        skip?: number;
        take?: number;
    }): Promise<{
        logs: AuditLog[];
        total: number;
    }>;
    getLogsByOperation(operation: string, options?: {
        startDate?: Date;
        endDate?: Date;
        skip?: number;
        take?: number;
    }): Promise<{
        logs: AuditLog[];
        total: number;
    }>;
    getLogsByModule(module: string, options?: {
        startDate?: Date;
        endDate?: Date;
        skip?: number;
        take?: number;
    }): Promise<{
        logs: AuditLog[];
        total: number;
    }>;
    getLogsByStatus(status: string, options?: {
        startDate?: Date;
        endDate?: Date;
        skip?: number;
        take?: number;
    }): Promise<{
        logs: AuditLog[];
        total: number;
    }>;
}
