import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards, Request, UseInterceptors, UploadedFile, BadRequestException, ParseFilePipe, MaxFileSizeValidator, FileTypeValidator, Query } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { LoanApplicationService } from '../services/loan-application.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RolesGuard } from '../guards/roles.guard';
import { Roles } from '../decorators/roles.decorator';
import { User } from '../decorators/user.decorator';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { DocumentType } from '../entities/loan-document.entity';
import { CreateLoanApplicationDto } from '../dto/create-loan-application.dto';
import { Role } from '../enums/role.enum';
import { User as UserEntity } from '../entities/user.entity';
import { LoanApplication } from '../entities/loan-application.entity';
import { LoanStatus } from '../enums/loan-status.enum';
import { LoanType } from '../enums/loan-type.enum';
import { LoanApplicationDto } from '../dto/loan-application.dto';

@ApiTags('贷款申请')
@Controller('loan-applications')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class LoanApplicationController {
  constructor(private readonly loanApplicationService: LoanApplicationService) {}

  @Post()
  @Roles(Role.USER)
  @ApiOperation({ summary: '创建贷款申请' })
  @ApiResponse({ status: 201, description: '贷款申请创建成功' })
  async create(@Request() req, @Body() createDto: CreateLoanApplicationDto): Promise<LoanApplication> {
    return this.loanApplicationService.create(createDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: '获取用户的贷款申请列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async findAll(@Request() req): Promise<LoanApplicationDto[]> {
    const applications = await this.loanApplicationService.findAllByUser(req.user.id);
    // DTO 转换
    return applications.map(app => this.loanApplicationService['mapToDto'](app));
  }

  @Get(':id')
  @ApiOperation({ summary: '获取贷款申请详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async findOne(@Request() req, @Param('id') id: string): Promise<LoanApplication> {
    return this.loanApplicationService.getApplication(id, req.user.id);
  }

  @Post(':id/update')
  @Roles(Role.USER, Role.ADMIN)
  @ApiOperation({ summary: '更新贷款申请' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateApplication(
    @Param('id') id: string,
    @Body() updateData: any,
    @User() user: UserEntity
  ): Promise<LoanApplication> {
    return this.loanApplicationService.updateApplication(id, user, updateData);
  }

  @Post(':id/cancel')
  @Roles(Role.USER, Role.ADMIN)
  @ApiOperation({ summary: '取消贷款申请' })
  @ApiResponse({ status: 200, description: '取消成功' })
  async cancelApplication(
    @Param('id') id: string,
    @User() user: UserEntity
  ): Promise<LoanApplication> {
    return this.loanApplicationService.cancelApplication(id, user);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: '获取用户的贷款申请列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUserApplications(
    @Param('userId') userId: string,
    @User() user: UserEntity
  ): Promise<LoanApplicationDto[]> {
    const applications = await this.loanApplicationService.getUserApplications(userId, user);
    return applications.map(app => this.loanApplicationService['mapToDto'](app));
  }

  @Get('statistics')
  @Roles(Role.ADMIN, Role.RISK_ANALYST)
  @ApiOperation({ summary: '获取贷款申请统计数据' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getApplicationStatistics(
    @User() user: UserEntity
  ): Promise<any> {
    return this.loanApplicationService.getApplicationStatistics(user);
  }

  @Get('trends')
  @Roles(Role.ADMIN, Role.RISK_ANALYST)
  @ApiOperation({ summary: '获取贷款申请趋势数据' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getApplicationTrends(
    @User() user: UserEntity
  ): Promise<any> {
    return this.loanApplicationService.getApplicationTrends(user);
  }

  @Post(':id/approve')
  @Roles(Role.ADMIN, Role.LOAN_OFFICER)
  @ApiOperation({ summary: '审批贷款申请' })
  @ApiResponse({ status: 200, description: '审批成功' })
  async approveApplication(
    @Param('id') id: string,
    @User() user: UserEntity
  ): Promise<LoanApplication> {
    return this.loanApplicationService.approveApplication(id, user);
  }

  @Post(':id/reject')
  @Roles(Role.ADMIN, Role.LOAN_OFFICER)
  @ApiOperation({ summary: '拒绝贷款申请' })
  @ApiResponse({ status: 200, description: '拒绝成功' })
  async rejectApplication(
    @Param('id') id: string,
    @Body('reason') reason: string,
    @User() user: UserEntity
  ): Promise<LoanApplication> {
    return this.loanApplicationService.rejectApplication(id, reason, user);
  }

  @Post(':id/documents')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        type: {
          type: 'string',
          enum: Object.values(DocumentType),
        },
      },
    },
  })
  @ApiOperation({ summary: '上传贷款文档' })
  @ApiResponse({ status: 201, description: '上传成功' })
  async uploadDocument(
    @Param('id') applicationId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body('type') type: DocumentType,
    @User() user: UserEntity
  ) {
    return this.loanApplicationService.uploadDocument(applicationId, file, type, user);
  }

  @Post(':id/documents/:documentId/verify')
  @Roles(Role.ADMIN, Role.LOAN_OFFICER)
  @ApiOperation({ summary: '验证贷款文档' })
  @ApiResponse({ status: 200, description: '验证成功' })
  async verifyDocument(
    @Param('id') applicationId: string,
    @Param('documentId') documentId: string,
    @Body('isVerified') isVerified: boolean,
    @Body('notes') notes: string,
    @User() user: UserEntity
  ) {
    return this.loanApplicationService.verifyDocument(
      applicationId,
      documentId,
      user,
      isVerified,
      notes
    );
  }

  @Put(':id/status')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN, Role.LOAN_OFFICER)
  async updateStatus(
    @Param('id') id: string,
    @Body('status') status: LoanStatus,
    @Request() req
  ): Promise<LoanApplication> {
    return this.loanApplicationService.updateStatus(id, status);
  }
}