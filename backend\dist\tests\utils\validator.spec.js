"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const validator_1 = require("../../utils/validator");
describe('Validator', () => {
    let validator;
    beforeEach(() => {
        validator = new validator_1.Validator();
    });
    describe('validateInput', () => {
        it('should validate valid input', async () => {
            const input = {
                amount: 10000,
                term: 12,
                purpose: 'business',
                income: 50000
            };
            const result = await validator.validateInput(input);
            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });
        it('should validate invalid input', async () => {
            const input = {
                amount: -1000,
                term: 0,
                purpose: '',
                income: -5000
            };
            const result = await validator.validateInput(input);
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(4);
            expect(result.errors[0]).toHaveProperty('property');
            expect(result.errors[0]).toHaveProperty('constraints');
        });
        it('should handle empty input', async () => {
            const input = {};
            const result = await validator.validateInput(input);
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(4);
        });
    });
    describe('validateAmount', () => {
        it('should validate valid amount', () => {
            const result = validator.validateAmount(10000);
            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });
        it('should validate invalid amount', () => {
            const result = validator.validateAmount(-1000);
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0]).toHaveProperty('property', 'amount');
            expect(result.errors[0]).toHaveProperty('constraints');
        });
        it('should validate amount range', () => {
            const result = validator.validateAmount(1000000);
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0]).toHaveProperty('property', 'amount');
            expect(result.errors[0]).toHaveProperty('constraints');
        });
    });
    describe('validateTerm', () => {
        it('should validate valid term', () => {
            const result = validator.validateTerm(12);
            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });
        it('should validate invalid term', () => {
            const result = validator.validateTerm(0);
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0]).toHaveProperty('property', 'term');
            expect(result.errors[0]).toHaveProperty('constraints');
        });
        it('should validate term range', () => {
            const result = validator.validateTerm(61);
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0]).toHaveProperty('property', 'term');
            expect(result.errors[0]).toHaveProperty('constraints');
        });
    });
    describe('validatePurpose', () => {
        it('should validate valid purpose', () => {
            const result = validator.validatePurpose('business');
            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });
        it('should validate invalid purpose', () => {
            const result = validator.validatePurpose('');
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0]).toHaveProperty('property', 'purpose');
            expect(result.errors[0]).toHaveProperty('constraints');
        });
        it('should validate purpose enum', () => {
            const result = validator.validatePurpose('invalid');
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0]).toHaveProperty('property', 'purpose');
            expect(result.errors[0]).toHaveProperty('constraints');
        });
    });
    describe('validateIncome', () => {
        it('should validate valid income', () => {
            const result = validator.validateIncome(50000);
            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });
        it('should validate invalid income', () => {
            const result = validator.validateIncome(-5000);
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0]).toHaveProperty('property', 'income');
            expect(result.errors[0]).toHaveProperty('constraints');
        });
        it('should validate income range', () => {
            const result = validator.validateIncome(1000000);
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0]).toHaveProperty('property', 'income');
            expect(result.errors[0]).toHaveProperty('constraints');
        });
    });
    describe('formatValidationErrors', () => {
        it('should format validation errors', () => {
            const errors = [
                {
                    property: 'amount',
                    constraints: {
                        min: 'Amount must be greater than 0',
                        max: 'Amount must be less than 1000000'
                    }
                },
                {
                    property: 'term',
                    constraints: {
                        min: 'Term must be greater than 0',
                        max: 'Term must be less than 60'
                    }
                }
            ];
            const result = validator.formatValidationErrors(errors);
            expect(result).toEqual([
                {
                    field: 'amount',
                    messages: [
                        'Amount must be greater than 0',
                        'Amount must be less than 1000000'
                    ]
                },
                {
                    field: 'term',
                    messages: [
                        'Term must be greater than 0',
                        'Term must be less than 60'
                    ]
                }
            ]);
        });
        it('should handle empty errors', () => {
            const result = validator.formatValidationErrors([]);
            expect(result).toEqual([]);
        });
    });
});
//# sourceMappingURL=validator.spec.js.map