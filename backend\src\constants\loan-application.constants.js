"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LOAN_APPLICATION_CONSTANTS = void 0;
exports.LOAN_APPLICATION_CONSTANTS = {
    // 贷款金额限制
    MIN_LOAN_AMOUNT: 1000,
    MAX_LOAN_AMOUNT: 1000000,
    // 贷款期限限制
    MIN_LOAN_TERM: 3,
    MAX_LOAN_TERM: 360,
    // 利率限制
    MIN_INTEREST_RATE: 0.01,
    MAX_INTEREST_RATE: 0.24,
    // 月供限制
    MIN_MONTHLY_PAYMENT: 100,
    MAX_MONTHLY_PAYMENT: 50000,
    // 风险分数限制
    MIN_RISK_SCORE: 300,
    MAX_RISK_SCORE: 850,
    RISK_LEVELS: {
        LOW: 700,
        MEDIUM: 600,
        HIGH: 500
    },
    // 收入限制
    MIN_MONTHLY_INCOME: 2000,
    MAX_MONTHLY_INCOME: 1000000,
    // 就业时长限制
    MIN_EMPLOYMENT_DURATION: 0,
    MAX_EMPLOYMENT_DURATION: 600,
    // 负债收入比限制
    MIN_DEBT_TO_INCOME_RATIO: 0,
    MAX_DEBT_TO_INCOME_RATIO: 1,
    MAX_RECOMMENDED_DEBT_TO_INCOME_RATIO: 0.43,
    // 缓存时间
    CACHE_TTL: {
        APPLICATION: 3600,
        USER_APPLICATIONS: 3600,
        STATISTICS: 3600,
        TRENDS: 3600
    },
    // 文件上传限制
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_FILE_TYPES: [
        'application/pdf',
        'image/jpeg',
        'image/png'
    ],
    // 状态转换规则
    STATUS_TRANSITIONS: {
        pending: ['reviewing', 'cancelled'],
        reviewing: ['approved', 'rejected', 'cancelled'],
        approved: ['cancelled'],
        rejected: ['cancelled'],
        cancelled: []
    },
    // 决策规则
    DECISION_RULES: {
        FRAUD_THRESHOLD: 0.5,
        RISK_SCORE_THRESHOLD: 600,
        STRESS_TEST_THRESHOLD: 0.2,
        MAX_DEFAULT_PROBABILITY: 0.1,
        MIN_RECOVERY_RATE: 0.5,
        MAX_EXPECTED_LOSS_RATIO: 0.1
    },
    // 产品推荐权重
    RECOMMENDATION_WEIGHTS: {
        creditScore: 0.3,
        income: 0.2,
        employment: 0.15,
        age: 0.1,
        education: 0.1,
        maritalStatus: 0.05,
        houseStatus: 0.05,
        carStatus: 0.05
    },
    // 错误消息
    ERROR_MESSAGES: {
        INVALID_AMOUNT: '贷款金额必须在1000-1000000之间',
        INVALID_TERM: '贷款期限必须在3-360个月之间',
        INVALID_INCOME: '月收入必须在2000-1000000之间',
        INVALID_CREDIT_SCORE: '信用分数必须在300-850之间',
        INVALID_DEBT_RATIO: '负债收入比必须在0-1之间',
        INVALID_FILE: '文件大小不能超过10MB，且必须是PDF或图片格式',
        INVALID_STATUS: '无效的状态转换',
        APPLICATION_NOT_FOUND: '申请不存在',
        UNAUTHORIZED: '未授权访问',
        FORBIDDEN: '权限不足'
    },
    // 成功消息
    SUCCESS_MESSAGES: {
        CREATED: '贷款申请创建成功',
        UPDATED: '贷款申请更新成功',
        CANCELLED: '贷款申请取消成功',
        APPROVED: '贷款申请已批准',
        REJECTED: '贷款申请已拒绝'
    }
};
