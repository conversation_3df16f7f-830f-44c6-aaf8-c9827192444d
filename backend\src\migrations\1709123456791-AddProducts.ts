import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddProducts1709123456791 implements MigrationInterface {
  name = 'AddProducts1709123456791';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建产品表
    await queryRunner.query(`
      CREATE TABLE "products" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "code" character varying NOT NULL,
        "description" text NOT NULL,
        "min_amount" decimal(10,2) NOT NULL,
        "max_amount" decimal(10,2) NOT NULL,
        "min_term" integer NOT NULL,
        "max_term" integer NOT NULL,
        "interest_rate" decimal(5,2) NOT NULL,
        "processing_fee" decimal(5,2),
        "early_repayment_fee" decimal(5,2),
        "requirements" jsonb,
        "features" jsonb,
        "benefits" jsonb,
        "comparison" jsonb,
        "metadata" jsonb,
        "is_active" boolean NOT NULL DEFAULT true,
        "is_featured" boolean NOT NULL DEFAULT false,
        "sort_order" integer NOT NULL DEFAULT 0,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_products_code" UNIQUE ("code"),
        CONSTRAINT "PK_products" PRIMARY KEY ("id")
      )
    `);

    // 添加产品ID到贷款申请表
    await queryRunner.query(`
      ALTER TABLE "loan_applications"
      ADD COLUMN "product_id" uuid NOT NULL,
      ADD CONSTRAINT "FK_loan_applications_product" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE RESTRICT
    `);

    // 创建索引
    await queryRunner.query(`CREATE INDEX "IDX_products_code" ON "products" ("code")`);
    await queryRunner.query(`CREATE INDEX "IDX_products_is_active" ON "products" ("is_active")`);
    await queryRunner.query(`CREATE INDEX "IDX_products_is_featured" ON "products" ("is_featured")`);
    await queryRunner.query(`CREATE INDEX "IDX_loan_applications_product" ON "loan_applications" ("product_id")`);

    // 插入示例产品数据
    await queryRunner.query(`
      INSERT INTO "products" (
        "name", "code", "description", "min_amount", "max_amount", 
        "min_term", "max_term", "interest_rate", "processing_fee", 
        "early_repayment_fee", "requirements", "features", "benefits", 
        "comparison", "metadata", "is_active", "is_featured", "sort_order"
      ) VALUES (
        '个人信用贷款',
        'PCL-001',
        '面向个人用户的信用贷款产品，无需抵押，快速审批',
        10000.00,
        500000.00,
        12,
        60,
        4.35,
        0.50,
        1.00,
        '{"minAge": 22, "maxAge": 60, "minIncome": 5000, "minCreditScore": 650, "employmentTypes": ["全职", "兼职"], "requiredDocuments": ["身份证", "收入证明", "银行流水"]}',
        '[{"name": "快速审批", "description": "最快1小时完成审批", "icon": "speed"}, {"name": "灵活还款", "description": "支持提前还款", "icon": "flexibility"}]',
        '[{"name": "低门槛", "description": "无需抵押，信用良好即可申请", "icon": "low-threshold"}, {"name": "高额度", "description": "最高可贷50万", "icon": "high-limit"}]',
        '[{"feature": "年利率", "value": "4.35%", "highlight": true}, {"feature": "手续费", "value": "0.5%", "highlight": false}]',
        '{"category": "信用贷款", "tags": ["个人贷款", "信用贷款", "无抵押"], "riskLevel": "中等", "popularity": 0.8, "approvalRate": 0.75, "averageProcessingTime": 24}',
        true,
        true,
        1
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除索引
    await queryRunner.query(`DROP INDEX "IDX_loan_applications_product"`);
    await queryRunner.query(`DROP INDEX "IDX_products_is_featured"`);
    await queryRunner.query(`DROP INDEX "IDX_products_is_active"`);
    await queryRunner.query(`DROP INDEX "IDX_products_code"`);

    // 删除产品ID外键
    await queryRunner.query(`
      ALTER TABLE "loan_applications"
      DROP CONSTRAINT "FK_loan_applications_product",
      DROP COLUMN "product_id"
    `);

    // 删除产品表
    await queryRunner.query(`DROP TABLE "products"`);
  }
} 