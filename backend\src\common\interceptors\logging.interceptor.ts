import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { LoggerService } from '../../logger/logger.service';
import { MonitorService } from '../../monitor/monitor.service';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(
    private readonly logger: LoggerService,
    private readonly monitor: MonitorService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url, body, query, params } = request;
    const user = request.user;
    const requestId = Math.random().toString(36).substring(7);

    this.monitor.startOperation(`request:${requestId}`);
    this.logger.log(
      '请求开始',
      JSON.stringify({
        requestId,
        method,
        url,
        body,
        query,
        params,
        user: user ? { id: user.id, username: user.username } : null,
      })
    );

    const now = Date.now();
    return next.handle().pipe(
      tap({
        next: (data) => {
          const responseTime = Date.now() - now;
          this.logger.log(
            '请求完成',
            JSON.stringify({
              requestId,
              method,
              url,
              responseTime,
              statusCode: 200,
            })
          );
          this.monitor.endOperation(`request:${requestId}`);
        },
        error: (error) => {
          const responseTime = Date.now() - now;
          this.logger.error(
            '请求失败',
            error.stack,
            JSON.stringify({
              requestId,
              method,
              url,
              responseTime,
              error: error.message,
              stack: error.stack,
            })
          );
          this.monitor.endOperation(`request:${requestId}`);
        },
      }),
    );
  }
} 