# SmartLoan Backend Startup Script
Write-Host "Starting SmartLoan Backend..." -ForegroundColor Green

# Get current directory
$currentDir = Get-Location
$backendDir = Join-Path $currentDir "backend"

Write-Host "Current directory: $currentDir" -ForegroundColor Cyan
Write-Host "Backend directory: $backendDir" -ForegroundColor Cyan

# Check if backend directory exists
if (Test-Path $backendDir) {
    Write-Host "Backend directory found!" -ForegroundColor Green
    
    # Change to backend directory
    Set-Location $backendDir
    Write-Host "Changed to: $(Get-Location)" -ForegroundColor Cyan
    
    # Check Node.js
    try {
        $nodeVersion = node --version
        Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "Node.js not found!" -ForegroundColor Red
        exit 1
    }
    
    # Start simple server
    if (Test-Path "simple-server.js") {
        Write-Host "Starting simple server..." -ForegroundColor Yellow
        node simple-server.js
    }
    else {
        Write-Host "Simple server not found, creating..." -ForegroundColor Yellow
        
        # Create simple server content
        $content = @'
const http = require('http');
const PORT = 3001;

const server = http.createServer((req, res) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Content-Type', 'application/json');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  if (req.url === '/api/health') {
    res.writeHead(200);
    res.end(JSON.stringify({
      status: 'ok',
      service: 'SmartLoan Backend 2025',
      timestamp: new Date().toISOString()
    }));
    return;
  }
  
  res.writeHead(404);
  res.end(JSON.stringify({ error: 'Not found' }));
});

server.listen(PORT, () => {
  console.log('SmartLoan Backend started on port', PORT);
  console.log('Health check: http://localhost:' + PORT + '/api/health');
});
'@
        
        $content | Out-File -FilePath "simple-server.js" -Encoding UTF8
        Write-Host "Simple server created!" -ForegroundColor Green
        
        Write-Host "Starting server..." -ForegroundColor Yellow
        node simple-server.js
    }
}
else {
    Write-Host "Backend directory not found: $backendDir" -ForegroundColor Red
    Write-Host "Current directory contents:" -ForegroundColor Yellow
    Get-ChildItem -Name
    exit 1
}
