import { ReviewStatus } from '../enums/review-status.enum';
import { ReviewType } from '../enums/review-type.enum';
export interface ILoanReview {
    id: number;
    loanApplicationId: number;
    reviewerId: number;
    status: ReviewStatus;
    type: ReviewType;
    comments?: string;
    riskFactors?: {
        factor: string;
        score: number;
        weight: number;
    }[];
    verificationResults?: {
        documentType: string;
        verified: boolean;
        notes?: string;
    }[];
    decisionFactors?: {
        factor: string;
        impact: 'positive' | 'negative' | 'neutral';
        weight: number;
    }[];
    createdAt: Date;
    updatedAt: Date;
}
