import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QualificationReview } from './entities/qualification-review.entity';
import { LoanApplication } from '../loan/entities/loan-application.entity';
import { OcrService } from '../ocr/ocr.service';
import { GpuService } from '../../services/gpu.service';
import { Inject } from '@nestjs/common';
// 使用Redis缓存而不是NestJS缓存管理器
import { RedisService } from '../../services/redis.service';
import * as fs from 'fs';
import * as path from 'path';
import * as util from 'util';

const writeFile = util.promisify(fs.writeFile);
const mkdir = util.promisify(fs.mkdir);

@Injectable()
export class QualificationService {
  private readonly logger = new Logger(QualificationService.name);
  private readonly UPLOAD_DIR = 'uploads/documents';
  private readonly CACHE_TTL = 3600; // 缓存时间1小时
    constructor(
    @InjectRepository(QualificationReview)
    private reviewRepository: Repository<QualificationReview>,
    @InjectRepository(LoanApplication)
    private applicationRepository: Repository<LoanApplication>,
    private ocrService: OcrService,
    private gpuService: GpuService,
    private redisService: RedisService,
  ) {
    // 确保上传目录存在
    this.ensureUploadDirExists();
  }

  async processDocument(
    applicationId: number,
    documentType: string,
    file: Express.Multer.File,
  ): Promise<QualificationReview> {
    try {
      this.logger.log(`开始处理文件: ${file.originalname}, 类型: ${documentType}`);
        // 生成缓存键
      const cacheKey = `document:${applicationId}:${documentType}:${file.size}:${file.originalname}`;
      
      // 检查缓存
      const cachedResult = await this.redisService.get(cacheKey);
      if (cachedResult) {
        this.logger.log(`使用缓存的审核结果，文档类型: ${documentType}`);
        return JSON.parse(cachedResult);
      }
      
      // 保存文件并获取URL
      const documentUrl = await this.uploadFile(file);
      
      // 创建审核记录
      const review = new QualificationReview();
      review.application_id = applicationId.toString();
      review.document_type = documentType;
      review.document_url = documentUrl;
      review.verification_status = 'PROCESSING';
      
      // 先保存初始记录，以便异步处理
      await this.reviewRepository.save(review);

      // 使用GPU加速的OCR处理
      try {
        this.logger.log(`使用GPU加速OCR处理文档: ${documentType}`);
        // 读取文件内容
        const fileContent = file.buffer;
        // 调用GPU加速服务进行文档识别
        const imageData = fileContent.toString('base64');
        // 使用GPU服务的文档处理方法
        const gpuResult = await this.gpuService.processDocument(imageData);
        
        // 提取OCR结果
        const ocrResult = {
          documentType,
          extractedText: gpuResult.data || {},
          confidence: gpuResult.confidence || 0,
          processed: true,
          timestamp: new Date().toISOString()
        };
        
        review.ocr_result = ocrResult;
        review.verification_status = 'OCR_COMPLETED';
        
        // 立即进行异步验证
        this.verifyOcrResultAsync(review);
      } catch (error) {
        this.logger.error(`GPU加速OCR处理失败，回退到标准处理: ${error.message}`);
        
        // 回退到常规OCR处理
        try {
          const ocrResult = await this.ocrService.process(documentUrl, documentType);
          review.ocr_result = ocrResult;
          review.verification_status = 'OCR_COMPLETED';
        } catch (error) {
          review.verification_status = 'OCR_FAILED';
          review.review_result = `OCR处理失败: ${error.message}`;
        }
      }
      
      // 更新记录
      const savedReview = await this.reviewRepository.save(review);
        // 缓存结果
      await this.redisService.set(cacheKey, JSON.stringify(savedReview), this.CACHE_TTL);
      
      return savedReview;
    } catch (error) {
      this.logger.error(`文档处理失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 异步验证OCR结果，避免阻塞主请求
  private async verifyOcrResultAsync(review: QualificationReview): Promise<void> {
    try {
      const verificationResult = await this.validateOcrResult(review.ocr_result);
      
      review.verification_status = verificationResult.success ? 'VERIFIED' : 'REJECTED';
      review.review_result = verificationResult.message;
      
      await this.reviewRepository.save(review);
      this.logger.log(`异步OCR验证完成，状态: ${review.verification_status}`);
    } catch (error) {
      this.logger.error(`异步OCR验证失败: ${error.message}`);
      review.verification_status = 'VERIFICATION_FAILED';
      review.review_result = `验证过程失败: ${error.message}`;
      await this.reviewRepository.save(review);
    }
  }

  private async ensureUploadDirExists(): Promise<void> {
    try {
      await mkdir(this.UPLOAD_DIR, { recursive: true });
    } catch (error) {
      this.logger.error(`创建上传目录失败: ${error.message}`);
    }
  }

  private async uploadFile(file: Express.Multer.File): Promise<string> {
    const fileName = `${Date.now()}-${file.originalname.replace(/[^a-zA-Z0-9.]/g, '_')}`;
    const filePath = path.join(this.UPLOAD_DIR, fileName);
    const fileUrl = `/${filePath}`;
    
    try {
      await writeFile(filePath, file.buffer);
      this.logger.log(`文件成功上传到: ${filePath}`);
      return fileUrl;
    } catch (error) {
      this.logger.error(`文件上传失败: ${error.message}`);
      throw new Error(`文件上传失败: ${error.message}`);
    }
  }

  private async validateOcrResult(ocrResult: any): Promise<{success: boolean; message: string}> {
    // 实现OCR结果验证逻辑
    if (!ocrResult) {
      return { success: false, message: 'OCR结果为空' };
    }

    // TODO: 添加更多验证规则
    return { success: true, message: 'verification passed' };
  }

  async getApplicationReviews(applicationId: number): Promise<QualificationReview[]> {
    return this.reviewRepository.find({
      where: { application_id: applicationId.toString() },
      order: { created_at: 'DESC' }
    });
  }
}
