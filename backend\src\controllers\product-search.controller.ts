import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ProductSearchService, SearchResult } from '../services/product-search.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

@Controller('product-search')
export class ProductSearchController {
  constructor(
    private readonly productSearchService: ProductSearchService
  ) {}

  @Get('search')
  @UseGuards(JwtAuthGuard)
  async searchProducts(@Query('query') query: string): Promise<SearchResult> {
    const searchParams = { keyword: query };
    return this.productSearchService.searchProducts(searchParams);
  }

  @Get('category')
  @UseGuards(JwtAuthGuard)
  async searchByCategory(@Query('category') category: string) {
    return this.productSearchService.searchByCategory(category);
  }

  @Get('amount-range')
  @UseGuards(JwtAuthGuard)
  async searchByAmountRange(
    @Query('minAmount') minAmount: number,
    @Query('maxAmount') maxAmount: number
  ) {
    return this.productSearchService.searchByAmountRange(minAmount, maxAmount);
  }

  @Get('term-range')
  @UseGuards(JwtAuthGuard)
  async searchByTermRange(
    @Query('minTerm') minTerm: number,
    @Query('maxTerm') maxTerm: number
  ) {
    return this.productSearchService.searchByTermRange(minTerm, maxTerm);
  }

  @Get('interest-rate')
  @UseGuards(JwtAuthGuard)
  async searchByInterestRate(@Query('maxInterestRate') maxInterestRate: number) {
    return this.productSearchService.searchByInterestRate(maxInterestRate);
  }
} 