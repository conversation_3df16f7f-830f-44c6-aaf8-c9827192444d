import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  UploadedFiles,
  UseInterceptors,
  HttpStatus,
  Get,
  Param,
  Query
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBearerAuth } from '@nestjs/swagger';
import { MultiModalAuditService } from '../services/multi-modal-audit.service';
import { OcrService } from '../services/ocr.service';
import { FaceVerificationService } from '../services/face-verification.service';

@ApiTags('多模态资质审核')
@Controller('api/multi-modal-audit')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class MultiModalAuditController {
  constructor(
    private readonly multiModalAuditService: MultiModalAuditService,
    private readonly ocrService: OcrService,
    private readonly faceVerificationService: FaceVerificationService,
  ) {}

  @Post('ocr/identity-card')
  @UseInterceptors(FilesInterceptor('files', 2))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '身份证OCR识别' })
  @ApiResponse({ status: HttpStatus.OK, description: '识别成功' })
  async recognizeIdentityCard(
    @UploadedFiles() files: Express.Multer.File[],
    @Req() req: any,
  ) {
    try {
      const userId = req.user.id;
      const results = [];

      for (const file of files) {
        const ocrResult = await this.ocrService.recognizeIdentityCard(file.buffer);
        results.push({
          fileName: file.originalname,
          ...ocrResult
        });
      }

      return {
        success: true,
        data: results,
        message: '身份证识别完成'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '身份证识别失败',
        error: error
      };
    }
  }

  @Post('ocr/bank-card')
  @UseInterceptors(FilesInterceptor('files', 5))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '银行卡OCR识别' })
  @ApiResponse({ status: HttpStatus.OK, description: '识别成功' })
  async recognizeBankCard(
    @UploadedFiles() files: Express.Multer.File[],
    @Req() req: any,
  ) {
    try {
      const userId = req.user.id;
      const results = [];

      for (const file of files) {
        const ocrResult = await this.ocrService.recognizeBankCard(file.buffer);
        results.push({
          fileName: file.originalname,
          ...ocrResult
        });
      }

      return {
        success: true,
        data: results,
        message: '银行卡识别完成'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '银行卡识别失败',
        error: error
      };
    }
  }

  @Post('ocr/income-proof')
  @UseInterceptors(FilesInterceptor('files', 10))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '收入证明OCR识别' })
  @ApiResponse({ status: HttpStatus.OK, description: '识别成功' })
  async recognizeIncomeProof(
    @UploadedFiles() files: Express.Multer.File[],
    @Req() req: any,
  ) {
    try {
      const userId = req.user.id;
      const results = [];

      for (const file of files) {
        const ocrResult = await this.ocrService.recognizeIncomeProof(file.buffer);
        results.push({
          fileName: file.originalname,
          ...ocrResult
        });
      }

      return {
        success: true,
        data: results,
        message: '收入证明识别完成'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '收入证明识别失败',
        error: error
      };
    }
  }

  @Post('face-detection')
  @UseInterceptors(FilesInterceptor('files', 1))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '活体检测' })
  @ApiResponse({ status: HttpStatus.OK, description: '检测成功' })
  async faceDetection(
    @UploadedFiles() files: Express.Multer.File[],
    @Req() req: any,
  ) {
    try {
      const userId = req.user.id;
      
      if (!files || files.length === 0) {
        return {
          success: false,
          message: '请上传图片文件'
        };
      }

      const file = files[0];
      const detectionResult = await this.faceVerificationService.detectLiveness(file.buffer);

      return {
        success: true,
        data: detectionResult,
        message: '活体检测完成'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '活体检测失败',
        error: error
      };
    }
  }

  @Post('face-verification')
  @UseInterceptors(FilesInterceptor('files', 2))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '人脸比对验证' })
  @ApiResponse({ status: HttpStatus.OK, description: '验证成功' })
  async faceVerification(
    @UploadedFiles() files: Express.Multer.File[],
    @Body() body: { identityCardImage?: string },
    @Req() req: any,
  ) {
    try {
      const userId = req.user.id;
      
      if (!files || files.length < 2) {
        return {
          success: false,
          message: '请上传两张图片进行比对'
        };
      }

      const [livePhoto, idCardPhoto] = files;
      const verificationResult = await this.faceVerificationService.verifyFace(
        livePhoto.buffer,
        idCardPhoto.buffer
      );

      return {
        success: true,
        data: verificationResult,
        message: '人脸验证完成'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '人脸验证失败',
        error: error
      };
    }
  }

  @Post('comprehensive-audit')
  @UseInterceptors(FilesInterceptor('files', 20))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '综合多模态资质审核' })
  @ApiResponse({ status: HttpStatus.OK, description: '审核完成' })
  async comprehensiveAudit(
    @UploadedFiles() files: Express.Multer.File[],
    @Body() auditData: {
      applicationId: string;
      documentTypes: string[];
      verificationLevel: 'basic' | 'enhanced' | 'premium';
    },
    @Req() req: any,
  ) {
    try {
      const userId = req.user.id;
      
      const auditResult = await this.multiModalAuditService.performComprehensiveAudit({
        userId,
        applicationId: auditData.applicationId,
        files,
        documentTypes: auditData.documentTypes,
        verificationLevel: auditData.verificationLevel || 'basic'
      });

      return {
        success: true,
        data: auditResult,
        message: '综合审核完成'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '综合审核失败',
        error: error
      };
    }
  }

  @Get('audit-result/:auditId')
  @ApiOperation({ summary: '获取审核结果' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  async getAuditResult(
    @Param('auditId') auditId: string,
    @Req() req: any,
  ) {
    try {
      const userId = req.user.id;
      const result = await this.multiModalAuditService.getAuditResult(auditId, userId);

      return {
        success: true,
        data: result,
        message: '获取审核结果成功'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '获取审核结果失败',
        error: error
      };
    }
  }

  @Get('audit-history')
  @ApiOperation({ summary: '获取审核历史' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  async getAuditHistory(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Req() req: any,
  ) {
    try {
      const userId = req.user.id;
      const history = await this.multiModalAuditService.getAuditHistory(userId, {
        page,
        limit
      });

      return {
        success: true,
        data: history,
        message: '获取审核历史成功'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '获取审核历史失败',
        error: error
      };
    }
  }
}
