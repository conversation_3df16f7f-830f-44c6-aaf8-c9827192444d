import { RedisService } from './redis.service';
import { LoggerService } from './logger.service';
export declare class CacheService {
    private readonly redisService;
    private readonly logger;
    constructor(redisService: RedisService, logger: LoggerService);
    get<T>(key: string): Promise<T | null>;
    set(key: string, value: any, ttl?: number): Promise<void>;
    del(key: string): Promise<void>;
    exists(key: string): Promise<boolean>;
    incr(key: string): Promise<number>;
    decr(key: string): Promise<number>;
    expire(key: string, seconds: number): Promise<void>;
    ttl(key: string): Promise<number>;
    keys(pattern: string): Promise<string[]>;
    flushAll(): Promise<void>;
    getOrSet<T>(key: string, factory: () => Promise<T>, ttl?: number): Promise<T>;
    cacheUser(userId: string, userData: any): Promise<void>;
    getCachedUser(userId: string): Promise<any>;
    cacheLoanApplication(applicationId: string, applicationData: any): Promise<void>;
    getCachedLoanApplication(applicationId: string): Promise<any>;
    cacheProduct(productId: string, productData: any): Promise<void>;
    getCachedProduct(productId: string): Promise<any>;
    cacheProductList(products: any[]): Promise<void>;
    getCachedProductList(): Promise<any[]>;
    cacheSession(sessionId: string, sessionData: any): Promise<void>;
    getCachedSession(sessionId: string): Promise<any>;
    deleteSession(sessionId: string): Promise<void>;
    cacheVerificationCode(email: string, code: string): Promise<void>;
    getCachedVerificationCode(email: string): Promise<string | null>;
    deleteVerificationCode(email: string): Promise<void>;
}
