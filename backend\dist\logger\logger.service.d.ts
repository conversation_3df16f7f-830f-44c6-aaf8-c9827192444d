import { LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
export declare class LoggerService implements NestLoggerService {
    private readonly configService;
    private logger;
    private readonly logDir;
    constructor(configService: ConfigService);
    private formatMessage;
    log(message: any, context?: string): void;
    error(message: any, trace?: string, context?: string): void;
    warn(message: any, context?: string): void;
    debug(message: any, context?: string): void;
    verbose(message: any, context?: string): void;
    info(message: any, meta?: any): void;
}
