import { RedisService } from './redis.service';
import { MonitoringService } from './monitoring.service';
import { GpuService } from './gpu.service';
export declare class RiskDataProcessorService {
    private readonly redisService;
    private readonly monitoringService;
    private readonly gpuService;
    private readonly BATCH_SIZE;
    constructor(redisService: RedisService, monitoringService: MonitoringService, gpuService: GpuService);
    processBatchAssessments(tasks: any[]): Promise<any[]>;
    private splitIntoBatches;
    preprocessData(data: any): Promise<any>;
    private normalizeUserData;
    private normalizeApplicationData;
    private enrichWithMarketData;
    private normalizeCreditScore;
    private normalizeIncome;
    private normalizeAmount;
    private normalizeTerm;
    private fetchMarketData;
    private getDefaultMarketData;
}
