import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ProductComparisonService } from '../services/product-comparison.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

@Controller('product-comparison')
export class ProductComparisonController {
  constructor(
    private readonly productComparisonService: ProductComparisonService
  ) {}

  @Get('compare/:ids')
  @UseGuards(JwtAuthGuard)
  async compareProducts(@Param('ids') ids: string) {
    const productIds = ids.split(',');
    return this.productComparisonService.compareProducts(productIds);
  }

  @Get('matrix/:ids')
  @UseGuards(JwtAuthGuard)
  async getComparisonMatrix(@Param('ids') ids: string) {
    const productIds = ids.split(',');
    return this.productComparisonService.getComparisonMatrix(productIds);
  }
} 