import { User } from '../../user/entities/user.entity';
export declare enum LoanStatus {
    PENDING = "pending",
    APPROVED = "approved",
    REJECTED = "rejected",
    PROCESSING = "processing",
    COMPLETED = "completed"
}
export declare class LoanApplication {
    id: number;
    user: User;
    amount: number;
    interestRate: number;
    term: number;
    loanType: string;
    status: LoanStatus;
    riskAssessment: any;
    documents: any;
    rejectionReason: string;
    createdAt: Date;
    updatedAt: Date;
}
