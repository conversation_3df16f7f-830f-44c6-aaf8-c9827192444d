import { RiskAssessmentService } from '../services/risk-assessment.service';
export declare class RiskAssessmentController {
    private readonly riskAssessmentService;
    constructor(riskAssessmentService: RiskAssessmentService);
    assessRisk(applicationId: string): Promise<any>;
    getRiskMetrics(): Promise<{
        totalApplications: number;
        averageRiskScore: number;
        riskDistribution: {
            low: number;
            medium: number;
            high: number;
        };
        recentAlerts: any[];
    }>;
}
