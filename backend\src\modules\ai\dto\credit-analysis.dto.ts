import { ApiProperty } from '@nestjs/swagger';
import { IsObject, IsNotEmpty } from 'class-validator';

export class CreditAnalysisDto {
  @ApiProperty({
    description: '征信报告数据',
    example: {
      personalInfo: {
        name: '张三',
        idNumber: '110101199001011234',
        phone: '13800138000',
      },
      creditHistory: {
        totalLoans: 3,
        currentLoans: 1,
        overdueCount: 0,
        creditScore: 750,
      },
      loanDetails: [
        {
          loanType: '房贷',
          amount: 1000000,
          startDate: '2020-01-01',
          endDate: '2030-01-01',
          status: '正常',
        },
      ],
    },
  })
  @IsObject()
  @IsNotEmpty()
  report: Record<string, any>;
} 