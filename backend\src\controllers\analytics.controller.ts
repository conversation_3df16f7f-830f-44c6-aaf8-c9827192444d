import { Controller, Get, Post, Body, Query, UseGuards, Req } from '@nestjs/common';
import { AnalyticsService } from '../services/analytics.service';
import { ExportService } from '../services/export.service';
import { LoggerService } from '../services/logger.service';
import { <PERSON>rror<PERSON>and<PERSON> } from '../utils/error-handler';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../enums/role.enum';
import { Request } from 'express';
import { User } from '../entities/user.entity';

@Controller('analytics')
@UseGuards(JwtAuthGuard, RolesGuard)
export class AnalyticsController {
  constructor(
    private readonly analyticsService: AnalyticsService,
    private readonly exportService: ExportService,
    private readonly logger: LoggerService,
    private readonly errorHandler: ErrorHandler
  ) {}

  @Get('dashboard')
  @Roles(Role.ADMIN, Role.ANALYST)
  async getDashboardData(@Query() query: any, @Req() req: Request) {
    try {
      const user = req.user as User;
      const data = await this.analyticsService.getDashboardData(query, user);
      return { success: true, data };
    } catch (error) {
      this.logger.error('获取仪表盘数据失败', { error, query });
      throw this.errorHandler.handle(error);
    }
  }

  @Get('data')
  @Roles(Role.ADMIN, Role.ANALYST)
  async getAnalyticsData(@Query() query: any) {
    try {
      const data = await this.analyticsService.getAnalyticsData(query);
      return { success: true, data };
    } catch (error) {
      this.logger.error('获取分析数据失败', { error, query });
      throw this.errorHandler.handle(error);
    }
  }

  @Post('export')
  @Roles(Role.ADMIN, Role.ANALYST)
  async exportData(@Body() query: any, @Req() req: Request) {
    try {
      const user = req.user as User;
      const data = await this.analyticsService.exportData(query, user);
      return { success: true, data };
    } catch (error) {
      this.logger.error('导出数据失败', { error, query });
      throw this.errorHandler.handle(error);
    }
  }

  @Get('export/:id')
  @Roles(Role.ADMIN, Role.ANALYST)
  async getExportData(@Query('id') id: string, @Req() req: Request) {
    try {
      const user = req.user as User;
      const data = await this.analyticsService.getExportData(id, user);
      return { success: true, data };
    } catch (error) {
      this.logger.error('获取导出数据失败', { error, id });
      throw this.errorHandler.handle(error);
    }
  }

  @Get('predictions')
  @Roles(Role.ADMIN, Role.ANALYST)
  async predictTrends(@Query() query: any, @Req() req: Request) {
    try {
      const user = req.user as User;
      const data = await this.analyticsService.predictTrends(query, user);
      return { success: true, data };
    } catch (error) {
      this.logger.error('预测趋势失败', { error, query });
      throw this.errorHandler.handle(error);
    }
  }

  @Get('anomalies')
  @Roles(Role.ADMIN, Role.ANALYST)
  async detectAnomalies(@Query() query: any, @Req() req: Request) {
    try {
      const user = req.user as User;
      const data = await this.analyticsService.detectAnomalies(query, user);
      return { success: true, data };
    } catch (error) {
      this.logger.error('检测异常失败', { error, query });
      throw this.errorHandler.handle(error);
    }
  }

  @Get('recommendations')
  @Roles(Role.ADMIN, Role.ANALYST)
  async getRecommendations(@Query() query: any, @Req() req: Request) {
    try {
      const user = req.user as User;
      const data = await this.analyticsService.getRecommendations(query, user);
      return { success: true, data };
    } catch (error) {
      this.logger.error('获取建议失败', { error, query });
      throw this.errorHandler.handle(error);
    }
  }
}