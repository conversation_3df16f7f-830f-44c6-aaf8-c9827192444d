export declare class CreateProductDto {
    name: string;
    code: string;
    description: string;
    minAmount: number;
    maxAmount: number;
    minTerm: number;
    maxTerm: number;
    interestRate: number;
    processingFee: number;
    earlyRepaymentFee: number;
    requirements: string[];
    features: string[];
    benefits: string[];
    comparison: Record<string, any>;
    metadata: Record<string, any>;
    isActive?: boolean;
    isFeatured?: boolean;
    sortOrder?: number;
}
