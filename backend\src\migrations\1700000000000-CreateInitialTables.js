"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateInitialTables1700000000000 = void 0;
var CreateInitialTables1700000000000 = /** @class */ (function () {
    function CreateInitialTables1700000000000() {
        this.name = 'CreateInitialTables1700000000000';
    }
    CreateInitialTables1700000000000.prototype.up = function (queryRunner) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: 
                    // 创建用户表
                    return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"users\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"username\" VARCHAR NOT NULL UNIQUE,\n        \"password\" VARCHAR NOT NULL,\n        \"email\" VARCHAR NOT NULL UNIQUE,\n        \"phone\" VARCHAR,\n        \"avatar\" VARCHAR,\n        \"roles\" VARCHAR[] NOT NULL DEFAULT '{}',\n        \"permissions\" VARCHAR[] NOT NULL DEFAULT '{}',\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now(),\n        \"updated_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 1:
                        // 创建用户表
                        _a.sent();
                        // 创建贷款申请表
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TYPE \"loan_status\" AS ENUM ('pending', 'approved', 'rejected', 'processing', 'completed')\n    ")];
                    case 2:
                        // 创建贷款申请表
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("\n      CREATE TABLE \"loan_applications\" (\n        \"id\" SERIAL PRIMARY KEY,\n        \"user_id\" INTEGER NOT NULL REFERENCES \"users\"(\"id\"),\n        \"amount\" DECIMAL(10,2) NOT NULL,\n        \"interest_rate\" DECIMAL(5,2) NOT NULL,\n        \"term\" INTEGER NOT NULL,\n        \"status\" loan_status NOT NULL DEFAULT 'pending',\n        \"risk_assessment\" JSONB,\n        \"documents\" JSONB,\n        \"rejection_reason\" VARCHAR,\n        \"created_at\" TIMESTAMP NOT NULL DEFAULT now(),\n        \"updated_at\" TIMESTAMP NOT NULL DEFAULT now()\n      )\n    ")];
                    case 3:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    CreateInitialTables1700000000000.prototype.down = function (queryRunner) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, queryRunner.query("DROP TABLE \"loan_applications\"")];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TYPE \"loan_status\"")];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, queryRunner.query("DROP TABLE \"users\"")];
                    case 3:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    return CreateInitialTables1700000000000;
}());
exports.CreateInitialTables1700000000000 = CreateInitialTables1700000000000;
