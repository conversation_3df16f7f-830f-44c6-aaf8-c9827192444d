import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import { format } from 'winston';
import * as path from 'path';
import * as fs from 'fs';

@Injectable()
export class LoggerService implements NestLoggerService {
  private logger: winston.Logger;
  private readonly logDir: string;

  constructor(private readonly configService: ConfigService) {
    // 设置日志目录
    this.logDir = path.join(process.cwd(), 'logs');

    // 确保日志目录存在
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }

    // 创建 Winston Logger 实例
    this.logger = winston.createLogger({
      level: this.configService.get('LOG_LEVEL', 'info'),
      format: format.combine(
        format.timestamp(),
        format.json()
      ),
      transports: [
        new winston.transports.Console({
          format: format.combine(
            format.colorize(),
            format.simple()
          )
        }),
        new winston.transports.File({
          filename: path.join(this.logDir, 'error.log'),
          level: 'error'
        }),
        new winston.transports.File({
          filename: path.join(this.logDir, 'combined.log')
        })
      ]
    });
  }

  private formatMessage(message: any, meta?: any): string {
    if (typeof message === 'object') {
      return JSON.stringify(message);
    }
    return message;
  }

  log(message: any, context?: string) {
    this.info(message, { context });
  }

  error(message: any, trace?: string, context?: string) {
    this.logger.error(this.formatMessage(message), { trace, context });
  }

  warn(message: any, context?: string) {
    this.logger.warn(this.formatMessage(message), { context });
  }

  debug(message: any, context?: string) {
    this.logger.debug(this.formatMessage(message), { context });
  }

  verbose(message: any, context?: string) {
    this.logger.verbose(this.formatMessage(message), { context });
  }

  info(message: any, meta?: any) {
    this.logger.info(this.formatMessage(message), meta);
  }
}