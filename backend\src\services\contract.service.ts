import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Contract } from '../entities/contract.entity';
import { SignContractDto } from '../dto/sign-contract.dto';
import * as fs from 'fs';
import * as path from 'path';
import PDFDocument from 'pdfkit';

@Injectable()
export class ContractService {
  constructor(
    @InjectRepository(Contract)
    private contractRepository: Repository<Contract>,
  ) {}

  async getContract(id: string) {
    const contract = await this.contractRepository.findOne({
      where: { id },
      relations: ['repaymentPlan'],
    });

    if (!contract) {
      throw new NotFoundException('合同不存在');
    }

    return {
      contract: {
        contractNumber: contract.contractNumber,
        applicationNumber: contract.applicationNumber,
        loanType: contract.loanType,
        loanAmount: contract.loanAmount,
        loanTerm: contract.loanTerm,
        interestRate: contract.interestRate,
        repaymentMethod: contract.repaymentMethod,
        guaranteeMethod: contract.guaranteeMethod,
        disbursementDate: contract.disbursementDate,
        maturityDate: contract.maturityDate,
        loanPurpose: contract.loanPurpose,
        repaymentDay: contract.repaymentDay,
        guarantor: contract.guarantor,
      },
      repaymentPlan: contract.repaymentPlan,
    };
  }

  async generateContractFile(id: string): Promise<string> {
    const contract = await this.getContract(id);
    const doc = new PDFDocument();
    const filePath = path.join(process.cwd(), 'temp', `${contract.contract.contractNumber}.pdf`);

    // 确保temp目录存在
    if (!fs.existsSync(path.join(process.cwd(), 'temp'))) {
      fs.mkdirSync(path.join(process.cwd(), 'temp'));
    }

    // 创建写入流
    const stream = fs.createWriteStream(filePath);
    doc.pipe(stream);

    // 添加合同内容
    doc.fontSize(20).text('贷款合同', { align: 'center' });
    doc.moveDown();
    doc.fontSize(12).text(`合同编号：${contract.contract.contractNumber}`);
    doc.text(`申请编号：${contract.contract.applicationNumber}`);
    doc.text(`贷款类型：${contract.contract.loanType}`);
    doc.text(`贷款金额：${contract.contract.loanAmount}`);
    doc.text(`贷款期限：${contract.contract.loanTerm}个月`);
    doc.text(`年利率：${contract.contract.interestRate}%`);
    doc.text(`还款方式：${contract.contract.repaymentMethod}`);
    doc.text(`担保方式：${contract.contract.guaranteeMethod}`);
    doc.text(`放款日期：${contract.contract.disbursementDate}`);
    doc.text(`到期日期：${contract.contract.maturityDate}`);

    // 添加还款计划
    doc.moveDown();
    doc.fontSize(16).text('还款计划');
    doc.moveDown();
    contract.repaymentPlan.forEach((plan) => {
      doc.fontSize(12).text(`期数：${plan.period}`);
      doc.text(`还款日期：${plan.paymentDate}`);
      doc.text(`还款金额：${plan.paymentAmount}`);
      doc.text(`本金：${plan.principal}`);
      doc.text(`利息：${plan.interest}`);
      doc.text(`剩余本金：${plan.remainingPrincipal}`);
      doc.text(`状态：${plan.status}`);
      doc.moveDown();
    });

    // 结束文档
    doc.end();

    return new Promise((resolve, reject) => {
      stream.on('finish', () => resolve(filePath));
      stream.on('error', reject);
    });
  }

  async signContract(id: string, signContractDto: SignContractDto) {
    const contract = await this.contractRepository.findOne({
      where: { id },
    });

    if (!contract) {
      throw new NotFoundException('合同不存在');
    }

    if (contract.status === '已签署') {
      throw new BadRequestException('合同已签署');
    }

    // 验证签名数据
    if (!this.validateSignature(signContractDto.signature)) {
      throw new BadRequestException('无效的签名数据');
    }

    // 更新合同状态
    contract.status = '已签署';
    contract.signature = signContractDto.signature;
    contract.signedAt = new Date();

    await this.contractRepository.save(contract);

    return {
      message: '合同签署成功',
      contract: {
        id: contract.id,
        status: contract.status,
        signedAt: contract.signedAt,
      },
    };
  }

  private validateSignature(signature: string): boolean {
    // 验证签名数据格式
    if (!signature || !signature.startsWith('data:image/png;base64,')) {
      return false;
    }

    // 验证签名数据大小
    const base64Data = signature.replace('data:image/png;base64,', '');
    const buffer = Buffer.from(base64Data, 'base64');
    if (buffer.length > 1024 * 1024) { // 限制签名大小不超过1MB
      return false;
    }

    return true;
  }
} 