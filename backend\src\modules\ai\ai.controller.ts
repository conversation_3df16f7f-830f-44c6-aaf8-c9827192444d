import { Controller, Post, Body, UseInterceptors, UploadedFile, UseGuards } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { AIService } from './ai-service';
import { ApiTags, ApiOperation, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { AIAdvisorQueryDto } from './dto/ai-advisor.dto';
import { CreditAnalysisDto } from './dto/credit-analysis.dto';
import { CustomThrottlerGuard } from '../../common/guards/throttler.guard';
import { CacheInterceptor } from '../../common/interceptors/cache.interceptor';

@ApiTags('AI服务')
@Controller('ai')
@UseGuards(CustomThrottlerGuard)
export class AIController {
  constructor(private readonly aiService: AIService) {}

  @Post('ocr')
  @UseInterceptors(FileInterceptor('image'))
  @ApiOperation({ summary: 'OCR识别' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async performOCR(@UploadedFile() file: Express.Multer.File) {
    return this.aiService.performOCR(file.buffer);
  }

  @Post('liveness')
  @UseInterceptors(FileInterceptor('video'))
  @ApiOperation({ summary: '活体检测' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        video: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async detectLiveness(@UploadedFile() file: Express.Multer.File) {
    return this.aiService.detectLiveness(file.buffer);
  }

  @Post('advisor')
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'AI顾问咨询' })
  async getAIAdvisorResponse(@Body() queryDto: AIAdvisorQueryDto) {
    return this.aiService.getAIAdvisorResponse(queryDto.query, queryDto.context);
  }

  @Post('credit-analysis')
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: '征信分析' })
  async analyzeCreditReport(@Body() analysisDto: CreditAnalysisDto) {
    return this.aiService.analyzeCreditReport(analysisDto.report);
  }
} 