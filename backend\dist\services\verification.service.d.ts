import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { LoggerService } from './logger.service';
import { <PERSON>rror<PERSON>and<PERSON> } from '../utils/error-handler';
import { GpuService } from './gpu.service';
import { OcrVerificationService } from './ocr-verification.service';
import { UserService } from './user.service';
export declare class VerificationService {
    private readonly userRepository;
    private readonly logger;
    private readonly errorHandler;
    private readonly gpuService;
    private readonly ocrService;
    private readonly userService;
    constructor(userRepository: Repository<User>, logger: LoggerService, errorHandler: ErrorHandler, gpuService: GpuService, ocrService: OcrVerificationService, userService: UserService);
    verifyIdentity(userId: number, imageData: string): Promise<{
        success: boolean;
        message: string;
    }>;
    verifyDocuments(userId: number, documents: any[]): Promise<{
        success: boolean;
        results: ({
            documentId: any;
            isValid: boolean;
            details: {
                name?: string;
                idNumber?: string;
                validityPeriod?: string;
                error?: string;
            };
            matchScore?: undefined;
        } | {
            documentId: any;
            isValid: any;
            matchScore: any;
            details?: undefined;
        })[];
    }>;
    private processDocument;
    private validateDocument;
    performLivenessDetection(userId: number, videoData: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
