import { LoanService, CreateLoanApplicationDto } from './loan.service';
export declare class LoanController {
    private readonly loanService;
    constructor(loanService: LoanService);
    createApplication(createDto: CreateLoanApplicationDto): Promise<{
        success: boolean;
        data: import("./loan.entity").LoanApplication;
        message: string;
    }>;
    getAllApplications(page?: number, limit?: number): Promise<{
        success: boolean;
        data: import("./loan.service").LoanApplicationWithDetails[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
        message: string;
    }>;
    getStatistics(): Promise<{
        success: boolean;
        data: {
            total: number;
            pending: number;
            under_review: number;
            approved: number;
            rejected: number;
            disbursed: number;
            total_amount: number;
            approved_amount: number;
        };
        message: string;
    }>;
    getUserApplications(userId: number): Promise<{
        success: boolean;
        data: import("./loan.entity").LoanApplication[];
        message: string;
    }>;
    getApplicationById(id: number): Promise<{
        success: boolean;
        data: import("./loan.service").LoanApplicationWithDetails;
        message: string;
    }>;
    updateStatus(id: number, updateData: {
        status: string;
        officer_id?: number;
        comment?: string;
    }): Promise<{
        success: boolean;
        data: import("./loan.entity").LoanApplication;
        message: string;
    }>;
    approveApplication(id: number, approvalData: {
        approval_amount: number;
        approved_rate: number;
        approved_term: number;
        officer_id: number;
        comment?: string;
    }): Promise<{
        success: boolean;
        data: import("./loan.entity").LoanApplication;
        message: string;
    }>;
    rejectApplication(id: number, rejectionData: {
        rejection_reason: string;
        officer_id: number;
    }): Promise<{
        success: boolean;
        data: import("./loan.entity").LoanApplication;
        message: string;
    }>;
    disburseApplication(id: number, disbursementData: {
        officer_id: number;
    }): Promise<{
        success: boolean;
        data: import("./loan.entity").LoanApplication;
        message: string;
    }>;
    updateAIAssessment(id: number, aiData: {
        ai_score: number;
        ai_recommendation: string;
        risk_assessment?: any;
    }): Promise<{
        success: boolean;
        data: import("./loan.entity").LoanApplication;
        message: string;
    }>;
}
