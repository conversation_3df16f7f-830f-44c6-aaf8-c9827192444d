import { OnModuleInit } from '@nestjs/common';
import { Repository } from 'typeorm';
import { AuditLog } from '../entities/audit-log.entity';
import { LoggerService } from './logger.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { RedisService } from './redis.service';
interface SystemMetrics {
    cpu: {
        usage: number;
    };
    memory: {
        usage: number;
    };
    disk: {
        usage: number;
    };
    errors: number;
}
export declare class MonitorService implements OnModuleInit {
    private auditLogRepository;
    private readonly loggerService;
    private readonly configService;
    private readonly redisService;
    private readonly logger;
    private readonly metrics;
    private readonly alerts;
    private readonly eventEmitter;
    private operationTimers;
    private readonly metricsPrefix;
    private readonly systemMetricsKey;
    private readonly apiMetricsKey;
    private readonly errorMetricsKey;
    constructor(auditLogRepository: Repository<AuditLog>, loggerService: LoggerService, configService: ConfigService, eventEmitter: EventEmitter2, redisService: RedisService);
    onModuleInit(): Promise<void>;
    private initializeAlerts;
    incrementMetric(name: string, value?: number): void;
    setMetric(name: string, value: number): void;
    getMetric(name: string): number;
    getAllMetrics(): Map<string, number>;
    resetMetrics(): void;
    logApplicationEvent(event: string, data: any): void;
    logError(error: Error, context?: string): void;
    logPerformanceMetric(operation: string, duration: number): void;
    checkAlerts(): Promise<void>;
    getActiveAlerts(): string[];
    private getSystemMetrics;
    private getCpuUsage;
    private getMemoryUsage;
    private getDiskUsage;
    checkHealth(): Promise<{
        status: 'healthy' | 'warning' | 'critical';
        details: SystemMetrics;
    }>;
    sendAlert(level: 'info' | 'warning' | 'error' | 'critical', message: string, details?: any): Promise<void>;
    startOperation(operationName: string): void;
    endOperation(operationName: string): void;
    monitorAsync<T>(operationName: string, operation: () => Promise<T>): Promise<T>;
    private startSystemMetricsCollection;
    private collectSystemMetrics;
    recordApiCall(path: string, method: string, statusCode: number, duration: number): Promise<void>;
    recordError(errorType: string, message: string, stack?: string): Promise<void>;
}
export {};
