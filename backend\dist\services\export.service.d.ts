/// <reference types="node" />
/// <reference types="node" />
import { Repository } from 'typeorm';
import { LoanApplication } from '../entities/loan-application.entity';
import { LoggerService } from './logger.service';
import { ErrorHandler } from '../utils/error-handler';
export declare class ExportService {
    private loanApplicationRepository;
    private readonly logger;
    private readonly errorHandler;
    private readonly writeFileAsync;
    private readonly readFileAsync;
    private readonly templatesDir;
    constructor(loanApplicationRepository: Repository<LoanApplication>, logger: LoggerService, errorHandler: ErrorHandler);
    exportData(query: any): Promise<Buffer>;
    private exportToExcel;
    private exportToPDF;
    private exportToCSV;
    private exportToJSON;
    private exportToXML;
    private exportToHTML;
    private applyExcelTemplate;
    private applyPDFTemplate;
    private applyCSVTemplate;
    private getExcelColumns;
    private applyExcelStyles;
    private addPDFContent;
    private getData;
    private convertToXML;
    private convertToHTML;
}
