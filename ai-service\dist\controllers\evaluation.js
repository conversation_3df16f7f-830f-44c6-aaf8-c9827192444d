"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.evaluateLoanApplication = void 0;
const evaluation_1 = require("../services/evaluation");
const evaluateLoanApplication = async (req, res) => {
    try {
        const application = req.body;
        // 验证必填字段
        if (!application.amount || !application.term || !application.purpose) {
            return res.status(400).json({
                success: false,
                error: '缺少必填字段：amount, term, purpose'
            });
        }
        // 调用评估服务
        const result = await (0, evaluation_1.evaluateLoan)(application);
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('贷款评估失败：', error);
        res.status(500).json({
            success: false,
            error: '评估失败'
        });
    }
};
exports.evaluateLoanApplication = evaluateLoanApplication;
