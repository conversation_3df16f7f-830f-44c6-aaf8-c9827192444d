import { LoanApplication } from './loan-application.entity';
import { User } from './user.entity';
export declare enum ReviewStatus {
    PENDING = "pending",
    APPROVED = "approved",
    REJECTED = "rejected",
    NEEDS_MORE_INFO = "needsMoreInfo"
}
export declare enum ReviewType {
    INITIAL = "initial",
    FINAL = "final",
    APPEAL = "appeal"
}
export declare class LoanReview {
    id: number;
    loanApplicationId: number;
    loanApplication: LoanApplication;
    reviewerId: number;
    reviewer: User;
    status: ReviewStatus;
    type: ReviewType;
    comments: string;
    riskFactors: {
        factor: string;
        score: number;
        weight: number;
    }[];
    verificationResults: {
        documentType: string;
        verified: boolean;
        notes?: string;
    }[];
    decisionFactors: {
        factor: string;
        impact: 'positive' | 'negative' | 'neutral';
        weight: number;
    }[];
    createdAt: Date;
    updatedAt: Date;
}
