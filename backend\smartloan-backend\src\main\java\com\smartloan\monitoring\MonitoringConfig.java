/**
 * 监控体系配置
 * 集成Prometheus、Micrometer、ELK
 * 支持GPU使用率、API性能、业务指标监控
 */

package com.smartloan.monitoring;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.prometheus.PrometheusConfig;
import io.micrometer.prometheus.PrometheusMeterRegistry;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.boot.actuator.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.boot.actuator.health.Health;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicDouble;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Configuration
public class MonitoringConfig {

    /**
     * Prometheus监控注册表
     */
    @Bean
    public PrometheusMeterRegistry prometheusMeterRegistry() {
        return new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
    }

    /**
     * 自定义监控指标注册
     */
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> registry.config()
            .commonTags(
                "application", "smartloan-backend",
                "version", "2025.1.0",
                "environment", "production"
            );
    }

    /**
     * 业务监控指标服务
     */
    @Component
    @RequiredArgsConstructor
    public static class BusinessMetricsService {
        
        private final MeterRegistry meterRegistry;
        
        // 业务计数器
        private final Counter productMatchCounter;
        private final Counter ocrProcessCounter;
        private final Counter riskAssessmentCounter;
        private final Counter loanApplicationCounter;
        
        // 业务计时器
        private final Timer productMatchTimer;
        private final Timer ocrProcessTimer;
        private final Timer riskAssessmentTimer;
        private final Timer gpuProcessTimer;
        
        // 业务指标
        private final AtomicLong activeUsers = new AtomicLong(0);
        private final AtomicDouble gpuUtilization = new AtomicDouble(0.0);
        private final AtomicLong totalApplications = new AtomicLong(0);
        private final AtomicDouble approvalRate = new AtomicDouble(0.0);

        public BusinessMetricsService(MeterRegistry meterRegistry) {
            this.meterRegistry = meterRegistry;
            
            // 初始化计数器
            this.productMatchCounter = Counter.builder("smartloan.product.match.total")
                .description("产品匹配请求总数")
                .register(meterRegistry);
                
            this.ocrProcessCounter = Counter.builder("smartloan.ocr.process.total")
                .description("OCR处理请求总数")
                .register(meterRegistry);
                
            this.riskAssessmentCounter = Counter.builder("smartloan.risk.assessment.total")
                .description("风险评估请求总数")
                .register(meterRegistry);
                
            this.loanApplicationCounter = Counter.builder("smartloan.loan.application.total")
                .description("贷款申请总数")
                .register(meterRegistry);
            
            // 初始化计时器
            this.productMatchTimer = Timer.builder("smartloan.product.match.duration")
                .description("产品匹配处理时间")
                .register(meterRegistry);
                
            this.ocrProcessTimer = Timer.builder("smartloan.ocr.process.duration")
                .description("OCR处理时间")
                .register(meterRegistry);
                
            this.riskAssessmentTimer = Timer.builder("smartloan.risk.assessment.duration")
                .description("风险评估处理时间")
                .register(meterRegistry);
                
            this.gpuProcessTimer = Timer.builder("smartloan.gpu.process.duration")
                .description("GPU处理时间")
                .register(meterRegistry);
            
            // 注册Gauge指标
            Gauge.builder("smartloan.users.active")
                .description("当前活跃用户数")
                .register(meterRegistry, activeUsers, AtomicLong::get);
                
            Gauge.builder("smartloan.gpu.utilization")
                .description("GPU使用率")
                .register(meterRegistry, gpuUtilization, AtomicDouble::get);
                
            Gauge.builder("smartloan.applications.total")
                .description("累计申请总数")
                .register(meterRegistry, totalApplications, AtomicLong::get);
                
            Gauge.builder("smartloan.approval.rate")
                .description("审批通过率")
                .register(meterRegistry, approvalRate, AtomicDouble::get);
        }

        /**
         * 记录产品匹配指标
         */
        public void recordProductMatch(Duration duration, boolean success) {
            productMatchCounter.increment();
            productMatchTimer.record(duration);
            
            if (success) {
                meterRegistry.counter("smartloan.product.match.success").increment();
            } else {
                meterRegistry.counter("smartloan.product.match.failure").increment();
            }
            
            log.debug("📊 产品匹配指标记录 | duration={}ms | success={}", 
                duration.toMillis(), success);
        }

        /**
         * 记录OCR处理指标
         */
        public void recordOcrProcess(Duration duration, String documentType, boolean success) {
            ocrProcessCounter.increment();
            ocrProcessTimer.record(duration);
            
            meterRegistry.counter("smartloan.ocr.process.by.type", "type", documentType).increment();
            
            if (success) {
                meterRegistry.counter("smartloan.ocr.process.success").increment();
            } else {
                meterRegistry.counter("smartloan.ocr.process.failure").increment();
            }
            
            log.debug("📊 OCR处理指标记录 | type={} | duration={}ms | success={}", 
                documentType, duration.toMillis(), success);
        }

        /**
         * 记录风险评估指标
         */
        public void recordRiskAssessment(Duration duration, String riskLevel, boolean success) {
            riskAssessmentCounter.increment();
            riskAssessmentTimer.record(duration);
            
            meterRegistry.counter("smartloan.risk.assessment.by.level", "level", riskLevel).increment();
            
            if (success) {
                meterRegistry.counter("smartloan.risk.assessment.success").increment();
            } else {
                meterRegistry.counter("smartloan.risk.assessment.failure").increment();
            }
            
            log.debug("📊 风险评估指标记录 | level={} | duration={}ms | success={}", 
                riskLevel, duration.toMillis(), success);
        }

        /**
         * 记录GPU处理指标
         */
        public void recordGpuProcess(Duration duration, String taskType, double utilization) {
            gpuProcessTimer.record(duration);
            gpuUtilization.set(utilization);
            
            meterRegistry.counter("smartloan.gpu.process.by.type", "type", taskType).increment();
            
            log.debug("📊 GPU处理指标记录 | type={} | duration={}ms | utilization={}%", 
                taskType, duration.toMillis(), utilization);
        }

        /**
         * 更新活跃用户数
         */
        public void updateActiveUsers(long count) {
            activeUsers.set(count);
        }

        /**
         * 更新申请总数和通过率
         */
        public void updateApplicationMetrics(long total, double rate) {
            totalApplications.set(total);
            approvalRate.set(rate);
        }
    }

    /**
     * 系统健康检查
     */
    @Component
    public static class SmartLoanHealthIndicator implements HealthIndicator {

        @Override
        public Health health() {
            try {
                // 检查数据库连接
                boolean dbHealthy = checkDatabaseHealth();
                
                // 检查Redis连接
                boolean redisHealthy = checkRedisHealth();
                
                // 检查GPU服务
                boolean gpuHealthy = checkGpuHealth();
                
                // 检查AI服务
                boolean aiHealthy = checkAiHealth();
                
                if (dbHealthy && redisHealthy && gpuHealthy && aiHealthy) {
                    return Health.up()
                        .withDetail("database", "UP")
                        .withDetail("redis", "UP")
                        .withDetail("gpu", "UP")
                        .withDetail("ai", "UP")
                        .withDetail("timestamp", java.time.Instant.now())
                        .build();
                } else {
                    return Health.down()
                        .withDetail("database", dbHealthy ? "UP" : "DOWN")
                        .withDetail("redis", redisHealthy ? "UP" : "DOWN")
                        .withDetail("gpu", gpuHealthy ? "UP" : "DOWN")
                        .withDetail("ai", aiHealthy ? "UP" : "DOWN")
                        .withDetail("timestamp", java.time.Instant.now())
                        .build();
                }
                
            } catch (Exception e) {
                return Health.down()
                    .withDetail("error", e.getMessage())
                    .withDetail("timestamp", java.time.Instant.now())
                    .build();
            }
        }

        private boolean checkDatabaseHealth() {
            // 简化的数据库健康检查
            return true;
        }

        private boolean checkRedisHealth() {
            // 简化的Redis健康检查
            return true;
        }

        private boolean checkGpuHealth() {
            // 简化的GPU健康检查
            return true;
        }

        private boolean checkAiHealth() {
            // 简化的AI服务健康检查
            return true;
        }
    }

    /**
     * 性能监控定时任务
     */
    @Component
    @RequiredArgsConstructor
    public static class PerformanceMonitor {
        
        private final BusinessMetricsService metricsService;
        private final Map<String, Long> performanceCounters = new ConcurrentHashMap<>();

        /**
         * 每分钟收集性能指标
         */
        @Scheduled(fixedRate = 60000) // 1分钟
        public void collectPerformanceMetrics() {
            try {
                // 模拟收集活跃用户数
                long activeUsers = Math.round(Math.random() * 1000 + 500);
                metricsService.updateActiveUsers(activeUsers);
                
                // 模拟收集申请数据
                long totalApplications = performanceCounters.getOrDefault("applications", 0L) + 
                    Math.round(Math.random() * 10 + 5);
                performanceCounters.put("applications", totalApplications);
                
                double approvalRate = 0.75 + Math.random() * 0.2; // 75%-95%
                metricsService.updateApplicationMetrics(totalApplications, approvalRate);
                
                log.debug("📊 性能指标收集完成 | activeUsers={} | totalApplications={} | approvalRate={}%", 
                    activeUsers, totalApplications, approvalRate * 100);
                
            } catch (Exception e) {
                log.error("性能指标收集失败: {}", e.getMessage(), e);
            }
        }

        /**
         * 每5分钟检查系统告警
         */
        @Scheduled(fixedRate = 300000) // 5分钟
        public void checkSystemAlerts() {
            try {
                // 检查响应时间告警
                checkResponseTimeAlerts();
                
                // 检查错误率告警
                checkErrorRateAlerts();
                
                // 检查GPU使用率告警
                checkGpuUtilizationAlerts();
                
                log.debug("🚨 系统告警检查完成");
                
            } catch (Exception e) {
                log.error("系统告警检查失败: {}", e.getMessage(), e);
            }
        }

        private void checkResponseTimeAlerts() {
            // 响应时间告警逻辑
            double avgResponseTime = Math.random() * 2000; // 模拟响应时间
            if (avgResponseTime > 1000) {
                log.warn("🐌 响应时间告警 | avgResponseTime={}ms", avgResponseTime);
            }
        }

        private void checkErrorRateAlerts() {
            // 错误率告警逻辑
            double errorRate = Math.random() * 0.1; // 模拟错误率
            if (errorRate > 0.05) {
                log.warn("❌ 错误率告警 | errorRate={}%", errorRate * 100);
            }
        }

        private void checkGpuUtilizationAlerts() {
            // GPU使用率告警逻辑
            double gpuUtilization = Math.random() * 100; // 模拟GPU使用率
            if (gpuUtilization > 85) {
                log.warn("🎮 GPU使用率告警 | utilization={}%", gpuUtilization);
            }
        }
    }
}
