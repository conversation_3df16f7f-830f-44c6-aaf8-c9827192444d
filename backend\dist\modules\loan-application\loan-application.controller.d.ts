import { LoanApplicationService } from './loan-application.service';
import { LoanApplication, LoanStatus } from './entities/loan-application.entity';
export declare class LoanApplicationController {
    private readonly loanApplicationService;
    constructor(loanApplicationService: LoanApplicationService);
    create(req: any, applicationData: Partial<LoanApplication>): Promise<LoanApplication>;
    findAll(): Promise<LoanApplication[]>;
    findMyApplications(req: any): Promise<LoanApplication[]>;
    findOne(id: string): Promise<LoanApplication>;
    updateStatus(id: string, status: LoanStatus, rejectionReason?: string): Promise<LoanApplication>;
}
