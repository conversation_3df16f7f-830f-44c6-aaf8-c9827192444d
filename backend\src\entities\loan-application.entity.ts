import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { User } from './user.entity';
import { Product } from './product.entity';
import { LoanDocument } from './loan-document.entity';
import { LoanStatus } from '../enums/loan-status.enum';
import { LoanType } from '../enums/loan-type.enum';
import { LoanPurpose } from '../enums/loan-purpose.enum';
import { LoanReview } from './loan-review.entity';
import { EmploymentStatus } from '../enums/employment-status.enum';
import { CollateralType } from '../enums/collateral-type.enum';

export { LoanStatus } from '../enums/loan-status.enum';
export { LoanType } from '../enums/loan-type.enum';

@Entity('loan_applications')
export class LoanApplication {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, user => user.loanApplications)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column()
  userId: string;

  @ManyToOne(() => Product, product => product.applications)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Column()
  productId: string;

  @Column('decimal', { precision: 10, scale: 2 })
  amount: number;

  @Column('int')
  term: number;

  @Column({
    type: 'enum',
    enum: LoanType,
    default: LoanType.PERSONAL
  })
  type: LoanType;
  @Column({
    type: 'enum',
    enum: LoanPurpose,
    default: LoanPurpose.OTHER
  })
  purpose: LoanPurpose;

  @Column({
    type: 'enum',
    enum: LoanStatus,
    default: LoanStatus.PENDING
  })
  status: LoanStatus;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  monthlyPayment: number;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  totalPayment: number;

  @Column('decimal', { precision: 10, scale: 2 })
  annualIncome: number;

  @Column('decimal', { precision: 5, scale: 2 })
  debtToIncomeRatio: number;

  @Column()
  employmentStatus: string;

  @Column('int')
  workExperience: number;

  @Column('int', { nullable: true })
  creditScore: number;

  @Column('int', { nullable: true })
  riskScore: number;

  @Column('jsonb', { nullable: true })
  metadata: any;

  @Column('jsonb', { nullable: true })
  documentMetadata: any;

  @Column('jsonb', { nullable: true })
  riskAssessment: any;

  @Column('jsonb', { nullable: true })
  riskFactors: any;

  @Column({ type: 'text', nullable: true })
  riskDecision: string;

  @Column({ nullable: true })
  approvedAt: Date;

  @Column({ nullable: true })
  rejectedAt: Date;

  @Column({ nullable: true })
  cancelledAt: Date;

  @Column({ nullable: true })
  cancelledBy: string;

  @Column({ nullable: true })
  cancellationReason: string;

  @Column({ nullable: true })
  rejectionReason: string;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ nullable: true })
  rejectedBy: string;

  @Column({
    type: 'enum',
    enum: EmploymentStatus,
    default: EmploymentStatus.EMPLOYED
  })
  employmentStatusEnum: EmploymentStatus;

  @Column({
    type: 'enum',
    enum: CollateralType,
    default: CollateralType.NONE
  })
  collateral: CollateralType;

  @Column('text', { nullable: true })
  notes: string;

  @OneToMany(() => LoanDocument, document => document.application)
  documents: LoanDocument[];

  @OneToMany(() => LoanReview, review => review.loanApplication)
  reviews: LoanReview[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}