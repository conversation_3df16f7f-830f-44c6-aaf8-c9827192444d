{"riskControl": {"gpu": {"enabled": true, "serviceUrl": "http://localhost:3001", "batchSize": 32, "modelVersion": "v2", "timeout": 5000}, "cache": {"enabled": true, "ttl": 3600, "prefix": "risk:"}, "rules": {"defaultWeight": 1.0, "maxPriority": 100, "evaluationTimeout": 2000}, "scoring": {"minScore": 0, "maxScore": 100, "thresholds": {"approve": 80, "review": 60, "reject": 40}, "weights": {"creditScore": 0.4, "debtRatio": 0.3, "income": 0.2, "history": 0.1}}, "monitoring": {"enabled": true, "metricsInterval": 60000, "alertThresholds": {"errorRate": 0.05, "latency": 2000, "gpuUtilization": 0.8}}, "fallback": {"enabled": true, "maxRetries": 3, "retryDelay": 1000}}}