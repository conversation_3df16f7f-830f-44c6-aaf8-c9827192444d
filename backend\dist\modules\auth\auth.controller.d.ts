import { AuthService } from './auth.service';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(req: any): Promise<{
        access_token: string;
        user: {
            id: number;
            username: string;
            email: string;
            roles: import("../user/entities/role.entity").Role[];
        };
    }>;
}
