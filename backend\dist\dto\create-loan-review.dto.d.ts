import { ReviewStatus } from '../enums/review-status.enum';
import { ReviewType } from '../enums/review-type.enum';
export declare class CreateLoanReviewDto {
    loanApplicationId: number;
    status: ReviewStatus;
    type: ReviewType;
    comments?: string;
    riskFactors?: RiskFactorDto[];
    verificationResults?: VerificationResultDto[];
    decisionFactors?: DecisionFactorDto[];
}
export declare class RiskFactorDto {
    factor: string;
    score: number;
    weight: number;
}
export declare class VerificationResultDto {
    documentType: string;
    verified: boolean;
    notes?: string;
}
export declare class DecisionFactorDto {
    factor: string;
    impact: 'positive' | 'negative' | 'neutral';
    weight: number;
}
