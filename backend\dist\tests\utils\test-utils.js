"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMockService = exports.createMockRepository = exports.createMockUser = void 0;
const role_enum_1 = require("../enums/role.enum");
const createMockUser = () => ({
    id: '1',
    username: 'testuser',
    email: '<EMAIL>',
    password: 'hashedPassword',
    name: 'Test User',
    phone: '13800138000',
    role: role_enum_1.Role.USER,
    isActive: true,
    lastLoginAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
});
exports.createMockUser = createMockUser;
roles: ['ADMIN'];
;
const createMockRepository = () => ({
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([])
    }))
});
exports.createMockRepository = createMockRepository;
const createMockService = (methods) => ({
    ...methods
});
exports.createMockService = createMockService;
//# sourceMappingURL=test-utils.js.map