import { Controller, Post, Body, UseGuards, Req } from '@nestjs/common';
import { VerificationService } from '../services/verification.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../enums/role.enum';
import { Request } from 'express';

@Controller('verification')
@UseGuards(JwtAuthGuard, RolesGuard)
export class VerificationController {
  constructor(private readonly verificationService: VerificationService) {}

  @Post('identity')
  @Roles(Role.USER)
  async verifyIdentity(@Body() body: { imageData: string }, @Req() req: Request) {
    const userId = (req.user as any).sub;
    return this.verificationService.verifyIdentity(userId, body.imageData);
  }

  @Post('documents')
  @Roles(Role.USER)
  async verifyDocuments(@Body() body: { documents: any[] }, @Req() req: Request) {
    const userId = (req.user as any).sub;
    return this.verificationService.verifyDocuments(userId, body.documents);
  }

  @Post('liveness')
  @Roles(Role.USER)
  async performLivenessDetection(@Body() body: { videoData: string }, @Req() req: Request) {
    const userId = (req.user as any).sub;
    return this.verificationService.performLivenessDetection(userId, body.videoData);
  }
} 