import { Controller, Post, Body, Get, Param, UseGuards } from '@nestjs/common';
import { RiskAssessmentService } from '../services/risk-assessment.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('risk-assessment')
@Controller('risk-assessment')
@UseGuards(JwtAuthGuard)
export class RiskAssessmentController {
  constructor(
    private readonly riskAssessmentService: RiskAssessmentService
  ) {}
  @Post(':applicationId/assess')
  @ApiOperation({ summary: '风险评估' })
  @ApiResponse({ status: 200, description: '风险评估完成' })
  async assessRisk(
    @Param('applicationId') applicationId: string
  ) {
    return this.riskAssessmentService.assessRisk(parseInt(applicationId));
  }

  @Get('metrics')
  @ApiOperation({ summary: '获取风险指标' })
  @ApiResponse({ status: 200, description: '风险指标列表' })
  async getRiskMetrics() {
    return this.riskAssessmentService.getRiskMetrics();
  }
}
