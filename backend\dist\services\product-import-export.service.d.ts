/// <reference types="multer" />
import { Repository } from 'typeorm';
import { Product } from '../entities/product.entity';
import { LoggerService } from './logger.service';
export declare class ProductImportExportService {
    private readonly productRepository;
    private readonly logger;
    constructor(productRepository: Repository<Product>, logger: LoggerService);
    importProducts(file: Express.Multer.File): Promise<{
        total: number;
        created: number;
        updated: number;
        failed: number;
        details: ({
            code: string;
            status: string;
            error?: undefined;
        } | {
            code: string;
            status: string;
            error: any;
        })[];
    }>;
    exportProducts(): Promise<{
        buffer: any;
        filename: string;
        contentType: string;
    }>;
    exportTemplate(): Promise<{
        buffer: any;
        filename: string;
        contentType: string;
    }>;
}
