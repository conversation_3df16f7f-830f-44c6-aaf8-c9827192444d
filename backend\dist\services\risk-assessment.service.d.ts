import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { LoanApplication } from '../entities/loan-application.entity';
import { LoggerService } from './logger.service';
import { <PERSON>rror<PERSON>and<PERSON> } from '../utils/error-handler';
import { GpuService } from './gpu.service';
export declare class RiskAssessmentService {
    private readonly userRepository;
    private readonly loanApplicationRepository;
    private readonly logger;
    private readonly errorHandler;
    private readonly gpuService;
    constructor(userRepository: Repository<User>, loanApplicationRepository: Repository<LoanApplication>, logger: LoggerService, errorHandler: ErrorHandler, gpuService: GpuService);
    assessRisk(applicationId: number): Promise<any>;
    private collectRiskData;
    private triggerRiskAlert;
    getRiskMetrics(): Promise<{
        totalApplications: number;
        averageRiskScore: number;
        riskDistribution: {
            low: number;
            medium: number;
            high: number;
        };
        recentAlerts: any[];
    }>;
    private calculateAverageRiskScore;
    private calculateRiskDistribution;
    private getRecentAlerts;
}
