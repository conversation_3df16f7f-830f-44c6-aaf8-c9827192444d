import { Repository } from 'typeorm';
import { LoanApplication } from '../entities/loan-application.entity';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../logger/logger.service';
import { <PERSON>rror<PERSON>and<PERSON> } from '../utils/error-handler';
import { MonitorService } from '../monitor/monitor.service';
import { CacheService } from '../cache/cache.service';
import { RiskAssessment } from '../interfaces/risk-assessment.interface';
export declare class RiskDecisionService {
    private readonly loanApplicationRepository;
    private readonly configService;
    private readonly logger;
    private readonly errorHandler;
    private readonly monitorService;
    private readonly cacheService;
    private readonly baseInterestRate;
    private readonly maxDebtToIncomeRatio;
    private readonly minCreditScore;
    private readonly minRiskScore;
    constructor(loanApplicationRepository: Repository<LoanApplication>, configService: ConfigService, logger: LoggerService, errorHandler: ErrorHand<PERSON>, monitorService: MonitorService, cacheService: CacheService);
    assessRisk(application: LoanApplication): Promise<RiskAssessment>;
    private calculateRiskScore;
    private determineRiskLevel;
    private calculateInterestRate;
    private evaluateApplication;
    private calculateMaxLoanAmount;
    private suggestLoanTerm;
}
