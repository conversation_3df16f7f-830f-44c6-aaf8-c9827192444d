import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { Product } from '../entities/product.entity';
import { LoanProduct } from '../interfaces/loan-product.interface';
import { UserProfileDto } from '../dto/user-profile.dto';
import { CacheService } from './cache.service';
import { LoggerService } from './logger.service';

@Injectable()
export class ProductMatchingService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    private readonly cacheService: CacheService,
    private readonly logger: LoggerService
  ) {}

  async matchProducts(applicationId: string, user: User): Promise<LoanProduct[]> {
    const userProfile = await this.getUserProfile(user.id);
    const products = await this.findEligibleProducts(userProfile);
    return this.rankProducts(products, userProfile);
  }

  private async getUserProfile(userId: string): Promise<UserProfileDto> {
    const user = await this.userRepository.findOne({ 
      where: { id: userId.toString() } 
    });

    if (!user) {
      throw new NotFoundException(`User ${userId} not found`);
    }

    // Transform user data into profile
    return {
      id: user.id,
      creditScore: 750, // TODO: Get from credit service
      employmentStatus: user.employmentStatus,
      monthlyIncome: user.monthlyIncome,
      annualIncome: user.annualIncome,
      debtToIncomeRatio: 0.3, // TODO: Calculate from user data
      desiredAmount: 100000, // TODO: Get from application
      desiredTerm: 12 // TODO: Get from application
    };
  }

  private async findEligibleProducts(userProfile: UserProfileDto): Promise<LoanProduct[]> {
    // TODO: Implement actual product filtering logic
    return [];
  }

  private rankProducts(products: LoanProduct[], userProfile: UserProfileDto): LoanProduct[] {
    // TODO: Implement actual ranking logic
    return products;
  }
}