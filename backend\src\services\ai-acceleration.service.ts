import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GpuService } from './gpu.service';
import { RedisService } from './redis.service';

@Injectable()
export class AIAccelerationService {
  private readonly logger = new Logger(AIAccelerationService.name);
  private readonly BATCH_SIZE = 32;
  
  constructor(
    private gpuService: GpuService,
    private redisService: RedisService,
    private configService: ConfigService
  ) {}

  /**
   * GPU加速OCR处理
   */
  async accelerateOcr(images: Buffer[], documentTypes: string[]): Promise<any[]> {
    try {
      // 批量处理图片
      const batches = this.createBatches(images, this.BATCH_SIZE);
      const results = [];

      for (const batch of batches) {
        // 使用GPU服务的OCR批处理方法
        const batchResults = await this.gpuService.processOcrBatch(batch);
        results.push(...batchResults);
      }

      return results;
    } catch (error) {
      this.logger.error('GPU OCR处理失败', error.stack);
      throw error;
    }
  }

  /**
   * GPU加速风控模型推理
   */
  async accelerateRiskModel(data: any[]): Promise<any[]> {
    try {
      const batches = this.createBatches(data, this.BATCH_SIZE);
      const results = [];

      for (const batch of batches) {
        const batchResults = await this.gpuService.processRiskModelBatch(batch);
        results.push(...batchResults);
      }

      return results;
    } catch (error) {
      this.logger.error('GPU风控模型推理失败', error.stack);
      throw error;
    }
  }

  /**
   * GPU加速产品匹配计算
   */
  async accelerateProductMatching(userProfiles: any[], products: any[]): Promise<any[]> {
    try {
      const batchSize = Math.min(this.BATCH_SIZE, products.length);
      const batches = this.createBatches(userProfiles, batchSize);
      const results = [];

      for (const batch of batches) {
        // 将产品信息合并到批次数据中
        const batchWithProducts = batch.map(user => ({ user, products }));
        const batchResults = await this.gpuService.processMatchingBatch(batchWithProducts);
        results.push(...batchResults);
      }

      return results;
    } catch (error) {
      this.logger.error('GPU产品匹配计算失败', error.stack);
      throw error;
    }
  }

  /**
   * 创建数据批次
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }
}
