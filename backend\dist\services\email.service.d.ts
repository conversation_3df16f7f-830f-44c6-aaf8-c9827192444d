import { ConfigService } from '@nestjs/config';
export declare class EmailService {
    private configService;
    private transporter;
    constructor(configService: ConfigService);
    sendPasswordResetEmail(email: string, newPassword: string): Promise<void>;
    sendLoanApplicationNotification(email: string, applicationId: string): Promise<void>;
    sendLoanStatusUpdate(email: string, applicationId: string, status: string): Promise<void>;
}
