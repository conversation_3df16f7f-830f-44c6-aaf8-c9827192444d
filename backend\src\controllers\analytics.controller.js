"use strict";
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsController = void 0;
var common_1 = require("@nestjs/common");
var jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
var roles_guard_1 = require("../auth/guards/roles.guard");
var roles_decorator_1 = require("../auth/decorators/roles.decorator");
var AnalyticsController = function () {
    var _classDecorators = [(0, common_1.Controller)('analytics'), (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard)];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _instanceExtraInitializers = [];
    var _getDashboardData_decorators;
    var _exportData_decorators;
    var _predictTrends_decorators;
    var _detectAnomalies_decorators;
    var _getRecommendations_decorators;
    var AnalyticsController = _classThis = /** @class */ (function () {
        function AnalyticsController_1(analyticsService, exportService, logger, errorHandler) {
            this.analyticsService = (__runInitializers(this, _instanceExtraInitializers), analyticsService);
            this.exportService = exportService;
            this.logger = logger;
            this.errorHandler = errorHandler;
        }
        AnalyticsController_1.prototype.getDashboardData = function (query, req) {
            return __awaiter(this, void 0, void 0, function () {
                var data, error_1;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            this.logger.log("\u83B7\u53D6\u4EEA\u8868\u76D8\u6570\u636E - \u7528\u6237ID: ".concat(req.user.id), 'AnalyticsController');
                            return [4 /*yield*/, this.analyticsService.getDashboardData(query)];
                        case 1:
                            data = _a.sent();
                            return [2 /*return*/, { success: true, data: data }];
                        case 2:
                            error_1 = _a.sent();
                            return [2 /*return*/, this.errorHandler.handle(error_1)];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        AnalyticsController_1.prototype.exportData = function (query, req) {
            return __awaiter(this, void 0, void 0, function () {
                var data, error_2;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            this.logger.log("\u5BFC\u51FA\u6570\u636E - \u7528\u6237ID: ".concat(req.user.id), 'AnalyticsController');
                            return [4 /*yield*/, this.exportService.exportData(query)];
                        case 1:
                            data = _a.sent();
                            return [2 /*return*/, { success: true, data: data }];
                        case 2:
                            error_2 = _a.sent();
                            return [2 /*return*/, this.errorHandler.handle(error_2)];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        AnalyticsController_1.prototype.predictTrends = function (data, req) {
            return __awaiter(this, void 0, void 0, function () {
                var predictions, error_3;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            this.logger.log("\u9884\u6D4B\u8D8B\u52BF - \u7528\u6237ID: ".concat(req.user.id), 'AnalyticsController');
                            return [4 /*yield*/, this.analyticsService.predictTrends(data)];
                        case 1:
                            predictions = _a.sent();
                            return [2 /*return*/, { success: true, data: predictions }];
                        case 2:
                            error_3 = _a.sent();
                            return [2 /*return*/, this.errorHandler.handle(error_3)];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        AnalyticsController_1.prototype.detectAnomalies = function (query, req) {
            return __awaiter(this, void 0, void 0, function () {
                var anomalies, error_4;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            this.logger.log("\u68C0\u6D4B\u5F02\u5E38 - \u7528\u6237ID: ".concat(req.user.id), 'AnalyticsController');
                            return [4 /*yield*/, this.analyticsService.detectAnomalies(query)];
                        case 1:
                            anomalies = _a.sent();
                            return [2 /*return*/, { success: true, data: anomalies }];
                        case 2:
                            error_4 = _a.sent();
                            return [2 /*return*/, this.errorHandler.handle(error_4)];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        AnalyticsController_1.prototype.getRecommendations = function (query, req) {
            return __awaiter(this, void 0, void 0, function () {
                var recommendations, error_5;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            this.logger.log("\u83B7\u53D6\u63A8\u8350 - \u7528\u6237ID: ".concat(req.user.id), 'AnalyticsController');
                            return [4 /*yield*/, this.analyticsService.getRecommendations(query)];
                        case 1:
                            recommendations = _a.sent();
                            return [2 /*return*/, { success: true, data: recommendations }];
                        case 2:
                            error_5 = _a.sent();
                            return [2 /*return*/, this.errorHandler.handle(error_5)];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        return AnalyticsController_1;
    }());
    __setFunctionName(_classThis, "AnalyticsController");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        _getDashboardData_decorators = [(0, common_1.Get)('dashboard'), (0, roles_decorator_1.Roles)('admin', 'analyst')];
        _exportData_decorators = [(0, common_1.Get)('export'), (0, roles_decorator_1.Roles)('admin', 'analyst')];
        _predictTrends_decorators = [(0, common_1.Post)('predict'), (0, roles_decorator_1.Roles)('admin', 'analyst')];
        _detectAnomalies_decorators = [(0, common_1.Get)('anomalies'), (0, roles_decorator_1.Roles)('admin', 'analyst')];
        _getRecommendations_decorators = [(0, common_1.Get)('recommendations'), (0, roles_decorator_1.Roles)('admin', 'analyst')];
        __esDecorate(_classThis, null, _getDashboardData_decorators, { kind: "method", name: "getDashboardData", static: false, private: false, access: { has: function (obj) { return "getDashboardData" in obj; }, get: function (obj) { return obj.getDashboardData; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _exportData_decorators, { kind: "method", name: "exportData", static: false, private: false, access: { has: function (obj) { return "exportData" in obj; }, get: function (obj) { return obj.exportData; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _predictTrends_decorators, { kind: "method", name: "predictTrends", static: false, private: false, access: { has: function (obj) { return "predictTrends" in obj; }, get: function (obj) { return obj.predictTrends; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _detectAnomalies_decorators, { kind: "method", name: "detectAnomalies", static: false, private: false, access: { has: function (obj) { return "detectAnomalies" in obj; }, get: function (obj) { return obj.detectAnomalies; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getRecommendations_decorators, { kind: "method", name: "getRecommendations", static: false, private: false, access: { has: function (obj) { return "getRecommendations" in obj; }, get: function (obj) { return obj.getRecommendations; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        AnalyticsController = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return AnalyticsController = _classThis;
}();
exports.AnalyticsController = AnalyticsController;
