/**
 * SmartLoan 2025 设计Token系统
 * 科技金融+国潮美学融合风格
 * 遵循Apple Human Interface Guidelines
 */

// ==================== 色彩系统 ====================
:root {
  // 主色系 - 科技蓝
  --color-primary: #1A3A8F;
  --color-primary-light: #2E4BA6;
  --color-primary-dark: #0F2A7A;
  --color-primary-alpha-10: rgba(26, 58, 143, 0.1);
  --color-primary-alpha-20: rgba(26, 58, 143, 0.2);
  --color-primary-alpha-50: rgba(26, 58, 143, 0.5);

  // 辅助色 - 金色
  --color-secondary: #D4AF37;
  --color-secondary-light: #E6C55A;
  --color-secondary-dark: #B8941F;
  --color-secondary-alpha-10: rgba(212, 175, 55, 0.1);
  --color-secondary-alpha-20: rgba(212, 175, 55, 0.2);

  // 中性色系
  --color-neutral-900: #2C3E50;  // 深灰 - 文字标题
  --color-neutral-800: #34495E;
  --color-neutral-700: #5D6D7E;
  --color-neutral-600: #85929E;
  --color-neutral-500: #AEB6BF;
  --color-neutral-400: #D5DBDB;
  --color-neutral-300: #ECF0F1;  // 浅灰 - 背景色
  --color-neutral-200: #F8F9FA;
  --color-neutral-100: #FFFFFF;

  // 功能色系
  --color-success: #27AE60;      // 成功色
  --color-success-light: #58D68D;
  --color-success-dark: #1E8449;
  --color-success-alpha-10: rgba(39, 174, 96, 0.1);

  --color-warning: #F39C12;      // 警告色
  --color-warning-light: #F8C471;
  --color-warning-dark: #D68910;

  --color-error: #E74C3C;        // 错误色
  --color-error-light: #EC7063;
  --color-error-dark: #C0392B;
  --color-error-alpha-10: rgba(231, 76, 60, 0.1);

  --color-info: #3498DB;         // 信息色
  --color-info-light: #5DADE2;
  --color-info-dark: #2980B9;

  // 渐变色系
  --gradient-primary: linear-gradient(135deg, #1A3A8F 0%, #2E4BA6 100%);
  --gradient-secondary: linear-gradient(135deg, #D4AF37 0%, #E6C55A 100%);
  --gradient-success: linear-gradient(135deg, #27AE60 0%, #58D68D 100%);
  --gradient-tech: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-ink: linear-gradient(180deg, #2C3E50 0%, #34495E 100%);
}

// ==================== 字体系统 ====================
:root {
  // 字体族
  --font-family-primary: 'SF Pro Display', 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-secondary: 'Helvetica Neue', 'Microsoft YaHei', sans-serif;
  --font-family-mono: 'SF Mono', 'Monaco', 'Consolas', monospace;

  // 字号层级
  --text-xxl: 28px;    // 大标题
  --text-xl: 22px;     // 模块标题
  --text-lg: 18px;     // 重点内容
  --text-md: 16px;     // 正文
  --text-sm: 14px;     // 辅助信息
  --text-xs: 12px;     // 标签文字

  // 字重
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  // 行高
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
}

// ==================== 间距系统 ====================
:root {
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  --spacing-xxxl: 64px;

  // 组件间距
  --component-padding-sm: 12px;
  --component-padding-md: 20px;
  --component-padding-lg: 32px;

  // 容器间距
  --container-padding: 20px;
  --container-max-width: 1200px;
}

// ==================== 圆角系统 ====================
:root {
  --border-radius-xs: 4px;
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 20px;
  --border-radius-full: 50%;

  // 组件圆角
  --border-radius-button: 8px;
  --border-radius-card: 12px;
  --border-radius-modal: 16px;
}

// ==================== 阴影系统 ====================
:root {
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

  // 特殊阴影
  --shadow-card: 0 4px 12px rgba(26, 58, 143, 0.1);
  --shadow-button: 0 2px 8px rgba(26, 58, 143, 0.2);
  --shadow-modal: 0 20px 40px rgba(0, 0, 0, 0.15);
}

// ==================== 动画系统 ====================
:root {
  // 动画时长
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  // 缓动函数
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  // 特殊动画
  --transition-all: all var(--duration-normal) var(--ease-in-out);
  --transition-transform: transform var(--duration-normal) var(--ease-in-out);
  --transition-opacity: opacity var(--duration-normal) var(--ease-in-out);
}

// ==================== Z-index系统 ====================
:root {
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
}

// ==================== 断点系统 ====================
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 1024px;
  --breakpoint-lg: 1280px;
  --breakpoint-xl: 1536px;
}

// ==================== 组件特定变量 ====================
:root {
  // 按钮
  --button-height-sm: 32px;
  --button-height-md: 40px;
  --button-height-lg: 48px;
  --button-padding-x: 16px;

  // 输入框
  --input-height: 48px;
  --input-padding-x: 16px;
  --input-border-width: 1px;
  --input-border-color: var(--color-neutral-400);
  --input-border-color-focus: var(--color-primary);

  // 卡片
  --card-padding: 24px;
  --card-background: var(--color-neutral-100);
  --card-border: 1px solid var(--color-neutral-300);

  // 导航
  --nav-height: 64px;
  --nav-background: var(--color-neutral-100);
  --nav-border: 1px solid var(--color-neutral-300);

  // 侧边栏
  --sidebar-width: 280px;
  --sidebar-background: var(--color-neutral-100);
}

// ==================== 响应式混合器 ====================
@mixin mobile-only {
  @media (max-width: #{var(--breakpoint-sm) - 1px}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: #{var(--breakpoint-sm)}) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: #{var(--breakpoint-md)}) {
    @content;
  }
}

@mixin large-desktop-up {
  @media (min-width: #{var(--breakpoint-lg)}) {
    @content;
  }
}

// ==================== 工具类混合器 ====================
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// ==================== 国潮特色样式 ====================
.ink-wash-effect {
  background: linear-gradient(135deg, 
    rgba(44, 62, 80, 0.1) 0%, 
    rgba(52, 73, 94, 0.05) 50%, 
    rgba(44, 62, 80, 0.1) 100%);
  backdrop-filter: blur(10px);
}

.tech-gradient {
  background: var(--gradient-tech);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.golden-accent {
  color: var(--color-secondary);
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
}

// ==================== 动画关键帧 ====================
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// ==================== 打印样式 ====================
@media print {
  :root {
    --color-primary: #000;
    --color-secondary: #666;
    --shadow-card: none;
    --shadow-button: none;
  }
  
  .no-print {
    display: none !important;
  }
}
