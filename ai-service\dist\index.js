"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const evaluation_1 = require("./services/evaluation");
const app = (0, express_1.default)();
const port = process.env.PORT || 9527;
// 中间件
app.use((0, cors_1.default)());
app.use(express_1.default.json());
// 健康检查路由
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        service: 'AI评估服务'
    });
});
// 贷款评估路由
app.post('/evaluate', async (req, res) => {
    try {
        const result = await (0, evaluation_1.evaluateLoan)(req.body);
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('评估失败：', error);
        res.status(500).json({
            success: false,
            error: '评估失败'
        });
    }
});
app.listen(port, () => {
    console.log(`AI服务运行在 http://localhost:${port}`);
});
