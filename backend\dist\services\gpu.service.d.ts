/// <reference types="node" />
/// <reference types="node" />
import { ConfigService } from '@nestjs/config';
export declare class GpuService {
    private configService;
    private readonly logger;
    private readonly gpuEnabled;
    private readonly cpuFallback;
    constructor(configService: ConfigService);
    prepareProductTensor(products: any[]): Promise<any>;
    prepareCriteriaTensor(criteria: any): Promise<any>;
    processOcrBatch(images: Buffer[]): Promise<any[]>;
    processRiskModelBatch(applications: any[]): Promise<any[]>;
    processMatchingBatch(requests: any[]): Promise<any[]>;
    processDocument(imageData: string, options: {
        documentType: string;
    }): Promise<{
        data: any;
        confidence: number;
    }>;
    private processDocumentCpu;
    private prepareProductTensorCpu;
    private prepareCriteriaTensorCpu;
    private processOcrSingle;
    private processRiskModelSingle;
    private processMatchingSingle;
    accelerateFaceRecognition(imageData: Buffer | any): Promise<any>;
    accelerateProductMatching(userProfile: any, products: any[]): Promise<any>;
    accelerateRiskAssessment(riskData: any): Promise<any>;
    private processFaceRecognitionCpu;
    private processProductMatchingCpu;
    private processRiskAssessmentCpu;
    checkGpuAvailability(): Promise<boolean>;
}
