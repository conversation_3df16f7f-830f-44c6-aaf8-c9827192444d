import { Module, NestModule, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ThrottlerModule } from '@nestjs/throttler';
import { CacheModule } from '@nestjs/cache-manager';
import { LoggerService } from './services/logger.service';
import { MonitorService } from './services/monitor.service';
import { CacheService } from './services/cache.service';
import { ProductRecommendationService } from './services/product-recommendation.service';
import { LoanModule } from './modules/loan/loan.module';
import { ProductModule } from './modules/product/product.module';
import { RiskModule } from './modules/risk/risk.module';
import { AuditModule } from './modules/audit/audit.module';
import configuration from './config/configuration';
import { MonitoringModule } from './modules/monitoring.module';
import { MonitoringMiddleware } from './middleware/monitoring.middleware';
import { User } from './entities/user.entity';
import { LoanApplication } from './entities/loan-application.entity';
import { ProductMatchingService } from './services/product-matching.service';
import { GpuService } from './services/gpu.service';
import { VerificationService } from './services/verification.service';
import { RiskAssessmentService } from './services/risk-assessment.service';
import { VerificationController } from './controllers/verification.controller';
import { RiskAssessmentController } from './controllers/risk-assessment.controller';
import { HealthController } from './health/health.controller';
import { ErrorHandler } from './utils/error-handler';
import { WinstonModule } from 'nest-winston';
import { winstonConfig } from './config/monitoring.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: 'localhost',
      port: 5432,
      username: 'postgres',
      password: 'postgres',
      database: 'smartloan',
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: true,
    }),
    TypeOrmModule.forFeature([User, LoanApplication]),
    EventEmitterModule.forRoot(),
    ThrottlerModule.forRoot([{
      ttl: 60,
      limit: 10,
    }]),
    CacheModule.register({
      isGlobal: true,
      ttl: 60000, // 1 minute
    }),
    LoanModule,
    ProductModule,
    RiskModule,
    AuditModule,
    MonitoringModule,
    WinstonModule.forRoot(winstonConfig),
  ],
  controllers: [
    VerificationController,
    RiskAssessmentController,
    HealthController,
  ],
  providers: [
    ProductMatchingService,
    GpuService,
    VerificationService,
    RiskAssessmentService,
    LoggerService,
    ErrorHandler,
    MonitorService,
    CacheService,
    ProductRecommendationService,
  ],
  exports: [
    LoggerService,
    MonitorService,
    CacheService,
    ProductRecommendationService,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(MonitoringMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
} 