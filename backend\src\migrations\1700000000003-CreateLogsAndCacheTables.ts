import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateLogsAndCacheTables1700000000003 implements MigrationInterface {
  name = 'CreateLogsAndCacheTables1700000000003';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建用户日志表
    await queryRunner.query(`
      CREATE TABLE "user_logs" (
        "id" SERIAL PRIMARY KEY,
        "user_id" INTEGER NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
        "action" VARCHAR NOT NULL,
        "details" JSONB,
        "ip_address" VARCHAR,
        "user_agent" VARCHAR,
        "created_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // 创建系统日志表
    await queryRunner.query(`
      CREATE TABLE "system_logs" (
        "id" SERIAL PRIMARY KEY,
        "level" VARCHAR NOT NULL,
        "message" TEXT NOT NULL,
        "context" JSONB,
        "timestamp" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // 创建缓存表
    await queryRunner.query(`
      CREATE TABLE "cache" (
        "key" VARCHAR PRIMARY KEY,
        "value" TEXT NOT NULL,
        "expires_at" TIMESTAMP,
        "created_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // 创建索引
    await queryRunner.query(`
      CREATE INDEX "idx_user_logs_user_id" ON "user_logs"("user_id");
      CREATE INDEX "idx_user_logs_action" ON "user_logs"("action");
      CREATE INDEX "idx_system_logs_level" ON "system_logs"("level");
      CREATE INDEX "idx_system_logs_timestamp" ON "system_logs"("timestamp");
      CREATE INDEX "idx_cache_expires_at" ON "cache"("expires_at");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "user_logs"`);
    await queryRunner.query(`DROP TABLE "system_logs"`);
    await queryRunner.query(`DROP TABLE "cache"`);
  }
} 