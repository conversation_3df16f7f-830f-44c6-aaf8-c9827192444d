import { ProductService, ProductMatchCriteria } from './product.service';
export declare class ProductController {
    private readonly productService;
    constructor(productService: ProductService);
    findAll(type?: string): Promise<import("./entities/financial-product.entity").FinancialProduct[]>;
    findById(id: number): Promise<{
        success: boolean;
        data: import("./entities/financial-product.entity").FinancialProduct;
        message: string;
    }>;
    matchProducts(criteria: {
        amount: number;
        term_months: number;
        product_type?: string;
    }): Promise<{
        success: boolean;
        data: import("./entities/financial-product.entity").FinancialProduct[];
        message: string;
    }>;
    smartMatch(criteria: ProductMatchCriteria): Promise<{
        success: boolean;
        data: import("./product.service").ProductMatch[];
        message: string;
        total: number;
    }>;
    calculateLoan(loanData: {
        amount: number;
        term_months: number;
        annual_rate: number;
    }): Promise<{
        success: boolean;
        data: {
            monthlyPayment: number;
            totalInterest: number;
            totalPayment: number;
        };
        message: string;
    }>;
}
