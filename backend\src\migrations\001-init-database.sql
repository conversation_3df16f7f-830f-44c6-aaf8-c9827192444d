﻿-- SmartLoan 完整数据库结构设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS smartloan;
USE smartloan;

-- 1. 用户表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    phone VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(50) NOT NULL,
    real_name VARCHAR(50),
    id_card VARCHAR(18),
    avatar_url VARCHAR(500),
    gender SMALLINT DEFAULT 0,
    birth_date DATE,
    address TEXT,
    profession VARCHAR(100),
    company VARCHAR(200),
    annual_income DECIMAL(15,2),
    credit_score INTEGER DEFAULT 0,
    risk_level SMALLINT DEFAULT 0,
    status SMALLINT DEFAULT 1,
    last_login_at TIMESTAMP,
    last_login_ip INET,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

-- 2. 金融产品表
CREATE TABLE products (
    id BIGSERIAL PRIMARY KEY,
    institution_id BIGINT NOT NULL,
    product_code VARCHAR(50) UNIQUE NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    product_type SMALLINT NOT NULL,
    category VARCHAR(50),
    min_amount DECIMAL(15,2) NOT NULL,
    max_amount DECIMAL(15,2) NOT NULL,
    min_rate DECIMAL(5,4) NOT NULL,
    max_rate DECIMAL(5,4) NOT NULL,
    min_term INTEGER NOT NULL,
    max_term INTEGER NOT NULL,
    approval_time INTEGER DEFAULT 24,
    requirements TEXT,
    features JSON,
    hot_score INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0,
    tags TEXT[],
    status SMALLINT DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 3. 金融机构表
CREATE TABLE institutions (
    id BIGSERIAL PRIMARY KEY,
    institution_code VARCHAR(50) UNIQUE NOT NULL,
    institution_name VARCHAR(200) NOT NULL,
    institution_type SMALLINT NOT NULL,
    license_number VARCHAR(100),
    logo_url VARCHAR(500),
    description TEXT,
    contact_phone VARCHAR(50),
    contact_email VARCHAR(100),
    website VARCHAR(200),
    address TEXT,
    registered_capital DECIMAL(15,2),
    rating VARCHAR(10),
    cooperation_status SMALLINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 4. 贷款申请表
CREATE TABLE loan_applications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    application_no VARCHAR(50) UNIQUE NOT NULL,
    apply_amount DECIMAL(15,2) NOT NULL,
    apply_term INTEGER NOT NULL,
    purpose SMALLINT NOT NULL,
    purpose_desc TEXT,
    monthly_income DECIMAL(15,2),
    work_years INTEGER,
    house_status SMALLINT,
    marriage_status SMALLINT,
    education SMALLINT,
    contact_name VARCHAR(50),
    contact_phone VARCHAR(20),
    contact_relation SMALLINT,
    emergency_contact VARCHAR(50),
    emergency_phone VARCHAR(20),
    bank_flow JSON,
    status SMALLINT DEFAULT 1,
    approval_amount DECIMAL(15,2),
    approval_rate DECIMAL(5,4),
    approval_term INTEGER,
    refuse_reason TEXT,
    apply_ip INET,
    apply_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    audit_time TIMESTAMP,
    approval_time TIMESTAMP,
    loan_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_id_card ON users(id_card);
CREATE INDEX idx_products_type ON products(product_type);
CREATE INDEX idx_applications_user ON loan_applications(user_id);
