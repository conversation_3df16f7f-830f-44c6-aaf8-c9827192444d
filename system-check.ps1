# SmartLoan System Health Check
Write-Host "SmartLoan System Health Check" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green

# Check backend API
Write-Host "`nChecking backend API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Backend API is running" -ForegroundColor Green
        $healthData = $response.Content | ConvertFrom-Json
        Write-Host "   Service: $($healthData.service)" -ForegroundColor White
        Write-Host "   Version: $($healthData.version)" -ForegroundColor White
    }
}
catch {
    Write-Host "❌ Backend API is not running" -ForegroundColor Red
    Write-Host "   Run: node quick-api.cjs" -ForegroundColor Gray
}

# Check database
Write-Host "`nChecking database..." -ForegroundColor Yellow
try {
    $dockerStatus = docker ps --filter "name=postgres" --format "{{.Names}}"
    if ($dockerStatus -match "postgres") {
        Write-Host "✅ PostgreSQL database is running" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️ PostgreSQL database is not running" -ForegroundColor Yellow
        Write-Host "   Run: docker-compose up -d" -ForegroundColor Gray
    }
}
catch {
    Write-Host "❌ Cannot check Docker status" -ForegroundColor Red
}

# Check critical files
Write-Host "`nChecking critical files..." -ForegroundColor Yellow
$files = @("demo.html", "quick-api.cjs", "README.md")
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    }
    else {
        Write-Host "❌ $file" -ForegroundColor Red
    }
}

# Check directories
Write-Host "`nChecking directories..." -ForegroundColor Yellow
$dirs = @("frontend", "backend", "ai-service")
foreach ($dir in $dirs) {
    if (Test-Path $dir) {
        Write-Host "✅ $dir/" -ForegroundColor Green
    }
    else {
        Write-Host "❌ $dir/" -ForegroundColor Red
    }
}

# Test API functionality
Write-Host "`nTesting API functionality..." -ForegroundColor Yellow
try {
    $testData = @{
        amount = 100000
        term_months = 24
        product_type = "personal"
        user_profile = @{
            income = 15000
            credit_score = 750
        }
    } | ConvertTo-Json -Depth 3

    $apiResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/products/match/smart" -Method POST -Body $testData -ContentType "application/json" -UseBasicParsing -TimeoutSec 10
    
    if ($apiResponse.StatusCode -eq 200) {
        Write-Host "✅ Smart matching API works" -ForegroundColor Green
    }
}
catch {
    Write-Host "❌ Smart matching API test failed" -ForegroundColor Red
}

# Summary
Write-Host "`nSystem Status Summary" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan
Write-Host "Check completed at: $(Get-Date)" -ForegroundColor White

Write-Host "`nRecommended actions:" -ForegroundColor Green
Write-Host "1. If backend API not running: node quick-api.cjs" -ForegroundColor White
Write-Host "2. If database not running: docker-compose up -d" -ForegroundColor White
Write-Host "3. Open demo: .\demo-start.ps1 or double-click demo.html" -ForegroundColor White

Write-Host "`nHealth check completed!" -ForegroundColor Green
