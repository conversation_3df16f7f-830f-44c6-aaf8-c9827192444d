import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInitialTables1716969600000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建用户表
    await queryRunner.query(`
      CREATE TYPE "public"."role_enum" AS ENUM ('user', 'admin', 'loan_officer', 'risk_analyst')
    `);

    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "username" character varying NOT NULL,
        "email" character varying NOT NULL,
        "password" character varying NOT NULL,
        "name" character varying,
        "phone" character varying,
        "roles" role_enum[] NOT NULL DEFAULT '{user}',
        "isActive" boolean NOT NULL DEFAULT true,
        "lastLoginAt" TIMESTAMP,
        "lastLoginIp" character varying,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "preferences" jsonb,
        "metadata" jsonb,
        CONSTRAINT "UQ_users_username" UNIQUE ("username"),
        CONSTRAINT "UQ_users_email" UNIQUE ("email"),
        CONSTRAINT "PK_users" PRIMARY KEY ("id")
      )
    `);

    // 创建风控数据表
    await queryRunner.query(`
      CREATE TABLE "risk_data" (
        "id" SERIAL NOT NULL,
        "timeRange" character varying NOT NULL,
        "data" jsonb NOT NULL,
        "timestamp" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_risk_data" PRIMARY KEY ("id")
      )
    `);

    // 创建告警表
    await queryRunner.query(`
      CREATE TABLE "alerts" (
        "id" SERIAL NOT NULL,
        "level" character varying NOT NULL,
        "content" character varying NOT NULL,
        "type" character varying NOT NULL,
        "timestamp" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_alerts" PRIMARY KEY ("id")
      )
    `);

    // 创建产品表
    await queryRunner.query(`
      CREATE TABLE "products" (
        "id" SERIAL NOT NULL,
        "name" character varying NOT NULL,
        "description" text,
        "type" character varying NOT NULL,
        "interest_rate" decimal NOT NULL,
        "term_range" int4range NOT NULL,
        "amount_range" numrange NOT NULL,
        "requirements" jsonb,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_products" PRIMARY KEY ("id")
      )
    `);

    // 创建审核记录表
    await queryRunner.query(`
      CREATE TABLE "audit_records" (
        "id" SERIAL NOT NULL,
        "user_id" uuid NOT NULL,
        "application_id" uuid NOT NULL,
        "status" character varying NOT NULL,
        "reviewer_id" uuid,
        "review_notes" text,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_audit_records" PRIMARY KEY ("id"),
        CONSTRAINT "FK_audit_records_users" FOREIGN KEY ("user_id") REFERENCES "users"("id"),
        CONSTRAINT "FK_audit_records_reviewers" FOREIGN KEY ("reviewer_id") REFERENCES "users"("id")
      )
    `);

    // 创建风控模型表
    await queryRunner.query(`
      CREATE TABLE "risk_models" (
        "id" SERIAL NOT NULL,
        "name" character varying NOT NULL,
        "version" character varying NOT NULL,
        "type" character varying NOT NULL,
        "parameters" jsonb NOT NULL,
        "performance_metrics" jsonb,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_risk_models" PRIMARY KEY ("id")
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "alerts"`);
    await queryRunner.query(`DROP TABLE "risk_data"`);
    await queryRunner.query(`DROP TABLE "users"`);
    await queryRunner.query(`DROP TYPE "public"."role_enum"`);
    await queryRunner.query(`DROP TABLE "products"`);
    await queryRunner.query(`DROP TABLE "audit_records"`);
    await queryRunner.query(`DROP TABLE "risk_models"`);
  }
} 