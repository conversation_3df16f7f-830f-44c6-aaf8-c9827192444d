export type RiskLevel = 'LOW' | 'MEDIUM' | 'HIGH';
export interface CreditFactors {
    creditScore: number;
    debtToIncomeRatio: number;
    employmentStatus: string;
    annualIncome: number;
    paymentHistory?: number;
}
export interface RiskAssessment {
    score: number;
    approved: boolean;
    reasons: string[];
    riskLevel: RiskLevel;
    creditFactors: CreditFactors;
    recommendedInterestRate: number;
    maxLoanAmount?: number;
    suggestedTerm?: number;
    stressTestResults?: StressTestResults;
    additionalInfo?: Record<string, any>;
}
export interface FraudCheckResult {
    isFraud: boolean;
    riskFactors: string[];
    confidence: number;
}
export interface StressTestResults {
    passed: boolean;
    analysis: {
        defaultProbability: number;
        recoveryRate: number;
        expectedLoss: number;
    };
    recommendations?: string[];
}
