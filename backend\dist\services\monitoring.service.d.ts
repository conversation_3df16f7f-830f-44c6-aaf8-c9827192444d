export declare class MonitoringService {
    recordMetric(metricName: string, value: number): void;
    recordOperationDuration(operation: string, durationMs: number): void;
    recordError(operation: string, error: Error): void;
    recordSuccess(operation: string): void;
    private readonly logger;
    private emailTransporter;
    constructor();
    recordHttpRequestDuration(method: string, route: string, statusCode: number, duration: number): Promise<void>;
    recordRiskCalculation(type: string, duration: number): Promise<void>;
    recordAlertProcessing(level: string, duration: number): Promise<void>;
    sendAlert(type: string, data: any): Promise<void>;
    private formatAlertMessage;
}
