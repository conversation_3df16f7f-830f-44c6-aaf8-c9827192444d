import { MigrationInterface, QueryRunner } from 'typeorm';

export class Add<PERSON>eri<PERSON>Fields1709123456789 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE users
      ADD COLUMN is_verified BOOLEAN DEFAULT FALSE,
      ADD COLUMN verification_date TIMESTAMP,
      ADD COLUMN documents_verified BOOLEAN DEFAULT FALSE,
      ADD COLUMN documents_verification_date TIMESTAMP,
      ADD COLUMN liveness_verified BOOLEAN DEFAULT FALSE,
      ADD COLUMN liveness_verification_date TIMESTAMP,
      ADD COLUMN verification_documents JSONB,
      ADD COLUMN face_data JSONB;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE users
      DROP COLUMN is_verified,
      DROP COLUMN verification_date,
      DROP COLUMN documents_verified,
      DROP COLUMN documents_verification_date,
      DROP COLUMN liveness_verified,
      DROP COLUMN liveness_verification_date,
      DROP COLUMN verification_documents,
      DROP COLUMN face_data;
    `);
  }
} 