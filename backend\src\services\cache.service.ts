import { Injectable } from '@nestjs/common';
import { RedisService } from './redis.service';
import { LoggerService } from './logger.service';

@Injectable()
export class CacheService {
  constructor(
    private readonly redisService: RedisService,
    private readonly logger: LoggerService
  ) {}

  async get<T>(key: string): Promise<T | null> {
    try {
      const data = await this.redisService.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      this.logger.error(`获取缓存 ${key} 失败`, error);
      return null;
    }
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      await this.redisService.set(key, JSON.stringify(value), ttl);
    } catch (error) {
      this.logger.error(`设置缓存 ${key} 失败`, error);
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.redisService.del(key);
    } catch (error) {
      this.logger.error(`删除缓存 ${key} 失败`, error);
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      return await this.redisService.exists(key);
    } catch (error) {
      this.logger.error(`检查缓存 ${key} 是否存在失败`, error);
      return false;
    }
  }

  async incr(key: string): Promise<number> {
    try {
      return await this.redisService.incr(key);
    } catch (error) {
      this.logger.error(`增加缓存 ${key} 的值失败`, error);
      return 0;
    }
  }

  async decr(key: string): Promise<number> {
    try {
      return await this.redisService.decr(key);
    } catch (error) {
      this.logger.error(`减少缓存 ${key} 的值失败`, error);
      return 0;
    }
  }

  async expire(key: string, seconds: number): Promise<void> {
    try {
      await this.redisService.expire(key, seconds);
    } catch (error) {
      this.logger.error(`设置缓存 ${key} 的过期时间失败`, error);
    }
  }

  async ttl(key: string): Promise<number> {
    try {
      return await this.redisService.ttl(key);
    } catch (error) {
      this.logger.error(`获取缓存 ${key} 的剩余生存时间失败`, error);
      return -1;
    }
  }

  async keys(pattern: string): Promise<string[]> {
    try {
      return await this.redisService.keys(pattern);
    } catch (error) {
      this.logger.error(`获取缓存键模式 ${pattern} 的匹配键失败`, error);
      return [];
    }
  }

  async flushAll(): Promise<void> {
    try {
      await this.redisService.flushAll();
    } catch (error) {
      this.logger.error('清空缓存失败', error);
    }
  }

  // 获取或设置缓存的通用方法
  async getOrSet<T>(key: string, factory: () => Promise<T>, ttl?: number): Promise<T> {
    try {
      // 先尝试从缓存获取
      const cached = await this.get<T>(key);
      if (cached !== null) {
        return cached;
      }

      // 缓存不存在，调用工厂函数获取数据
      const data = await factory();
      
      // 将数据存入缓存
      await this.set(key, data, ttl);
      
      return data;
    } catch (error) {
      this.logger.error(`getOrSet 操作失败 ${key}`, error);
      // 出错时直接调用工厂函数获取数据，不使用缓存
      return await factory();
    }
  }

  // 缓存用户数据
  async cacheUser(userId: string, userData: any): Promise<void> {
    const key = `user:${userId}`;
    await this.set(key, userData, 3600); // 1小时过期
  }

  // 获取缓存的用户数据
  async getCachedUser(userId: string): Promise<any> {
    const key = `user:${userId}`;
    return this.get(key);
  }

  // 缓存贷款申请数据
  async cacheLoanApplication(applicationId: string, applicationData: any): Promise<void> {
    const key = `loan:${applicationId}`;
    await this.set(key, applicationData, 3600); // 1小时过期
  }

  // 获取缓存的贷款申请数据
  async getCachedLoanApplication(applicationId: string): Promise<any> {
    const key = `loan:${applicationId}`;
    return this.get(key);
  }

  // 缓存产品数据
  async cacheProduct(productId: string, productData: any): Promise<void> {
    const key = `product:${productId}`;
    await this.set(key, productData, 3600); // 1小时过期
  }

  // 获取缓存的产品数据
  async getCachedProduct(productId: string): Promise<any> {
    const key = `product:${productId}`;
    return this.get(key);
  }

  // 缓存产品列表
  async cacheProductList(products: any[]): Promise<void> {
    const key = 'products:list';
    await this.set(key, products, 3600); // 1小时过期
  }

  // 获取缓存的产品列表
  async getCachedProductList(): Promise<any[]> {
    const key = 'products:list';
    return this.get(key) || [];
  }

  // 缓存用户会话
  async cacheSession(sessionId: string, sessionData: any): Promise<void> {
    const key = `session:${sessionId}`;
    await this.set(key, sessionData, 86400); // 24小时过期
  }

  // 获取缓存的用户会话
  async getCachedSession(sessionId: string): Promise<any> {
    const key = `session:${sessionId}`;
    return this.get(key);
  }

  // 删除用户会话
  async deleteSession(sessionId: string): Promise<void> {
    const key = `session:${sessionId}`;
    await this.del(key);
  }

  // 缓存验证码
  async cacheVerificationCode(email: string, code: string): Promise<void> {
    const key = `verification:${email}`;
    await this.set(key, code, 300); // 5分钟过期
  }

  // 获取缓存的验证码
  async getCachedVerificationCode(email: string): Promise<string | null> {
    const key = `verification:${email}`;
    return this.get(key);
  }

  // 删除验证码
  async deleteVerificationCode(email: string): Promise<void> {
    const key = `verification:${email}`;
    await this.del(key);
  }
}