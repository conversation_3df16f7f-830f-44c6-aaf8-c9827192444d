/**
 * API网关配置
 * 基于Spring Cloud Gateway实现请求路由、限流、熔断
 * 支持5000+并发处理
 */

package com.smartloan.gateway;

import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter;
import org.springframework.web.server.ServerWebExchange;

import reactor.core.publisher.Mono;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;

@Slf4j
@Configuration
public class ApiGatewayConfig {

    /**
     * 路由配置
     */
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
            // 产品匹配服务路由
            .route("product-service", r -> r
                .path("/api/v1/products/**")
                .filters(f -> f
                    .requestRateLimiter(c -> c
                        .setRateLimiter(redisRateLimiter())
                        .setKeyResolver(userKeyResolver()))
                    .circuitBreaker(c -> c
                        .setName("product-circuit-breaker")
                        .setFallbackUri("forward:/fallback/products"))
                    .retry(retryConfig -> retryConfig
                        .setRetries(3)
                        .setBackoff(Duration.ofMillis(100), Duration.ofMillis(1000), 2, false))
                    .addRequestHeader("X-Service", "product-service")
                    .addResponseHeader("X-Response-Time", "#{T(System).currentTimeMillis()}"))
                .uri("lb://product-service"))
            
            // 资质审核服务路由
            .route("approval-service", r -> r
                .path("/api/v1/audits/**", "/api/v1/ocr/**", "/api/v1/liveness/**")
                .filters(f -> f
                    .requestRateLimiter(c -> c
                        .setRateLimiter(redisRateLimiter())
                        .setKeyResolver(userKeyResolver()))
                    .circuitBreaker(c -> c
                        .setName("approval-circuit-breaker")
                        .setFallbackUri("forward:/fallback/approval"))
                    .addRequestHeader("X-Service", "approval-service")
                    .addRequestHeader("X-GPU-Enabled", "true"))
                .uri("lb://approval-service"))
            
            // 风控服务路由
            .route("risk-service", r -> r
                .path("/api/v1/risk/**")
                .filters(f -> f
                    .requestRateLimiter(c -> c
                        .setRateLimiter(strictRateLimiter()) // 风控服务更严格的限流
                        .setKeyResolver(userKeyResolver()))
                    .circuitBreaker(c -> c
                        .setName("risk-circuit-breaker")
                        .setFallbackUri("forward:/fallback/risk"))
                    .addRequestHeader("X-Service", "risk-service")
                    .addRequestHeader("X-Security-Level", "HIGH"))
                .uri("lb://risk-service"))
            
            // AI虚拟顾问服务路由
            .route("ai-advisor-service", r -> r
                .path("/api/v1/ai/**", "/api/v1/chat/**")
                .filters(f -> f
                    .requestRateLimiter(c -> c
                        .setRateLimiter(aiRateLimiter())
                        .setKeyResolver(userKeyResolver()))
                    .circuitBreaker(c -> c
                        .setName("ai-circuit-breaker")
                        .setFallbackUri("forward:/fallback/ai"))
                    .addRequestHeader("X-Service", "ai-advisor-service")
                    .addRequestHeader("X-Model", "Fin-R1"))
                .uri("lb://ai-advisor-service"))
            
            // 用户服务路由
            .route("user-service", r -> r
                .path("/api/v1/users/**", "/api/v1/auth/**")
                .filters(f -> f
                    .requestRateLimiter(c -> c
                        .setRateLimiter(redisRateLimiter())
                        .setKeyResolver(ipKeyResolver())) // 用户服务使用IP限流
                    .addRequestHeader("X-Service", "user-service"))
                .uri("lb://user-service"))
            
            // 健康检查路由
            .route("health-check", r -> r
                .path("/api/health", "/actuator/**")
                .filters(f -> f
                    .addRequestHeader("X-Health-Check", "true"))
                .uri("lb://health-service"))
            
            .build();
    }

    /**
     * 标准限流器 - 每用户每分钟100请求
     */
    @Bean
    public RedisRateLimiter redisRateLimiter() {
        return new RedisRateLimiter(100, 200, 1);
    }

    /**
     * 严格限流器 - 风控服务每用户每分钟50请求
     */
    @Bean
    public RedisRateLimiter strictRateLimiter() {
        return new RedisRateLimiter(50, 100, 1);
    }

    /**
     * AI服务限流器 - 每用户每分钟30请求
     */
    @Bean
    public RedisRateLimiter aiRateLimiter() {
        return new RedisRateLimiter(30, 60, 1);
    }

    /**
     * 用户ID限流解析器
     */
    @Bean
    public KeyResolver userKeyResolver() {
        return exchange -> {
            String userId = exchange.getRequest().getHeaders().getFirst("X-User-ID");
            if (userId != null) {
                return Mono.just(userId);
            }
            
            // 从JWT Token中提取用户ID
            String authorization = exchange.getRequest().getHeaders().getFirst("Authorization");
            if (authorization != null && authorization.startsWith("Bearer ")) {
                try {
                    String token = authorization.substring(7);
                    String extractedUserId = extractUserIdFromToken(token);
                    return Mono.just(extractedUserId != null ? extractedUserId : "anonymous");
                } catch (Exception e) {
                    log.warn("提取用户ID失败: {}", e.getMessage());
                }
            }
            
            return Mono.just("anonymous");
        };
    }

    /**
     * IP地址限流解析器
     */
    @Bean
    public KeyResolver ipKeyResolver() {
        return exchange -> {
            String xForwardedFor = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");
            if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
                return Mono.just(xForwardedFor.split(",")[0].trim());
            }
            
            String xRealIp = exchange.getRequest().getHeaders().getFirst("X-Real-IP");
            if (xRealIp != null && !xRealIp.isEmpty()) {
                return Mono.just(xRealIp);
            }
            
            return Mono.just(exchange.getRequest().getRemoteAddress().getAddress().getHostAddress());
        };
    }

    /**
     * 从JWT Token中提取用户ID
     */
    private String extractUserIdFromToken(String token) {
        try {
            // 简化的JWT解析，实际应用中应使用JWT库
            String[] parts = token.split("\\.");
            if (parts.length == 3) {
                String payload = new String(java.util.Base64.getUrlDecoder().decode(parts[1]));
                // 解析JSON获取用户ID
                if (payload.contains("\"sub\":")) {
                    int start = payload.indexOf("\"sub\":\"") + 7;
                    int end = payload.indexOf("\"", start);
                    return payload.substring(start, end);
                }
            }
        } catch (Exception e) {
            log.debug("JWT解析失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 全局过滤器 - 请求日志和监控
     */
    @Bean
    public org.springframework.cloud.gateway.filter.GlobalFilter globalLoggingFilter() {
        return (exchange, chain) -> {
            long startTime = System.currentTimeMillis();
            String requestId = java.util.UUID.randomUUID().toString();
            
            // 添加请求ID到响应头
            exchange.getResponse().getHeaders().add("X-Request-ID", requestId);
            
            log.info("🌐 API请求开始 | requestId={} | method={} | path={} | userAgent={}", 
                requestId,
                exchange.getRequest().getMethod(),
                exchange.getRequest().getPath().value(),
                exchange.getRequest().getHeaders().getFirst("User-Agent"));
            
            return chain.filter(exchange).then(
                Mono.fromRunnable(() -> {
                    long duration = System.currentTimeMillis() - startTime;
                    log.info("✅ API请求完成 | requestId={} | status={} | duration={}ms", 
                        requestId,
                        exchange.getResponse().getStatusCode(),
                        duration);
                    
                    // 记录性能指标
                    recordMetrics(exchange, duration);
                })
            );
        };
    }

    /**
     * 记录性能指标
     */
    private void recordMetrics(ServerWebExchange exchange, long duration) {
        try {
            String path = exchange.getRequest().getPath().value();
            String method = exchange.getRequest().getMethod().name();
            int status = exchange.getResponse().getStatusCode().value();
            
            // 这里可以集成Prometheus或其他监控系统
            log.debug("📊 性能指标 | path={} | method={} | status={} | duration={}ms", 
                path, method, status, duration);
            
            // 慢请求告警
            if (duration > 1000) {
                log.warn("🐌 慢请求告警 | path={} | duration={}ms", path, duration);
            }
            
        } catch (Exception e) {
            log.error("记录性能指标失败: {}", e.getMessage());
        }
    }
}
