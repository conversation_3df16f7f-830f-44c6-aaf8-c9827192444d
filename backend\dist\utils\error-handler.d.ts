import { HttpException, HttpStatus } from '@nestjs/common';
export declare class ErrorHandler {
    private readonly logger;
    static handle(error: any): {
        error: string;
    };
    handle(error: any): HttpException;
    private handleDatabaseError;
    handleBusinessError(message: string, status?: HttpStatus): HttpException;
    handlePermissionError(message?: string): HttpException;
    handleNotFoundError(message?: string): HttpException;
    handleValidationError(messageOrErrors: string | any[]): HttpException;
    handleAuthenticationError(message?: string): HttpException;
    handleAuthError(error: any): HttpException;
    handleFileError(error: any): HttpException;
    static handleValidationError(errors: any[]): HttpException;
    static isOperational(error: any): boolean;
    static handleAsync<T>(promise: Promise<T>): Promise<T>;
}
