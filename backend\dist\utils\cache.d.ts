export interface CacheOptions {
    ttl?: number;
    maxSize?: number;
}
export declare class Cache {
    private cache;
    private readonly defaultTtl;
    private readonly maxSize;
    set(key: string, value: any, ttl?: number): void;
    get<T = any>(key: string): T | undefined;
    has(key: string): boolean;
    delete(key: string): boolean;
    clear(): void;
    size(): number;
    keys(): string[];
    private cleanup;
    getOrSet<T>(key: string, factory: () => Promise<T>, ttl?: number): Promise<T>;
    mget(keys: string[]): Array<{
        key: string;
        value: any;
    }>;
    mset(items: Array<{
        key: string;
        value: any;
        ttl?: number;
    }>): void;
}
