import { Injectable } from '@nestjs/common';
import { RedisService } from './redis.service';
import { MonitoringService } from './monitoring.service';
import { GpuService } from './gpu.service';

@Injectable()
export class RiskDataProcessorService {
  private readonly BATCH_SIZE = 32;

  constructor(
    private readonly redisService: RedisService,
    private readonly monitoringService: MonitoringService,
    private readonly gpuService: GpuService
  ) {}

  // 批量处理风险评估任务
  async processBatchAssessments(tasks: any[]): Promise<any[]> {
    const startTime = Date.now();

    try {
      // 按批次大小分组
      const batches = this.splitIntoBatches(tasks, this.BATCH_SIZE);

      // 并行处理每个批次
      const results = await Promise.all(
        batches.map(batch => this.gpuService.processRiskModelBatch(batch))
      );

      // 合并结果
      const processedResults = results.flat();

      // 记录性能指标
      this.monitoringService.recordMetric(
        'batch_process_time',
        Date.now() - startTime
      );
      this.monitoringService.recordMetric(
        'processed_tasks',
        processedResults.length
      );

      return processedResults;
    } catch (error) {
      this.monitoringService.recordError('batch_process', error);
      throw error;
    }
  }

  // 将数据分批
  private splitIntoBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  // 并行数据预处理
  async preprocessData(data: any): Promise<any> {
    const tasks = [
      this.normalizeUserData(data.user),
      this.normalizeApplicationData(data.application),
      this.enrichWithMarketData(data)
    ];

    const [userData, applicationData, marketData] = await Promise.all(tasks);

    return {
      user: userData,
      application: applicationData,
      market: marketData,
      timestamp: new Date()
    };
  }

  private async normalizeUserData(userData: any): Promise<any> {
    // 标准化用户数据
    return {
      ...userData,
      creditScore: this.normalizeCreditScore(userData.creditScore),
      income: this.normalizeIncome(userData.income)
    };
  }

  private async normalizeApplicationData(appData: any): Promise<any> {
    // 标准化申请数据
    return {
      ...appData,
      amount: this.normalizeAmount(appData.amount),
      term: this.normalizeTerm(appData.term)
    };
  }

  private async enrichWithMarketData(data: any): Promise<any> {
    // 使用市场数据丰富风险评估
    try {
      const marketData = await this.fetchMarketData();
      return {
        ...data,
        marketIndicators: marketData
      };
    } catch (error) {
      return {
        ...data,
        marketIndicators: this.getDefaultMarketData()
      };
    }
  }

  private normalizeCreditScore(score: number): number {
    return Math.max(300, Math.min(850, score));
  }

  private normalizeIncome(income: number): number {
    return Math.max(0, income);
  }

  private normalizeAmount(amount: number): number {
    return Math.max(0, amount);
  }

  private normalizeTerm(term: number): number {
    return Math.max(1, Math.min(360, term));
  }

  private async fetchMarketData(): Promise<any> {
    // 实现市场数据获取逻辑
    return {};
  }

  private getDefaultMarketData(): any {
    return {
      marketVolatility: 0.5,
      interestRate: 0.05,
      economicGrowth: 0.03
    };
  }
}
