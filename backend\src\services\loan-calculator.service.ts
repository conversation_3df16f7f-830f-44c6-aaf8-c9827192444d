import { Injectable, Logger } from '@nestjs/common';

export interface LoanCalculationParams {
  loanType: '公积金贷款' | '商业贷款' | '组合贷款';
  totalAmount?: number;
  commercialAmount?: number;
  providentAmount?: number;
  commercialRate?: number;
  providentRate?: number;
  loanTerm: number; // 贷款期限（月）
  repaymentMethod: '等额本息' | '等额本金' | '先息后本';
}

export interface MonthlyPayment {
  month: number;
  principal: number;
  interest: number;
  totalPayment: number;
  remainingPrincipal: number;
}

export interface LoanCalculationResult {
  monthlyPayments: MonthlyPayment[];
  totalInterest: number;
  totalPayment: number;
  monthlyPaymentAmount: number;
  summary: {
    loanAmount: number;
    interestRate: number;
    loanTerm: number;
    repaymentMethod: string;
  };
}

@Injectable()
export class LoanCalculatorService {
  private readonly logger = new Logger(LoanCalculatorService.name);

  // 2025年最新利率
  private readonly interestRates = {
    providentFund: {
      fiveYearsBelow: 0.026, // 2.60%
      fiveYearsAbove: 0.031  // 3.10%
    },
    commercial: {
      oneYearBelow: 0.0435,   // 4.35%
      oneToFiveYears: 0.0475, // 4.75%
      fiveYearsAbove: 0.049   // 4.90%
    }
  };

  /**
   * 计算贷款
   */
  async calculateLoan(params: LoanCalculationParams): Promise<LoanCalculationResult> {
    try {
      this.logger.log(`计算贷款: ${JSON.stringify(params)}`);

      switch (params.loanType) {
        case '公积金贷款':
          return this.calculateProvidentFundLoan(params);
        case '商业贷款':
          return this.calculateCommercialLoan(params);
        case '组合贷款':
          return this.calculateCombinedLoan(params);
        default:
          throw new Error('不支持的贷款类型');
      }
    } catch (error) {
      this.logger.error('贷款计算失败', error);
      throw error;
    }
  }

  /**
   * 计算公积金贷款
   */
  private calculateProvidentFundLoan(params: LoanCalculationParams): LoanCalculationResult {
    const amount = params.totalAmount || 0;
    const termYears = params.loanTerm / 12;
    const rate = termYears <= 5 
      ? this.interestRates.providentFund.fiveYearsBelow 
      : this.interestRates.providentFund.fiveYearsAbove;

    return this.performCalculation(amount, rate, params.loanTerm, params.repaymentMethod);
  }

  /**
   * 计算商业贷款
   */
  private calculateCommercialLoan(params: LoanCalculationParams): LoanCalculationResult {
    const amount = params.totalAmount || 0;
    const rate = params.commercialRate || this.getCommercialRate(params.loanTerm);

    return this.performCalculation(amount, rate, params.loanTerm, params.repaymentMethod);
  }

  /**
   * 计算组合贷款
   */
  private calculateCombinedLoan(params: LoanCalculationParams): LoanCalculationResult {
    const commercialAmount = params.commercialAmount || 0;
    const providentAmount = params.providentAmount || 0;
    const totalAmount = commercialAmount + providentAmount;

    // 分别计算商业贷款和公积金贷款
    const commercialRate = params.commercialRate || this.getCommercialRate(params.loanTerm);
    const providentRate = params.providentRate || this.getProvidentRate(params.loanTerm);

    const commercialResult = this.performCalculation(
      commercialAmount, 
      commercialRate, 
      params.loanTerm, 
      params.repaymentMethod
    );

    const providentResult = this.performCalculation(
      providentAmount, 
      providentRate, 
      params.loanTerm, 
      params.repaymentMethod
    );

    // 合并结果
    const monthlyPayments: MonthlyPayment[] = [];
    for (let i = 0; i < params.loanTerm; i++) {
      const commercial = commercialResult.monthlyPayments[i];
      const provident = providentResult.monthlyPayments[i];

      monthlyPayments.push({
        month: i + 1,
        principal: commercial.principal + provident.principal,
        interest: commercial.interest + provident.interest,
        totalPayment: commercial.totalPayment + provident.totalPayment,
        remainingPrincipal: commercial.remainingPrincipal + provident.remainingPrincipal
      });
    }

    return {
      monthlyPayments,
      totalInterest: commercialResult.totalInterest + providentResult.totalInterest,
      totalPayment: commercialResult.totalPayment + providentResult.totalPayment,
      monthlyPaymentAmount: commercialResult.monthlyPaymentAmount + providentResult.monthlyPaymentAmount,
      summary: {
        loanAmount: totalAmount,
        interestRate: (commercialRate * commercialAmount + providentRate * providentAmount) / totalAmount,
        loanTerm: params.loanTerm,
        repaymentMethod: params.repaymentMethod
      }
    };
  }

  /**
   * 执行具体计算
   */
  private performCalculation(
    amount: number, 
    annualRate: number, 
    termMonths: number, 
    method: string
  ): LoanCalculationResult {
    const monthlyRate = annualRate / 12;
    const monthlyPayments: MonthlyPayment[] = [];
    let remainingPrincipal = amount;
    let totalInterest = 0;

    switch (method) {
      case '等额本息':
        return this.calculateEqualPayment(amount, monthlyRate, termMonths);
      case '等额本金':
        return this.calculateEqualPrincipal(amount, monthlyRate, termMonths);
      case '先息后本':
        return this.calculateInterestFirst(amount, monthlyRate, termMonths);
      default:
        throw new Error('不支持的还款方式');
    }
  }

  /**
   * 等额本息计算
   */
  private calculateEqualPayment(amount: number, monthlyRate: number, termMonths: number): LoanCalculationResult {
    const monthlyPayment = amount * monthlyRate * Math.pow(1 + monthlyRate, termMonths) / 
                          (Math.pow(1 + monthlyRate, termMonths) - 1);
    
    const monthlyPayments: MonthlyPayment[] = [];
    let remainingPrincipal = amount;
    let totalInterest = 0;

    for (let month = 1; month <= termMonths; month++) {
      const interest = remainingPrincipal * monthlyRate;
      const principal = monthlyPayment - interest;
      remainingPrincipal -= principal;
      totalInterest += interest;

      monthlyPayments.push({
        month,
        principal: Math.round(principal * 100) / 100,
        interest: Math.round(interest * 100) / 100,
        totalPayment: Math.round(monthlyPayment * 100) / 100,
        remainingPrincipal: Math.round(remainingPrincipal * 100) / 100
      });
    }

    return {
      monthlyPayments,
      totalInterest: Math.round(totalInterest * 100) / 100,
      totalPayment: Math.round((amount + totalInterest) * 100) / 100,
      monthlyPaymentAmount: Math.round(monthlyPayment * 100) / 100,
      summary: {
        loanAmount: amount,
        interestRate: monthlyRate * 12,
        loanTerm: termMonths,
        repaymentMethod: '等额本息'
      }
    };
  }

  /**
   * 等额本金计算
   */
  private calculateEqualPrincipal(amount: number, monthlyRate: number, termMonths: number): LoanCalculationResult {
    const monthlyPrincipal = amount / termMonths;
    const monthlyPayments: MonthlyPayment[] = [];
    let remainingPrincipal = amount;
    let totalInterest = 0;

    for (let month = 1; month <= termMonths; month++) {
      const interest = remainingPrincipal * monthlyRate;
      const totalPayment = monthlyPrincipal + interest;
      remainingPrincipal -= monthlyPrincipal;
      totalInterest += interest;

      monthlyPayments.push({
        month,
        principal: Math.round(monthlyPrincipal * 100) / 100,
        interest: Math.round(interest * 100) / 100,
        totalPayment: Math.round(totalPayment * 100) / 100,
        remainingPrincipal: Math.round(remainingPrincipal * 100) / 100
      });
    }

    return {
      monthlyPayments,
      totalInterest: Math.round(totalInterest * 100) / 100,
      totalPayment: Math.round((amount + totalInterest) * 100) / 100,
      monthlyPaymentAmount: Math.round((monthlyPrincipal + monthlyPayments[0].interest) * 100) / 100,
      summary: {
        loanAmount: amount,
        interestRate: monthlyRate * 12,
        loanTerm: termMonths,
        repaymentMethod: '等额本金'
      }
    };
  }

  /**
   * 先息后本计算
   */
  private calculateInterestFirst(amount: number, monthlyRate: number, termMonths: number): LoanCalculationResult {
    const monthlyInterest = amount * monthlyRate;
    const monthlyPayments: MonthlyPayment[] = [];
    let totalInterest = 0;

    for (let month = 1; month <= termMonths; month++) {
      const isLastMonth = month === termMonths;
      const principal = isLastMonth ? amount : 0;
      const interest = monthlyInterest;
      const totalPayment = principal + interest;
      const remainingPrincipal = isLastMonth ? 0 : amount;
      totalInterest += interest;

      monthlyPayments.push({
        month,
        principal: Math.round(principal * 100) / 100,
        interest: Math.round(interest * 100) / 100,
        totalPayment: Math.round(totalPayment * 100) / 100,
        remainingPrincipal: Math.round(remainingPrincipal * 100) / 100
      });
    }

    return {
      monthlyPayments,
      totalInterest: Math.round(totalInterest * 100) / 100,
      totalPayment: Math.round((amount + totalInterest) * 100) / 100,
      monthlyPaymentAmount: Math.round(monthlyInterest * 100) / 100,
      summary: {
        loanAmount: amount,
        interestRate: monthlyRate * 12,
        loanTerm: termMonths,
        repaymentMethod: '先息后本'
      }
    };
  }

  /**
   * 获取商业贷款利率
   */
  private getCommercialRate(termMonths: number): number {
    const termYears = termMonths / 12;
    if (termYears <= 1) return this.interestRates.commercial.oneYearBelow;
    if (termYears <= 5) return this.interestRates.commercial.oneToFiveYears;
    return this.interestRates.commercial.fiveYearsAbove;
  }

  /**
   * 获取公积金贷款利率
   */
  private getProvidentRate(termMonths: number): number {
    const termYears = termMonths / 12;
    return termYears <= 5 
      ? this.interestRates.providentFund.fiveYearsBelow 
      : this.interestRates.providentFund.fiveYearsAbove;
  }
}
