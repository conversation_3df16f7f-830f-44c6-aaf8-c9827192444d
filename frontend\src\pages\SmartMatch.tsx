import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Slider,
  Typography,
  Space,
  Tag,
  Progress,
  Divider,
  message,
  Spin
} from 'antd';
import {
  SearchOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  UserOutlined,
  BankOutlined,
  StarOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface ProductMatch {
  product: {
    id: number;
    name: string;
    provider: string;
    interest_rate: number;
    amount_min: number;
    amount_max: number;
    loan_term_min: number;
    loan_term_max: number;
    product_type: string;
  };
  match_score: number;
  ai_reasoning: string;
  recommended_amount: number;
  recommended_rate: number;
}

interface MatchCriteria {
  amount: number;
  term_months: number;
  product_type: string;
  user_profile: {
    age: number;
    income: number;
    credit_score: number;
    employment_type: string;
  };
}

const SmartMatch: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [matches, setMatches] = useState<ProductMatch[]>([]);
  const [searchCriteria, setSearchCriteria] = useState<MatchCriteria | null>(null);

  const productTypes = [
    { value: 'personal', label: '个人贷款' },
    { value: 'business', label: '企业贷款' },
    { value: 'mortgage', label: '房屋贷款' },
    { value: 'auto', label: '汽车贷款' }
  ];

  const employmentTypes = [
    { value: 'full_time', label: '全职员工' },
    { value: 'part_time', label: '兼职员工' },
    { value: 'self_employed', label: '自雇人员' },
    { value: 'unemployed', label: '无业' }
  ];

  const handleSearch = async (values: any) => {
    setLoading(true);
    try {
      const criteria: MatchCriteria = {
        amount: values.amount,
        term_months: values.term_months,
        product_type: values.product_type,
        user_profile: {
          age: values.age,
          income: values.income,
          credit_score: values.credit_score,
          employment_type: values.employment_type
        }
      };

      setSearchCriteria(criteria);

      const response = await axios.post('/api/products/match/smart', criteria);
      
      if (response.data.success) {
        setMatches(response.data.data);
        message.success('产品匹配成功！');
      } else {
        message.error('匹配失败，请重试');
      }
    } catch (error) {
      console.error('匹配错误:', error);
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return '#52c41a';
    if (score >= 0.6) return '#faad14';
    return '#ff4d4f';
  };

  const getScoreText = (score: number) => {
    if (score >= 0.8) return '高度匹配';
    if (score >= 0.6) return '中等匹配';
    return '低度匹配';
  };

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      {/* 页面标题 */}
      <div style={{ textAlign: 'center', marginBottom: '32px' }}>
        <Title level={2}>
          <BankOutlined style={{ color: '#1890ff', marginRight: '8px' }} />
          智能产品匹配
        </Title>
        <Paragraph type="secondary">
          基于AI算法，为您推荐最适合的金融产品
        </Paragraph>
      </div>

      <Row gutter={[24, 24]}>
        {/* 搜索表单 */}
        <Col xs={24} lg={8}>
          <Card title="匹配条件" bordered={false}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSearch}
              initialValues={{
                amount: 100000,
                term_months: 36,
                product_type: 'personal',
                age: 30,
                income: 10000,
                credit_score: 700,
                employment_type: 'full_time'
              }}
            >
              <Form.Item
                name="product_type"
                label="产品类型"
                rules={[{ required: true, message: '请选择产品类型' }]}
              >
                <Select placeholder="选择产品类型">
                  {productTypes.map(type => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="amount"
                label="贷款金额"
                rules={[{ required: true, message: '请输入贷款金额' }]}
              >
                <div>
                  <Slider
                    min={10000}
                    max={1000000}
                    step={10000}
                    tooltip={{ formatter: (value) => formatCurrency(value || 0) }}
                    onChange={(value) => form.setFieldsValue({ amount: value })}
                  />
                  <Input
                    prefix={<DollarOutlined />}
                    suffix="元"
                    placeholder="输入金额"
                  />
                </div>
              </Form.Item>

              <Form.Item
                name="term_months"
                label="贷款期限（月）"
                rules={[{ required: true, message: '请选择贷款期限' }]}
              >
                <Select placeholder="选择期限">
                  <Option value={12}>12个月</Option>
                  <Option value={24}>24个月</Option>
                  <Option value={36}>36个月</Option>
                  <Option value={48}>48个月</Option>
                  <Option value={60}>60个月</Option>
                </Select>
              </Form.Item>

              <Divider>个人信息</Divider>

              <Form.Item
                name="age"
                label="年龄"
                rules={[{ required: true, message: '请输入年龄' }]}
              >
                <Input prefix={<UserOutlined />} suffix="岁" placeholder="输入年龄" />
              </Form.Item>

              <Form.Item
                name="income"
                label="月收入"
                rules={[{ required: true, message: '请输入月收入' }]}
              >
                <Input prefix={<DollarOutlined />} suffix="元" placeholder="输入月收入" />
              </Form.Item>

              <Form.Item
                name="credit_score"
                label="信用分数"
                rules={[{ required: true, message: '请输入信用分数' }]}
              >
                <Slider
                  min={300}
                  max={850}
                  tooltip={{ formatter: (value) => `${value}分` }}
                  onChange={(value) => form.setFieldsValue({ credit_score: value })}
                />
              </Form.Item>

              <Form.Item
                name="employment_type"
                label="就业类型"
                rules={[{ required: true, message: '请选择就业类型' }]}
              >
                <Select placeholder="选择就业类型">
                  {employmentTypes.map(type => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  size="large"
                  icon={<SearchOutlined />}
                >
                  智能匹配
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 匹配结果 */}
        <Col xs={24} lg={16}>
          <Card title="匹配结果" bordered={false}>
            {loading ? (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin size="large" />
                <div style={{ marginTop: '16px' }}>
                  <Text>AI正在为您匹配最优产品...</Text>
                </div>
              </div>
            ) : matches.length > 0 ? (
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                {matches.map((match, index) => (
                  <Card
                    key={match.product.id}
                    size="small"
                    style={{
                      border: index === 0 ? '2px solid #1890ff' : '1px solid #d9d9d9'
                    }}
                    extra={
                      index === 0 && (
                        <Tag color="gold" icon={<StarOutlined />}>
                          最佳匹配
                        </Tag>
                      )
                    }
                  >
                    <Row gutter={[16, 16]}>
                      <Col xs={24} md={16}>
                        <Space direction="vertical" size="small" style={{ width: '100%' }}>
                          <div>
                            <Title level={4} style={{ margin: 0 }}>
                              {match.product.name}
                            </Title>
                            <Text type="secondary">{match.product.provider}</Text>
                          </div>
                          
                          <div>
                            <Text strong>AI推荐理由：</Text>
                            <Paragraph style={{ margin: '4px 0' }}>
                              {match.ai_reasoning}
                            </Paragraph>
                          </div>

                          <Row gutter={16}>
                            <Col span={8}>
                              <Text type="secondary">推荐金额</Text>
                              <div>
                                <Text strong>{formatCurrency(match.recommended_amount)}</Text>
                              </div>
                            </Col>
                            <Col span={8}>
                              <Text type="secondary">推荐利率</Text>
                              <div>
                                <Text strong>{match.recommended_rate}%</Text>
                              </div>
                            </Col>
                            <Col span={8}>
                              <Text type="secondary">产品类型</Text>
                              <div>
                                <Tag>{match.product.product_type}</Tag>
                              </div>
                            </Col>
                          </Row>
                        </Space>
                      </Col>
                      
                      <Col xs={24} md={8}>
                        <div style={{ textAlign: 'center' }}>
                          <Progress
                            type="circle"
                            percent={Math.round(match.match_score * 100)}
                            strokeColor={getScoreColor(match.match_score)}
                            format={() => getScoreText(match.match_score)}
                          />
                          <div style={{ marginTop: '8px' }}>
                            <Button type="primary" size="large" block>
                              立即申请
                            </Button>
                          </div>
                        </div>
                      </Col>
                    </Row>
                  </Card>
                ))}
              </Space>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Text type="secondary">请填写匹配条件并点击"智能匹配"按钮</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SmartMatch;
