<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartLoan 2025 - 风控看板</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; background: #0a0e27; color: white; min-height: 100vh; }
        .dashboard { display: grid; grid-template-columns: 250px 1fr; height: 100vh; }
        .sidebar { background: #1a1f3a; padding: 20px; border-right: 1px solid #2a2f4a; }
        .main-content { padding: 20px; overflow-y: auto; }
        .logo { font-size: 1.5rem; font-weight: bold; margin-bottom: 30px; color: #4fc3f7; }
        .nav-item { padding: 12px 16px; margin-bottom: 8px; border-radius: 8px; cursor: pointer; transition: all 0.3s; }
        .nav-item:hover { background: #2a2f4a; }
        .nav-item.active { background: #4fc3f7; color: #0a0e27; }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); padding: 25px; border-radius: 15px; position: relative; overflow: hidden; }
        .stat-card::before { content: ''; position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%; transform: translate(30px, -30px); }
        .stat-value { font-size: 2.5rem; font-weight: bold; margin-bottom: 8px; }
        .stat-label { font-size: 0.9rem; opacity: 0.8; }
        .stat-change { font-size: 0.8rem; margin-top: 8px; }
        .stat-change.positive { color: #4caf50; }
        .stat-change.negative { color: #f44336; }
        .chart-container { background: #1a1f3a; border-radius: 15px; padding: 25px; margin-bottom: 20px; }
        .chart-title { font-size: 1.2rem; margin-bottom: 20px; color: #4fc3f7; }
        .chart-placeholder { height: 300px; background: linear-gradient(45deg, #2a2f4a, #3a3f5a); border-radius: 10px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden; }
        .chart-placeholder::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(79, 195, 247, 0.1) 10px, rgba(79, 195, 247, 0.1) 20px); animation: slide 2s linear infinite; }
        @keyframes slide { 0% { transform: translateX(-20px); } 100% { transform: translateX(20px); } }
        .risk-map { height: 400px; background: #1a1f3a; border-radius: 15px; position: relative; overflow: hidden; }
        .map-overlay { position: absolute; top: 20px; left: 20px; background: rgba(0,0,0,0.7); padding: 15px; border-radius: 8px; }
        .risk-level { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .risk-low { background: #4caf50; }
        .risk-medium { background: #ff9800; }
        .risk-high { background: #f44336; }
        .alert-panel { background: #2a1810; border: 1px solid #ff9800; border-radius: 10px; padding: 20px; margin-bottom: 20px; }
        .alert-item { display: flex; align-items: center; padding: 10px 0; border-bottom: 1px solid #3a2820; }
        .alert-item:last-child { border-bottom: none; }
        .alert-icon { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; }
        .alert-high { background: #f44336; }
        .alert-medium { background: #ff9800; }
        .real-time-data { display: grid; grid-template-columns: 2fr 1fr; gap: 20px; }
        .data-stream { background: #1a1f3a; border-radius: 15px; padding: 20px; height: 300px; overflow-y: auto; }
        .stream-item { padding: 8px 12px; margin-bottom: 8px; background: #2a2f4a; border-radius: 6px; font-size: 0.9rem; }
        .performance-metrics { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-bottom: 20px; }
        .metric-card { background: #1a1f3a; padding: 20px; border-radius: 10px; text-align: center; }
        .metric-value { font-size: 1.8rem; font-weight: bold; color: #4fc3f7; }
        .metric-label { font-size: 0.8rem; opacity: 0.7; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">🛡️ SmartLoan 风控</div>
            <div class="nav-item active">📊 总览</div>
            <div class="nav-item">🗺️ 风险地图</div>
            <div class="nav-item">⚠️ 实时预警</div>
            <div class="nav-item">📈 趋势分析</div>
            <div class="nav-item">🔍 详细报告</div>
            <div class="nav-item">⚙️ 设置</div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 头部 -->
            <div class="header">
                <h1>风控监控看板</h1>
                <div style="font-size: 0.9rem; opacity: 0.7;">
                    最后更新: <span id="lastUpdate">2025-01-20 14:30:25</span>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">1,247</div>
                    <div class="stat-label">今日申请</div>
                    <div class="stat-change positive">↗ +12.5%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">89.2%</div>
                    <div class="stat-label">通过率</div>
                    <div class="stat-change positive">↗ +2.1%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">2.3%</div>
                    <div class="stat-label">风险率</div>
                    <div class="stat-change negative">↘ -0.8%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">0.8s</div>
                    <div class="stat-label">平均处理时间</div>
                    <div class="stat-change positive">↗ -0.2s</div>
                </div>
            </div>

            <!-- 性能指标 -->
            <div class="performance-metrics">
                <div class="metric-card">
                    <div class="metric-value">99.9%</div>
                    <div class="metric-label">系统可用性</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">5,247</div>
                    <div class="metric-label">并发处理</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">95.8%</div>
                    <div class="metric-label">AI准确率</div>
                </div>
            </div>

            <!-- 实时数据和预警 -->
            <div class="real-time-data">
                <div>
                    <!-- 风险趋势图 -->
                    <div class="chart-container">
                        <div class="chart-title">📈 风险趋势分析</div>
                        <div class="chart-placeholder">
                            <div style="text-align: center; z-index: 1; position: relative;">
                                <div style="font-size: 2rem; margin-bottom: 10px;">📊</div>
                                <div>实时风险趋势图表</div>
                                <div style="font-size: 0.8rem; opacity: 0.7; margin-top: 10px;">基于AI模型实时分析</div>
                            </div>
                        </div>
                    </div>

                    <!-- GIS风险地图 -->
                    <div class="chart-container">
                        <div class="chart-title">🗺️ GIS风险热力图</div>
                        <div class="risk-map">
                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                                <div style="font-size: 3rem; margin-bottom: 15px;">🌍</div>
                                <div style="font-size: 1.2rem; margin-bottom: 10px;">全国风险分布图</div>
                                <div style="font-size: 0.9rem; opacity: 0.7;">实时更新区域风险等级</div>
                            </div>
                            <div class="map-overlay">
                                <div style="margin-bottom: 10px; font-weight: bold;">风险等级</div>
                                <div><span class="risk-level risk-low"></span>低风险 (60%)</div>
                                <div><span class="risk-level risk-medium"></span>中风险 (35%)</div>
                                <div><span class="risk-level risk-high"></span>高风险 (5%)</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <!-- 实时预警 -->
                    <div class="alert-panel">
                        <h3 style="margin-bottom: 15px;">⚠️ 实时预警</h3>
                        <div class="alert-item">
                            <div class="alert-icon alert-high">🚨</div>
                            <div>
                                <div style="font-weight: bold;">高风险申请</div>
                                <div style="font-size: 0.8rem; opacity: 0.7;">用户ID: 12847 - 异常行为检测</div>
                            </div>
                        </div>
                        <div class="alert-item">
                            <div class="alert-icon alert-medium">⚠️</div>
                            <div>
                                <div style="font-weight: bold;">批量申请异常</div>
                                <div style="font-size: 0.8rem; opacity: 0.7;">IP: ************* - 短时间内多次申请</div>
                            </div>
                        </div>
                        <div class="alert-item">
                            <div class="alert-icon alert-medium">📊</div>
                            <div>
                                <div style="font-weight: bold;">模型准确率下降</div>
                                <div style="font-size: 0.8rem; opacity: 0.7;">风控模型V2.1 - 准确率降至92%</div>
                            </div>
                        </div>
                    </div>

                    <!-- 实时数据流 -->
                    <div class="chart-container">
                        <div class="chart-title">📡 实时数据流</div>
                        <div class="data-stream" id="dataStream">
                            <div class="stream-item">14:30:25 - 用户申请: 身份验证通过</div>
                            <div class="stream-item">14:30:23 - OCR识别: 身份证识别成功 (95%)</div>
                            <div class="stream-item">14:30:21 - 活体检测: 验证通过 (98%)</div>
                            <div class="stream-item">14:30:19 - 风险评估: 低风险 (评分: 85)</div>
                            <div class="stream-item">14:30:17 - 产品匹配: 推荐3款产品</div>
                            <div class="stream-item">14:30:15 - 用户申请: 贷款计算完成</div>
                            <div class="stream-item">14:30:13 - AI顾问: 用户咨询回复</div>
                            <div class="stream-item">14:30:11 - 系统监控: GPU使用率 65%</div>
                            <div class="stream-item">14:30:09 - 数据库: 查询响应时间 0.3s</div>
                            <div class="stream-item">14:30:07 - 缓存命中: Redis命中率 94%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟实时数据更新
        function updateRealTimeData() {
            const dataStream = document.getElementById('dataStream');
            const now = new Date();
            const timeStr = now.toTimeString().split(' ')[0];
            
            const events = [
                '用户申请: 身份验证通过',
                'OCR识别: 营业执照识别成功 (96%)',
                '活体检测: 验证通过 (97%)',
                '风险评估: 中风险 (评分: 72)',
                '产品匹配: 推荐2款产品',
                'AI顾问: 智能推荐回复',
                '系统监控: CPU使用率 45%',
                '数据库: 连接池状态正常',
                '缓存更新: 产品数据刷新'
            ];
            
            const randomEvent = events[Math.floor(Math.random() * events.length)];
            const newItem = document.createElement('div');
            newItem.className = 'stream-item';
            newItem.textContent = `${timeStr} - ${randomEvent}`;
            
            dataStream.insertBefore(newItem, dataStream.firstChild);
            
            // 保持最多10条记录
            while (dataStream.children.length > 10) {
                dataStream.removeChild(dataStream.lastChild);
            }
        }

        // 更新时间戳
        function updateTimestamp() {
            const now = new Date();
            document.getElementById('lastUpdate').textContent = now.toLocaleString('zh-CN');
        }

        // 启动实时更新
        setInterval(updateRealTimeData, 3000);
        setInterval(updateTimestamp, 1000);

        // 页面加载完成
        window.onload = function() {
            console.log('🛡️ SmartLoan 风控看板已加载');
            console.log('✅ 实时监控已启动');
            console.log('✅ GIS热力图已就绪');
            console.log('✅ 预警系统已激活');
        };
    </script>
</body>
</html>
