import { Entity, Column, PrimaryGeneratedColumn, ManyToOne } from 'typeorm';
import { Product } from './product.entity';

@Entity('product_requirements')
export class ProductRequirements {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  value: string;

  @Column({ default: false })
  required: boolean;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'int', nullable: true })
  minCreditScore: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  minIncome: number;

  @Column({ type: 'int', nullable: true })
  minAge: number;

  @Column({ type: 'int', nullable: true })
  maxAge: number;

  @Column({ type: 'int', nullable: true })
  minEmploymentMonths: number;

  @ManyToOne(() => Product, product => product.requirements)
  product: Product;
}
