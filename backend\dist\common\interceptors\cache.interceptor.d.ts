import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { CacheService } from '../../services/cache.service';
export interface CacheOptions {
    ttl?: number;
    key?: string;
}
export declare class CacheInterceptor implements NestInterceptor {
    private readonly cacheService;
    private readonly logger;
    constructor(cacheService: CacheService);
    intercept(context: ExecutionContext, next: CallHandler, options?: CacheOptions): Promise<Observable<any>>;
    private generateCacheKey;
}
