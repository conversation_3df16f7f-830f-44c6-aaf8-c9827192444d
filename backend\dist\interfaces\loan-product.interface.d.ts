import { EmploymentStatus } from '../enums/employment-status.enum';
import { LoanType } from '../enums/loan-type.enum';
export interface LoanProduct {
    id: string;
    name: string;
    type: LoanType;
    minAmount: number;
    maxAmount: number;
    term: number;
    interestRate: number;
    approvalRate: number;
    minCreditScore: number;
    eligibleEmploymentStatus?: EmploymentStatus[];
    requirements: ProductRequirements[];
}
export interface ProductRequirements {
    name: string;
    value: string | number;
    required: boolean;
    description?: string;
}
