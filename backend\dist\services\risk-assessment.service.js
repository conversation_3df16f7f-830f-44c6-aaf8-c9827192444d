"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RiskAssessmentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../entities/user.entity");
const loan_application_entity_1 = require("../entities/loan-application.entity");
const logger_service_1 = require("./logger.service");
const error_handler_1 = require("../utils/error-handler");
const gpu_service_1 = require("./gpu.service");
let RiskAssessmentService = class RiskAssessmentService {
    constructor(userRepository, loanApplicationRepository, logger, errorHandler, gpuService) {
        this.userRepository = userRepository;
        this.loanApplicationRepository = loanApplicationRepository;
        this.logger = logger;
        this.errorHandler = errorHandler;
        this.gpuService = gpuService;
    }
    async assessRisk(applicationId) {
        try {
            const application = await this.loanApplicationRepository.findOne({
                where: { id: applicationId },
                relations: ['user']
            });
            if (!application) {
                throw new Error('申请不存在');
            }
            const riskData = await this.collectRiskData(application);
            const assessmentResult = await this.gpuService.accelerateRiskAssessment(riskData);
            await this.loanApplicationRepository.update({ id: applicationId.toString() }, {
                riskAssessment: JSON.stringify({
                    score: assessmentResult.score,
                    factors: assessmentResult.factors,
                    decision: assessmentResult.decision,
                    timestamp: new Date()
                })
            });
            if (assessmentResult.score > 0.7) {
                await this.triggerRiskAlert(application, assessmentResult);
            }
            return assessmentResult;
        }
        catch (error) {
            this.logger.error('风险评估失败', { error, applicationId });
            throw this.errorHandler.handle(error);
        }
    }
    async collectRiskData(application) {
        const userHistory = await this.loanApplicationRepository.find({
            where: { userId: application.userId },
            order: { createdAt: 'DESC' }
        });
        const user = await this.userRepository.findOne({
            where: { id: application.userId }
        });
        return {
            application,
            userHistory,
            user,
            timestamp: new Date()
        };
    }
    async triggerRiskAlert(application, assessmentResult) {
        this.logger.warn('触发风险预警', {
            applicationId: application.id,
            userId: application.userId,
            riskScore: assessmentResult.score,
            factors: assessmentResult.factors
        });
    }
    async getRiskMetrics() {
        try {
            const applications = await this.loanApplicationRepository.find({
                where: {
                    riskScore: { $ne: null }
                }
            });
            return {
                totalApplications: applications.length,
                averageRiskScore: this.calculateAverageRiskScore(applications),
                riskDistribution: this.calculateRiskDistribution(applications),
                recentAlerts: await this.getRecentAlerts()
            };
        }
        catch (error) {
            this.logger.error('获取风险指标失败', { error });
            throw this.errorHandler.handle(error);
        }
    }
    calculateAverageRiskScore(applications) {
        const scores = applications.map(app => app.riskScore);
        return scores.reduce((a, b) => a + b, 0) / scores.length;
    }
    calculateRiskDistribution(applications) {
        const distribution = {
            low: 0,
            medium: 0,
            high: 0
        };
        applications.forEach(app => {
            if (app.riskScore < 0.3)
                distribution.low++;
            else if (app.riskScore < 0.7)
                distribution.medium++;
            else
                distribution.high++;
        });
        return distribution;
    }
    async getRecentAlerts() {
        return [];
    }
};
RiskAssessmentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(loan_application_entity_1.LoanApplication)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        logger_service_1.LoggerService,
        error_handler_1.ErrorHandler,
        gpu_service_1.GpuService])
], RiskAssessmentService);
exports.RiskAssessmentService = RiskAssessmentService;
//# sourceMappingURL=risk-assessment.service.js.map