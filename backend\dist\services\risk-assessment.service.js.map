{"version": 3, "file": "risk-assessment.service.js", "sourceRoot": "", "sources": ["../../src/services/risk-assessment.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AACrC,yDAA+C;AAC/C,iFAAsE;AACtE,qDAAiD;AACjD,0DAAsD;AACtD,+CAA2C;AAGpC,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,YAEmB,cAAgC,EAEhC,yBAAsD,EACtD,MAAqB,EACrB,YAA0B,EAC1B,UAAsB;QALtB,mBAAc,GAAd,cAAc,CAAkB;QAEhC,8BAAyB,GAAzB,yBAAyB,CAA6B;QACtD,WAAM,GAAN,MAAM,CAAe;QACrB,iBAAY,GAAZ,YAAY,CAAc;QAC1B,eAAU,GAAV,UAAU,CAAY;IACtC,CAAC;IAEJ,KAAK,CAAC,UAAU,CAAC,aAAqB;QACpC,IAAI;YACF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;gBAC/D,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;gBAC5B,SAAS,EAAE,CAAC,MAAM,CAAC;aACpB,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,EAAE;gBAChB,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;aAC1B;YAGD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAGzD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YAGlF,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CACzC,EAAE,EAAE,EAAE,aAAa,CAAC,QAAQ,EAAE,EAAE,EAChC;gBACE,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC;oBAC7B,KAAK,EAAE,gBAAgB,CAAC,KAAK;oBAC7B,OAAO,EAAE,gBAAgB,CAAC,OAAO;oBACjC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;oBACnC,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;aACH,CACF,CAAC;YAGF,IAAI,gBAAgB,CAAC,KAAK,GAAG,GAAG,EAAE;gBAChC,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;aAC5D;YAED,OAAO,gBAAgB,CAAC;SACzB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;YACtD,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACvC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,WAA4B;QAExD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;YAC5D,KAAK,EAAE,EAAE,MAAM,EAAE,WAAW,CAAC,MAAM,EAAE;YACrC,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;QAGH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,CAAC,MAAM,EAAE;SAClC,CAAC,CAAC;QAEH,OAAO;YACL,WAAW;YACX,WAAW;YACX,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,WAA4B,EAAE,gBAAqB;QAEhF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;YACzB,aAAa,EAAE,WAAW,CAAC,EAAE;YAC7B,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,SAAS,EAAE,gBAAgB,CAAC,KAAK;YACjC,OAAO,EAAE,gBAAgB,CAAC,OAAO;SAClC,CAAC,CAAC;IAGL,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI;YACF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;gBAC7D,KAAK,EAAE;oBACL,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE;iBACzB;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,iBAAiB,EAAE,YAAY,CAAC,MAAM;gBACtC,gBAAgB,EAAE,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC;gBAC9D,gBAAgB,EAAE,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC;gBAC9D,YAAY,EAAE,MAAM,IAAI,CAAC,eAAe,EAAE;aAC3C,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACvC;IACH,CAAC;IAEO,yBAAyB,CAAC,YAA+B;QAC/D,MAAM,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACtD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IAC3D,CAAC;IAEO,yBAAyB,CAAC,YAA+B;QAC/D,MAAM,YAAY,GAAG;YACnB,GAAG,EAAE,CAAC;YACN,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,CAAC;SACR,CAAC;QAEF,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACzB,IAAI,GAAG,CAAC,SAAS,GAAG,GAAG;gBAAE,YAAY,CAAC,GAAG,EAAE,CAAC;iBACvC,IAAI,GAAG,CAAC,SAAS,GAAG,GAAG;gBAAE,YAAY,CAAC,MAAM,EAAE,CAAC;;gBAC/C,YAAY,CAAC,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,KAAK,CAAC,eAAe;QAE3B,OAAO,EAAE,CAAC;IACZ,CAAC;CACF,CAAA;AAlIY,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,yCAAe,CAAC,CAAA;qCADD,oBAAU;QAEC,oBAAU;QAC7B,8BAAa;QACP,4BAAY;QACd,wBAAU;GAR9B,qBAAqB,CAkIjC;AAlIY,sDAAqB"}