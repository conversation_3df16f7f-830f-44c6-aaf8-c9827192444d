import { ConfigService } from '@nestjs/config';
import { RiskDecisionService } from './risk-decision.service';
import { LoanApplication } from '../entities/loan-application.entity';
import { User } from '../entities/user.entity';
interface AIDecision {
    approved: boolean;
    score: number;
    reason: string;
    suggestedAmount?: number;
    suggestedTerm?: number;
}
interface AIRecommendation {
    products: Array<{
        id: string;
        name: string;
        description: string;
        matchScore: number;
    }>;
    reason: string;
}
export declare class AIService {
    private readonly configService;
    private readonly riskDecisionService;
    private readonly openai;
    private readonly logger;
    constructor(configService: ConfigService, riskDecisionService: RiskDecisionService);
    analyzeApplication(application: LoanApplication, user: User): Promise<AIDecision>;
    getRecommendations(application: LoanApplication, user: User): Promise<AIRecommendation>;
    private buildAnalysisPrompt;
    private buildRecommendationPrompt;
}
export {};
