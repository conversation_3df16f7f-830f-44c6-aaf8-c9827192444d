package com.smartloan.model;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "match_results")
public class MatchResult {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "requirement_id", nullable = false)
    private LoanRequirement requirement;
    
    @ManyToOne
    @JoinColumn(name = "product_id", nullable = false)
    private Product product;
    
    @Column(nullable = false)
    private BigDecimal matchedAmount;
    
    @Column(nullable = false)
    private Integer matchedTerm;
    
    @Column(nullable = false)
    private Double interestRate;
    
    @Column(nullable = false)
    private Double matchScore; // 匹配度评分
    
    @Column(nullable = false)
    private String status; // PENDING, ACCEPTED, REJECTED
    
    private String rejectReason;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (status == null) {
            status = "PENDING";
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
