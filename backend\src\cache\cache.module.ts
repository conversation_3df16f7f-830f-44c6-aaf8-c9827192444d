import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { LoggerModule } from '../logger/logger.module';
import { MonitorModule } from '../monitor/monitor.module';
import { CacheService } from './cache.service';

@Global()
@Module({
  imports: [ConfigModule, LoggerModule, MonitorModule],
  providers: [CacheService],
  exports: [CacheService],
})
export class CacheModule {} 