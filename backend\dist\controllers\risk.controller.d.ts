import { RiskService } from '../services/risk.service';
export declare class RiskController {
    private readonly riskService;
    constructor(riskService: RiskService);
    getRiskData(timeRange: string): Promise<{
        radar: {
            indicators: {
                name: string;
                max: number;
            }[];
            values: any[];
        };
        trend: {
            dates: any;
            values: any;
        };
        heatmap: {
            cities: any;
            riskTypes: any;
            data: any;
        };
    }>;
    getAlerts(): Promise<import("../entities/alert.entity").Alert[]>;
    subscribeAlerts(): Promise<import("rxjs").Observable<import("../entities/alert.entity").Alert>>;
}
