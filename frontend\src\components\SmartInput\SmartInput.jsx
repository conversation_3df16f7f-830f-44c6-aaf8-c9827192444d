/**
 * SmartInput 智能输入组件
 * 支持15种证件类型的AI预填和OCR识别
 * 实时验证和智能提示
 */

import React, { useState, useRef, useEffect } from 'react';
import { Camera, Scan, Check, AlertCircle, Loader } from 'lucide-react';
import './SmartInput.scss';

const SmartInput = ({
  type = 'text',
  label,
  placeholder,
  value,
  onChange,
  aiAssistant = false,
  scanButton = null,
  validation = {},
  disabled = false,
  required = false,
  className = '',
  ...props
}) => {
  const [inputValue, setInputValue] = useState(value || '');
  const [isValid, setIsValid] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const [isScanning, setIsScanning] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef(null);

  // 证件类型配置
  const documentTypes = {
    idCard: {
      pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      placeholder: '请输入18位身份证号码',
      maxLength: 18,
      aiHint: 'AI可自动识别身份证信息'
    },
    businessLicense: {
      pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
      placeholder: '请输入统一社会信用代码',
      maxLength: 18,
      aiHint: 'AI可自动识别营业执照信息'
    },
    bankCard: {
      pattern: /^[1-9]\d{12,18}$/,
      placeholder: '请输入银行卡号',
      maxLength: 19,
      aiHint: 'AI可自动识别银行卡信息'
    },
    phone: {
      pattern: /^1[3-9]\d{9}$/,
      placeholder: '请输入11位手机号码',
      maxLength: 11,
      aiHint: 'AI可智能验证手机号码'
    },
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      placeholder: '请输入邮箱地址',
      aiHint: 'AI可智能补全邮箱域名'
    }
  };

  const currentType = documentTypes[type] || {};

  // 实时验证
  useEffect(() => {
    if (validation.realtime && inputValue) {
      validateInput(inputValue);
    }
  }, [inputValue, validation.realtime]);

  // AI智能提示
  useEffect(() => {
    if (aiAssistant && inputValue.length > 2) {
      generateAISuggestions(inputValue);
    }
  }, [inputValue, aiAssistant]);

  const validateInput = (value) => {
    if (!value && required) {
      setIsValid(false);
      setErrorMessage('此字段为必填项');
      return false;
    }

    if (currentType.pattern && value && !currentType.pattern.test(value)) {
      setIsValid(false);
      setErrorMessage(getValidationMessage(type));
      return false;
    }

    if (validation.rules) {
      for (const rule of validation.rules) {
        if (!rule.validator(value)) {
          setIsValid(false);
          setErrorMessage(rule.message);
          return false;
        }
      }
    }

    setIsValid(true);
    setErrorMessage('');
    return true;
  };

  const getValidationMessage = (type) => {
    const messages = {
      idCard: '请输入正确的18位身份证号码',
      businessLicense: '请输入正确的统一社会信用代码',
      bankCard: '请输入正确的银行卡号',
      phone: '请输入正确的11位手机号码',
      email: '请输入正确的邮箱地址'
    };
    return messages[type] || '输入格式不正确';
  };

  const generateAISuggestions = async (value) => {
    // 模拟AI智能提示
    if (type === 'email') {
      const domains = ['@gmail.com', '@qq.com', '@163.com', '@126.com', '@outlook.com'];
      const suggestions = domains.map(domain => value.split('@')[0] + domain);
      setAiSuggestions(suggestions.slice(0, 3));
      setShowSuggestions(true);
    } else if (type === 'phone') {
      // 模拟手机号码智能补全
      if (value.length === 3) {
        const suggestions = ['***********', '***********', '***********']
          .filter(phone => phone.startsWith(value))
          .slice(0, 3);
        setAiSuggestions(suggestions);
        setShowSuggestions(suggestions.length > 0);
      }
    }
  };

  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange && onChange(newValue);
  };

  const handleScanClick = async () => {
    if (!scanButton || isScanning) return;
    
    setIsScanning(true);
    try {
      // 调用OCR识别
      const result = await scanButton.action();
      if (result && result.extractedData) {
        const extractedValue = extractValueFromOCR(result.extractedData, type);
        if (extractedValue) {
          setInputValue(extractedValue);
          onChange && onChange(extractedValue);
        }
      }
    } catch (error) {
      console.error('OCR识别失败:', error);
    } finally {
      setIsScanning(false);
    }
  };

  const extractValueFromOCR = (data, type) => {
    const extractors = {
      idCard: () => data.idNumber || data.id_number,
      businessLicense: () => data.registrationNumber || data.registration_number,
      bankCard: () => data.cardNumber || data.card_number,
      phone: () => data.phone || data.mobile,
      email: () => data.email
    };
    
    return extractors[type] ? extractors[type]() : null;
  };

  const handleSuggestionClick = (suggestion) => {
    setInputValue(suggestion);
    onChange && onChange(suggestion);
    setShowSuggestions(false);
  };

  const getInputStatus = () => {
    if (errorMessage) return 'error';
    if (inputValue && isValid) return 'success';
    return 'default';
  };

  return (
    <div className={`smart-input ${className}`}>
      {label && (
        <label className="smart-input__label">
          {label}
          {required && <span className="smart-input__required">*</span>}
        </label>
      )}
      
      <div className={`smart-input__wrapper smart-input__wrapper--${getInputStatus()}`}>
        <input
          ref={inputRef}
          type="text"
          className="smart-input__field"
          placeholder={placeholder || currentType.placeholder}
          value={inputValue}
          onChange={handleInputChange}
          disabled={disabled}
          maxLength={currentType.maxLength}
          {...props}
        />
        
        {/* AI助手图标 */}
        {aiAssistant && (
          <div className="smart-input__ai-indicator" title={currentType.aiHint}>
            <div className="smart-input__ai-icon">🤖</div>
          </div>
        )}
        
        {/* 扫描按钮 */}
        {scanButton && (
          <button
            type="button"
            className="smart-input__scan-button"
            onClick={handleScanClick}
            disabled={isScanning || disabled}
            title="扫描识别"
          >
            {isScanning ? (
              <Loader className="smart-input__scan-icon smart-input__scan-icon--loading" />
            ) : (
              <Camera className="smart-input__scan-icon" />
            )}
          </button>
        )}
        
        {/* 状态图标 */}
        <div className="smart-input__status-icon">
          {getInputStatus() === 'success' && (
            <Check className="smart-input__icon smart-input__icon--success" />
          )}
          {getInputStatus() === 'error' && (
            <AlertCircle className="smart-input__icon smart-input__icon--error" />
          )}
        </div>
      </div>
      
      {/* AI智能提示 */}
      {showSuggestions && aiSuggestions.length > 0 && (
        <div className="smart-input__suggestions">
          {aiSuggestions.map((suggestion, index) => (
            <button
              key={index}
              type="button"
              className="smart-input__suggestion"
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <Scan className="smart-input__suggestion-icon" />
              {suggestion}
            </button>
          ))}
        </div>
      )}
      
      {/* 错误信息 */}
      {errorMessage && (
        <div className="smart-input__error">
          <AlertCircle className="smart-input__error-icon" />
          {errorMessage}
        </div>
      )}
      
      {/* AI提示信息 */}
      {aiAssistant && currentType.aiHint && !errorMessage && (
        <div className="smart-input__hint">
          <div className="smart-input__hint-icon">💡</div>
          {currentType.aiHint}
        </div>
      )}
    </div>
  );
};

export default SmartInput;
