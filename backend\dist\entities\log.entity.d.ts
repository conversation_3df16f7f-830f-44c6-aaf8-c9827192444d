export declare class Log {
    id: number;
    level: 'info' | 'warn' | 'error' | 'debug';
    category: 'system' | 'security' | 'audit' | 'business' | 'performance';
    source: string;
    message: string;
    context: {
        userId?: string;
        action?: string;
        resource?: string;
        ip?: string;
        userAgent?: string;
        requestId?: string;
        [key: string]: any;
    };
    metadata: {
        duration?: number;
        statusCode?: number;
        errorCode?: string;
        stackTrace?: string;
        tags?: string[];
    };
    timestamp: Date;
}
