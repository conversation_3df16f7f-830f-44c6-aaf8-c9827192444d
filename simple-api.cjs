const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
  console.log(`${req.method} ${req.url}`);
  
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  if (req.url === '/api/health') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      status: 'healthy',
      message: 'SmartLoan 2025 API正常运行'
    }));
    return;
  }
  
  if (req.url === '/api/products') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: [{
        id: 'icbc_2025_001',
        name: '工商银行融e借2025版',
        institution: '中国工商银行',
        rate_min: 3.85
      }]
    }));
    return;
  }

  if (req.url === '/api/products/match/smart' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: [{
          product: {
            id: 'icbc_2025_001',
            name: '工商银行融e借2025版',
            institution: '中国工商银行',
            rate_min: 3.85,
            rate_max: 4.20
          },
          match_score: 0.95,
          ai_reasoning: '该产品利率优惠，支持数字人民币，审批速度快'
        }]
      }));
    });
    return;
  }

  if (req.url === '/api/ai/ocr' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          document_type: 'identity_card',
          extracted_data: {
            name: '张三',
            idNumber: '110101199001011234'
          },
          confidence: 0.96,
          processing_time: '800ms'
        }
      }));
    });
    return;
  }

  if (req.url === '/api/ai/liveness' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          is_live: true,
          confidence: 0.98,
          processing_time: '1200ms'
        }
      }));
    });
    return;
  }

  if (req.url === '/api/ai/risk-assessment' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          risk_score: 750,
          risk_level: 'LOW',
          processing_time: '1500ms'
        }
      }));
    });
    return;
  }

  if (req.url === '/api/ai/advisor/chat' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          response: '基于您的需求，我推荐工商银行融e借2025版，支持数字人民币，30秒审批。',
          model: 'Fin-R1-2025'
        }
      }));
    });
    return;
  }

  if (req.url === '/api/loan/calculator' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        const monthlyPayment = Math.round(data.totalAmount * 0.006);
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            monthlyPaymentAmount: monthlyPayment,
            totalPayment: monthlyPayment * data.loanTerm,
            totalInterest: monthlyPayment * data.loanTerm - data.totalAmount
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, error: 'Invalid JSON' }));
      }
    });
    return;
  }
  
  // 静态文件服务
  if (req.url === '/') {
    fs.readFile(path.join(__dirname, 'smartloan-api-demo.html'), (err, data) => {
      if (err) {
        res.writeHead(404);
        res.end('File not found');
        return;
      }
      res.setHeader('Content-Type', 'text/html');
      res.writeHead(200);
      res.end(data);
    });
    return;
  }

  res.writeHead(404);
  res.end(JSON.stringify({
    success: false,
    error: 'API接口未找到'
  }));
});

server.listen(3006, () => {
  console.log('✅ 简单API服务器启动: http://localhost:3006');
  console.log('🔗 主页面: http://localhost:3006/');
  console.log('🔗 健康检查: http://localhost:3006/api/health');
});

server.on('error', console.error);
