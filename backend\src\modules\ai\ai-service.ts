import { Injectable, Logger } from '@nestjs/common';
import { LoggerService } from '../../logger/logger.service';
import { ConfigService } from '@nestjs/config';
import { retry } from 'rxjs/operators';
import { timeout } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { of } from 'rxjs';
import { Observable } from 'rxjs';

@Injectable()
export class AIService {
  private readonly gpuEndpoint: string;
  private readonly modelEndpoint: string;
  private readonly timeout: number;
  private readonly retryAttempts: number;
  private readonly retryDelay: number;
  private readonly logger = new Logger(AIService.name);

  constructor(
    private readonly loggerService: LoggerService,
    private readonly config: ConfigService,
  ) {
    const aiConfig = this.config.get('ai');
    this.gpuEndpoint = aiConfig.gpuEndpoint;
    this.modelEndpoint = aiConfig.modelEndpoint;
    this.timeout = aiConfig.timeout;
    this.retryAttempts = aiConfig.retryAttempts;
    this.retryDelay = aiConfig.retryDelay;
  }

  async performOCR(image: Buffer): Promise<any> {
    try {
      const response = await this.makeRequest(
        `${this.gpuEndpoint}/ocr`,
        {
          image: image.toString('base64'),
        },
        'OCR服务调用失败',
      );

      return response;
    } catch (error) {
      this.loggerService.error('OCR处理失败', { error });
      throw error;
    }
  }

  async detectLiveness(video: Buffer): Promise<any> {
    try {
      const response = await this.makeRequest(
        `${this.gpuEndpoint}/liveness`,
        {
          video: video.toString('base64'),
        },
        '活体检测服务调用失败',
      );

      return response;
    } catch (error) {
      this.loggerService.error('活体检测失败', { error });
      throw error;
    }
  }

  async getAIAdvisorResponse(query: string, context: any): Promise<any> {
    try {
      const response = await this.makeRequest(
        `${this.modelEndpoint}/chat`,
        {
          query,
          context,
        },
        'AI顾问服务调用失败',
      );

      return response;
    } catch (error) {
      this.loggerService.error('AI顾问响应失败', { error });
      throw error;
    }
  }

  async analyzeCreditReport(report: any): Promise<any> {
    try {
      const response = await this.makeRequest(
        `${this.gpuEndpoint}/credit-analysis`,
        {
          report,
        },
        '征信分析服务调用失败',
      );

      return response;
    } catch (error) {
      this.loggerService.error('征信分析失败', { error });
      throw error;
    }
  }

  private async makeRequest(url: string, data: any, errorMessage: string): Promise<any> {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`${errorMessage}: ${response.statusText}`);
    }

    return response.json();
  }
} 