export declare class ProductRequirementDto {
    id?: string;
    productId?: string;
    minAge?: number;
    maxAge?: number;
    minIncome?: number;
    minCreditScore?: number;
    maxDebtToIncomeRatio?: number;
    employmentTypes?: string[];
    requiredDocuments?: string[];
    workExperienceYears?: number;
    additionalFactors?: string[];
    description?: string;
    isActive?: boolean;
}
export declare class CreateProductRequirementDto extends ProductRequirementDto {
    productId: string;
}
export declare class UpdateProductRequirementDto extends ProductRequirementDto {
}
