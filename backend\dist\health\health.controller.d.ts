/// <reference types="node" />
export declare class HealthController {
    getHealth(): {
        status: string;
        timestamp: string;
        service: string;
        version: string;
        uptime: number;
        memory: NodeJS.MemoryUsage;
        environment: string;
    };
    getDetailedHealth(): {
        status: string;
        timestamp: string;
        service: string;
        version: string;
        uptime: number;
        memory: NodeJS.MemoryUsage;
        environment: string;
        database: {
            status: string;
            type: string;
        };
        redis: {
            status: string;
        };
        ai_service: {
            status: string;
            endpoint: string;
        };
        features: {
            ocr_recognition: boolean;
            liveness_detection: boolean;
            risk_assessment: boolean;
            product_matching: boolean;
            ai_advisor: boolean;
        };
    };
}
