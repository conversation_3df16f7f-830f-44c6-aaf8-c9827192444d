import { Repository } from 'typeorm';
import { Cache } from 'cache-manager';
import { Product } from '../entities/product.entity';
import { RiskAssessment } from '../entities/risk-assessment.entity';
import { User } from '../entities/user.entity';
import { MonitoringService } from '../services/monitoring.service';
import { GpuService } from '../services/gpu.service';
import { MatchedProduct, ProductMatchCriteria } from '../interfaces/matching.interface';
export declare class OptimizedProductMatchingService {
    private readonly productRepository;
    private readonly riskRepository;
    private readonly cacheManager;
    private readonly gpuService;
    private readonly monitoringService;
    private readonly logger;
    private readonly BATCH_SIZE;
    private readonly CACHE_TTL;
    private readonly MATCH_WEIGHTS;
    constructor(productRepository: Repository<Product>, riskRepository: Repository<RiskAssessment>, cacheManager: Cache, gpuService: GpuService, monitoringService: MonitoringService);
    matchProducts(user: User, criteria: ProductMatchCriteria): Promise<MatchedProduct[]>;
    private getEligibleProducts;
    private performGpuMatching;
    private cpuMatchProducts;
    private calculateDetailScores;
    private calculateOverallScore;
    private calculateAmountScore;
    private calculateTermScore;
    private calculateRateScore;
    private calculateFeatureScore;
    private calculateLocationScore;
    private parseLocationRules;
    private parseUserLocation;
    private matchLocationRule;
    private postProcessResults;
    private calculateFinalScore;
    private calculateRiskPenalty;
    private isProductSuitableForRisk;
    private generateCacheKey;
    private getRiskAssessment;
}
