import { Injectable, Logger, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from './entities/product.entity';
import { Institution } from './entities/institution.entity';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { GpuService } from '../../services/gpu.service';

@Injectable()
export class ProductMatchingService {
  private readonly logger = new Logger(ProductMatchingService.name);
  private readonly CACHE_TTL = 3600; // 缓存时间1小时

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(Institution)
    private readonly institutionRepository: Repository<Institution>,
    @Inject(CACHE_MANAGER)
    private cacheManager: any, // 使用any类型避免类型错误
    private gpuService: GpuService,
  ) {}

  async matchProducts(userProfile: any, requirements: any): Promise<Product[]> {
    const { amount, term, type } = requirements;
    
    // 生成缓存键
    const cacheKey = `product:match:${JSON.stringify(userProfile)}:${JSON.stringify(requirements)}`;
    
    // 尝试从缓存获取
    const cachedResult = await this.cacheManager.get(cacheKey);
    if (cachedResult) {
      this.logger.log('从缓存获取产品匹配结果');
      return cachedResult;
    }

    // 基础产品筛选 - 使用索引优化的查询
    const products = await this.productRepository
      .createQueryBuilder('product')
      .leftJoinAndSelect('product.institution', 'institution')
      .where('product.min_amount <= :amount', { amount })
      .andWhere('product.max_amount >= :amount', { amount })
      .andWhere('product.type = :type', { type })
      .andWhere(':term = ANY(product.term_range)', { term })
      .getMany();

    // 分批计算匹配度，减少内存压力
    let scoredProducts = [];
    const BATCH_SIZE = 50;
    
    try {
      // 尝试使用GPU加速计算匹配度
      if (products.length > 100) {
        this.logger.log('使用GPU加速计算产品匹配度');
        // 注释掉GPU加速调用，使用标准处理
        // const result = await this.gpuService.accelerateProductMatching(userProfile, products);
        // scoredProducts = result.matchedProducts;
        // 回退到标准处理
        scoredProducts = products.map(product => ({
          ...product,
          score: this.calculateMatchScore(product, userProfile, requirements),
        }));
      } else {
        // 批量处理小数据集
        for (let i = 0; i < products.length; i += BATCH_SIZE) {
          const batch = products.slice(i, i + BATCH_SIZE);
          const scoredBatch = batch.map(product => ({
            ...product,
            score: this.calculateMatchScore(product, userProfile, requirements),
          }));
          scoredProducts.push(...scoredBatch);
        }
      }
    } catch (error) {
      this.logger.error('GPU加速失败，回退到CPU处理', error);
      // 回退到标准计算方式
      scoredProducts = products.map(product => ({
        ...product,
        score: this.calculateMatchScore(product, userProfile, requirements),
      }));
    }

    // 按匹配度排序并返回前3个产品
    const result = scoredProducts
      .sort((a, b) => b.score - a.score)
      .slice(0, 3);
      
    // 将结果缓存
    await this.cacheManager.set(cacheKey, result, this.CACHE_TTL);
    
    return result;
  }

  private calculateMatchScore(product: Product, userProfile: any, requirements: any): number {
    let score = 0;

    // 金额匹配度 (30%)
    const amountScore = this.calculateAmountScore(product, requirements.amount);
    score += amountScore * 0.3;

    // 期限匹配度 (20%)
    const termScore = this.calculateTermScore(product, requirements.term);
    score += termScore * 0.2;

    // 利率竞争力 (30%)
    const rateScore = this.calculateRateScore(product);
    score += rateScore * 0.3;

    // 用户资质匹配度 (20%)
    const profileScore = this.calculateProfileScore(product, userProfile);
    score += profileScore * 0.2;

    return score;
  }

  private calculateAmountScore(product: Product, amount: number): number {
    const range = product.max_amount - product.min_amount;
    const position = (amount - product.min_amount) / range;
    return 1 - Math.abs(position - 0.5) * 2;
  }

  private calculateTermScore(product: Product, term: number): number {
    const termRange = product.term_range;
    const minTerm = Math.min(...termRange);
    const maxTerm = Math.max(...termRange);
    const range = maxTerm - minTerm;
    const position = (term - minTerm) / range;
    return 1 - Math.abs(position - 0.5) * 2;
  }

  private calculateRateScore(product: Product): number {
    // 假设市场平均利率为5%
    const marketAverageRate = 5;
    const rateDiff = marketAverageRate - product.interest_rate;
    return Math.max(0, 1 - Math.abs(rateDiff) / marketAverageRate);
  }

  private calculateProfileScore(product: Product, userProfile: any): number {
    // 根据用户资质和产品要求计算匹配度
    let score = 0;
    const requirements = product.requirements;

    if (requirements.creditScore && userProfile.creditScore) {
      score += userProfile.creditScore >= requirements.creditScore ? 0.4 : 0;
    }

    if (requirements.income && userProfile.income) {
      score += userProfile.income >= requirements.income ? 0.3 : 0;
    }

    if (requirements.employment && userProfile.employment) {
      score += userProfile.employment === requirements.employment ? 0.3 : 0;
    }

    return score;
  }
}