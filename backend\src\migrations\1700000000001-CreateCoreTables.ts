import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCoreTables1700000000001 implements MigrationInterface {
  name = 'CreateCoreTables1700000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建金融机构表
    await queryRunner.query(`
      CREATE TABLE "institutions" (
        "id" SERIAL PRIMARY KEY,
        "name" VARCHAR NOT NULL,
        "type" VARCHAR NOT NULL,
        "license_number" VARCHAR NOT NULL,
        "contact_info" JSONB,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // 创建产品表
    await queryRunner.query(`
      CREATE TABLE "products" (
        "id" SERIAL PRIMARY KEY,
        "institution_id" INTEGER NOT NULL REFERENCES "institutions"("id"),
        "name" VARCHAR NOT NULL,
        "type" VARCHAR NOT NULL,
        "min_amount" DECIMAL(10,2) NOT NULL,
        "max_amount" DECIMAL(10,2) NOT NULL,
        "interest_rate" DECIMAL(5,2) NOT NULL,
        "term_range" INTEGER[] NOT NULL,
        "requirements" JSONB,
        "features" JSONB,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // 创建风控规则表
    await queryRunner.query(`
      CREATE TABLE "risk_rules" (
        "id" SERIAL PRIMARY KEY,
        "name" VARCHAR NOT NULL,
        "type" VARCHAR NOT NULL,
        "conditions" JSONB NOT NULL,
        "actions" JSONB NOT NULL,
        "priority" INTEGER NOT NULL,
        "is_active" BOOLEAN NOT NULL DEFAULT true,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // 创建审核记录表
    await queryRunner.query(`
      CREATE TABLE "audits" (
        "id" SERIAL PRIMARY KEY,
        "user_id" INTEGER NOT NULL REFERENCES "users"("id"),
        "loan_application_id" INTEGER NOT NULL REFERENCES "loan_applications"("id"),
        "type" VARCHAR NOT NULL,
        "status" VARCHAR NOT NULL,
        "result" JSONB,
        "documents" JSONB,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // 创建用户行为日志表
    await queryRunner.query(`
      CREATE TABLE "user_logs" (
        "id" SERIAL PRIMARY KEY,
        "user_id" INTEGER NOT NULL REFERENCES "users"("id"),
        "action" VARCHAR NOT NULL,
        "details" JSONB,
        "ip_address" VARCHAR,
        "user_agent" VARCHAR,
        "created_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "user_logs"`);
    await queryRunner.query(`DROP TABLE "audits"`);
    await queryRunner.query(`DROP TABLE "risk_rules"`);
    await queryRunner.query(`DROP TABLE "products"`);
    await queryRunner.query(`DROP TABLE "institutions"`);
  }
} 