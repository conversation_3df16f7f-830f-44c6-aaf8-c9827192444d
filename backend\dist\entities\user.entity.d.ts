import { Role } from '../enums/role.enum';
import { LoanApplication } from './loan-application.entity';
import { LoanReview } from './loan-review.entity';
export declare class User {
    id: string;
    username: string;
    email: string;
    password: string;
    name: string;
    phone: string;
    salt: string;
    password_hash: string;
    real_name: string;
    credit_score: number;
    risk_level: string;
    role: Role;
    last_login_at: Date;
    metadata: any;
    permissions: any;
    isActive: boolean;
    lastLoginAt: Date;
    loanApplications: LoanApplication[];
    loanReviews: LoanReview[];
    createdAt: Date;
    updatedAt: Date;
    isVerified: boolean;
    verificationDate: Date;
    documentsVerified: boolean;
    documentsVerificationDate: Date;
    livenessVerified: boolean;
    livenessVerificationDate: Date;
    verificationDocuments: any;
    faceData: any;
    hashPassword(): Promise<void>;
    validatePassword(password: string): Promise<boolean>;
}
