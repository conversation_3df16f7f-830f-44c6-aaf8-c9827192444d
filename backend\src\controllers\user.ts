import { Controller, Get, Post, Put, Body, Param, Request, UseGuards, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { UserService } from '../services/user.service';

@ApiTags('users')
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取用户信息' })
  async getUserProfile(@Request() req: any) {
    try {
      const user = await this.userService.findById(req.user.id);
      if (!user) {
        throw new HttpException('用户不存在', HttpStatus.NOT_FOUND);
      }
      return {
        code: 0,
        data: user
      };
    } catch (error) {
      throw new HttpException('获取用户信息失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Put('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新用户信息' })
  async updateUserProfile(@Request() req: any, @Body() updateData: any) {
    try {
      const user = await this.userService.update(req.user.id, updateData);
      return {
        code: 0,
        message: '更新成功',
        data: user
      };
    } catch (error) {
      throw new HttpException('更新用户信息失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取用户信息' })
  async getUserById(@Param('id') id: string) {
    try {
      const user = await this.userService.findById(id);
      if (!user) {
        throw new HttpException('用户不存在', HttpStatus.NOT_FOUND);
      }
      return {
        code: 0,
        data: user
      };
    } catch (error) {
      throw new HttpException('获取用户信息失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}

// Express 风格的导出函数，用于路由兼容
export const register = async (req: any, res: any) => {
  try {
    // 这里需要实现注册逻辑
    res.json({ code: 0, message: '注册功能待实现' });
  } catch (error) {
    res.status(500).json({ code: 1, message: '注册失败' });
  }
};

export const login = async (req: any, res: any) => {
  try {
    // 这里需要实现登录逻辑
    res.json({ code: 0, message: '登录功能待实现' });
  } catch (error) {
    res.status(500).json({ code: 1, message: '登录失败' });
  }
};

export const getUserInfo = async (req: any, res: any) => {
  try {
    // 这里需要实现获取用户信息逻辑
    res.json({ code: 0, message: '获取用户信息功能待实现' });
  } catch (error) {
    res.status(500).json({ code: 1, message: '获取用户信息失败' });
  }
};

export const updateUserInfo = async (req: any, res: any) => {
  try {
    // 这里需要实现更新用户信息逻辑
    res.json({ code: 0, message: '更新用户信息功能待实现' });
  } catch (error) {
    res.status(500).json({ code: 1, message: '更新用户信息失败' });
  }
};