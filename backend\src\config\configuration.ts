export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_DATABASE || 'smartloan',
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'your-jwt-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  upload: {
    dir: process.env.UPLOAD_DIR || 'uploads',
    maxSize: parseInt(process.env.MAX_FILE_SIZE, 10) || 5 * 1024 * 1024, // 5MB
    allowedTypes: process.env.ALLOWED_FILE_TYPES?.split(',') || ['image/jpeg', 'image/png', 'application/pdf'],
  },
  cache: {
    ttl: parseInt(process.env.CACHE_TTL, 10) || 3600, // 1 hour
  },
  monitoring: {
    enabled: process.env.ENABLE_MONITORING === 'true',
    metrics: {
      enabled: process.env.ENABLE_METRICS === 'true',
      interval: parseInt(process.env.METRICS_INTERVAL, 10) || 60000, // 1 minute
    },
    alerts: {
      enabled: process.env.ENABLE_ALERTS === 'true',
      threshold: {
        cpu: parseInt(process.env.CPU_THRESHOLD, 10) || 80,
        memory: parseInt(process.env.MEMORY_THRESHOLD, 10) || 80,
        disk: parseInt(process.env.DISK_THRESHOLD, 10) || 80,
      },
    },
    email: {
      enabled: process.env.ALERT_EMAIL ? true : false,
      from: process.env.ALERT_EMAIL,
      to: process.env.ALERT_EMAIL,
      smtp: {
        host: process.env.SMTP_HOST || 'smtp.example.com',
        port: parseInt(process.env.SMTP_PORT, 10) || 587,
        secure: true,
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      },
    },
    slack: {
      enabled: process.env.ALERT_SLACK_WEBHOOK ? true : false,
      webhookUrl: process.env.ALERT_SLACK_WEBHOOK,
    },
    thresholds: {
      responseTime: parseInt(process.env.RESPONSE_TIME_THRESHOLD, 10) || 1000,
      errorRate: parseFloat(process.env.ERROR_RATE_THRESHOLD) || 0.01,
      cpuUsage: parseInt(process.env.CPU_USAGE_THRESHOLD, 10) || 80,
      memoryUsage: parseInt(process.env.MEMORY_USAGE_THRESHOLD, 10) || 80,
      diskUsage: parseInt(process.env.DISK_USAGE_THRESHOLD, 10) || 80,
    },
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
    file: process.env.LOG_FILE || 'logs/app.log',
  },
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    methods: process.env.CORS_METHODS?.split(',') || ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: process.env.CORS_HEADERS?.split(',') || ['Content-Type', 'Authorization'],
  },
}); 