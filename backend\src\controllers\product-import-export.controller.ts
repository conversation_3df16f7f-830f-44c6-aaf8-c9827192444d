import { Controller, Get, Post, UseGuards, UploadedFile, UseInterceptors, Response } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ProductImportExportService } from '../services/product-import-export.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RolesGuard } from '../guards/roles.guard';
import { Roles } from '../decorators/roles.decorator';
import { Role } from '../enums/role.enum';
import { Response as ExpressResponse } from 'express';

@Controller('product-import-export')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ProductImportExportController {
  constructor(
    private readonly productImportExportService: ProductImportExportService
  ) {}

  @Post('import')
  @Roles(Role.ADMIN)
  @UseInterceptors(FileInterceptor('file'))
  async importProducts(@UploadedFile() file: Express.Multer.File) {
    return this.productImportExportService.importProducts(file);
  }

  @Get('export')
  @Roles(Role.ADMIN)
  async exportProducts(@Response() res: ExpressResponse) {
    const { buffer } = await this.productImportExportService.exportProducts();
    res.set({
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': 'attachment; filename=products.xlsx',
      'Content-Length': buffer.length,
    });
    res.send(buffer);
  }

  @Get('template')
  @Roles(Role.ADMIN)
  async exportTemplate(@Response() res: ExpressResponse) {
    const { buffer } = await this.productImportExportService.exportTemplate();
    res.set({
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': 'attachment; filename=product_template.xlsx',
      'Content-Length': buffer.length,
    });
    res.send(buffer);
  }
} 