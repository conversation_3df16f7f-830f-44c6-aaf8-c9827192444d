"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var config_1 = require("@nestjs/config");
exports.default = (0, config_1.registerAs)('ai', function () { return ({
    gpuEndpoint: process.env.MUXI_GPU_ENDPOINT || 'http://localhost:8000',
    modelEndpoint: process.env.FIN_R1_ENDPOINT || 'http://localhost:8001',
    rateLimit: parseInt(process.env.AI_RATE_LIMIT, 10) || 100,
    cacheTtl: parseInt(process.env.AI_CACHE_TTL, 10) || 3600,
    timeout: parseInt(process.env.AI_TIMEOUT, 10) || 30000,
    retryAttempts: parseInt(process.env.AI_RETRY_ATTEMPTS, 10) || 3,
    retryDelay: parseInt(process.env.AI_RETRY_DELAY, 10) || 1000,
}); });
