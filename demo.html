<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartLoan 智能金融服务平台 - 演示版</title>
    <link href="https://cdn.jsdelivr.net/npm/antd@5.11.0/dist/reset.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/antd@5.11.0/dist/antd.min.css" rel="stylesheet">
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/antd@5.11.0/dist/antd.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 16px 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        
        .logo-icon {
            width: 32px;
            height: 32px;
            margin-right: 12px;
            background: linear-gradient(45deg, #1890ff, #722ed1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .nav-menu {
            display: flex;
            gap: 24px;
        }
        
        .nav-item {
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
            font-weight: 500;
        }
        
        .nav-item:hover, .nav-item.active {
            background: #1890ff;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }
        
        .hero-section {
            text-align: center;
            padding: 60px 0;
            color: white;
        }
        
        .hero-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 16px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .hero-subtitle {
            font-size: 20px;
            margin-bottom: 32px;
            opacity: 0.9;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 48px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
        }
        
        .feature-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            background: linear-gradient(45deg, #1890ff, #722ed1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
        }
        
        .feature-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #333;
        }
        
        .feature-desc {
            color: #666;
            line-height: 1.6;
        }
        
        .stats-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 48px;
            margin: 48px 0;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 32px;
            margin-top: 32px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 16px;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 48px;
            margin: 48px 0;
        }
        
        .demo-title {
            font-size: 28px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 32px;
            color: #333;
        }
        
        .demo-buttons {
            display: flex;
            justify-content: center;
            gap: 16px;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(45deg, #1890ff, #722ed1);
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(24, 144, 255, 0.4);
            color: white;
        }
        
        .footer {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            text-align: center;
            padding: 32px;
            margin-top: 48px;
        }
        
        .ink-animation {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, rgba(24, 144, 255, 0.8) 0%, rgba(24, 144, 255, 0.2) 70%, transparent 100%);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.2); opacity: 0.4; }
            100% { transform: scale(1); opacity: 0.8; }
        }
        
        .tech-stack {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 48px;
            margin: 48px 0;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-top: 32px;
        }
        
        .tech-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
        }
        
        .tech-icon {
            font-size: 32px;
            margin-bottom: 12px;
        }
        
        .tech-name {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        
        .tech-desc {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">💰</div>
                SmartLoan 智能金融服务平台
            </div>
            <nav class="nav-menu">
                <div class="nav-item active">首页</div>
                <div class="nav-item">产品匹配</div>
                <div class="nav-item">资质审核</div>
                <div class="nav-item">风控看板</div>
                <div class="nav-item">关于我们</div>
            </nav>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="container">
        <!-- 英雄区域 -->
        <section class="hero-section">
            <h1 class="hero-title">SmartLoan</h1>
            <p class="hero-subtitle">基于沐曦MetaX GPU算力与Gitee AI平台的智能金融服务系统</p>
            <div class="demo-buttons">
                <a href="#features" class="demo-btn">🚀 体验功能</a>
                <a href="#demo" class="demo-btn">📱 在线演示</a>
                <a href="#tech" class="demo-btn">🔧 技术架构</a>
            </div>
        </section>

        <!-- 核心功能 -->
        <section id="features" class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <h3 class="feature-title">智能产品匹配</h3>
                <p class="feature-desc">基于AI算法分析用户画像，从500+金融产品中智能推荐TOP3最适合的产品，匹配度高达95%</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔍</div>
                <h3 class="feature-title">多模态资质审核</h3>
                <p class="feature-desc">支持15类证件OCR识别，活体检测技术，自动化资质审核，审批效率提升300%</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3 class="feature-title">实时风控决策</h3>
                <p class="feature-desc">联邦学习风控模型，实时反欺诈检测，现金流压力测试，风险识别准确率99.2%</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🤖</div>
                <h3 class="feature-title">AI虚拟顾问</h3>
                <p class="feature-desc">Fin-R1大模型驱动，7×24小时智能客服，支持语音、文字多模态交互</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🌐</div>
                <h3 class="feature-title">全渠道服务</h3>
                <p class="feature-desc">Web 3D对比、小程序瀑布流、APP AR可视化，多端一体化用户体验</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">极速处理</h3>
                <p class="feature-desc">征信解析≤1s，支持5000+并发，99.9%系统可用性，运营成本降低65%</p>
            </div>
        </section>

        <!-- 系统数据 -->
        <section class="stats-section">
            <h2 class="demo-title">平台核心数据</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">金融产品</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">300%</div>
                    <div class="stat-label">审批效率提升</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">65%</div>
                    <div class="stat-label">运营成本降低</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">系统可用性</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">≤1s</div>
                    <div class="stat-label">征信解析速度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">5000+</div>
                    <div class="stat-label">并发支持</div>
                </div>
            </div>
        </section>

        <!-- 在线演示 -->
        <section id="demo" class="demo-section">
            <h2 class="demo-title">🎮 在线功能演示</h2>
            <div class="demo-buttons">
                <button class="demo-btn" onclick="showSmartMatch()">🎯 智能产品匹配</button>
                <button class="demo-btn" onclick="showLoanCalculator()">💰 贷款计算器</button>
                <button class="demo-btn" onclick="showQualification()">🔍 资质审核流程</button>
                <button class="demo-btn" onclick="showRiskDashboard()">📊 风控看板</button>
                <button class="demo-btn" onclick="showAIAdvisor()">🤖 AI虚拟顾问</button>
            </div>
            <div id="demo-content" style="margin-top: 32px; min-height: 200px; background: #f8f9fa; border-radius: 12px; padding: 24px; display: none;">
                <div id="demo-text"></div>
            </div>
        </section>

        <!-- 技术架构 -->
        <section id="tech" class="tech-stack">
            <h2 class="demo-title">🔧 技术架构</h2>
            <div class="tech-grid">
                <div class="tech-item">
                    <div class="tech-icon">⚛️</div>
                    <div class="tech-name">前端技术栈</div>
                    <div class="tech-desc">React 18 + TypeScript + Ant Design + Vite</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">🚀</div>
                    <div class="tech-name">后端技术栈</div>
                    <div class="tech-desc">NestJS + TypeORM + PostgreSQL + Redis</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">🤖</div>
                    <div class="tech-name">AI服务</div>
                    <div class="tech-desc">沐曦MetaX GPU + Gitee AI + Fin-R1大模型</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">🐳</div>
                    <div class="tech-name">部署运维</div>
                    <div class="tech-desc">Docker + Kubernetes + CI/CD</div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <p>&copy; 2024 SmartLoan 智能金融服务平台. 基于沐曦MetaX GPU算力与Gitee AI平台构建</p>
        <p>🏆 三位一体：智能匹配-精准评估-实时风控 | 🌟 三大核心：产品推荐-资质审核-风险管控</p>
    </footer>

    <!-- 水墨动画效果 -->
    <div class="ink-animation"></div>

    <script>
        // 演示功能
        function showDemo(title, content) {
            const demoContent = document.getElementById('demo-content');
            const demoText = document.getElementById('demo-text');
            
            demoText.innerHTML = `
                <h3 style="color: #1890ff; margin-bottom: 16px;">${title}</h3>
                <div style="line-height: 1.6; color: #333;">${content}</div>
            `;
            
            demoContent.style.display = 'block';
            demoContent.scrollIntoView({ behavior: 'smooth' });
        }

        function showSmartMatch() {
            const demoContent = document.getElementById('demo-content');
            const demoText = document.getElementById('demo-text');

            demoText.innerHTML = `
                <h3 style="color: #1890ff; margin-bottom: 16px;">🎯 智能产品匹配演示</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                    <div>
                        <h4>📝 填写申请信息</h4>
                        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 4px; font-weight: bold;">贷款金额 (元):</label>
                                <input type="number" id="loanAmount" value="100000" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 4px; font-weight: bold;">贷款期限:</label>
                                <select id="loanTerm" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="12">12个月</option>
                                    <option value="24" selected>24个月</option>
                                    <option value="36">36个月</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 4px; font-weight: bold;">月收入 (元):</label>
                                <input type="number" id="monthlyIncome" value="15000" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 4px; font-weight: bold;">信用分数:</label>
                                <input type="number" id="creditScore" value="750" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <button onclick="performSmartMatch()" style="width: 100%; padding: 12px; background: linear-gradient(45deg, #1890ff, #722ed1); color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">
                                🚀 开始智能匹配
                            </button>
                        </div>
                    </div>
                    <div>
                        <h4>🎯 匹配结果</h4>
                        <div id="matchResults" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); min-height: 300px;">
                            <div style="text-align: center; color: #999; padding: 40px 0;">
                                点击"开始智能匹配"查看结果
                            </div>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 24px; padding: 16px; background: #f0f8ff; border-radius: 8px; border-left: 4px solid #1890ff;">
                    <h4 style="margin: 0 0 8px 0; color: #1890ff;">💡 功能特点</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>🔍 基于2025年最新AI算法的智能匹配</li>
                        <li>🎯 支持数字人民币、元宇宙场景、绿色金融</li>
                        <li>📊 实时计算匹配度，提供详细推荐理由</li>
                        <li>⚡ 毫秒级响应，支持5000+并发用户</li>
                    </ul>
                </div>
            `;

            demoContent.style.display = 'block';
            demoContent.scrollIntoView({ behavior: 'smooth' });
        }

        // 执行智能匹配
        async function performSmartMatch() {
            const amount = document.getElementById('loanAmount').value;
            const term = document.getElementById('loanTerm').value;
            const income = document.getElementById('monthlyIncome').value;
            const creditScore = document.getElementById('creditScore').value;

            const resultsDiv = document.getElementById('matchResults');
            resultsDiv.innerHTML = '<div style="text-align: center; padding: 40px 0;"><div style="color: #1890ff; font-size: 18px;">🔄 AI智能匹配中...</div><div style="color: #999; margin-top: 8px;">正在分析您的需求</div></div>';

            try {
                const response = await fetch('http://localhost:3001/api/products/match/smart', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: parseInt(amount),
                        term_months: parseInt(term),
                        product_type: 'personal',
                        user_profile: {
                            income: parseInt(income),
                            credit_score: parseInt(creditScore),
                            employment_type: 'full_time'
                        }
                    })
                });

                const data = await response.json();

                if (data.success) {
                    let resultsHtml = '<h4 style="color: #52c41a; margin: 0 0 16px 0;">🎉 智能匹配成功</h4>';
                    data.data.forEach((match, index) => {
                        const product = match.product;
                        const scoreColor = match.match_score >= 0.8 ? '#52c41a' : match.match_score >= 0.6 ? '#faad14' : '#ff4d4f';
                        resultsHtml += `
                            <div style="border: 1px solid #f0f0f0; border-radius: 8px; padding: 16px; margin-bottom: 12px; background: ${index === 0 ? '#f6ffed' : '#fafafa'};">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <h5 style="margin: 0; color: #333;">🏆 TOP ${index + 1}: ${product.name}</h5>
                                    <div style="background: ${scoreColor}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                                        匹配度: ${(match.match_score * 100).toFixed(1)}%
                                    </div>
                                </div>
                                <div style="font-size: 14px; line-height: 1.5;">
                                    <p style="margin: 4px 0;"><strong>🏦 提供方：</strong>${product.provider}</p>
                                    <p style="margin: 4px 0;"><strong>💰 推荐利率：</strong><span style="color: #1890ff; font-weight: bold;">${match.recommended_rate}%</span></p>
                                    <p style="margin: 4px 0;"><strong>💳 最高额度：</strong>¥${product.amount_max.toLocaleString()}</p>
                                    <p style="margin: 4px 0;"><strong>📊 推荐金额：</strong>¥${match.recommended_amount.toLocaleString()}</p>
                                    <p style="margin: 4px 0;"><strong>🤖 AI分析：</strong>${match.ai_reasoning}</p>
                                    <p style="margin: 4px 0;"><strong>✨ 产品特色：</strong>${product.description}</p>
                                </div>
                            </div>
                        `;
                    });
                    resultsDiv.innerHTML = resultsHtml;
                } else {
                    resultsDiv.innerHTML = '<div style="text-align: center; color: #ff4d4f; padding: 40px 0;">❌ 匹配失败，请检查输入信息</div>';
                }
            } catch (error) {
                console.error('匹配请求失败:', error);
                // 使用模拟数据展示功能
                resultsDiv.innerHTML = `
                    <div style="background: #fff7e6; border: 1px solid #ffd591; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
                        <h4 style="color: #fa8c16; margin: 0 0 8px 0;">⚠️ 后端服务未连接</h4>
                        <p style="margin: 0; font-size: 14px;">正在使用模拟数据展示功能效果</p>
                    </div>
                    <h4 style="color: #52c41a; margin: 0 0 16px 0;">🎉 智能匹配成功 (模拟)</h4>
                    <div style="border: 1px solid #f0f0f0; border-radius: 8px; padding: 16px; margin-bottom: 12px; background: #f6ffed;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <h5 style="margin: 0; color: #333;">🏆 TOP 1: 工商银行融e借2025版</h5>
                            <div style="background: #52c41a; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                                匹配度: 95.2%
                            </div>
                        </div>
                        <div style="font-size: 14px; line-height: 1.5;">
                            <p style="margin: 4px 0;"><strong>🏦 提供方：</strong>中国工商银行</p>
                            <p style="margin: 4px 0;"><strong>💰 推荐利率：</strong><span style="color: #1890ff; font-weight: bold;">3.85%</span></p>
                            <p style="margin: 4px 0;"><strong>💳 最高额度：</strong>¥800,000</p>
                            <p style="margin: 4px 0;"><strong>📊 推荐金额：</strong>¥${parseInt(amount).toLocaleString()}</p>
                            <p style="margin: 4px 0;"><strong>🤖 AI分析：</strong>基于您的信用分数${creditScore}分和月收入¥${parseInt(income).toLocaleString()}，该产品非常适合您</p>
                            <p style="margin: 4px 0;"><strong>✨ 产品特色：</strong>2025年全新升级，AI智能审批，30秒放款，支持数字人民币</p>
                        </div>
                    </div>
                `;
            }
        }

        // 贷款计算器演示
        function showLoanCalculator() {
            const demoContent = document.getElementById('demo-content');
            const demoText = document.getElementById('demo-text');

            demoText.innerHTML = `
                <h3 style="color: #1890ff; margin-bottom: 16px;">💰 贷款计算器演示</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                    <div>
                        <h4>📊 贷款参数设置</h4>
                        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 4px; font-weight: bold;">贷款类型:</label>
                                <select id="loanType" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" onchange="toggleLoanFields()">
                                    <option value="商业贷款">商业贷款</option>
                                    <option value="公积金贷款">公积金贷款</option>
                                    <option value="组合贷款">组合贷款</option>
                                </select>
                            </div>
                            <div id="singleLoanFields">
                                <div style="margin-bottom: 16px;">
                                    <label style="display: block; margin-bottom: 4px; font-weight: bold;">贷款总额 (万元):</label>
                                    <input type="number" id="totalAmount" value="100" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                            </div>
                            <div id="combinedLoanFields" style="display: none;">
                                <div style="margin-bottom: 16px;">
                                    <label style="display: block; margin-bottom: 4px; font-weight: bold;">商业贷款额度 (万元):</label>
                                    <input type="number" id="commercialAmount" value="60" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                <div style="margin-bottom: 16px;">
                                    <label style="display: block; margin-bottom: 4px; font-weight: bold;">公积金贷款额度 (万元):</label>
                                    <input type="number" id="providentAmount" value="40" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                            </div>
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 4px; font-weight: bold;">贷款期限:</label>
                                <select id="loanTermYears" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="10">10年</option>
                                    <option value="15">15年</option>
                                    <option value="20" selected>20年</option>
                                    <option value="25">25年</option>
                                    <option value="30">30年</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 4px; font-weight: bold;">还款方式:</label>
                                <select id="repaymentMethod" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="等额本息" selected>等额本息</option>
                                    <option value="等额本金">等额本金</option>
                                    <option value="先息后本">先息后本</option>
                                </select>
                            </div>
                            <button onclick="calculateLoanPayment()" style="width: 100%; padding: 12px; background: linear-gradient(45deg, #1890ff, #722ed1); color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">
                                🧮 开始计算
                            </button>
                        </div>
                    </div>
                    <div>
                        <h4>📈 计算结果</h4>
                        <div id="calculatorResults" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); min-height: 300px;">
                            <div style="text-align: center; color: #999; padding: 40px 0;">
                                <div style="font-size: 48px; margin-bottom: 16px;">🧮</div>
                                <div>点击"开始计算"查看结果</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 24px; padding: 16px; background: #f0f8ff; border-radius: 8px; border-left: 4px solid #1890ff;">
                    <h4 style="margin: 0 0 8px 0; color: #1890ff;">💡 计算器特色</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>📊 支持商业贷款、公积金贷款、组合贷款三种类型</li>
                        <li>💰 基于2025年最新央行基准利率计算</li>
                        <li>🔄 支持等额本息、等额本金、先息后本三种还款方式</li>
                        <li>⚡ 实时计算，精确到分</li>
                    </ul>
                </div>
            `;

            demoContent.style.display = 'block';
            demoContent.scrollIntoView({ behavior: 'smooth' });
        }

        // 切换贷款字段显示
        function toggleLoanFields() {
            const loanType = document.getElementById('loanType').value;
            const singleFields = document.getElementById('singleLoanFields');
            const combinedFields = document.getElementById('combinedLoanFields');

            if (loanType === '组合贷款') {
                singleFields.style.display = 'none';
                combinedFields.style.display = 'block';
            } else {
                singleFields.style.display = 'block';
                combinedFields.style.display = 'none';
            }
        }

        // 执行贷款计算
        async function calculateLoanPayment() {
            const loanType = document.getElementById('loanType').value;
            const repaymentMethod = document.getElementById('repaymentMethod').value;
            const loanTermYears = parseInt(document.getElementById('loanTermYears').value);
            const loanTerm = loanTermYears * 12; // 转换为月数

            let requestData = {
                loanType,
                repaymentMethod,
                loanTerm
            };

            if (loanType === '组合贷款') {
                requestData.commercialAmount = parseFloat(document.getElementById('commercialAmount').value) * 10000;
                requestData.providentAmount = parseFloat(document.getElementById('providentAmount').value) * 10000;
            } else {
                requestData.totalAmount = parseFloat(document.getElementById('totalAmount').value) * 10000;
            }

            const resultsDiv = document.getElementById('calculatorResults');
            resultsDiv.innerHTML = '<div style="text-align: center; padding: 40px 0;"><div style="color: #1890ff; font-size: 18px;">🧮 计算中...</div><div style="color: #999; margin-top: 8px;">正在计算还款方案</div></div>';

            try {
                const response = await fetch('http://localhost:3001/api/loan/calculator', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();

                if (data.success) {
                    const result = data.data;
                    resultsDiv.innerHTML = `
                        <h4 style="color: #52c41a; margin: 0 0 16px 0;">🎉 计算完成</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">
                            <div style="background: #f6ffed; padding: 16px; border-radius: 8px; border-left: 4px solid #52c41a;">
                                <div style="font-size: 14px; color: #666; margin-bottom: 4px;">月供金额</div>
                                <div style="font-size: 24px; font-weight: bold; color: #52c41a;">¥${result.monthlyPaymentAmount.toLocaleString()}</div>
                            </div>
                            <div style="background: #fff7e6; padding: 16px; border-radius: 8px; border-left: 4px solid #faad14;">
                                <div style="font-size: 14px; color: #666; margin-bottom: 4px;">总利息</div>
                                <div style="font-size: 24px; font-weight: bold; color: #faad14;">¥${result.totalInterest.toLocaleString()}</div>
                            </div>
                        </div>
                        <div style="background: #f0f8ff; padding: 16px; border-radius: 8px; margin-bottom: 16px;">
                            <h5 style="margin: 0 0 12px 0; color: #1890ff;">📊 贷款详情</h5>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 14px;">
                                <div><strong>贷款金额：</strong>¥${result.summary.loanAmount.toLocaleString()}</div>
                                <div><strong>贷款期限：</strong>${loanTermYears}年</div>
                                <div><strong>年利率：</strong>${(result.summary.interestRate * 100).toFixed(2)}%</div>
                                <div><strong>还款方式：</strong>${result.summary.repaymentMethod}</div>
                                <div><strong>还款总额：</strong>¥${result.totalPayment.toLocaleString()}</div>
                                <div><strong>利息总额：</strong>¥${result.totalInterest.toLocaleString()}</div>
                            </div>
                        </div>
                        <div style="background: #f6f8fa; padding: 12px; border-radius: 8px; font-size: 12px; color: #666;">
                            💡 提示：以上计算结果基于2025年最新央行基准利率，实际利率可能因银行政策和个人资质有所差异
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = '<div style="text-align: center; color: #ff4d4f; padding: 40px 0;">❌ 计算失败，请检查输入参数</div>';
                }
            } catch (error) {
                console.error('计算请求失败:', error);
                // 使用模拟计算
                const mockResult = simulateLoanCalculation(requestData);
                resultsDiv.innerHTML = `
                    <div style="background: #fff7e6; border: 1px solid #ffd591; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
                        <div style="color: #fa8c16; font-size: 14px;">⚠️ 后端服务未连接，使用模拟计算</div>
                    </div>
                    <h4 style="color: #52c41a; margin: 0 0 16px 0;">🎉 计算完成 (模拟)</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">
                        <div style="background: #f6ffed; padding: 16px; border-radius: 8px; border-left: 4px solid #52c41a;">
                            <div style="font-size: 14px; color: #666; margin-bottom: 4px;">月供金额</div>
                            <div style="font-size: 24px; font-weight: bold; color: #52c41a;">¥${mockResult.monthlyPayment.toLocaleString()}</div>
                        </div>
                        <div style="background: #fff7e6; padding: 16px; border-radius: 8px; border-left: 4px solid #faad14;">
                            <div style="font-size: 14px; color: #666; margin-bottom: 4px;">总利息</div>
                            <div style="font-size: 24px; font-weight: bold; color: #faad14;">¥${mockResult.totalInterest.toLocaleString()}</div>
                        </div>
                    </div>
                    <div style="background: #f0f8ff; padding: 16px; border-radius: 8px; margin-bottom: 16px;">
                        <h5 style="margin: 0 0 12px 0; color: #1890ff;">📊 贷款详情</h5>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 14px;">
                            <div><strong>贷款金额：</strong>¥${mockResult.loanAmount.toLocaleString()}</div>
                            <div><strong>贷款期限：</strong>${loanTermYears}年</div>
                            <div><strong>年利率：</strong>${mockResult.interestRate}%</div>
                            <div><strong>还款方式：</strong>${repaymentMethod}</div>
                            <div><strong>还款总额：</strong>¥${mockResult.totalPayment.toLocaleString()}</div>
                            <div><strong>利息总额：</strong>¥${mockResult.totalInterest.toLocaleString()}</div>
                        </div>
                    </div>
                `;
            }
        }

        // 模拟贷款计算
        function simulateLoanCalculation(params) {
            const { loanType, totalAmount, commercialAmount, providentAmount, loanTerm, repaymentMethod } = params;

            let amount = totalAmount || (commercialAmount + providentAmount) || 1000000;
            let rate = 0.049; // 4.9% 商业贷款利率

            if (loanType === '公积金贷款') {
                rate = 0.031; // 3.1% 公积金利率
            } else if (loanType === '组合贷款') {
                rate = 0.042; // 混合利率
            }

            const monthlyRate = rate / 12;
            let monthlyPayment, totalInterest;

            if (repaymentMethod === '等额本息') {
                monthlyPayment = amount * monthlyRate * Math.pow(1 + monthlyRate, loanTerm) /
                                (Math.pow(1 + monthlyRate, loanTerm) - 1);
                totalInterest = monthlyPayment * loanTerm - amount;
            } else if (repaymentMethod === '等额本金') {
                const monthlyPrincipal = amount / loanTerm;
                monthlyPayment = monthlyPrincipal + (amount * monthlyRate);
                totalInterest = amount * monthlyRate * (loanTerm + 1) / 2;
            } else { // 先息后本
                monthlyPayment = amount * monthlyRate;
                totalInterest = monthlyPayment * loanTerm;
            }

            return {
                loanAmount: amount,
                monthlyPayment: Math.round(monthlyPayment),
                totalInterest: Math.round(totalInterest),
                totalPayment: Math.round(amount + totalInterest),
                interestRate: (rate * 100).toFixed(2)
            };
        }

        function showQualification() {
            showDemo('🔍 资质审核流程演示', `
                <p><strong>审核流程：</strong></p>
                <ol>
                    <li>📷 证件上传：支持身份证、营业执照等15类证件</li>
                    <li>🔍 OCR识别：自动提取证件信息，准确率99.5%</li>
                    <li>👤 活体检测：人脸识别+活体检测，防止欺诈</li>
                    <li>📋 信息确认：用户确认提取的信息</li>
                    <li>⚡ AI评估：1-3秒完成资质评估</li>
                </ol>
                <p><strong>技术优势：</strong>多模态AI技术，支持图像、视频、文本多种输入方式</p>
                <p><strong>安全保障：</strong>等保三级认证，AES-256加密，确保数据安全</p>
            `);
        }

        function showRiskDashboard() {
            showDemo('📊 风控看板演示', `
                <p><strong>实时监控指标：</strong></p>
                <ul>
                    <li>📈 申请量趋势：实时监控贷款申请数量变化</li>
                    <li>⚠️ 风险分布：高/中/低风险申请占比分析</li>
                    <li>🎯 批准率统计：动态计算批准率和拒绝率</li>
                    <li>🚨 异常预警：智能识别异常申请模式</li>
                </ul>
                <p><strong>AI风控模型：</strong></p>
                <ul>
                    <li>🧠 联邦学习：多机构数据联合训练，保护隐私</li>
                    <li>⚡ 实时决策：毫秒级风险评估响应</li>
                    <li>🔄 自适应学习：模型持续优化，适应市场变化</li>
                </ul>
            `);
        }

        function showAIAdvisor() {
            const demoContent = document.getElementById('demo-content');
            const demoText = document.getElementById('demo-text');

            demoText.innerHTML = `
                <h3 style="color: #1890ff; margin-bottom: 16px;">🤖 AI虚拟顾问演示</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                    <div>
                        <h4>💬 与AI顾问对话</h4>
                        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 4px; font-weight: bold;">请输入您的问题:</label>
                                <input type="text" id="userQuery" placeholder="例如：贷款利率、申请条件、审批时间..." style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="display: flex; gap: 8px; margin-bottom: 16px; flex-wrap: wrap;">
                                <button onclick="askAI('贷款利率')" style="padding: 6px 12px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; font-size: 12px;">贷款利率</button>
                                <button onclick="askAI('申请条件')" style="padding: 6px 12px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; font-size: 12px;">申请条件</button>
                                <button onclick="askAI('审批时间')" style="padding: 6px 12px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; font-size: 12px;">审批时间</button>
                                <button onclick="askAI('数字货币')" style="padding: 6px 12px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; font-size: 12px;">数字货币</button>
                            </div>
                            <button onclick="askAI()" style="width: 100%; padding: 12px; background: linear-gradient(45deg, #1890ff, #722ed1); color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">
                                🤖 咨询AI顾问
                            </button>
                        </div>
                    </div>
                    <div>
                        <h4>💭 AI顾问回复</h4>
                        <div id="aiResponse" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); min-height: 300px;">
                            <div style="text-align: center; color: #999; padding: 40px 0;">
                                <div style="font-size: 48px; margin-bottom: 16px;">🤖</div>
                                <div>您好！我是SmartLoan AI顾问</div>
                                <div style="font-size: 14px; margin-top: 8px;">请输入问题开始对话</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 24px; padding: 16px; background: #f0f8ff; border-radius: 8px; border-left: 4px solid #1890ff;">
                    <h4 style="margin: 0 0 8px 0; color: #1890ff;">🌟 AI顾问特色</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>🧠 基于Fin-R1大模型，专业金融知识</li>
                        <li>💬 支持自然语言理解，智能语义分析</li>
                        <li>🕐 7×24小时在线服务，即时响应</li>
                        <li>🎯 个性化建议，精准匹配用户需求</li>
                    </ul>
                </div>
            `;

            demoContent.style.display = 'block';
            demoContent.scrollIntoView({ behavior: 'smooth' });
        }

        // AI顾问对话
        async function askAI(predefinedQuery) {
            const query = predefinedQuery || document.getElementById('userQuery').value;
            if (!query.trim()) {
                alert('请输入您的问题');
                return;
            }

            const responseDiv = document.getElementById('aiResponse');
            responseDiv.innerHTML = '<div style="text-align: center; padding: 40px 0;"><div style="color: #1890ff; font-size: 18px;">🤖 AI顾问思考中...</div><div style="color: #999; margin-top: 8px;">正在分析您的问题</div></div>';

            try {
                const response = await fetch('http://localhost:3001/api/ai/advisor/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        context: {
                            user_type: 'demo_user',
                            session_id: 'demo_session'
                        }
                    })
                });

                const data = await response.json();

                if (data.success) {
                    const aiData = data.data;
                    responseDiv.innerHTML = `
                        <div style="border-bottom: 1px solid #f0f0f0; padding-bottom: 16px; margin-bottom: 16px;">
                            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                <div style="width: 32px; height: 32px; background: linear-gradient(45deg, #1890ff, #722ed1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; margin-right: 12px;">🤖</div>
                                <div>
                                    <div style="font-weight: bold; color: #333;">SmartLoan AI顾问</div>
                                    <div style="font-size: 12px; color: #999;">${aiData.ai_model} • 置信度: ${(aiData.confidence * 100).toFixed(1)}%</div>
                                </div>
                            </div>
                            <div style="background: #f6f8fa; padding: 16px; border-radius: 8px; line-height: 1.6;">
                                ${aiData.response}
                            </div>
                        </div>
                        <div>
                            <h5 style="margin: 0 0 8px 0; color: #666;">💡 相关建议</h5>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                ${aiData.suggestions ? aiData.suggestions.map(suggestion =>
                                    `<button onclick="askAI('${suggestion}')" style="padding: 6px 12px; background: #f0f8ff; border: 1px solid #d6e4ff; border-radius: 4px; cursor: pointer; font-size: 12px; color: #1890ff;">${suggestion}</button>`
                                ).join('') : ''}
                            </div>
                        </div>
                    `;
                } else {
                    responseDiv.innerHTML = '<div style="text-align: center; color: #ff4d4f; padding: 40px 0;">❌ AI顾问暂时无法回复，请稍后再试</div>';
                }
            } catch (error) {
                console.error('AI顾问请求失败:', error);
                // 使用模拟回复
                const mockResponses = {
                    '贷款利率': '2025年央行基准利率下调后，个人信用贷款利率普遍在3.8%-6.5%之间，具体利率根据您的信用状况、数字资产和ESG评分确定。',
                    '申请条件': '2025年贷款申请更加便民：年满18周岁、稳定收入（包括零工经济）、良好信用记录。支持Web3.0身份认证和数字人民币。',
                    '审批时间': '采用2025年最新AI技术，结合区块链征信，最快30秒完成审批，平均审批时间缩短至2小时。',
                    '数字货币': '全面支持数字人民币(CBDC)，享受更低手续费和更快到账速度。'
                };

                let response = mockResponses[query] || '您好！我是SmartLoan 2025年AI金融顾问。基于最新的AI技术，我可以为您提供个性化的金融服务建议。请问您想了解什么？';

                responseDiv.innerHTML = `
                    <div style="background: #fff7e6; border: 1px solid #ffd591; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
                        <div style="color: #fa8c16; font-size: 14px;">⚠️ 后端服务未连接，使用模拟回复</div>
                    </div>
                    <div style="border-bottom: 1px solid #f0f0f0; padding-bottom: 16px; margin-bottom: 16px;">
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <div style="width: 32px; height: 32px; background: linear-gradient(45deg, #1890ff, #722ed1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; margin-right: 12px;">🤖</div>
                            <div>
                                <div style="font-weight: bold; color: #333;">SmartLoan AI顾问 (模拟)</div>
                                <div style="font-size: 12px; color: #999;">Fin-R1-2025 • 置信度: 95.0%</div>
                            </div>
                        </div>
                        <div style="background: #f6f8fa; padding: 16px; border-radius: 8px; line-height: 1.6;">
                            ${response}
                        </div>
                    </div>
                    <div>
                        <h5 style="margin: 0 0 8px 0; color: #666;">💡 相关建议</h5>
                        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                            <button onclick="askAI('贷款利率')" style="padding: 6px 12px; background: #f0f8ff; border: 1px solid #d6e4ff; border-radius: 4px; cursor: pointer; font-size: 12px; color: #1890ff;">贷款利率</button>
                            <button onclick="askAI('申请条件')" style="padding: 6px 12px; background: #f0f8ff; border: 1px solid #d6e4ff; border-radius: 4px; cursor: pointer; font-size: 12px; color: #1890ff;">申请条件</button>
                            <button onclick="askAI('审批时间')" style="padding: 6px 12px; background: #f0f8ff; border: 1px solid #d6e4ff; border-radius: 4px; cursor: pointer; font-size: 12px; color: #1890ff;">审批时间</button>
                            <button onclick="askAI('数字货币')" style="padding: 6px 12px; background: #f0f8ff; border: 1px solid #d6e4ff; border-radius: 4px; cursor: pointer; font-size: 12px; color: #1890ff;">数字货币</button>
                        </div>
                    </div>
                `;
            }

            // 清空输入框
            if (!predefinedQuery) {
                document.getElementById('userQuery').value = '';
            }
        }

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // 导航菜单交互
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
