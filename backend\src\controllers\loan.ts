import { Controller, Get, Post, Put, Delete, Body, Param, Request, UseGuards, HttpException, HttpStatus, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { LoanApplicationService } from '../services/loan-application.service';
import { CreateLoanApplicationDto } from '../dto/create-loan-application.dto';
import { UpdateLoanApplicationDto } from '../dto/update-loan-application.dto';

@ApiTags('loans')
@Controller('loans')
export class LoanController {
  constructor(private readonly loanApplicationService: LoanApplicationService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建贷款申请' })
  async createLoan(@Body() createDto: CreateLoanApplicationDto, @Request() req: any) {
    try {
      const application = await this.loanApplicationService.create(createDto, req.user.id);
      return {
        code: 0,
        message: '贷款申请创建成功',
        data: application
      };
    } catch (error) {
      throw new HttpException('创建贷款申请失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取贷款列表' })
  async getLoans(@Request() req: any, @Query() query: any) {
    try {
      const loans = await this.loanApplicationService.findAllByUser(req.user.id);
      return {
        code: 0,
        data: loans
      };
    } catch (error) {
      throw new HttpException('获取贷款列表失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get(':loanId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取贷款详情' })
  async getLoanDetail(@Param('loanId') loanId: string, @Request() req: any) {
    try {
      const loan = await this.loanApplicationService.findById(loanId);
      if (!loan) {
        throw new HttpException('贷款申请不存在', HttpStatus.NOT_FOUND);
      }
      return {
        code: 0,
        data: loan
      };
    } catch (error) {
      throw new HttpException('获取贷款详情失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Put(':loanId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新贷款申请' })
  async updateLoan(@Param('loanId') loanId: string, @Body() updateDto: UpdateLoanApplicationDto, @Request() req: any) {
    try {
      // 处理 documents 字段类型转换
      const { documents, ...updateData } = updateDto;
      const processedUpdateData = {
        ...updateData,
        // documents 字段在后端通过单独的 API 处理
      };
      
      const loan = await this.loanApplicationService.updateApplication(loanId, req.user, processedUpdateData);
      return {
        code: 0,
        message: '贷款申请更新成功',
        data: loan
      };
    } catch (error) {
      throw new HttpException('更新贷款申请失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}

// Express 风格的导出函数，用于路由兼容
export const createLoan = async (req: any, res: any) => {
  try {
    // 这里需要实现创建贷款逻辑
    res.json({ code: 0, message: '创建贷款功能待实现' });
  } catch (error) {
    res.status(500).json({ code: 1, message: '创建贷款失败' });
  }
};

export const getLoans = async (req: any, res: any) => {
  try {
    // 这里需要实现获取贷款列表逻辑
    res.json({ code: 0, message: '获取贷款列表功能待实现' });
  } catch (error) {
    res.status(500).json({ code: 1, message: '获取贷款列表失败' });
  }
};

export const getLoanDetail = async (req: any, res: any) => {
  try {
    // 这里需要实现获取贷款详情逻辑
    res.json({ code: 0, message: '获取贷款详情功能待实现' });
  } catch (error) {
    res.status(500).json({ code: 1, message: '获取贷款详情失败' });
  }
};