interface LoanEvaluationParams {
    amount: number;
    term: number;
    monthlyIncome: number;
    creditScore: number;
    houseStatus: string;
    carStatus: string;
}
interface LoanEvaluationResult {
    approved: boolean;
    score: number;
    reason: string;
    suggestedAmount?: number;
    suggestedTerm?: number;
}
export declare const evaluateLoan: (params: LoanEvaluationParams) => Promise<LoanEvaluationResult>;
export {};
