import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { LoanApplication } from './loan-application.entity';
import { LoanType } from '../enums/loan-type.enum';
import { LoanPurpose } from '../enums/loan-purpose.enum';

export enum ProductCategory {
  PERSONAL_LOAN = 'personal_loan',
  BUSINESS_LOAN = 'business_loan',
  MORTGAGE = 'mortgage',
  CAR_LOAN = 'car_loan',
  CREDIT_CARD = 'credit_card',
  OTHER = 'other'
}

interface ProductFeature {
  name: string;
  description: string;
  icon: string;
}

interface ProductRequirements {
  minAge: number;
  maxAge: number;
  minIncome: number;
  minCreditScore: number;
  maxDebtToIncomeRatio: number;
  employmentTypes: string[];
  requiredDocuments: string[];
  workExperienceYears?: number;
  additionalFactors?: string[];
}

interface ProductMetadata {
  category: string;
  tags: string[];
  riskLevel: string;
  popularity: number;
  approvalRate: number;
  averageProcessingTime: number;
}

interface RiskAssessment {
  maxDebtToIncomeRatio: number;
  minCreditScore: number;
  requiredDocuments: string[];
  additionalFactors: string[];
}

@Entity('products')
export class Product {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  name: string;

  @Column({ length: 50, unique: true })
  code: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ length: 50, nullable: true })
  category: string;

  @Column({ length: 50, nullable: true })
  loanType: string;

  @Column({ type: 'decimal', precision: 12, scale: 2 })
  minAmount: number;

  @Column({ type: 'decimal', precision: 12, scale: 2 })
  maxAmount: number;

  @Column({ type: 'int' })
  minTerm: number;

  @Column({ type: 'int' })
  maxTerm: number;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  interestRate: number;

  @Column({ length: 20, nullable: true })
  interestType: string;

  @Column({ length: 20, nullable: true })
  interestCalculationMethod: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  processingFee: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  earlyRepaymentFee: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  latePaymentFee: number;

  @Column({ type: 'int', nullable: true })
  minAge: number;

  @Column({ type: 'int', nullable: true })
  maxAge: number;

  @Column({ type: 'decimal', precision: 12, scale: 2, nullable: true })
  minIncome: number;

  @Column({ type: 'int', nullable: true })
  minCreditScore: number;

  @Column({ type: 'jsonb', nullable: true })
  features: ProductFeature[];

  @Column({ type: 'simple-array', nullable: true })
  benefits: string[];

  @Column({ type: 'jsonb', nullable: true })
  requirements: ProductRequirements;

  @Column({ type: 'jsonb', nullable: true })
  metadata: ProductMetadata;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isFeatured: boolean;

  @Column({ type: 'int', default: 0 })
  sortOrder: number;

  @Column("simple-array", { nullable: true })
  tags: string[];

  @Column({ type: 'jsonb', nullable: true })
  riskAssessment: RiskAssessment;

  @OneToMany(() => LoanApplication, application => application.product)
  applications: LoanApplication[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}