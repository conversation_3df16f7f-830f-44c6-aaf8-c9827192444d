import { Injectable, NestInterceptor, Execution<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CacheService } from '../../services/cache.service';

export interface CacheOptions {
  ttl?: number;
  key?: string;
}

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheInterceptor.name);

  constructor(private readonly cacheService: CacheService) {}

  async intercept(context: ExecutionContext, next: CallHandler, options: CacheOptions = {}): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const key = options.key || this.generateCacheKey(request);

    try {
      const cachedData = await this.cacheService.get(key);
      if (cachedData) {
        this.logger.debug(`Cache hit for key: ${key}`);
        return of(cachedData);
      }

      this.logger.debug(`Cache miss for key: ${key}`);
      return next.handle().pipe(
        tap(async (data) => {
          try {
            await this.cacheService.set(key, data, options.ttl);
            this.logger.debug(`Data cached for key: ${key}`);
          } catch (error) {
            this.logger.error(`Failed to cache data for key: ${key}`, error);
          }
        })
      );
    } catch (error) {
      this.logger.error(`Cache operation failed for key: ${key}`, error);
      return next.handle();
    }
  }

  private generateCacheKey(request: any): string {
    const { method, url, params, query, body } = request;
    return `${method}:${url}:${JSON.stringify(params)}:${JSON.stringify(query)}:${JSON.stringify(body)}`;
  }
} 