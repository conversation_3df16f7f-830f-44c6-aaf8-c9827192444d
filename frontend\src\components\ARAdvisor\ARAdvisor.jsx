/**
 * ARAdvisor AR顾问交互组件
 * 基于Fin-R1大模型的智能金融顾问
 * 支持语音、手势、AR多通道交互
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX, 
  Eye, 
  EyeOff,
  MessageCircle,
  Zap,
  Brain,
  Sparkles
} from 'lucide-react';
import './ARAdvisor.scss';

const ARAdvisor = ({
  model = 'Fin-R1',
  gestures = ['swipe', 'pinch'],
  voiceControl = {
    languages: ['zh-CN', 'en-US'],
    wakeWord: '小智'
  },
  visualElements = {
    dataTips: true,
    pathHighlights: true
  },
  onMessage,
  onGesture,
  className = ''
}) => {
  const [isActive, setIsActive] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [currentMessage, setCurrentMessage] = useState('');
  const [conversationHistory, setConversationHistory] = useState([]);
  const [arMode, setArMode] = useState(false);
  const [gestureDetected, setGestureDetected] = useState(null);
  const [aiThinking, setAiThinking] = useState(false);
  
  const canvasRef = useRef(null);
  const speechRecognition = useRef(null);
  const speechSynthesis = useRef(null);
  const gestureDetector = useRef(null);

  // 初始化语音识别
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      speechRecognition.current = new SpeechRecognition();
      speechRecognition.current.continuous = true;
      speechRecognition.current.interimResults = true;
      speechRecognition.current.lang = voiceControl.languages[0];

      speechRecognition.current.onresult = (event) => {
        const transcript = Array.from(event.results)
          .map(result => result[0].transcript)
          .join('');
        
        if (transcript.includes(voiceControl.wakeWord)) {
          handleWakeWordDetected();
        }
        
        setCurrentMessage(transcript);
      };

      speechRecognition.current.onend = () => {
        setIsListening(false);
      };
    }

    // 初始化语音合成
    if ('speechSynthesis' in window) {
      speechSynthesis.current = window.speechSynthesis;
    }

    return () => {
      if (speechRecognition.current) {
        speechRecognition.current.stop();
      }
    };
  }, [voiceControl]);

  // 初始化手势检测
  useEffect(() => {
    if (arMode && canvasRef.current) {
      initializeGestureDetection();
    }
  }, [arMode]);

  const initializeGestureDetection = () => {
    // 模拟手势检测初始化
    console.log('🤲 初始化手势检测:', gestures);
    
    // 模拟手势事件
    const gestureEvents = ['swipe-left', 'swipe-right', 'pinch-in', 'pinch-out', 'tap'];
    
    const simulateGesture = () => {
      if (arMode) {
        const randomGesture = gestureEvents[Math.floor(Math.random() * gestureEvents.length)];
        setGestureDetected(randomGesture);
        onGesture && onGesture(randomGesture);
        
        setTimeout(() => setGestureDetected(null), 2000);
      }
    };

    gestureDetector.current = setInterval(simulateGesture, 10000);
  };

  const handleWakeWordDetected = () => {
    setIsActive(true);
    setIsListening(true);
    playWakeSound();
  };

  const startListening = () => {
    if (speechRecognition.current && !isListening) {
      setIsListening(true);
      speechRecognition.current.start();
    }
  };

  const stopListening = () => {
    if (speechRecognition.current && isListening) {
      setIsListening(false);
      speechRecognition.current.stop();
      
      if (currentMessage.trim()) {
        processUserMessage(currentMessage);
      }
    }
  };

  const processUserMessage = async (message) => {
    setAiThinking(true);
    
    // 添加用户消息到历史
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: message,
      timestamp: new Date().toISOString()
    };
    
    setConversationHistory(prev => [...prev, userMessage]);
    setCurrentMessage('');

    try {
      // 模拟AI处理
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const aiResponse = await generateAIResponse(message);
      
      const aiMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: aiResponse,
        timestamp: new Date().toISOString(),
        model: model
      };
      
      setConversationHistory(prev => [...prev, aiMessage]);
      
      // 语音播报AI回复
      if (speechSynthesis.current) {
        speakMessage(aiResponse);
      }
      
      onMessage && onMessage(aiMessage);
    } catch (error) {
      console.error('AI处理失败:', error);
    } finally {
      setAiThinking(false);
    }
  };

  const generateAIResponse = async (userMessage) => {
    // 模拟Fin-R1大模型响应
    const responses = {
      '贷款': '基于您的信用状况，我推荐工商银行融e借2025版，支持数字人民币，30秒审批，利率3.85%起。',
      '利率': '2025年央行基准利率为4.35%，优质客户可享受3.5%-4.0%的优惠利率。建议您选择固定利率产品。',
      '额度': '根据您的收入和信用评分，预估可申请额度为50-80万元。具体额度需要通过AI风控模型评估。',
      '审批': '采用沐曦GPU加速的AI审批系统，一般30秒内完成初审，1小时内完成终审。',
      '风险': '您的风险等级为低风险，建议选择稳健型金融产品，可考虑ESG认证的绿色金融产品。',
      'default': '我是基于Fin-R1大模型的智能金融顾问，可以为您提供专业的贷款咨询服务。请问您需要了解什么？'
    };

    for (const [keyword, response] of Object.entries(responses)) {
      if (userMessage.includes(keyword)) {
        return response;
      }
    }
    
    return responses.default;
  };

  const speakMessage = (message) => {
    if (speechSynthesis.current) {
      setIsSpeaking(true);
      const utterance = new SpeechSynthesisUtterance(message);
      utterance.lang = voiceControl.languages[0];
      utterance.rate = 0.9;
      utterance.pitch = 1.1;
      
      utterance.onend = () => {
        setIsSpeaking(false);
      };
      
      speechSynthesis.current.speak(utterance);
    }
  };

  const playWakeSound = () => {
    // 播放唤醒音效
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
    
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);
  };

  const toggleArMode = () => {
    setArMode(!arMode);
    if (!arMode) {
      setIsActive(true);
    }
  };

  const getStatusText = () => {
    if (aiThinking) return 'AI思考中...';
    if (isListening) return '正在聆听...';
    if (isSpeaking) return '正在回复...';
    if (isActive) return '等待指令';
    return '点击激活';
  };

  return (
    <div className={`ar-advisor ${className} ${isActive ? 'ar-advisor--active' : ''} ${arMode ? 'ar-advisor--ar-mode' : ''}`}>
      {/* AR画布 */}
      {arMode && (
        <canvas 
          ref={canvasRef}
          className="ar-advisor__canvas"
          width={300}
          height={200}
        />
      )}

      {/* 主控制面板 */}
      <div className="ar-advisor__control-panel">
        {/* 状态指示器 */}
        <div className="ar-advisor__status">
          <div className={`ar-advisor__status-indicator ar-advisor__status-indicator--${isActive ? 'active' : 'inactive'}`}>
            <Brain className="ar-advisor__status-icon" />
            {aiThinking && <div className="ar-advisor__thinking-dots"></div>}
          </div>
          <span className="ar-advisor__status-text">{getStatusText()}</span>
        </div>

        {/* 控制按钮 */}
        <div className="ar-advisor__controls">
          <button
            className={`ar-advisor__control-btn ar-advisor__control-btn--voice ${isListening ? 'ar-advisor__control-btn--active' : ''}`}
            onClick={isListening ? stopListening : startListening}
            title={isListening ? '停止聆听' : '开始语音'}
          >
            {isListening ? <MicOff size={20} /> : <Mic size={20} />}
          </button>

          <button
            className={`ar-advisor__control-btn ar-advisor__control-btn--speaker ${isSpeaking ? 'ar-advisor__control-btn--active' : ''}`}
            onClick={() => speechSynthesis.current?.cancel()}
            title={isSpeaking ? '停止播报' : '语音播报'}
          >
            {isSpeaking ? <VolumeX size={20} /> : <Volume2 size={20} />}
          </button>

          <button
            className={`ar-advisor__control-btn ar-advisor__control-btn--ar ${arMode ? 'ar-advisor__control-btn--active' : ''}`}
            onClick={toggleArMode}
            title={arMode ? '退出AR模式' : '启用AR模式'}
          >
            {arMode ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        </div>
      </div>

      {/* 对话历史 */}
      {isActive && (
        <div className="ar-advisor__conversation">
          <div className="ar-advisor__conversation-header">
            <MessageCircle size={16} />
            <span>对话记录</span>
            <div className="ar-advisor__model-badge">
              <Sparkles size={12} />
              {model}
            </div>
          </div>
          
          <div className="ar-advisor__messages">
            {conversationHistory.slice(-3).map((message) => (
              <div 
                key={message.id}
                className={`ar-advisor__message ar-advisor__message--${message.type}`}
              >
                <div className="ar-advisor__message-content">
                  {message.content}
                </div>
                <div className="ar-advisor__message-time">
                  {new Date(message.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
            
            {aiThinking && (
              <div className="ar-advisor__message ar-advisor__message--ai ar-advisor__message--thinking">
                <div className="ar-advisor__thinking-animation">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 手势检测提示 */}
      {gestureDetected && (
        <div className="ar-advisor__gesture-feedback">
          <Zap size={16} />
          检测到手势: {gestureDetected}
        </div>
      )}

      {/* 语音波形动画 */}
      {isListening && (
        <div className="ar-advisor__voice-wave">
          <div className="ar-advisor__wave-bar"></div>
          <div className="ar-advisor__wave-bar"></div>
          <div className="ar-advisor__wave-bar"></div>
          <div className="ar-advisor__wave-bar"></div>
          <div className="ar-advisor__wave-bar"></div>
        </div>
      )}

      {/* 唤醒词提示 */}
      {!isActive && (
        <div className="ar-advisor__wake-hint">
          <div className="ar-advisor__wake-icon">🎤</div>
          <div className="ar-advisor__wake-text">
            说出 "{voiceControl.wakeWord}" 唤醒AI顾问
          </div>
        </div>
      )}
    </div>
  );
};

export default ARAdvisor;
