/// <reference types="multer" />
import { ProductImportExportService } from '../services/product-import-export.service';
import { Response as ExpressResponse } from 'express';
export declare class ProductImportExportController {
    private readonly productImportExportService;
    constructor(productImportExportService: ProductImportExportService);
    importProducts(file: Express.Multer.File): Promise<{
        total: number;
        created: number;
        updated: number;
        failed: number;
        details: ({
            code: string;
            status: string;
            error?: undefined;
        } | {
            code: string;
            status: string;
            error: any;
        })[];
    }>;
    exportProducts(res: ExpressResponse): Promise<void>;
    exportTemplate(res: ExpressResponse): Promise<void>;
}
