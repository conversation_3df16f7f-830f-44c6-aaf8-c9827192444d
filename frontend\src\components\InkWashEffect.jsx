/**
 * React+Three.js水墨动效组件
 * 基于WebGL硬件加速的国潮风格动效
 * 2025年最新技术栈
 */

import React, { useRef, useEffect, useMemo } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Points, PointMaterial, Sphere } from '@react-three/drei';
import * as THREE from 'three';

// 水墨粒子系统
function InkParticles({ count = 5000 }) {
  const mesh = useRef();
  const [positions, colors] = useMemo(() => {
    const positions = new Float32Array(count * 3);
    const colors = new Float32Array(count * 3);
    
    for (let i = 0; i < count; i++) {
      // 随机分布粒子位置
      positions[i * 3] = (Math.random() - 0.5) * 20;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 20;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 20;
      
      // 水墨色彩渐变
      const intensity = Math.random();
      colors[i * 3] = intensity * 0.1; // R
      colors[i * 3 + 1] = intensity * 0.1; // G
      colors[i * 3 + 2] = intensity * 0.2; // B
    }
    
    return [positions, colors];
  }, [count]);

  useFrame((state) => {
    const time = state.clock.getElapsedTime();
    
    if (mesh.current) {
      // 水墨流动动画
      mesh.current.rotation.x = Math.sin(time / 4) * 0.1;
      mesh.current.rotation.y = Math.sin(time / 2) * 0.1;
      
      // 粒子位置更新
      const positions = mesh.current.geometry.attributes.position.array;
      for (let i = 0; i < count; i++) {
        const i3 = i * 3;
        positions[i3 + 1] += Math.sin(time + positions[i3]) * 0.01;
      }
      mesh.current.geometry.attributes.position.needsUpdate = true;
    }
  });

  return (
    <Points ref={mesh} positions={positions} colors={colors}>
      <PointMaterial
        size={0.05}
        vertexColors
        transparent
        opacity={0.6}
        sizeAttenuation={true}
        depthWrite={false}
        blending={THREE.AdditiveBlending}
      />
    </Points>
  );
}

// 水墨扩散效果
function InkDiffusion() {
  const meshRef = useRef();
  
  useFrame((state) => {
    const time = state.clock.getElapsedTime();
    if (meshRef.current) {
      meshRef.current.material.uniforms.time.value = time;
    }
  });

  const shaderMaterial = useMemo(() => {
    return new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        resolution: { value: new THREE.Vector2(window.innerWidth, window.innerHeight) }
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec2 resolution;
        varying vec2 vUv;
        
        float noise(vec2 p) {
          return sin(p.x * 10.0 + time) * sin(p.y * 10.0 + time * 0.5) * 0.5 + 0.5;
        }
        
        void main() {
          vec2 uv = vUv;
          float n = noise(uv * 3.0);
          float ink = smoothstep(0.3, 0.7, n);
          
          vec3 color = mix(
            vec3(0.05, 0.05, 0.1),  // 深墨色
            vec3(0.8, 0.8, 0.9),    // 淡墨色
            ink
          );
          
          gl_FragColor = vec4(color, 0.3);
        }
      `,
      transparent: true,
      blending: THREE.MultiplyBlending
    });
  }, []);

  return (
    <mesh ref={meshRef} position={[0, 0, -5]}>
      <planeGeometry args={[20, 20]} />
      <primitive object={shaderMaterial} />
    </mesh>
  );
}

// 3D产品展示组件
function Product3D({ productData }) {
  const meshRef = useRef();
  
  useFrame((state) => {
    const time = state.clock.getElapsedTime();
    if (meshRef.current) {
      meshRef.current.rotation.y = time * 0.5;
      meshRef.current.position.y = Math.sin(time * 2) * 0.1;
    }
  });

  return (
    <group ref={meshRef}>
      <Sphere args={[1, 32, 32]} position={[0, 0, 0]}>
        <meshStandardMaterial
          color="#4a90e2"
          metalness={0.7}
          roughness={0.2}
          transparent
          opacity={0.8}
        />
      </Sphere>
      
      {/* 产品信息环绕 */}
      <mesh position={[0, 1.5, 0]}>
        <boxGeometry args={[2, 0.1, 0.1]} />
        <meshStandardMaterial color="#gold" />
      </mesh>
    </group>
  );
}

// 主要水墨效果组件
export default function InkWashEffect({ children, showProduct = false, productData = null }) {
  return (
    <div style={{ width: '100%', height: '100vh', position: 'relative' }}>
      <Canvas
        camera={{ position: [0, 0, 10], fov: 60 }}
        style={{ position: 'absolute', top: 0, left: 0 }}
        gl={{ alpha: true, antialias: true }}
      >
        {/* 环境光照 */}
        <ambientLight intensity={0.4} />
        <pointLight position={[10, 10, 10]} intensity={0.8} />
        <pointLight position={[-10, -10, -10]} intensity={0.3} color="#4a90e2" />
        
        {/* 水墨粒子系统 */}
        <InkParticles count={3000} />
        
        {/* 水墨扩散背景 */}
        <InkDiffusion />
        
        {/* 3D产品展示 */}
        {showProduct && productData && <Product3D productData={productData} />}
        
        {/* 后处理效果 */}
        <fog attach="fog" args={['#f0f0f0', 5, 25]} />
      </Canvas>
      
      {/* 前景内容 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 10,
        pointerEvents: 'none'
      }}>
        <div style={{
          textAlign: 'center',
          color: '#2c3e50',
          pointerEvents: 'auto'
        }}>
          <h1 style={{
            fontSize: '3.5rem',
            fontWeight: 700,
            marginBottom: '1rem',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textShadow: '2px 2px 4px rgba(0,0,0,0.1)',
            animation: 'titleFadeIn 2s ease-out'
          }}>
            SmartLoan 智慧金融
          </h1>
          <p style={{
            fontSize: '1.5rem',
            fontWeight: 300,
            opacity: 0.8,
            animation: 'subtitleFadeIn 2s ease-out 0.5s both'
          }}>
            AI驱动的个性化贷款服务平台
          </p>
          {children}
        </div>
      </div>
      
      <style jsx>{`
        @keyframes titleFadeIn {
          from {
            opacity: 0;
            transform: translateY(-30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes subtitleFadeIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 0.8;
            transform: translateY(0);
          }
        }
        
        @media (max-width: 768px) {
          h1 {
            font-size: 2.5rem !important;
          }
          p {
            font-size: 1.2rem !important;
          }
        }
      `}</style>
    </div>
  );
}
