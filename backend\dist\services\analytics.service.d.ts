import { Repository } from 'typeorm';
import { LoanApplication } from '../entities/loan-application.entity';
import { User } from '../entities/user.entity';
import { LoggerService } from './logger.service';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/error-handler';
import { DashboardData, AnalyticsData, ExportData, TrendPrediction, Anomaly, Recommendation } from '../interfaces/analytics.interface';
import { Analytics } from '../entities/analytics.entity';
export declare class AnalyticsService {
    private readonly loanApplicationRepository;
    private readonly logger;
    private readonly errorHandler;
    private analyticsRepository;
    constructor(loanApplicationRepository: Repository<LoanApplication>, logger: LoggerService, errorHandler: ErrorHandler, analyticsRepository: Repository<Analytics>);
    getDashboardData(query: any, user: User): Promise<DashboardData>;
    getAnalyticsData(query: any): Promise<AnalyticsData>;
    exportData(query: any, user: User): Promise<ExportData>;
    getExportData(id: string, user: User): Promise<ExportData>;
    predictTrends(query: any, user: User): Promise<TrendPrediction[]>;
    detectAnomalies(query: any, user: User): Promise<Anomaly[]>;
    getRecommendations(query: any, user: User): Promise<Recommendation[]>;
    private getApplicationsInDateRange;
    private calculateAverageScore;
    private calculateTrends;
    private calculateDistributions;
    private generateExportId;
    logError(error: any, query: any): Promise<Analytics>;
    logSearch(query: any): Promise<Analytics>;
    logView(id: string): Promise<Analytics>;
    logAction(action: string, data: any): Promise<Analytics>;
}
