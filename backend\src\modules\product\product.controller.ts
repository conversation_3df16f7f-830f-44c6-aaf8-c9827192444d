import { Controller, Get, Post, Body, Query, UseGuards, Param } from '@nestjs/common';
import { ProductService, ProductMatchCriteria } from './product.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('金融产品')
@Controller('products')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Get()
  @ApiOperation({ summary: '获取产品列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async findAll(@Query('type') type?: string) {
    if (type) {
      return this.productService.findByType(type);
    }
    return this.productService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: '获取产品详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async findById(@Param('id') id: number) {
    const product = await this.productService.findById(id);
    return {
      success: true,
      data: product,
      message: '获取产品详情成功'
    };
  }

  @Post('match')
  @ApiOperation({ summary: '智能产品匹配' })
  @ApiResponse({ status: 200, description: '匹配成功' })
  async matchProducts(
    @Body()
    criteria: {
      amount: number;
      term_months: number;
      product_type?: string;
    },
  ) {
    const products = await this.productService.matchProducts(criteria);
    return {
      success: true,
      data: products,
      message: '产品匹配成功'
    };
  }

  @Post('match/smart')
  @ApiOperation({ summary: '智能产品匹配（增强版）' })
  @ApiResponse({ status: 200, description: '智能匹配成功' })
  async smartMatch(@Body() criteria: ProductMatchCriteria) {
    const matches = await this.productService.findMatchingProducts(criteria);
    return {
      success: true,
      data: matches,
      message: '智能产品匹配成功',
      total: matches.length
    };
  }

  @Post('calculate')
  @ApiOperation({ summary: '计算贷款还款计划' })
  @ApiResponse({ status: 200, description: '计算成功' })
  async calculateLoan(
    @Body()
    loanData: {
      amount: number;
      term_months: number;
      annual_rate: number;
    },
  ) {
    const calculation = await this.productService.calculateMonthlyPayment(
      loanData.amount,
      loanData.term_months,
      loanData.annual_rate,
    );
    return {
      success: true,
      data: calculation,
      message: '贷款计算成功'
    };
  }
}
