import { LoanApplication } from './loan-application.entity';
import { ProductRequirements } from './product-requirements.entity';
import { LoanType } from '../enums/loan-type.enum';
export declare class Product {
    id: string;
    name: string;
    code: string;
    description: string;
    type: LoanType;
    minAmount: number;
    maxAmount: number;
    minTerm: number;
    maxTerm: number;
    interestRate: number;
    processingFee: number;
    lateFee: number;
    earlyRepaymentFee: number;
    features: Array<{
        name: string;
        description: string;
        icon: string;
    }>;
    requirements: ProductRequirements[];
    benefits: string[];
    metadata: {
        category: string;
        tags: string[];
        riskLevel: string;
        popularity: number;
        approvalRate: number;
        averageProcessingTime: number;
    };
    isActive: boolean;
    isFeatured: boolean;
    applications: LoanApplication[];
    createdAt: Date;
    updatedAt: Date;
}
