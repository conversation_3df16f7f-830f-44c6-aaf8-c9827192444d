import { User } from '../user/user.entity';
import { FinancialProduct } from '../product/entities/financial-product.entity';
export declare class LoanApplication {
    id: number;
    user_id: number;
    product_id: number;
    application_number: string;
    amount: number;
    purpose: string;
    employment_info: {
        company_name?: string;
        position?: string;
        employment_type?: string;
        monthly_income?: number;
        work_years?: number;
        industry?: string;
    };
    financial_info: {
        monthly_income?: number;
        other_income?: number;
        monthly_expenses?: number;
        assets?: number;
        liabilities?: number;
        credit_score?: number;
    };
    collateral_info: {
        type?: string;
        value?: number;
        description?: string;
    };
    status: string;
    ai_score: number;
    ai_recommendation: string;
    approval_amount: number;
    approved_rate: number;
    approved_term: number;
    rejection_reason: string;
    disbursement_date: Date;
    officer_id: number;
    documents: {
        id_card?: string;
        income_proof?: string;
        bank_statements?: string[];
        collateral_docs?: string[];
        other_docs?: string[];
    };
    risk_assessment: {
        score?: number;
        level?: string;
        factors?: string[];
        recommendation?: string;
    };
    approval_history: Array<{
        action: string;
        officer_id: number;
        timestamp: Date;
        comment?: string;
    }>;
    created_at: Date;
    updated_at: Date;
    user: User;
    product: FinancialProduct;
    static generateApplicationNumber(): string;
    calculateMonthlyPayment(): number;
    get statusDescription(): string;
    get riskLevel(): string;
    get canDisburse(): boolean;
}
