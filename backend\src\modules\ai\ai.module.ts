import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AIService } from './ai-service';
import { AIController } from './ai.controller';
import { LoggerModule } from '../../logger/logger.module';
import aiConfig from './ai.config';

@Module({
  imports: [
    ConfigModule.forFeature(aiConfig),
    LoggerModule,
  ],
  controllers: [AIController],
  providers: [AIService],
  exports: [AIService],
})
export class AIModule {} 