import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from '../entities/product.entity';
import { CacheService } from './cache.service';
import { LoggerService } from './logger.service';

@Injectable()
export class ProductComparisonService {
  private readonly CACHE_TTL = 3600; // 1小时缓存

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    private readonly cacheService: CacheService,
    private readonly logger: LoggerService
  ) {}

  async compareProducts(productIds: string[]) {
    try {      const cacheKey = `product:compare:${productIds.sort().join(',')}`;
      const cachedResults = await this.cacheService.get(cacheKey);
      
      if (cachedResults) {
        return JSON.parse(cachedResults as string);
      }

      const products = await this.productRepository.findByIds(productIds);
      
      if (products.length !== productIds.length) {
        throw new Error('部分产品未找到');
      }

      const comparison = {
        basic: this.compareBasicInfo(products),
        features: this.compareFeatures(products),
        requirements: this.compareRequirements(products),
        costs: this.compareCosts(products),
        benefits: this.compareBenefits(products)
      };

      await this.cacheService.set(cacheKey, JSON.stringify(comparison), this.CACHE_TTL);
      
      return comparison;
    } catch (error) {
      this.logger.error('比较产品失败', error);
      throw error;
    }
  }

  private compareBasicInfo(products: Product[]) {
    return products.map(product => ({
      id: product.id,
      name: product.name,
      code: product.code,
      description: product.description,
      category: product.category,
      minAmount: product.minAmount,
      maxAmount: product.maxAmount,
      minTerm: product.minTerm,
      maxTerm: product.maxTerm,
      interestRate: product.interestRate,
      isActive: product.isActive,
      isFeatured: product.isFeatured,
      metadata: product.metadata
    }));
  }

  private compareFeatures(products: Product[]) {
    const allFeatures = new Set<string>();
    products.forEach(product => {
      product.features.forEach(feature => {
        allFeatures.add(feature.name);
      });
    });

    return Array.from(allFeatures).map(featureName => {
      const featureComparison = products.map(product => {
        const feature = product.features.find(f => f.name === featureName);
        return {
          productId: product.id,
          productName: product.name,
          hasFeature: !!feature,
          description: feature?.description || '',
          icon: feature?.icon || ''
        };
      });

      return {
        name: featureName,
        comparison: featureComparison
      };
    });
  }

  private compareRequirements(products: Product[]) {
    const allRequirements = new Set<string>();
    products.forEach(product => {
      product.requirements.forEach(requirement => {
        allRequirements.add(requirement.name);
      });
    });

    return Array.from(allRequirements).map(requirementName => {
      const requirementComparison = products.map(product => {
        const requirement = product.requirements.find(r => r.name === requirementName);
        return {
          productId: product.id,
          productName: product.name,
          hasRequirement: !!requirement,
          description: requirement?.description || '',
          value: requirement?.value || ''
        };
      });

      return {
        name: requirementName,
        comparison: requirementComparison
      };
    });
  }

  private compareCosts(products: Product[]) {
    return products.map(product => ({
      productId: product.id,
      productName: product.name,
      interestRate: product.interestRate,
      processingFee: product.processingFee || 0,
      lateFee: product.lateFee || 0,
      earlyRepaymentFee: product.earlyRepaymentFee || 0
    }));
  }

  private compareBenefits(products: Product[]) {
    const allBenefits = new Set<string>();
    products.forEach(product => {
      product.benefits.forEach(benefit => {
        allBenefits.add(benefit);
      });
    });

    return Array.from(allBenefits).map(benefit => {
      const benefitComparison = products.map(product => ({
        productId: product.id,
        productName: product.name,
        hasBenefit: product.benefits.includes(benefit)
      }));

      return {
        name: benefit,
        comparison: benefitComparison
      };
    });
  }

  async getComparisonMatrix(productIds: string[]) {
    try {
      const cacheKey = `product:matrix:${productIds.sort().join(',')}`;
      const cachedResults = await this.cacheService.get(cacheKey);
      
      if (cachedResults) {
        return JSON.parse(cachedResults);
      }

      const products = await this.productRepository.findByIds(productIds);
      
      if (products.length !== productIds.length) {
        throw new Error('部分产品未找到');
      }

      const matrix = {
        basic: this.getBasicMatrix(products),
        features: this.getFeaturesMatrix(products),
        requirements: this.getRequirementsMatrix(products),
        costs: this.getCostsMatrix(products),
        benefits: this.getBenefitsMatrix(products)
      };

      await this.cacheService.set(cacheKey, JSON.stringify(matrix), this.CACHE_TTL);
      
      return matrix;
    } catch (error) {
      this.logger.error('获取比较矩阵失败', error);
      throw error;
    }
  }

  private getBasicMatrix(products: Product[]) {
    return {
      headers: ['产品名称', '产品代码', '产品类别', '最小金额', '最大金额', '最小期限', '最大期限', '利率'],
      rows: products.map(product => [
        product.name,
        product.code,
        product.category,
        product.minAmount,
        product.maxAmount,
        product.minTerm,
        product.maxTerm,
        product.interestRate
      ])
    };
  }

  private getFeaturesMatrix(products: Product[]) {
    const allFeatures = new Set<string>();
    products.forEach(product => {
      product.features.forEach(feature => {
        allFeatures.add(feature.name);
      });
    });

    return {
      headers: ['特性', ...products.map(p => p.name)],
      rows: Array.from(allFeatures).map(featureName => [
        featureName,
        ...products.map(product => 
          product.features.some(f => f.name === featureName) ? '✓' : '✗'
        )
      ])
    };
  }

  private getRequirementsMatrix(products: Product[]) {
    const allRequirements = new Set<string>();
    products.forEach(product => {
      product.requirements.forEach(requirement => {
        allRequirements.add(requirement.name);
      });
    });

    return {
      headers: ['要求', ...products.map(p => p.name)],
      rows: Array.from(allRequirements).map(requirementName => [
        requirementName,
        ...products.map(product => {
          const requirement = product.requirements.find(r => r.name === requirementName);
          return requirement ? requirement.value || '✓' : '✗';
        })
      ])
    };
  }

  private getCostsMatrix(products: Product[]) {
    return {
      headers: ['费用类型', ...products.map(p => p.name)],
      rows: [
        ['利率', ...products.map(p => `${p.interestRate}%`)],
        ['手续费', ...products.map(p => `${p.processingFee || 0}%`)],
        ['逾期费', ...products.map(p => `${p.lateFee || 0}%`)],
        ['提前还款费', ...products.map(p => `${p.earlyRepaymentFee || 0}%`)]
      ]
    };
  }

  private getBenefitsMatrix(products: Product[]) {
    const allBenefits = new Set<string>();
    products.forEach(product => {
      product.benefits.forEach(benefit => {
        allBenefits.add(benefit);
      });
    });

    return {
      headers: ['优势', ...products.map(p => p.name)],
      rows: Array.from(allBenefits).map(benefit => [
        benefit,
        ...products.map(product => 
          product.benefits.includes(benefit) ? '✓' : '✗'
        )
      ])
    };
  }
} 