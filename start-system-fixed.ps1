# SmartLoan 智能金融服务平台启动脚本 (修复版)
# PowerShell 脚本用于启动整个系统

Write-Host "🚀 启动 SmartLoan 智能金融服务平台..." -ForegroundColor Green

# 获取当前脚本目录
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

Write-Host "📍 当前工作目录: $scriptPath" -ForegroundColor Cyan

# 检查必要目录
$requiredDirs = @("frontend", "backend", "ai-service")
foreach ($dir in $requiredDirs) {
    if (Test-Path $dir) {
        Write-Host "✅ 发现目录: $dir" -ForegroundColor Green
    } else {
        Write-Host "❌ 缺少目录: $dir" -ForegroundColor Red
        exit 1
    }
}

# 检查 Node.js 是否安装
Write-Host "📋 检查 Node.js 状态..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js 已安装: $nodeVersion" -ForegroundColor Green
}
catch {
    Write-Host "❌ Node.js 未安装，请先安装 Node.js" -ForegroundColor Red
    exit 1
}

# 检查 npm 是否可用
try {
    $npmVersion = npm --version
    Write-Host "✅ npm 已安装: $npmVersion" -ForegroundColor Green
}
catch {
    Write-Host "❌ npm 不可用" -ForegroundColor Red
    exit 1
}

# 启动后端服务
Write-Host "🔧 启动后端服务..." -ForegroundColor Yellow
$backendPath = Join-Path $scriptPath "backend"
if (Test-Path $backendPath) {
    Write-Host "📂 进入后端目录: $backendPath" -ForegroundColor Cyan
    
    # 检查是否需要安装依赖
    $backendNodeModules = Join-Path $backendPath "node_modules"
    if (-not (Test-Path $backendNodeModules)) {
        Write-Host "📦 安装后端依赖..." -ForegroundColor Yellow
        Set-Location $backendPath
        npm install
        Set-Location $scriptPath
    }
    
    # 启动后端服务
    Write-Host "🚀 启动后端开发服务器..." -ForegroundColor Green
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$backendPath'; npm run start:dev" -WindowStyle Normal
    Write-Host "✅ 后端服务已启动 (端口: 3001)" -ForegroundColor Green
} else {
    Write-Host "❌ 后端目录不存在: $backendPath" -ForegroundColor Red
}

# 等待后端启动
Start-Sleep -Seconds 5

# 启动前端服务
Write-Host "🎨 启动前端服务..." -ForegroundColor Yellow
$frontendPath = Join-Path $scriptPath "frontend"
if (Test-Path $frontendPath) {
    Write-Host "📂 进入前端目录: $frontendPath" -ForegroundColor Cyan
    
    # 检查是否需要安装依赖
    $frontendNodeModules = Join-Path $frontendPath "node_modules"
    if (-not (Test-Path $frontendNodeModules)) {
        Write-Host "📦 安装前端依赖..." -ForegroundColor Yellow
        Set-Location $frontendPath
        npm install
        Set-Location $scriptPath
    }
    
    # 启动前端服务
    Write-Host "🚀 启动前端开发服务器..." -ForegroundColor Green
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$frontendPath'; npm run dev" -WindowStyle Normal
    Write-Host "✅ 前端服务已启动 (端口: 3000)" -ForegroundColor Green
} else {
    Write-Host "❌ 前端目录不存在: $frontendPath" -ForegroundColor Red
}

# 等待前端启动
Start-Sleep -Seconds 5

# 启动AI服务
Write-Host "🤖 启动AI服务..." -ForegroundColor Yellow
$aiServicePath = Join-Path $scriptPath "ai-service"
if (Test-Path $aiServicePath) {
    Write-Host "📂 进入AI服务目录: $aiServicePath" -ForegroundColor Cyan
    
    # 检查是否需要安装依赖
    $aiServiceNodeModules = Join-Path $aiServicePath "node_modules"
    if (-not (Test-Path $aiServiceNodeModules)) {
        Write-Host "📦 安装AI服务依赖..." -ForegroundColor Yellow
        Set-Location $aiServicePath
        npm install
        Set-Location $scriptPath
    }
    
    # 启动AI服务
    Write-Host "🚀 启动AI开发服务器..." -ForegroundColor Green
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$aiServicePath'; npm run start:dev" -WindowStyle Normal
    Write-Host "✅ AI服务已启动 (端口: 3002)" -ForegroundColor Green
} else {
    Write-Host "❌ AI服务目录不存在: $aiServicePath" -ForegroundColor Red
}

# 等待所有服务启动
Write-Host "⏳ 等待所有服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

Write-Host ""
Write-Host "🎉 SmartLoan 智能金融服务平台启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📱 访问地址:" -ForegroundColor Cyan
Write-Host "   前端应用: http://localhost:3000" -ForegroundColor White
Write-Host "   后端API: http://localhost:3001" -ForegroundColor White
Write-Host "   AI服务:  http://localhost:3002" -ForegroundColor White
Write-Host ""
Write-Host "📄 演示页面:" -ForegroundColor Cyan
Write-Host "   本地演示: file:///$scriptPath/demo.html" -ForegroundColor White
Write-Host ""
Write-Host "🔧 管理命令:" -ForegroundColor Cyan
Write-Host "   检查状态: .\check-system.ps1" -ForegroundColor White
Write-Host "   查看文档: .\README.md" -ForegroundColor White
Write-Host ""
Write-Host "📊 核心功能:" -ForegroundColor Cyan
Write-Host "   ✨ 智能产品匹配 - AI驱动的个性化推荐" -ForegroundColor White
Write-Host "   🔍 多模态资质审核 - OCR识别 + 活体检测" -ForegroundColor White
Write-Host "   📈 实时风控看板 - 动态风险监控" -ForegroundColor White
Write-Host "   🤖 AI虚拟顾问 - 7×24小时智能服务" -ForegroundColor White
Write-Host ""

# 自动打开演示页面
Write-Host "🌐 正在打开演示页面..." -ForegroundColor Yellow
Start-Sleep -Seconds 2
$demoPath = Join-Path $scriptPath "demo.html"
if (Test-Path $demoPath) {
    Start-Process $demoPath
    Write-Host "✅ 演示页面已打开！" -ForegroundColor Green
} else {
    Write-Host "⚠️ 演示页面文件不存在" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 下一步操作:" -ForegroundColor Green
Write-Host "1. 查看演示页面了解功能特性" -ForegroundColor White
Write-Host "2. 访问 http://localhost:3000 体验前端应用" -ForegroundColor White
Write-Host "3. 访问 http://localhost:3001/api/health 检查后端状态" -ForegroundColor White
Write-Host "4. 运行 .\check-system.ps1 检查系统状态" -ForegroundColor White
Write-Host ""
Write-Host "✅ 系统启动完成，请开始体验 SmartLoan 智能金融服务平台！" -ForegroundColor Green
