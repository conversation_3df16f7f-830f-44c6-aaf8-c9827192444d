import { ConfigService } from '@nestjs/config';
export declare class OcrService {
    private configService;
    private readonly apiUrl;
    constructor(configService: ConfigService);
    process(imageUrl: string, documentType: string): Promise<any>;
    validateIdCard(ocrResult: any): Promise<boolean>;
    validateBusinessLicense(ocrResult: any): Promise<boolean>;
    private isValidIdNumber;
    private isValidRegistrationNumber;
}
