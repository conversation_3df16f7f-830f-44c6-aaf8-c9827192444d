import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { LoggerService } from '../../logger/logger.service';
import { MonitorService } from '../../monitor/monitor.service';
export declare class LoggingInterceptor implements NestInterceptor {
    private readonly logger;
    private readonly monitor;
    constructor(logger: LoggerService, monitor: MonitorService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
}
