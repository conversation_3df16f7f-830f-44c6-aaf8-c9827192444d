import { Repository } from 'typeorm';
import { RiskAssessment } from '../../entities/risk-assessment.entity';
import { LoanApplication } from '../../entities/loan-application.entity';
import { RedisService } from '../../services/redis.service';
import { MonitoringService } from '../../services/monitoring.service';
import { ConfigService } from '@nestjs/config';
export declare class RiskControlService {
    private riskRepository;
    private applicationRepository;
    private redisService;
    private monitoringService;
    private configService;
    private readonly logger;
    private readonly gpuServiceUrl;
    private readonly CACHE_TTL;
    private readonly MAX_RETRIES;
    private readonly RETRY_DELAY;
    private readonly BATCH_SIZE;
    private readonly CACHE_KEYS;
    constructor(riskRepository: Repository<RiskAssessment>, applicationRepository: Repository<LoanApplication>, redisService: RedisService, monitoringService: MonitoringService, configService: ConfigService);
    assessRisk(applicationId: string): Promise<RiskAssessment>;
    private retryWithFallback;
    private calculateRiskScoreFallback;
    private collectRiskData;
    private calculateRiskScore;
    private makeDecision;
    private getMarketRiskFactors;
    getRiskAssessment(applicationId: number): Promise<RiskAssessment>;
    updateRiskFactors(assessmentId: number, newFactors: any): Promise<RiskAssessment>;
    private getUserFinancialData;
    private getCreditHistory;
    private getBehavioralData;
}
