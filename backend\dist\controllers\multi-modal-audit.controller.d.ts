/// <reference types="multer" />
import { MultiModalAuditService } from '../modules/audit/multi-modal-audit.service';
import { OcrService } from '../services/ocr.service';
import { FaceVerificationService } from '../services/face-verification.service';
export declare class MultiModalAuditController {
    private readonly multiModalAuditService;
    private readonly ocrService;
    private readonly faceVerificationService;
    constructor(multiModalAuditService: MultiModalAuditService, ocrService: OcrService, faceVerificationService: FaceVerificationService);
    recognizeIdentityCard(files: Express.Multer.File[], req: any): Promise<{
        success: boolean;
        data: any[];
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        message: any;
        error: any;
        data?: undefined;
    }>;
    recognizeBankCard(files: Express.Multer.File[], req: any): Promise<{
        success: boolean;
        data: any[];
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        message: any;
        error: any;
        data?: undefined;
    }>;
    recognizeIncomeProof(files: Express.Multer.File[], req: any): Promise<{
        success: boolean;
        data: any[];
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        message: any;
        error: any;
        data?: undefined;
    }>;
    faceDetection(files: Express.Multer.File[], req: any): Promise<{
        success: boolean;
        message: string;
        data?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        data: {
            isLive: boolean;
            confidence: number;
        };
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        message: any;
        error: any;
        data?: undefined;
    }>;
    faceVerification(files: Express.Multer.File[], body: {
        identityCardImage?: string;
    }, req: any): Promise<{
        success: boolean;
        message: string;
        data?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        data: {
            isVerified: boolean;
            livePhotoAnalysis: {
                detection: {
                    faceFound: boolean;
                    faceCount: number;
                    confidence: number;
                    landmarks: {};
                };
                liveness: {
                    isLive: boolean;
                    confidence: number;
                };
                comparison: {
                    isMatch: boolean;
                    confidence: number;
                };
                isVerified: boolean;
            };
            similarity: number;
        };
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        message: any;
        error: any;
        data?: undefined;
    }>;
    comprehensiveAudit(files: Express.Multer.File[], auditData: {
        applicationId: string;
        documentTypes: string[];
        verificationLevel: 'basic' | 'enhanced' | 'premium';
    }, req: any): Promise<{
        success: boolean;
        data: any;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        message: any;
        error: any;
        data?: undefined;
    }>;
    getAuditResult(auditId: string, req: any): Promise<{
        success: boolean;
        data: any;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        message: any;
        error: any;
        data?: undefined;
    }>;
    getAuditHistory(page: number, limit: number, req: any): Promise<{
        success: boolean;
        data: any;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        message: any;
        error: any;
        data?: undefined;
    }>;
}
