import { Injectable, OnModuleD<PERSON>roy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import { LoggerService } from '../logger/logger.service';
import { MonitorService } from '../monitor/monitor.service';

@Injectable()
export class CacheService implements OnModuleDestroy {
  private readonly redis: Redis;

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
    private readonly monitor: MonitorService
  ) {
    this.redis = new Redis({
      host: this.configService.get('REDIS_HOST'),
      port: this.configService.get('REDIS_PORT'),
      password: this.configService.get('REDIS_PASSWORD'),
      db: this.configService.get('REDIS_DB'),
    });

    this.redis.on('error', (error) => {
      this.logger.error('Redis连接错误', error);
    });
  }

  async get<T>(key: string): Promise<T | null> {
    this.monitor.startOperation('cache.get');
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      this.logger.error('获取缓存失败', error);
      return null;
    } finally {
      this.monitor.endOperation('cache.get');
    }
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    this.monitor.startOperation('cache.set');
    try {
      const stringValue = JSON.stringify(value);
      if (ttl) {
        await this.redis.setex(key, ttl, stringValue);
      } else {
        await this.redis.set(key, stringValue);
      }
    } catch (error) {
      this.logger.error('设置缓存失败', error);
    } finally {
      this.monitor.endOperation('cache.set');
    }
  }

  async delete(key: string): Promise<void> {
    this.monitor.startOperation('cache.delete');
    try {
      await this.redis.del(key);
    } catch (error) {
      this.logger.error('删除缓存失败', error);
    } finally {
      this.monitor.endOperation('cache.delete');
    }
  }

  async clear(): Promise<void> {
    this.monitor.startOperation('cache.clear');
    try {
      await this.redis.flushdb();
    } catch (error) {
      this.logger.error('清空缓存失败', error);
    } finally {
      this.monitor.endOperation('cache.clear');
    }
  }

  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttlSeconds?: number
  ): Promise<T> {
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const value = await factory();
    await this.set(key, value, ttlSeconds);
    return value;
  }

  async onModuleDestroy() {
    await this.redis.quit();
  }
} 