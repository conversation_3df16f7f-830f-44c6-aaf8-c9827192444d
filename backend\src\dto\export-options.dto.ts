import { IsString, <PERSON>E<PERSON>, IsOptional, IsArray, IsObject, ValidateNested, IsBoolean, IsNumber, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export enum ExportFormat {
  EXCEL = 'excel',
  PDF = 'pdf',
  CSV = 'csv',
  JSON = 'json',
  XML = 'xml',
  HTML = 'html'
}

export class ExportOptionsDto {
  @IsEnum(ExportFormat)
  format: ExportFormat;

  @IsString()
  @IsOptional()
  template?: string;

  @IsString()
  @IsOptional()
  filename?: string;

  @IsArray()
  @IsOptional()
  fields?: string[];

  @IsObject()
  @IsOptional()
  filters?: Record<string, any>;

  @IsBoolean()
  @IsOptional()
  includeHeaders?: boolean;

  @IsBoolean()
  @IsOptional()
  includeMetadata?: boolean;

  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(10000)
  pageSize?: number;

  @IsNumber()
  @IsOptional()
  @Min(1)
  page?: number;

  @IsString()
  @IsOptional()
  sortBy?: string;

  @IsString()
  @IsOptional()
  sortOrder?: 'asc' | 'desc';

  @IsBoolean()
  @IsOptional()
  encrypt?: boolean;

  @IsString()
  @IsOptional()
  password?: string;

  @IsBoolean()
  @IsOptional()
  compress?: boolean;

  @IsString()
  @IsOptional()
  language?: string;

  @IsObject()
  @IsOptional()
  styling?: {
    headerColor?: string;
    headerFont?: string;
    headerSize?: number;
    rowColor?: string;
    rowFont?: string;
    rowSize?: number;
  };

  @IsObject()
  @IsOptional()
  layout?: {
    orientation?: 'portrait' | 'landscape';
    pageSize?: string;
    margins?: {
      top?: number;
      right?: number;
      bottom?: number;
      left?: number;
    };
  };

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ExportColumnDto)
  columns?: ExportColumnDto[];
}

export class ExportColumnDto {
  @IsString()
  field: string;

  @IsString()
  @IsOptional()
  header?: string;

  @IsString()
  @IsOptional()
  format?: string;

  @IsNumber()
  @IsOptional()
  width?: number;

  @IsBoolean()
  @IsOptional()
  visible?: boolean;

  @IsString()
  @IsOptional()
  alignment?: 'left' | 'center' | 'right';

  @IsString()
  @IsOptional()
  type?: 'string' | 'number' | 'date' | 'boolean' | 'currency';

  @IsObject()
  @IsOptional()
  style?: {
    font?: string;
    size?: number;
    color?: string;
    backgroundColor?: string;
    bold?: boolean;
    italic?: boolean;
    underline?: boolean;
  };
} 