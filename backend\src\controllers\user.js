"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUserInfo = exports.getUserInfo = exports.login = exports.register = void 0;
var jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
var user_1 = require("../models/user");
// 生成JWT令牌
var generateToken = function (userId) {
    if (typeof userId !== 'string') {
        throw new Error('Invalid user ID');
    }
    return jsonwebtoken_1.default.sign({ id: userId }, process.env.JWT_SECRET || 'default_secret', {
        expiresIn: '24h'
    });
};
// 用户注册
var register = function (req, res) { return __awaiter(void 0, void 0, void 0, function () {
    var _a, username, password, email, phone, existingUser, user, error_1;
    return __generator(this, function (_b) {
        switch (_b.label) {
            case 0:
                _b.trys.push([0, 3, , 4]);
                _a = req.body, username = _a.username, password = _a.password, email = _a.email, phone = _a.phone;
                return [4 /*yield*/, user_1.User.findOne({
                        $or: [{ username: username }, { email: email }, { phone: phone }]
                    })];
            case 1:
                existingUser = _b.sent();
                if (existingUser) {
                    return [2 /*return*/, res.status(400).json({
                            code: 1,
                            message: '用户名、邮箱或手机号已存在'
                        })];
                }
                user = new user_1.User({
                    username: username,
                    password: password,
                    email: email,
                    phone: phone
                });
                return [4 /*yield*/, user.save()];
            case 2:
                _b.sent();
                res.status(201).json({
                    code: 0,
                    message: '注册成功'
                });
                return [3 /*break*/, 4];
            case 3:
                error_1 = _b.sent();
                console.error('注册失败：', error_1);
                res.status(500).json({
                    code: 1,
                    message: '注册失败'
                });
                return [3 /*break*/, 4];
            case 4: return [2 /*return*/];
        }
    });
}); };
exports.register = register;
// 用户登录
var login = function (req, res) { return __awaiter(void 0, void 0, void 0, function () {
    var _a, username, password, user, isMatch, token, error_2;
    return __generator(this, function (_b) {
        switch (_b.label) {
            case 0:
                _b.trys.push([0, 3, , 4]);
                _a = req.body, username = _a.username, password = _a.password;
                return [4 /*yield*/, user_1.User.findOne({ username: username })];
            case 1:
                user = _b.sent();
                if (!user) {
                    return [2 /*return*/, res.status(401).json({
                            code: 1,
                            message: '用户名或密码错误'
                        })];
                }
                return [4 /*yield*/, user.comparePassword(password)];
            case 2:
                isMatch = _b.sent();
                if (!isMatch) {
                    return [2 /*return*/, res.status(401).json({
                            code: 1,
                            message: '用户名或密码错误'
                        })];
                }
                token = generateToken(user._id);
                res.json({
                    code: 0,
                    message: '登录成功',
                    data: {
                        token: token,
                        user: {
                            id: user._id,
                            username: user.username,
                            email: user.email,
                            phone: user.phone,
                            creditScore: user.creditScore,
                            loanCount: user.loanCount
                        }
                    }
                });
                return [3 /*break*/, 4];
            case 3:
                error_2 = _b.sent();
                console.error('登录失败：', error_2);
                res.status(500).json({
                    code: 1,
                    message: '登录失败'
                });
                return [3 /*break*/, 4];
            case 4: return [2 /*return*/];
        }
    });
}); };
exports.login = login;
// 获取用户信息
var getUserInfo = function (req, res) { return __awaiter(void 0, void 0, void 0, function () {
    var user, error_3;
    var _a;
    return __generator(this, function (_b) {
        switch (_b.label) {
            case 0:
                _b.trys.push([0, 2, , 3]);
                return [4 /*yield*/, user_1.User.findById((_a = req.user) === null || _a === void 0 ? void 0 : _a.userId)];
            case 1:
                user = _b.sent();
                if (!user) {
                    return [2 /*return*/, res.status(404).json({
                            code: 1,
                            message: '用户不存在'
                        })];
                }
                res.json({
                    code: 0,
                    data: {
                        id: user._id,
                        username: user.username,
                        email: user.email,
                        phone: user.phone,
                        creditScore: user.creditScore,
                        loanCount: user.loanCount
                    }
                });
                return [3 /*break*/, 3];
            case 2:
                error_3 = _b.sent();
                console.error('获取用户信息失败：', error_3);
                res.status(500).json({
                    code: 1,
                    message: '获取用户信息失败'
                });
                return [3 /*break*/, 3];
            case 3: return [2 /*return*/];
        }
    });
}); };
exports.getUserInfo = getUserInfo;
// 更新用户信息
var updateUserInfo = function (req, res) { return __awaiter(void 0, void 0, void 0, function () {
    var _a, email, phone, newPassword, user, error_4;
    var _b;
    return __generator(this, function (_c) {
        switch (_c.label) {
            case 0:
                _c.trys.push([0, 3, , 4]);
                _a = req.body, email = _a.email, phone = _a.phone, newPassword = _a.newPassword;
                return [4 /*yield*/, user_1.User.findById((_b = req.user) === null || _b === void 0 ? void 0 : _b.userId)];
            case 1:
                user = _c.sent();
                if (!user) {
                    return [2 /*return*/, res.status(404).json({
                            code: 1,
                            message: '用户不存在'
                        })];
                }
                // 更新基本信息
                if (email)
                    user.email = email;
                if (phone)
                    user.phone = phone;
                if (newPassword)
                    user.password = newPassword;
                return [4 /*yield*/, user.save()];
            case 2:
                _c.sent();
                res.json({
                    code: 0,
                    message: '更新成功'
                });
                return [3 /*break*/, 4];
            case 3:
                error_4 = _c.sent();
                console.error('更新用户信息失败：', error_4);
                res.status(500).json({
                    code: 1,
                    message: '更新用户信息失败'
                });
                return [3 /*break*/, 4];
            case 4: return [2 /*return*/];
        }
    });
}); };
exports.updateUserInfo = updateUserInfo;
