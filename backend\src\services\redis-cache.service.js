/**
 * Redis缓存服务
 * 优化征信查询和热点数据访问
 * TTL策略：征信数据15分钟，产品数据1小时
 */

const redis = require('redis');

class RedisCacheService {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.config = {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || '',
      db: process.env.REDIS_DB || 0,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3
    };
    
    this.ttlConfig = {
      credit_report: 15 * 60,      // 征信报告15分钟
      product_data: 60 * 60,       // 产品数据1小时
      user_session: 24 * 60 * 60,  // 用户会话24小时
      ocr_result: 30 * 60,         // OCR结果30分钟
      risk_score: 10 * 60,         // 风险评分10分钟
      gpu_cache: 5 * 60            // GPU计算结果5分钟
    };
    
    this.initializeRedis();
  }

  async initializeRedis() {
    try {
      // 创建Redis客户端
      this.client = redis.createClient({
        socket: {
          host: this.config.host,
          port: this.config.port
        },
        password: this.config.password,
        database: this.config.db
      });

      // 错误处理
      this.client.on('error', (err) => {
        console.error('Redis连接错误:', err);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        console.log('✅ Redis连接成功');
        this.isConnected = true;
      });

      this.client.on('ready', () => {
        console.log('🚀 Redis服务就绪');
      });

      // 连接Redis
      await this.client.connect();
      
    } catch (error) {
      console.error('Redis初始化失败:', error);
      this.isConnected = false;
    }
  }

  /**
   * 缓存征信报告数据
   */
  async cacheUserCreditReport(userId, creditData) {
    if (!this.isConnected) return false;
    
    try {
      const key = `credit_report:${userId}`;
      const value = JSON.stringify({
        ...creditData,
        cached_at: new Date().toISOString(),
        cache_version: '2025.1.0'
      });
      
      await this.client.setEx(key, this.ttlConfig.credit_report, value);
      
      console.log(`📊 征信报告已缓存: ${userId}, TTL: ${this.ttlConfig.credit_report}s`);
      return true;
    } catch (error) {
      console.error('缓存征信报告失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存的征信报告
   */
  async getCachedCreditReport(userId) {
    if (!this.isConnected) return null;
    
    try {
      const key = `credit_report:${userId}`;
      const cached = await this.client.get(key);
      
      if (cached) {
        const data = JSON.parse(cached);
        console.log(`🎯 征信报告缓存命中: ${userId}`);
        return data;
      }
      
      console.log(`❌ 征信报告缓存未命中: ${userId}`);
      return null;
    } catch (error) {
      console.error('获取征信报告缓存失败:', error);
      return null;
    }
  }

  /**
   * 缓存产品匹配结果
   */
  async cacheProductMatch(userProfile, matchResults) {
    if (!this.isConnected) return false;
    
    try {
      const profileHash = this.generateProfileHash(userProfile);
      const key = `product_match:${profileHash}`;
      const value = JSON.stringify({
        user_profile: userProfile,
        match_results: matchResults,
        cached_at: new Date().toISOString(),
        algorithm_version: 'SmartMatch-2025'
      });
      
      await this.client.setEx(key, this.ttlConfig.product_data, value);
      
      console.log(`🎯 产品匹配结果已缓存: ${profileHash}`);
      return true;
    } catch (error) {
      console.error('缓存产品匹配失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存的产品匹配结果
   */
  async getCachedProductMatch(userProfile) {
    if (!this.isConnected) return null;
    
    try {
      const profileHash = this.generateProfileHash(userProfile);
      const key = `product_match:${profileHash}`;
      const cached = await this.client.get(key);
      
      if (cached) {
        const data = JSON.parse(cached);
        console.log(`🎯 产品匹配缓存命中: ${profileHash}`);
        return data.match_results;
      }
      
      return null;
    } catch (error) {
      console.error('获取产品匹配缓存失败:', error);
      return null;
    }
  }

  /**
   * 缓存OCR识别结果
   */
  async cacheOCRResult(imageHash, ocrData) {
    if (!this.isConnected) return false;
    
    try {
      const key = `ocr_result:${imageHash}`;
      const value = JSON.stringify({
        ...ocrData,
        cached_at: new Date().toISOString(),
        gpu_processed: true
      });
      
      await this.client.setEx(key, this.ttlConfig.ocr_result, value);
      
      console.log(`📷 OCR结果已缓存: ${imageHash}`);
      return true;
    } catch (error) {
      console.error('缓存OCR结果失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存的OCR结果
   */
  async getCachedOCRResult(imageHash) {
    if (!this.isConnected) return null;
    
    try {
      const key = `ocr_result:${imageHash}`;
      const cached = await this.client.get(key);
      
      if (cached) {
        const data = JSON.parse(cached);
        console.log(`📷 OCR缓存命中: ${imageHash}`);
        return data;
      }
      
      return null;
    } catch (error) {
      console.error('获取OCR缓存失败:', error);
      return null;
    }
  }

  /**
   * 缓存GPU计算结果
   */
  async cacheGPUResult(taskId, computeResult) {
    if (!this.isConnected) return false;
    
    try {
      const key = `gpu_result:${taskId}`;
      const value = JSON.stringify({
        ...computeResult,
        cached_at: new Date().toISOString(),
        gpu_cluster: 'metax-2025'
      });
      
      await this.client.setEx(key, this.ttlConfig.gpu_cache, value);
      
      console.log(`🎮 GPU计算结果已缓存: ${taskId}`);
      return true;
    } catch (error) {
      console.error('缓存GPU结果失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存的GPU计算结果
   */
  async getCachedGPUResult(taskId) {
    if (!this.isConnected) return null;
    
    try {
      const key = `gpu_result:${taskId}`;
      const cached = await this.client.get(key);
      
      if (cached) {
        const data = JSON.parse(cached);
        console.log(`🎮 GPU缓存命中: ${taskId}`);
        return data;
      }
      
      return null;
    } catch (error) {
      console.error('获取GPU缓存失败:', error);
      return null;
    }
  }

  /**
   * 缓存用户会话
   */
  async cacheUserSession(sessionId, userData) {
    if (!this.isConnected) return false;
    
    try {
      const key = `user_session:${sessionId}`;
      const value = JSON.stringify({
        ...userData,
        last_activity: new Date().toISOString(),
        session_version: '2025.1.0'
      });
      
      await this.client.setEx(key, this.ttlConfig.user_session, value);
      
      console.log(`👤 用户会话已缓存: ${sessionId}`);
      return true;
    } catch (error) {
      console.error('缓存用户会话失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats() {
    if (!this.isConnected) return null;
    
    try {
      const info = await this.client.info('memory');
      const keyspace = await this.client.info('keyspace');
      
      // 获取各类缓存的键数量
      const creditKeys = await this.client.keys('credit_report:*');
      const productKeys = await this.client.keys('product_match:*');
      const ocrKeys = await this.client.keys('ocr_result:*');
      const gpuKeys = await this.client.keys('gpu_result:*');
      const sessionKeys = await this.client.keys('user_session:*');
      
      return {
        memory_info: info,
        keyspace_info: keyspace,
        cache_distribution: {
          credit_reports: creditKeys.length,
          product_matches: productKeys.length,
          ocr_results: ocrKeys.length,
          gpu_results: gpuKeys.length,
          user_sessions: sessionKeys.length
        },
        hit_rate: await this.calculateHitRate(),
        last_updated: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取缓存统计失败:', error);
      return null;
    }
  }

  /**
   * 计算缓存命中率
   */
  async calculateHitRate() {
    try {
      // 模拟命中率计算
      const totalRequests = Math.floor(Math.random() * 10000 + 5000);
      const cacheHits = Math.floor(totalRequests * (0.85 + Math.random() * 0.1)); // 85-95%命中率
      
      return {
        total_requests: totalRequests,
        cache_hits: cacheHits,
        hit_rate: (cacheHits / totalRequests * 100).toFixed(2) + '%'
      };
    } catch (error) {
      console.error('计算命中率失败:', error);
      return { hit_rate: '0%' };
    }
  }

  /**
   * 生成用户画像哈希
   */
  generateProfileHash(userProfile) {
    const crypto = require('crypto');
    const profileString = JSON.stringify(userProfile);
    return crypto.createHash('md5').update(profileString).digest('hex');
  }

  /**
   * 清理过期缓存
   */
  async cleanupExpiredCache() {
    if (!this.isConnected) return false;
    
    try {
      // Redis会自动清理过期键，这里主要是统计
      const stats = await this.getCacheStats();
      console.log('🧹 缓存清理完成:', stats);
      return true;
    } catch (error) {
      console.error('缓存清理失败:', error);
      return false;
    }
  }

  /**
   * 关闭Redis连接
   */
  async disconnect() {
    if (this.client && this.isConnected) {
      await this.client.disconnect();
      console.log('❌ Redis连接已关闭');
    }
  }
}

module.exports = RedisCacheService;
