import { Repository } from 'typeorm';
import { RiskRule } from '../entities/risk-rule.entity';
import { RedisService } from './redis.service';
import { GpuService } from './gpu.service';
import { MonitoringService } from './monitoring.service';
export declare class RiskRuleService {
    private readonly riskRuleRepository;
    private readonly redisService;
    private readonly gpuService;
    private readonly monitoringService;
    private readonly logger;
    private readonly CACHE_TTL;
    private readonly CACHE_KEY_PREFIX;
    constructor(riskRuleRepository: Repository<RiskRule>, redisService: RedisService, gpuService: GpuService, monitoringService: MonitoringService);
    getActiveRules(): Promise<RiskRule[]>;
    executeRules(data: any, rules: RiskRule[]): Promise<any[]>;
    private executeRulesSerially;
    private evaluateRule;
    private parseConditions;
    private evaluateConditions;
    private getFieldValue;
    private compareValues;
    updateRule(ruleId: number, updates: Partial<RiskRule>): Promise<RiskRule>;
    createRule(rule: Partial<RiskRule>): Promise<RiskRule>;
    deleteRule(ruleId: number): Promise<void>;
}
