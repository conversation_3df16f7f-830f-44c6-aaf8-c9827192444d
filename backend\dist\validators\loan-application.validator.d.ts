import { LoanApplication } from '../entities/loan-application.entity';
import { User } from '../entities/user.entity';
import { LoanStatus } from '../enums/loan-status.enum';
export declare class LoanApplicationValidator {
    validateCreateApplication(user: User, amount: number, term: number): void;
    validateUpdateApplication(application: LoanApplication, user: User, newStatus?: LoanStatus): void;
    validateApproveApplication(application: LoanApplication, user: User): void;
    validateRejectApplication(application: LoanApplication, user: User, reason: string): void;
    validateCancelApplication(application: LoanApplication, user: User): void;
    validateUploadDocument(application: LoanApplication, user: User, fileSize: number): void;
}
