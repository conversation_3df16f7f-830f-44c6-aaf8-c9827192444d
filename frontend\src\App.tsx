import React, { useState } from 'react';
import { Layout, Menu, ConfigProvider } from 'antd';
import {
  DashboardOutlined,
  FileTextOutlined,
  UserOutlined,
  SettingOutlined,
  BankOutlined,
  SearchOutlined,
  SafetyCertificateOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import Dashboard from './pages/Dashboard';
import LoanApplication from './pages/LoanApplication';
import UserManagement from './pages/UserManagement';
import Settings from './pages/Settings';
import SmartMatch from './pages/SmartMatch';
import QualificationReview from './pages/QualificationReview';
import RiskDashboard from './pages/RiskDashboard';
import zhCN from 'antd/locale/zh_CN';
import 'antd/dist/reset.css';

const { Header, Sider, Content } = Layout;

const App: React.FC = () => {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [collapsed, setCollapsed] = useState(false);

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: 'smart-match',
      icon: <SearchOutlined />,
      label: '智能匹配',
    },
    {
      key: 'qualification',
      icon: <SafetyCertificateOutlined />,
      label: '资质审核',
    },
    {
      key: 'risk-dashboard',
      icon: <BarChartOutlined />,
      label: '风控看板',
    },
    {
      key: 'loan-application',
      icon: <FileTextOutlined />,
      label: '贷款申请',
    },
    {
      key: 'user-management',
      icon: <UserOutlined />,
      label: '用户管理',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  const renderContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'smart-match':
        return <SmartMatch />;
      case 'qualification':
        return <QualificationReview />;
      case 'risk-dashboard':
        return <RiskDashboard />;
      case 'loan-application':
        return <LoanApplication />;
      case 'user-management':
        return <UserManagement />;
      case 'settings':
        return <Settings />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <ConfigProvider locale={zhCN}>
      <Layout style={{ minHeight: '100vh' }}>
        <Sider trigger={null} collapsible collapsed={collapsed}>
          <div style={{ 
            height: '32px', 
            margin: '16px', 
            color: '#fff', 
            fontSize: '18px',
            fontWeight: 'bold',
            textAlign: 'center',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <BankOutlined style={{ marginRight: collapsed ? 0 : '8px' }} />
            {!collapsed && 'SmartLoan'}
          </div>
          <Menu
            theme="dark"
            mode="inline"
            defaultSelectedKeys={['dashboard']}
            items={menuItems}
            onClick={({ key }) => setCurrentPage(key)}
          />
        </Sider>
        <Layout className="site-layout">
          <Header style={{ 
            padding: '0 16px', 
            background: '#fff', 
            display: 'flex', 
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 1px 4px rgba(0,21,41,.08)'
          }}>
            <h2 style={{ margin: 0, color: '#1890ff' }}>
              SmartLoan 智能金融服务平台
            </h2>
          </Header>
          <Content style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
            background: '#fff',
            borderRadius: '6px'
          }}>
            {renderContent()}
          </Content>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};

export default App;