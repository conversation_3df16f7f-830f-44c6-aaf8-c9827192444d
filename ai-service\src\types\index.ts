export interface LoanApplication {
  name: string
  idCard: string
  maritalStatus: string
  company: string
  position: string
  monthlyIncome: number
  houseStatus: string
  carStatus: string
  loanAmount: number
  loanTerm: number
  loanPurpose: string
}

export interface EvaluationResult {
  score: number
  status: 'approved' | 'rejected' | 'pending'
  reason: string
  suggestedAmount?: number
  suggestedTerm?: number
} 