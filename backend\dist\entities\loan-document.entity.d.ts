import { LoanApplication } from './loan-application.entity';
import { User } from './user.entity';
export declare enum DocumentType {
    ID_CARD = "ID_CARD",
    INCOME_PROOF = "INCOME_PROOF",
    BANK_STATEMENT = "BANK_STATEMENT",
    PROPERTY_CERTIFICATE = "PROPERTY_CERTIFICATE",
    OTHER = "OTHER"
}
export declare class LoanDocument {
    id: string;
    type: DocumentType;
    filename: string;
    mimetype: string;
    size: number;
    filePath: string;
    verificationNotes: string;
    isVerified: boolean;
    verifiedBy: string;
    verifiedAt: Date;
    application: LoanApplication;
    applicationId: string;
    uploadedBy: User;
    uploadDate: Date;
}
