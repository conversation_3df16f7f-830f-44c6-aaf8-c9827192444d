/// <reference types="multer" />
import { ProductService } from '../services/product.service';
import { ProductRecommendationService } from '../services/product-recommendation.service';
import { ProductComparisonService } from '../services/product-comparison.service';
import { ProductSearchService, SearchResult } from '../services/product-search.service';
import { ProductImportExportService } from '../services/product-import-export.service';
import { CreateProductDto, UpdateProductDto, ProductFilterDto } from '../dto/product.dto';
export declare class ProductController {
    private readonly productService;
    private readonly productRecommendationService;
    private readonly productComparisonService;
    private readonly productSearchService;
    private readonly productImportExportService;
    constructor(productService: ProductService, productRecommendationService: ProductRecommendationService, productComparisonService: ProductComparisonService, productSearchService: ProductSearchService, productImportExportService: ProductImportExportService);
    recognizeDocument(data: any): Promise<{
        success: boolean;
        data: {
            documentType: any;
            extractedData: {
                name: string;
                idNumber: string;
            };
            confidence: number;
            processed: boolean;
        };
    }>;
    livenessDetection(data: any): Promise<{
        success: boolean;
        data: {
            isLive: boolean;
            confidence: number;
            faceQuality: string;
        };
    }>;
    create(createProductDto: CreateProductDto): Promise<import("../modules/product/entities/product.entity").Product>;
    update(id: string, updateProductDto: UpdateProductDto): Promise<{
        name: string;
        code: string;
        description: string;
        category: any;
        minAmount?: number;
        maxAmount?: number;
        minTerm: number;
        maxTerm: number;
        interestRate?: number;
        processingFee: number;
        lateFee?: number;
        earlyRepaymentFee: number;
        features: any;
        benefits?: string[];
        requirements: any;
        metadata?: import("../dto/product.dto").ProductMetadataDto;
        isActive: boolean;
        isFeatured: boolean;
        sortOrder: number;
        id: number;
        institution: import("../modules/product/entities/institution.entity").Institution;
        type: string;
        min_amount: number;
        max_amount: number;
        interest_rate: number;
        term_range: number[];
        created_at: Date;
        updated_at: Date;
    } & import("../modules/product/entities/product.entity").Product>;
    delete(id: string): Promise<{
        message: string;
    }>;
    findAll(filterDto: ProductFilterDto): Promise<any>;
    findById(id: string): Promise<any>;
    findByCode(code: string): Promise<any>;
    getRecommendationsByUser(userId: string): Promise<import("../entities/product.entity").Product[]>;
    getPopularProducts(): Promise<import("../entities/product.entity").Product[]>;
    getSimilarProducts(id: string): Promise<import("../entities/product.entity").Product[]>;
    compareProducts(ids: string): Promise<any>;
    searchProducts(query: string): Promise<SearchResult>;
    importProducts(file: Express.Multer.File): Promise<{
        total: number;
        created: number;
        updated: number;
        failed: number;
        details: ({
            code: string;
            status: string;
            error?: undefined;
        } | {
            code: string;
            status: string;
            error: any;
        })[];
    }>;
    exportProducts(): Promise<{
        buffer: any;
        filename: string;
        contentType: string;
    }>;
}
