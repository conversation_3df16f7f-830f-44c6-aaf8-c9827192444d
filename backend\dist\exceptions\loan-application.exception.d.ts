import { HttpException, HttpStatus } from '@nestjs/common';
export declare class LoanApplicationException extends HttpException {
    constructor(message: string, status: HttpStatus);
}
export declare class InvalidLoanAmountException extends LoanApplicationException {
    constructor();
}
export declare class InvalidLoanTermException extends LoanApplicationException {
    constructor();
}
export declare class InvalidMonthlyIncomeException extends LoanApplicationException {
    constructor();
}
export declare class InvalidCreditScoreException extends LoanApplicationException {
    constructor();
}
export declare class InvalidDebtRatioException extends LoanApplicationException {
    constructor();
}
export declare class InvalidFileException extends LoanApplicationException {
    constructor();
}
export declare class InvalidStatusTransitionException extends LoanApplicationException {
    constructor();
}
export declare class LoanApplicationNotFoundException extends LoanApplicationException {
    constructor();
}
export declare class UnauthorizedException extends LoanApplicationException {
    constructor();
}
export declare class ForbiddenException extends LoanApplicationException {
    constructor();
}
