import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import { RiskDecisionService } from './risk-decision.service';
import { LoanApplication } from '../entities/loan-application.entity';
import { User } from '../entities/user.entity';

interface AIDecision {
  approved: boolean;
  score: number;
  reason: string;
  suggestedAmount?: number;
  suggestedTerm?: number;
}

interface AIRecommendation {
  products: Array<{
    id: string;
    name: string;
    description: string;
    matchScore: number;
  }>;
  reason: string;
}

@Injectable()
export class AIService {
  private readonly openai: OpenAI;
  private readonly logger = new Logger(AIService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly riskDecisionService: RiskDecisionService
  ) {
    this.openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });
  }
  async analyzeApplication(application: LoanApplication, user: User): Promise<AIDecision> {
    try {
      const prompt = this.buildAnalysisPrompt(application, user);
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an AI assistant that analyzes loan applications and returns JSON responses.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.7,
      });

      const result = JSON.parse(response.choices[0].message.content || '{}');
      return {
        approved: result.approved,
        score: result.score,
        reason: result.reason,
        suggestedAmount: result.suggestedAmount,
        suggestedTerm: result.suggestedTerm,
      };
    } catch (error) {
      this.logger.error('AI analysis failed', error);
      throw error;
    }
  }

  async getRecommendations(application: LoanApplication, user: User): Promise<AIRecommendation> {
    try {
      const prompt = this.buildRecommendationPrompt(application, user);
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an AI assistant that recommends financial products and returns JSON responses.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.7,      });

      const result = JSON.parse(response.choices[0].message.content || '{}');
      return {
        products: result.products,
        reason: result.reason,
      };
    } catch (error) {
      this.logger.error('AI recommendation failed', error);
      throw error;
    }
  }
  private buildAnalysisPrompt(application: LoanApplication, user: User): string {
    return `
      Analyze the following loan application:
      User: ${user.name}
      Loan Amount: ${application.amount}
      Loan Term: ${application.term}
      Purpose: ${application.purpose}
      
      Provide a JSON response with:
      {
        "approved": boolean,
        "score": number (0-100),
        "reason": string,
        "suggestedAmount": number (optional),
        "suggestedTerm": number (optional)
      }
    `;
  }
  private buildRecommendationPrompt(application: LoanApplication, user: User): string {
    return `
      Recommend products for the following loan application:
      User: ${user.name}
      Loan Amount: ${application.amount}
      Loan Term: ${application.term}
      Purpose: ${application.purpose}
      
      Provide a JSON response with:
      {
        "products": [
          {
            "id": string,
            "name": string,
            "description": string,
            "matchScore": number (0-100)
          }
        ],
        "reason": string
      }
    `;
  }
}