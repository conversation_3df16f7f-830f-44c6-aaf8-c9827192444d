import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn } from 'typeorm';

@Entity('analytics')
export class Analytics {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  metric: string;

  @Column('decimal', { precision: 10, scale: 2 })
  value: number;

  @Column({ type: 'jsonb', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;
}

export { Analytics as AnalyticsEntity };