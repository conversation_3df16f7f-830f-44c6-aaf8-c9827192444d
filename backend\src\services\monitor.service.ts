import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuditLog } from '../entities/audit-log.entity';
import { promisify } from 'util';
import * as os from 'os';
import * as fs from 'fs';
import { LoggerService } from './logger.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { withLoading, handleError } from '../utils/request'
import { RedisService } from './redis.service';

interface SystemMetrics {
  cpu: { usage: number };
  memory: { usage: number };
  disk: { usage: number };
  errors: number;
}

@Injectable()
export class MonitorService implements OnModuleInit {
  private readonly logger = new Logger(MonitorService.name);
  private readonly metrics: Map<string, { value: number; errors?: number }> = new Map();
  private readonly alerts: Map<string, boolean> = new Map();
  private readonly eventEmitter: EventEmitter2;
  private operationTimers: Map<string, number> = new Map();
  private readonly metricsPrefix = 'metrics:';
  private readonly systemMetricsKey = `${this.metricsPrefix}system`;
  private readonly apiMetricsKey = `${this.metricsPrefix}api`;
  private readonly errorMetricsKey = `${this.metricsPrefix}error`;

  constructor(
    @InjectRepository(AuditLog)
    private auditLogRepository: Repository<AuditLog>,
    private readonly loggerService: LoggerService,
    private readonly configService: ConfigService,
    eventEmitter: EventEmitter2,
    private readonly redisService: RedisService
  ) {
    this.eventEmitter = eventEmitter;
    this.initializeAlerts();
  }

  async onModuleInit() {
    // 启动系统指标收集
    this.startSystemMetricsCollection();
  }

  private initializeAlerts() {
    const thresholds = this.configService.get('monitoring.alerts.thresholds');
    if (thresholds) {
      this.alerts.set('cpu', false);
      this.alerts.set('memory', false);
      this.alerts.set('disk', false);
    }
  }

  incrementMetric(name: string, value: number = 1): void {
    const currentValue = this.metrics.get(name)?.value || 0;
    this.metrics.set(name, { value: currentValue + value });
    this.logger.debug(`Metric ${name} incremented to ${currentValue + value}`);
  }

  setMetric(name: string, value: number): void {
    this.metrics.set(name, { value });
    this.logger.debug(`Metric ${name} set to ${value}`);
  }

  getMetric(name: string): number {
    return this.metrics.get(name)?.value || 0;
  }

  getAllMetrics(): Map<string, number> {
    const result = new Map<string, number>();
    this.metrics.forEach((metric, key) => {
      result.set(key, metric.value);
    });
    return result;
  }

  resetMetrics(): void {
    this.metrics.clear();
    this.logger.debug('Metrics reset');
  }

  logApplicationEvent(event: string, data: any): void {
    this.eventEmitter.emit(event, data);
    this.logger.log(`Application event: ${event}`, data);
  }

  logError(error: Error, context?: string): void {
    this.logger.error(error.message, error.stack, context);
    this.incrementMetric('errors');
  }

  logPerformanceMetric(operation: string, duration: number): void {
    this.logger.debug(`${operation} completed in ${duration}ms`);
    this.incrementMetric(`${operation}_duration`, duration);
    this.incrementMetric(`${operation}_count`);
  }

  async checkAlerts(): Promise<void> {
    const thresholds = this.configService.get('monitoring.alerts.thresholds');
    if (!thresholds) return;

    const metrics = await this.getSystemMetrics();

    if (metrics.cpu.usage > thresholds.cpu) {
      if (!this.alerts.get('cpu')) {
        this.alerts.set('cpu', true);
        this.logger.warn(`CPU usage alert: ${metrics.cpu.usage}%`);
        this.eventEmitter.emit('alert.cpu', { usage: metrics.cpu.usage });
      }
    } else {
      this.alerts.set('cpu', false);
    }

    if (metrics.memory.usage > thresholds.memory) {
      if (!this.alerts.get('memory')) {
        this.alerts.set('memory', true);
        this.logger.warn(`Memory usage alert: ${metrics.memory.usage}%`);
        this.eventEmitter.emit('alert.memory', { usage: metrics.memory.usage });
      }
    } else {
      this.alerts.set('memory', false);
    }
  }

  getActiveAlerts(): string[] {
    return Array.from(this.alerts.entries())
      .filter(([_, active]) => active)
      .map(([name]) => name);
  }

  private async getSystemMetrics(): Promise<SystemMetrics> {
    const cpuUsage = await this.getCpuUsage();
    const memoryUsage = this.getMemoryUsage();
    const diskUsage = await this.getDiskUsage();
    const errorCount = Array.from(this.metrics.values())
      .reduce((acc, metric) => acc + (metric.errors || 0), 0);

    return {
      cpu: { usage: cpuUsage },
      memory: { usage: (memoryUsage.used / memoryUsage.total) * 100 },
      disk: { usage: (diskUsage.used / diskUsage.total) * 100 },
      errors: errorCount
    };
  }

  /**
   * 获取CPU使用率
   */
  private async getCpuUsage(): Promise<number> {
    const startMeasure = os.cpus().map(cpu => ({
      idle: cpu.times.idle,
      total: Object.values(cpu.times).reduce((acc, tv) => acc + tv, 0)
    }));

    await promisify(setTimeout)(100);

    const endMeasure = os.cpus().map(cpu => ({
      idle: cpu.times.idle,
      total: Object.values(cpu.times).reduce((acc, tv) => acc + tv, 0)
    }));

    const idleDifferences = endMeasure.map((end, i) => end.idle - startMeasure[i].idle);
    const totalDifferences = endMeasure.map((end, i) => end.total - startMeasure[i].total);

    const averageIdle = idleDifferences.reduce((acc, idle) => acc + idle, 0) / idleDifferences.length;
    const averageTotal = totalDifferences.reduce((acc, total) => acc + total, 0) / totalDifferences.length;

    return 100 - Math.round(100 * averageIdle / averageTotal);
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): {
    total: number;
    used: number;
    free: number;
  } {
    const total = os.totalmem();
    const free = os.freemem();
    const used = total - free;

    return {
      total,
      used,
      free
    };
  }

  /**
   * 获取磁盘使用情况
   */
  private async getDiskUsage(): Promise<{
    total: number;
    used: number;
    free: number;
  }> {
    const stats = await promisify(fs.statfs)('/');
    const total = stats.blocks * stats.bsize;
    const free = stats.bfree * stats.bsize;
    const used = total - free;

    return {
      total,
      used,
      free
    };
  }

  /**
   * 检查系统健康状态
   */
  async checkHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    details: SystemMetrics;
  }> {
    const metrics = await this.getSystemMetrics();
    let healthStatus: 'healthy' | 'warning' | 'critical' = 'healthy';

    if (
      metrics.cpu.usage > 90 ||
      metrics.memory.usage > 90 ||
      metrics.disk.usage > 90 ||
      metrics.errors > 100
    ) {
      healthStatus = 'critical';
    } else if (
      metrics.cpu.usage > 70 ||
      metrics.memory.usage > 70 ||
      metrics.disk.usage > 70 ||
      metrics.errors > 50
    ) {
      healthStatus = 'warning';
    }

    return {
      status: healthStatus,
      details: metrics
    };
  }

  /**
   * 发送告警
   * @param level 告警级别
   * @param message 告警信息
   * @param details 详细信息
   */
  async sendAlert(
    level: 'info' | 'warning' | 'error' | 'critical',
    message: string,
    details?: any
  ): Promise<void> {
    this.logger.log(`[${level.toUpperCase()}] ${message}`, details);

    // 记录到审计日志
    await this.auditLogRepository.save({
      operation: 'system_alert',
      details: {
        level,
        message,
        details
      },
      timestamp: new Date()
    });

    // TODO: 实现告警通知（邮件、短信、钉钉等）
  }

  startOperation(operationName: string) {
    this.operationTimers.set(operationName, Date.now());
  }

  endOperation(operationName: string) {
    const startTime = this.operationTimers.get(operationName);
    if (startTime) {
      const duration = Date.now() - startTime;
      this.loggerService.debug(`操作 ${operationName} 耗时: ${duration}ms`);
      this.operationTimers.delete(operationName);
    }
  }

  async monitorAsync<T>(operationName: string, operation: () => Promise<T>): Promise<T> {
    this.startOperation(operationName);
    try {
      const result = await operation();
      return result;
    } finally {
      this.endOperation(operationName);
    }
  }

  private startSystemMetricsCollection() {
    // 每5分钟收集一次系统指标
    setInterval(async () => {
      try {
        const metrics = await this.collectSystemMetrics();
        await this.redisService.set(
          this.systemMetricsKey,
          JSON.stringify(metrics),
          3600 // 1小时过期
        );
      } catch (error) {
        this.logger.error('收集系统指标失败', error);
      }
    }, 5 * 60 * 1000);
  }

  private async collectSystemMetrics() {
    const cpus = os.cpus();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const loadAvg = os.loadavg();

    return {
      timestamp: new Date().toISOString(),
      cpu: {
        cores: cpus.length,
        model: cpus[0].model,
        speed: cpus[0].speed,
        loadAverage: loadAvg
      },
      memory: {
        total: totalMemory,
        free: freeMemory,
        used: totalMemory - freeMemory,
        usagePercent: ((totalMemory - freeMemory) / totalMemory) * 100
      },
      uptime: os.uptime(),
      platform: os.platform(),
      hostname: os.hostname()
    };
  }

  // 记录 API 调用指标
  async recordApiCall(
    path: string,
    method: string,
    statusCode: number,
    duration: number
  ) {
    try {
      const key = `${this.apiMetricsKey}:${path}:${method}`;
      const metrics = {
        timestamp: new Date().toISOString(),
        path,
        method,
        statusCode,
        duration,
        count: 1
      };

      const existingMetrics = await this.redisService.get(key);
      if (existingMetrics) {
        const parsed = JSON.parse(existingMetrics);
        metrics.count = parsed.count + 1;
        metrics.duration = (parsed.duration + duration) / 2; // 计算平均响应时间
      }

      await this.redisService.set(key, JSON.stringify(metrics), 3600);
    } catch (error) {
      this.logger.error('记录 API 调用指标失败', error);
    }
  }

  // 记录错误指标
  async recordError(
    errorType: string,
    message: string,
    stack?: string
  ) {
    try {
      const key = `${this.errorMetricsKey}:${errorType}`;
      const metrics = {
        timestamp: new Date().toISOString(),
        type: errorType,
        message,
        stack,
        count: 1
      };

      const existingMetrics = await this.redisService.get(key);
      if (existingMetrics) {
        const parsed = JSON.parse(existingMetrics);
        metrics.count = parsed.count + 1;
      }

      await this.redisService.set(key, JSON.stringify(metrics), 3600);
    } catch (error) {
      this.logger.error('记录错误指标失败', error);
    }  }
}