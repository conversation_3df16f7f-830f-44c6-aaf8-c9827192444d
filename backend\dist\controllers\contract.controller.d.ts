import { Response } from 'express';
import { ContractService } from '../services/contract.service';
import { SignContractDto } from '../dto/sign-contract.dto';
export declare class ContractController {
    private readonly contractService;
    constructor(contractService: ContractService);
    getContract(id: string): Promise<{
        contract: {
            contractNumber: string;
            applicationNumber: string;
            loanType: string;
            loanAmount: number;
            loanTerm: number;
            interestRate: number;
            repaymentMethod: string;
            guaranteeMethod: string;
            disbursementDate: string;
            maturityDate: string;
            loanPurpose: string;
            repaymentDay: number;
            guarantor: string;
        };
        repaymentPlan: import("../entities/repayment-plan.entity").RepaymentPlan[];
    }>;
    downloadContract(id: string, res: Response): Promise<void>;
    signContract(id: string, signContractDto: SignContractDto): Promise<{
        message: string;
        contract: {
            id: string;
            status: string;
            signedAt: Date;
        };
    }>;
}
