import { Product, UserMatchingRequest, ProductMatchResult, FinancialInstitution } from '../entities/Product';
import { UserProfile, ProductMatchWeights } from './interfaces/ProductMatchingEngine';
import { Repository } from 'typeorm';
export declare class ProductMatchingEngine {
    private readonly productRepository;
    private readonly institutionRepository;
    private readonly DEFAULT_WEIGHTS;
    constructor(productRepository: Repository<Product>, institutionRepository: Repository<FinancialInstitution>);
    matchProducts(request: UserMatchingRequest, products: Product[], institutions: Map<string, FinancialInstitution>): Promise<ProductMatchResult[]>;
    private preFilterProducts;
    private calculateProductMatch;
    private calculateAmountMatch;
    private calculateRateMatch;
    private calculateTermMatch;
    private calculateRequirementMatch;
    private calculateFeatureMatch;
    private calculateLocationMatch;
    private getDynamicWeights;
    private generateRecommendationReasons;
    private calculateApprovalEstimate;
    private calculateCostAnalysis;
    findBestMatches(userProfile: UserProfile, filters?: Partial<Product>, weights?: ProductMatchWeights): Promise<Product[]>;
    calculateMatchScore(userProfile: UserProfile, product: Product, weights?: ProductMatchWeights): number;
    calculateApprovalProbability(userProfile: UserProfile, product: Product): number;
    calculateTotalCost(amount: number, product: Product, term: number): {
        totalAmount: number;
        monthlyPayment: number;
        totalInterest: number;
        fees: Record<string, number>;
    };
    private convertToMatchingRequest;
    private calculateAgeMatch;
    private calculateIncomeMatch;
    private calculateCreditMatch;
    private calculateWorkYearsMatch;
}
