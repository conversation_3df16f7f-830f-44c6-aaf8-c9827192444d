import { LoanType } from '../enums/loan-type.enum';
import { LoanStatus } from '../enums/loan-status.enum';
import { LoanPurpose } from '../enums/loan-purpose.enum';
import { CollateralType } from '../enums/collateral-type.enum';
declare class LoanMetadataDto {
    monthlyIncome: number;
    employmentDuration: number;
    creditScore: number;
    debtToIncomeRatio: number;
}
export declare class CreateLoanApplicationDto {
    amount: number;
    term: number;
    type: LoanType;
    purpose: LoanPurpose;
    annualIncome: number;
    debtToIncomeRatio: number;
    employmentStatus: string;
    workExperience: number;
    creditScore?: number;
    metadata: LoanMetadataDto;
}
export declare class UpdateLoanApplicationDto {
    amount?: number;
    term?: number;
    purpose?: LoanPurpose;
    annualIncome?: number;
    debtToIncomeRatio?: number;
    employmentStatus?: string;
    workExperience?: number;
    creditScore?: number;
    monthlyIncome?: number;
    employmentType?: string;
    employmentDuration?: number;
    education?: string;
    maritalStatus?: string;
    houseStatus?: string;
    carStatus?: string;
    loanPurpose?: string;
    collateral?: CollateralType;
    guarantor?: string;
    documents?: string[];
}
export declare class LoanApplicationDto {
    id: string;
    userId: string;
    amount: number;
    term: number;
    purpose: string;
    status: LoanStatus;
    createdAt: Date;
    updatedAt: Date;
    loanType: LoanType;
    monthlyPayment: number;
    totalPayment: number;
}
export {};
