import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Button, Table, Switch, Select, message, Spin, Slider } from 'antd';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';

const { Option } = Select;

interface Product {
  id: string;
  name: string;
  interestRate: number;
  maxAmount: number;
  maxTerm: number;
  processingFee: number;
  requirements: any;
  features: string[];
  category: string;
}

interface ComparisonData {
  products: Product[];
  comparison: any;
  recommendation: any;
}

interface ProductComparison3DProps {
  className?: string;
}

const ProductComparison3D: React.FC<ProductComparison3DProps> = ({ className }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  const animationRef = useRef<number>();

  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [comparisonData, setComparisonData] = useState<ComparisonData | null>(null);
  const [loading, setLoading] = useState(false);
  const [animationEnabled, setAnimationEnabled] = useState(true);
  const [viewMode, setViewMode] = useState<'3d' | 'table'>('3d');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [rotationSpeed, setRotationSpeed] = useState(1);

  useEffect(() => {
    loadProducts();
    if (viewMode === '3d') {
      init3DScene();
    }
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [viewMode]);

  const loadProducts = async () => {
    setLoading(true);
    try {
      // 模拟API调用，实际应该从后端获取
      const mockProducts: Product[] = [
        {
          id: '1',
          name: '快速贷',
          interestRate: 4.5,
          maxAmount: 500000,
          maxTerm: 36,
          processingFee: 2.0,
          requirements: {},
          features: ['快速审批', '无抵押'],
          category: 'personal'
        },
        {
          id: '2', 
          name: '企业贷',
          interestRate: 3.8,
          maxAmount: 5000000,
          maxTerm: 60,
          processingFee: 1.5,
          requirements: {},
          features: ['大额度', '长期限'],
          category: 'business'
        },
        {
          id: '3',
          name: '房屋贷',
          interestRate: 3.2,
          maxAmount: 10000000,
          maxTerm: 360,
          processingFee: 1.0,
          requirements: {},
          features: ['超低利率', '超长期限'],
          category: 'mortgage'
        }
      ];
      
      setProducts(mockProducts);
    } catch (error) {
      console.error('Error loading products:', error);
      message.error('加载产品失败');
    } finally {
      setLoading(false);
    }
  };

  const init3DScene = () => {
    if (!containerRef.current) return;

    // 初始化场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf0f0f0);
    sceneRef.current = scene;

    // 初始化相机
    const camera = new THREE.PerspectiveCamera(
      75,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 5, 10);
    cameraRef.current = camera;

    // 初始化渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // 添加轨道控制器
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controlsRef.current = controls;

    // 创建产品对比图表
    const createComparisonChart = () => {
      const features = product1.features;
      const radius = 2;
      const segments = features.length;      // 创建产品1的雷达图
      const product1Geometry = new THREE.BufferGeometry();
      const product1Vertices: number[] = [];
      const product1Colors: number[] = [];

      features.forEach((feature, index) => {
        const angle = (index / segments) * Math.PI * 2;
        const value = (feature.value / feature.maxValue) * radius;
        const x = Math.cos(angle) * value;
        const y = Math.sin(angle) * value;
        product1Vertices.push(x, y, 0);
        product1Colors.push(0.2, 0.4, 0.8);
      });

      product1Geometry.setAttribute(
        'position',
        new THREE.Float32BufferAttribute(product1Vertices, 3)
      );
      product1Geometry.setAttribute(
        'color',
        new THREE.Float32BufferAttribute(product1Colors, 3)
      );

      const product1Material = new THREE.LineBasicMaterial({
        vertexColors: true,
        linewidth: 2,
      });
      const product1Mesh = new THREE.LineLoop(product1Geometry, product1Material);
      scene.add(product1Mesh);      // 创建产品2的雷达图
      const product2Geometry = new THREE.BufferGeometry();
      const product2Vertices: number[] = [];
      const product2Colors: number[] = [];

      product2.features.forEach((feature, index) => {
        const angle = (index / segments) * Math.PI * 2;
        const value = (feature.value / feature.maxValue) * radius;
        const x = Math.cos(angle) * value;
        const y = Math.sin(angle) * value;
        product2Vertices.push(x, y, 0);
        product2Colors.push(0.8, 0.2, 0.2);
      });

      product2Geometry.setAttribute(
        'position',
        new THREE.Float32BufferAttribute(product2Vertices, 3)
      );
      product2Geometry.setAttribute(
        'color',
        new THREE.Float32BufferAttribute(product2Colors, 3)
      );

      const product2Material = new THREE.LineBasicMaterial({
        vertexColors: true,
        linewidth: 2,
      });
      const product2Mesh = new THREE.LineLoop(product2Geometry, product2Material);
      scene.add(product2Mesh);

      // 添加特征标签
      features.forEach((feature, index) => {
        const angle = (index / segments) * Math.PI * 2;
        const x = Math.cos(angle) * (radius + 0.3);
        const y = Math.sin(angle) * (radius + 0.3);
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        if (context) {
          context.font = '12px Arial';
          context.fillStyle = 'white';
          context.fillText(feature.name, 0, 0);
          const texture = new THREE.CanvasTexture(canvas);
          const material = new THREE.SpriteMaterial({ map: texture });
          const sprite = new THREE.Sprite(material);
          sprite.position.set(x, y, 0);
          scene.add(sprite);
        }
      });
    };

    createComparisonChart();

    // 动画循环
    const animate = () => {
      requestAnimationFrame(animate);
      controlsRef.current?.update();
      rendererRef.current?.render(scene, camera);
    };
    animate();

    // 处理窗口大小变化
    const handleResize = () => {
      if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;
      cameraRef.current.aspect = containerRef.current.clientWidth / containerRef.current.clientHeight;
      cameraRef.current.updateProjectionMatrix();
      rendererRef.current.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (containerRef.current && rendererRef.current) {
        containerRef.current.removeChild(rendererRef.current.domElement);
      }
      rendererRef.current?.dispose();
    };
  }, [product1, product2]);

  return (
    <Card title="3D产品对比">
      <div
        ref={containerRef}
        style={{ width: '100%', height: '400px', background: '#000' }}
      />
    </Card>
  );
};

export default ProductComparison3D; 