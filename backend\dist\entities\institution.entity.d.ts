export declare class Institution {
    id: number;
    name: string;
    code: string;
    type: 'bank' | 'credit_union' | 'fintech' | 'other';
    description: string;
    contact: {
        address: string;
        phone: string;
        email: string;
        website?: string;
    };
    licenses: Array<{
        type: string;
        number: string;
        issuer: string;
        issueDate: Date;
        expiryDate: Date;
        status: 'active' | 'expired' | 'suspended';
    }>;
    ratings: {
        creditRating?: string;
        riskRating?: string;
        lastUpdated?: Date;
        history?: Array<{
            date: Date;
            rating: string;
            agency: string;
        }>;
    };
    metadata: {
        foundedDate?: Date;
        employees?: number;
        assets?: number;
        branches?: number;
        services?: string[];
    };
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
