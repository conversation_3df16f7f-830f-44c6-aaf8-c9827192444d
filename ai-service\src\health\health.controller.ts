import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('AI服务健康检查')
@Controller('health')
export class HealthController {
  @Get()
  @ApiOperation({ summary: 'AI服务健康检查' })
  @ApiResponse({ status: 200, description: 'AI服务运行正常' })
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'SmartLoan AI Service',
      version: '1.0.0',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV || 'development',
      features: {
        ocr_recognition: true,
        liveness_detection: true,
        risk_assessment: true,
        ai_advisor: true,
        gpu_acceleration: process.env.GPU_ENABLED === 'true'
      },
      gpu_status: {
        enabled: process.env.GPU_ENABLED === 'true',
        endpoint: process.env.GPU_ENDPOINT || 'http://localhost:8080'
      }
    };
  }
}
