import { User } from './user.entity';
import { Product } from './product.entity';
import { LoanDocument } from './loan-document.entity';
import { LoanStatus } from '../enums/loan-status.enum';
import { LoanType } from '../enums/loan-type.enum';
import { LoanPurpose } from '../enums/loan-purpose.enum';
import { LoanReview } from './loan-review.entity';
import { EmploymentStatus } from '../enums/employment-status.enum';
import { CollateralType } from '../enums/collateral-type.enum';
export { LoanStatus } from '../enums/loan-status.enum';
export { LoanType } from '../enums/loan-type.enum';
export declare class LoanApplication {
    id: string;
    user: User;
    userId: string;
    product: Product;
    productId: string;
    amount: number;
    term: number;
    type: LoanType;
    purpose: LoanPurpose;
    status: LoanStatus;
    monthlyPayment: number;
    totalPayment: number;
    annualIncome: number;
    debtToIncomeRatio: number;
    employmentStatus: string;
    workExperience: number;
    creditScore: number;
    riskScore: number;
    metadata: any;
    documentMetadata: any;
    riskAssessment: any;
    approvedAt: Date;
    rejectedAt: Date;
    cancelledAt: Date;
    cancelledBy: string;
    cancellationReason: string;
    rejectionReason: string;
    approvedBy: string;
    rejectedBy: string;
    employmentStatusEnum: EmploymentStatus;
    collateral: CollateralType;
    notes: string;
    documents: LoanDocument[];
    reviews: LoanReview[];
    createdAt: Date;
    updatedAt: Date;
}
