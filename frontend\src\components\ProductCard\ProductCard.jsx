/**
 * ProductCard 产品对比卡片组件
 * 支持3D对比模式和金融产品展示
 * 集成风险评级和智能推荐
 */

import React, { useState, useRef, useEffect } from 'react';
import { 
  TrendingUp, 
  Clock, 
  Shield, 
  Star, 
  Eye, 
  Compare,
  Zap,
  Award,
  ChevronRight
} from 'lucide-react';
import './ProductCard.scss';

const ProductCard = ({
  data,
  features = [],
  comparisonView = false,
  actionButton = {},
  riskBadge = {},
  onCompare,
  onApply,
  className = '',
  variant = 'default' // default, premium, compact
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [is3DMode, setIs3DMode] = useState(false);
  const [animationPhase, setAnimationPhase] = useState('idle');
  const cardRef = useRef(null);

  // 3D效果处理
  useEffect(() => {
    if (comparisonView && cardRef.current) {
      const card = cardRef.current;
      
      const handleMouseMove = (e) => {
        if (!is3DMode) return;
        
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;
        
        card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;
      };
      
      const handleMouseLeave = () => {
        card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)';
      };
      
      card.addEventListener('mousemove', handleMouseMove);
      card.addEventListener('mouseleave', handleMouseLeave);
      
      return () => {
        card.removeEventListener('mousemove', handleMouseMove);
        card.removeEventListener('mouseleave', handleMouseLeave);
      };
    }
  }, [is3DMode, comparisonView]);

  // 风险等级配置
  const riskLevels = {
    1: { label: '极低风险', color: '#27AE60', bgColor: 'rgba(39, 174, 96, 0.1)' },
    2: { label: '低风险', color: '#2ECC71', bgColor: 'rgba(46, 204, 113, 0.1)' },
    3: { label: '中等风险', color: '#F39C12', bgColor: 'rgba(243, 156, 18, 0.1)' },
    4: { label: '较高风险', color: '#E67E22', bgColor: 'rgba(230, 126, 34, 0.1)' },
    5: { label: '高风险', color: '#E74C3C', bgColor: 'rgba(231, 76, 60, 0.1)' }
  };

  const currentRisk = riskLevels[riskBadge.level] || riskLevels[1];

  // 特色标签
  const getFeatureBadges = () => {
    const badges = [];
    
    if (data.digital_currency_support) {
      badges.push({ label: '数字人民币', icon: '💰', color: '#D4AF37' });
    }
    
    if (data.esg_certified) {
      badges.push({ label: 'ESG认证', icon: '🌱', color: '#27AE60' });
    }
    
    if (data.ai_powered) {
      badges.push({ label: 'AI智能', icon: '🤖', color: '#1A3A8F' });
    }
    
    if (data.instant_approval) {
      badges.push({ label: '秒批', icon: '⚡', color: '#E74C3C' });
    }
    
    return badges;
  };

  const handleApplyClick = () => {
    setAnimationPhase('applying');
    setTimeout(() => {
      onApply && onApply(data);
      setAnimationPhase('success');
      setTimeout(() => setAnimationPhase('idle'), 2000);
    }, 1000);
  };

  const handleCompareClick = () => {
    setIs3DMode(!is3DMode);
    onCompare && onCompare(data);
  };

  const formatRate = (rate) => {
    return typeof rate === 'number' ? `${rate.toFixed(2)}%` : rate;
  };

  const formatAmount = (amount) => {
    if (amount >= 10000) {
      return `${(amount / 10000).toFixed(0)}万`;
    }
    return amount.toLocaleString();
  };

  return (
    <div 
      ref={cardRef}
      className={`product-card product-card--${variant} ${className} ${isHovered ? 'product-card--hovered' : ''} ${is3DMode ? 'product-card--3d' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 卡片头部 */}
      <div className="product-card__header">
        <div className="product-card__institution">
          <div className="product-card__logo">
            {data.institution_logo ? (
              <img src={data.institution_logo} alt={data.institution_name} />
            ) : (
              <div className="product-card__logo-placeholder">
                {data.institution_name?.charAt(0) || '银'}
              </div>
            )}
          </div>
          <div className="product-card__institution-info">
            <h4 className="product-card__institution-name">{data.institution_name}</h4>
            <span className="product-card__product-type">{data.product_type}</span>
          </div>
        </div>
        
        {/* 风险等级徽章 */}
        <div 
          className="product-card__risk-badge"
          style={{ 
            color: currentRisk.color,
            backgroundColor: currentRisk.bgColor 
          }}
        >
          <Shield size={14} />
          {currentRisk.label}
        </div>
      </div>

      {/* 产品名称 */}
      <div className="product-card__title">
        <h3>{data.product_name}</h3>
        {data.match_score && (
          <div className="product-card__match-score">
            <Star className="product-card__match-icon" />
            {(data.match_score * 100).toFixed(0)}%匹配
          </div>
        )}
      </div>

      {/* 核心特征 */}
      <div className="product-card__features">
        {features.map((feature, index) => (
          <div key={index} className="product-card__feature">
            <div className="product-card__feature-icon">
              {feature.icon}
            </div>
            <div className="product-card__feature-content">
              <span className="product-card__feature-label">{feature.label}</span>
              <span className="product-card__feature-value">{feature.value}</span>
            </div>
          </div>
        ))}
        
        {/* 默认特征 */}
        <div className="product-card__feature">
          <div className="product-card__feature-icon">
            <TrendingUp size={16} />
          </div>
          <div className="product-card__feature-content">
            <span className="product-card__feature-label">利率范围</span>
            <span className="product-card__feature-value">
              {formatRate(data.interest_rate_min)} - {formatRate(data.interest_rate_max)}
            </span>
          </div>
        </div>
        
        <div className="product-card__feature">
          <div className="product-card__feature-icon">
            <Clock size={16} />
          </div>
          <div className="product-card__feature-content">
            <span className="product-card__feature-label">额度范围</span>
            <span className="product-card__feature-value">
              {formatAmount(data.amount_min)} - {formatAmount(data.amount_max)}
            </span>
          </div>
        </div>
      </div>

      {/* 特色标签 */}
      <div className="product-card__badges">
        {getFeatureBadges().map((badge, index) => (
          <span 
            key={index}
            className="product-card__badge"
            style={{ color: badge.color }}
          >
            <span className="product-card__badge-icon">{badge.icon}</span>
            {badge.label}
          </span>
        ))}
      </div>

      {/* AI推荐理由 */}
      {data.ai_reasoning && (
        <div className="product-card__ai-reasoning">
          <div className="product-card__ai-icon">🤖</div>
          <p>{data.ai_reasoning}</p>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="product-card__actions">
        {comparisonView && (
          <button 
            className="product-card__compare-btn"
            onClick={handleCompareClick}
            title={is3DMode ? '退出3D模式' : '3D对比模式'}
          >
            <Compare size={16} />
            {is3DMode ? '退出3D' : '3D对比'}
          </button>
        )}
        
        <button 
          className={`product-card__apply-btn product-card__apply-btn--${actionButton.variant || 'primary'} product-card__apply-btn--${animationPhase}`}
          onClick={handleApplyClick}
          disabled={animationPhase === 'applying'}
        >
          {animationPhase === 'applying' && (
            <div className="product-card__loading-spinner"></div>
          )}
          {animationPhase === 'success' && (
            <Award size={16} />
          )}
          {animationPhase === 'idle' && (
            <>
              <Zap size={16} />
              <ChevronRight size={16} />
            </>
          )}
          <span>
            {animationPhase === 'applying' && '处理中...'}
            {animationPhase === 'success' && '申请成功'}
            {animationPhase === 'idle' && (actionButton.label || '立即申请')}
          </span>
        </button>
      </div>

      {/* 3D模式指示器 */}
      {comparisonView && (
        <div className="product-card__3d-indicator">
          <Eye size={14} />
          3D对比模式
        </div>
      )}

      {/* 悬浮效果背景 */}
      <div className="product-card__hover-bg"></div>
    </div>
  );
};

export default ProductCard;
