/// <reference types="node" />
/// <reference types="node" />
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
type OCRResult = {
    name?: string;
    idNumber?: string;
    validityPeriod?: string;
    error?: string;
};
export declare class User {
    id: string;
    name: string;
    idNumber: string;
    validityPeriod: string;
}
export declare class OcrVerificationService {
    private readonly httpService;
    private readonly configService;
    private readonly userRepository;
    constructor(httpService: HttpService, configService: ConfigService, userRepository: Repository<User>);
    verifyIDCard(imageBuffer: Buffer): Promise<OCRResult>;
    private validateIDCardInfo;
    compareWithDatabase(userId: string, ocrData: OCRResult): Promise<boolean>;
}
export {};
