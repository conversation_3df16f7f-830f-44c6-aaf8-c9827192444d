import { Controller, Get, Post, Body, Param, Res, UseGuards } from '@nestjs/common';
import { Response } from 'express';
import { ContractService } from '../services/contract.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { SignContractDto } from '../dto/sign-contract.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('合同管理')
@Controller('contracts')
@UseGuards(JwtAuthGuard)
export class ContractController {
  constructor(private readonly contractService: ContractService) {}

  @Get(':id')
  @ApiOperation({ summary: '获取合同信息' })
  @ApiResponse({ status: 200, description: '成功获取合同信息' })
  async getContract(@Param('id') id: string) {
    return this.contractService.getContract(id);
  }

  @Get(':id/download')
  @ApiOperation({ summary: '下载合同文件' })
  @ApiResponse({ status: 200, description: '成功下载合同文件' })
  async downloadContract(@Param('id') id: string, @Res() res: Response) {
    const filePath = await this.contractService.generateContractFile(id);
    res.download(filePath);
  }

  @Post(':id/sign')
  @ApiOperation({ summary: '签署合同' })
  @ApiResponse({ status: 200, description: '成功签署合同' })
  async signContract(
    @Param('id') id: string,
    @Body() signContractDto: SignContractDto,
  ) {
    return this.contractService.signContract(id, signContractDto);
  }
} 