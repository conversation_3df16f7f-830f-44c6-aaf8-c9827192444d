import { Repository } from 'typeorm';
import { RiskRule } from './entities/risk-rule.entity';
import { LoggerService } from '../../logger/logger.service';
export declare class RiskAssessmentService {
    private readonly riskRuleRepository;
    private readonly logger;
    constructor(riskRuleRepository: Repository<RiskRule>, logger: LoggerService);
    assessRisk(application: any, userProfile: any): Promise<any>;
    private applyRule;
    private evaluateConditions;
    private determineRiskLevel;
}
