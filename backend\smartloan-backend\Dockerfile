# SmartLoan 2025 智能金融服务平台
# 多阶段构建优化镜像大小
# 支持沐曦MetaX GPU和Gitee AI集成

# 构建阶段
FROM maven:3.9.6-eclipse-temurin-17 AS builder

# 设置工作目录
WORKDIR /app

# 复制Maven配置文件
COPY pom.xml .
COPY .mvn .mvn
COPY mvnw .

# 下载依赖（利用Docker缓存层）
RUN mvn dependency:go-offline -B

# 复制源代码
COPY src ./src

# 构建应用
RUN mvn clean package -DskipTests -B

# 运行阶段
FROM eclipse-temurin:17-jre-alpine

# 安装必要的系统包
RUN apk add --no-cache \
    curl \
    jq \
    tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata

# 创建应用用户
RUN addgroup -g 1001 smartloan && \
    adduser -D -s /bin/sh -u 1001 -G smartloan smartloan

# 设置工作目录
WORKDIR /app

# 从构建阶段复制JAR文件
COPY --from=builder /app/target/smartloan-backend-*.jar app.jar

# 创建日志目录
RUN mkdir -p /app/logs && \
    chown -R smartloan:smartloan /app

# 切换到应用用户
USER smartloan

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080 8081 9090

# JVM参数优化
ENV JAVA_OPTS="-Xms2g -Xmx4g \
    -XX:+UseG1GC \
    -XX:G1HeapRegionSize=16m \
    -XX:+UseStringDeduplication \
    -XX:+OptimizeStringConcat \
    -XX:+UseCompressedOops \
    -XX:+UseCompressedClassPointers \
    -Djava.security.egd=file:/dev/./urandom \
    -Dspring.profiles.active=production \
    -Dmetax.gpu.enabled=true \
    -Dgitee.ai.enabled=true"

# 应用配置
ENV SERVER_PORT=8080
ENV MANAGEMENT_PORT=8081
ENV METRICS_PORT=9090

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]

# 元数据标签
LABEL maintainer="SmartLoan Team <<EMAIL>>" \
      version="2025.1.0" \
      description="SmartLoan智能金融服务平台后端服务" \
      vendor="SmartLoan Inc." \
      build-date="2025-01-20" \
      gpu-support="MetaX" \
      ai-platform="Gitee AI"
