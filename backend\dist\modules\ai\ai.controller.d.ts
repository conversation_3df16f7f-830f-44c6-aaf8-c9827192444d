/// <reference types="multer" />
import { AIService } from './ai-service';
import { AIAdvisorQueryDto } from './dto/ai-advisor.dto';
export declare class AIController {
    private readonly aiService;
    constructor(aiService: AIService);
    performOCR(file: Express.Multer.File): Promise<any>;
    performLivenessDetection(file: Express.Multer.File): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
    performRiskAssessment(data: {
        user_profile: any;
        loan_application: any;
        financial_data: any;
    }): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
    analyzeCreditReport(data: {
        credit_report: any;
        user_id: number;
    }): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
    chatWithAdvisor(data: {
        query: string;
        context?: any;
        user_id?: number;
    }): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
    detectLiveness(file: Express.Multer.File): Promise<any>;
    getAIAdvisorResponse(queryDto: AIAdvisorQueryDto): Promise<any>;
}
