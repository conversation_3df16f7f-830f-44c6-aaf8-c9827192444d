import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAdditionalTables1716969600001 implements MigrationInterface {
  name = 'CreateAdditionalTables1716969600001'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建产品表
    await queryRunner.query(`
      CREATE TABLE "products" (
        "id" SERIAL NOT NULL,
        "name" character varying NOT NULL,
        "description" text NOT NULL,
        "type" character varying NOT NULL,
        "interestRate" decimal(5,2) NOT NULL,
        "termRange" jsonb NOT NULL,
        "amountRange" jsonb NOT NULL,
        "requirements" jsonb NOT NULL,
        "metadata" jsonb,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_products" PRIMARY KEY ("id")
      )
    `);
    await queryRunner.query(`CREATE INDEX "IDX_products_name" ON "products" ("name")`);
    await queryRunner.query(`CREATE INDEX "IDX_products_type" ON "products" ("type")`);

    // 创建审核记录表
    await queryRunner.query(`
      CREATE TABLE "audit_records" (
        "id" SERIAL NOT NULL,
        "userId" uuid NOT NULL,
        "applicationId" uuid NOT NULL,
        "status" character varying NOT NULL,
        "reviewerId" uuid,
        "reviewNotes" text,
        "reviewDetails" jsonb,
        "metadata" jsonb,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_audit_records" PRIMARY KEY ("id"),
        CONSTRAINT "FK_audit_records_user" FOREIGN KEY ("userId") REFERENCES "users"("id"),
        CONSTRAINT "FK_audit_records_reviewer" FOREIGN KEY ("reviewerId") REFERENCES "users"("id")
      )
    `);
    await queryRunner.query(`CREATE INDEX "IDX_audit_records_userId" ON "audit_records" ("userId")`);
    await queryRunner.query(`CREATE INDEX "IDX_audit_records_applicationId" ON "audit_records" ("applicationId")`);
    await queryRunner.query(`CREATE INDEX "IDX_audit_records_status" ON "audit_records" ("status")`);

    // 创建风险模型表
    await queryRunner.query(`
      CREATE TABLE "risk_models" (
        "id" SERIAL NOT NULL,
        "name" character varying NOT NULL,
        "version" character varying NOT NULL,
        "description" text NOT NULL,
        "configuration" jsonb NOT NULL,
        "performance" jsonb,
        "metadata" jsonb,
        "isActive" boolean NOT NULL DEFAULT true,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_risk_models" PRIMARY KEY ("id")
      )
    `);
    await queryRunner.query(`CREATE INDEX "IDX_risk_models_name" ON "risk_models" ("name")`);
    await queryRunner.query(`CREATE INDEX "IDX_risk_models_version" ON "risk_models" ("version")`);

    // 创建知识图谱表
    await queryRunner.query(`
      CREATE TABLE "knowledge_graph" (
        "id" SERIAL NOT NULL,
        "nodeId" character varying NOT NULL,
        "nodeType" character varying NOT NULL,
        "properties" jsonb NOT NULL,
        "relationships" jsonb,
        "embeddings" jsonb,
        "metadata" jsonb,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_knowledge_graph" PRIMARY KEY ("id")
      )
    `);
    await queryRunner.query(`CREATE INDEX "IDX_knowledge_graph_nodeId" ON "knowledge_graph" ("nodeId")`);
    await queryRunner.query(`CREATE INDEX "IDX_knowledge_graph_nodeType" ON "knowledge_graph" ("nodeType")`);

    // 创建信用分表
    await queryRunner.query(`
      CREATE TABLE "credit_points" (
        "id" SERIAL NOT NULL,
        "userId" uuid NOT NULL,
        "score" decimal(5,2) NOT NULL,
        "factors" jsonb NOT NULL,
        "history" jsonb,
        "metadata" jsonb,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_credit_points" PRIMARY KEY ("id"),
        CONSTRAINT "FK_credit_points_user" FOREIGN KEY ("userId") REFERENCES "users"("id")
      )
    `);
    await queryRunner.query(`CREATE INDEX "IDX_credit_points_userId" ON "credit_points" ("userId")`);
    await queryRunner.query(`CREATE INDEX "IDX_credit_points_score" ON "credit_points" ("score")`);

    // 创建机构表
    await queryRunner.query(`
      CREATE TABLE "institutions" (
        "id" SERIAL NOT NULL,
        "name" character varying NOT NULL,
        "code" character varying NOT NULL,
        "type" character varying NOT NULL,
        "description" text NOT NULL,
        "contact" jsonb NOT NULL,
        "licenses" jsonb NOT NULL,
        "ratings" jsonb,
        "metadata" jsonb,
        "isActive" boolean NOT NULL DEFAULT true,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_institutions" PRIMARY KEY ("id")
      )
    `);
    await queryRunner.query(`CREATE INDEX "IDX_institutions_name" ON "institutions" ("name")`);
    await queryRunner.query(`CREATE INDEX "IDX_institutions_code" ON "institutions" ("code")`);

    // 创建日志表
    await queryRunner.query(`
      CREATE TABLE "logs" (
        "id" SERIAL NOT NULL,
        "level" character varying NOT NULL,
        "category" character varying NOT NULL,
        "source" character varying NOT NULL,
        "message" text NOT NULL,
        "context" jsonb,
        "metadata" jsonb,
        "timestamp" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_logs" PRIMARY KEY ("id")
      )
    `);
    await queryRunner.query(`CREATE INDEX "IDX_logs_level" ON "logs" ("level")`);
    await queryRunner.query(`CREATE INDEX "IDX_logs_category" ON "logs" ("category")`);
    await queryRunner.query(`CREATE INDEX "IDX_logs_source" ON "logs" ("source")`);
    await queryRunner.query(`CREATE INDEX "IDX_logs_timestamp" ON "logs" ("timestamp")`);

    // 创建缓存表
    await queryRunner.query(`
      CREATE TABLE "cache" (
        "id" SERIAL NOT NULL,
        "key" character varying NOT NULL,
        "value" text NOT NULL,
        "metadata" jsonb,
        "expiresAt" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "hits" integer NOT NULL DEFAULT 0,
        "lastHit" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_cache" PRIMARY KEY ("id")
      )
    `);
    await queryRunner.query(`CREATE INDEX "IDX_cache_key" ON "cache" ("key")`);
    await queryRunner.query(`CREATE INDEX "IDX_cache_expiresAt" ON "cache" ("expiresAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_cache_createdAt" ON "cache" ("createdAt")`);

    // 创建向量存储表
    await queryRunner.query(`
      CREATE TABLE "vector_store" (
        "id" SERIAL NOT NULL,
        "documentId" character varying NOT NULL,
        "content" text NOT NULL,
        "vector" float[] NOT NULL,
        "metadata" jsonb NOT NULL,
        "embeddings" jsonb,
        "searchMetadata" jsonb,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_vector_store" PRIMARY KEY ("id")
      )
    `);
    await queryRunner.query(`CREATE INDEX "IDX_vector_store_documentId" ON "vector_store" ("documentId")`);
    await queryRunner.query(`CREATE INDEX "IDX_vector_store_vector" ON "vector_store" USING gin ("vector")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "vector_store"`);
    await queryRunner.query(`DROP TABLE "cache"`);
    await queryRunner.query(`DROP TABLE "logs"`);
    await queryRunner.query(`DROP TABLE "institutions"`);
    await queryRunner.query(`DROP TABLE "credit_points"`);
    await queryRunner.query(`DROP TABLE "knowledge_graph"`);
    await queryRunner.query(`DROP TABLE "risk_models"`);
    await queryRunner.query(`DROP TABLE "audit_records"`);
    await queryRunner.query(`DROP TABLE "products"`);
  }
} 