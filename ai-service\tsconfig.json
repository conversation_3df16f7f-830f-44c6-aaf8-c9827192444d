{"compilerOptions": {"target": "es2018", "module": "commonjs", "lib": ["es2018", "esnext.asynciterable"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}