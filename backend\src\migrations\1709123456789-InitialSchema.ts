import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialSchema1709123456789 implements MigrationInterface {
  name = 'InitialSchema1709123456789';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建用户表
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "username" character varying NOT NULL,
        "email" character varying NOT NULL,
        "password" character varying NOT NULL,
        "role" character varying NOT NULL DEFAULT 'user',
        "is_active" boolean NOT NULL DEFAULT true,
        "last_login_at" TIMESTAMP,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_users_username" UNIQUE ("username"),
        CONSTRAINT "UQ_users_email" UNIQUE ("email"),
        CONSTRAINT "PK_users" PRIMARY KEY ("id")
      )
    `);

    // 创建贷款申请表
    await queryRunner.query(`
      CREATE TABLE "loan_applications" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid NOT NULL,
        "amount" decimal(10,2) NOT NULL,
        "term" integer NOT NULL,
        "type" character varying NOT NULL,
        "purpose" character varying NOT NULL,
        "status" character varying NOT NULL DEFAULT 'pending',
        "monthly_payment" decimal(10,2),
        "total_payment" decimal(10,2),
        "annual_income" decimal(10,2) NOT NULL,
        "debt_to_income_ratio" decimal(5,2) NOT NULL,
        "employment_status" character varying NOT NULL,
        "work_experience" integer NOT NULL,
        "credit_score" integer,
        "risk_score" integer,
        "metadata" jsonb,
        "document_metadata" jsonb,
        "risk_assessment" jsonb,
        "approved_at" TIMESTAMP,
        "rejected_at" TIMESTAMP,
        "cancelled_at" TIMESTAMP,
        "cancelled_by" uuid,
        "cancellation_reason" character varying,
        "rejection_reason" character varying,
        "approved_by" uuid,
        "rejected_by" uuid,
        "employment_status_enum" character varying NOT NULL DEFAULT 'employed',
        "collateral" character varying NOT NULL DEFAULT 'none',
        "notes" text,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_loan_applications" PRIMARY KEY ("id"),
        CONSTRAINT "FK_loan_applications_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE,
        CONSTRAINT "FK_loan_applications_approved_by" FOREIGN KEY ("approved_by") REFERENCES "users"("id"),
        CONSTRAINT "FK_loan_applications_rejected_by" FOREIGN KEY ("rejected_by") REFERENCES "users"("id"),
        CONSTRAINT "FK_loan_applications_cancelled_by" FOREIGN KEY ("cancelled_by") REFERENCES "users"("id")
      )
    `);

    // 创建贷款文档表
    await queryRunner.query(`
      CREATE TABLE "loan_documents" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "application_id" uuid NOT NULL,
        "type" character varying NOT NULL,
        "file_name" character varying NOT NULL,
        "file_path" character varying NOT NULL,
        "file_size" integer NOT NULL,
        "mime_type" character varying NOT NULL,
        "is_verified" boolean NOT NULL DEFAULT false,
        "verified_by" uuid,
        "notes" text,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_loan_documents" PRIMARY KEY ("id"),
        CONSTRAINT "FK_loan_documents_application" FOREIGN KEY ("application_id") REFERENCES "loan_applications"("id") ON DELETE CASCADE,
        CONSTRAINT "FK_loan_documents_verified_by" FOREIGN KEY ("verified_by") REFERENCES "users"("id")
      )
    `);

    // 创建贷款审核表
    await queryRunner.query(`
      CREATE TABLE "loan_reviews" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "application_id" uuid NOT NULL,
        "reviewer_id" uuid NOT NULL,
        "status" character varying NOT NULL,
        "type" character varying NOT NULL,
        "comments" text,
        "risk_factors" jsonb,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_loan_reviews" PRIMARY KEY ("id"),
        CONSTRAINT "FK_loan_reviews_application" FOREIGN KEY ("application_id") REFERENCES "loan_applications"("id") ON DELETE CASCADE,
        CONSTRAINT "FK_loan_reviews_reviewer" FOREIGN KEY ("reviewer_id") REFERENCES "users"("id")
      )
    `);

    // 创建索引
    await queryRunner.query(`CREATE INDEX "IDX_users_email" ON "users" ("email")`);
    await queryRunner.query(`CREATE INDEX "IDX_users_username" ON "users" ("username")`);
    await queryRunner.query(`CREATE INDEX "IDX_loan_applications_user" ON "loan_applications" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_loan_applications_status" ON "loan_applications" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_loan_documents_application" ON "loan_documents" ("application_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_loan_reviews_application" ON "loan_reviews" ("application_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_loan_reviews_reviewer" ON "loan_reviews" ("reviewer_id")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除索引
    await queryRunner.query(`DROP INDEX "IDX_loan_reviews_reviewer"`);
    await queryRunner.query(`DROP INDEX "IDX_loan_reviews_application"`);
    await queryRunner.query(`DROP INDEX "IDX_loan_documents_application"`);
    await queryRunner.query(`DROP INDEX "IDX_loan_applications_status"`);
    await queryRunner.query(`DROP INDEX "IDX_loan_applications_user"`);
    await queryRunner.query(`DROP INDEX "IDX_users_username"`);
    await queryRunner.query(`DROP INDEX "IDX_users_email"`);

    // 删除表
    await queryRunner.query(`DROP TABLE "loan_reviews"`);
    await queryRunner.query(`DROP TABLE "loan_documents"`);
    await queryRunner.query(`DROP TABLE "loan_applications"`);
    await queryRunner.query(`DROP TABLE "users"`);
  }
} 