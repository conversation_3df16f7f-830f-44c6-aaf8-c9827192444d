import { Product } from '../entities/product.entity';
export interface FeatureWeights {
    instant_approval: number;
    no_collateral: number;
    flexible_repayment: number;
    no_early_fee: number;
    purpose_match: number;
}
export interface MatchScoreDetails {
    amountScore: number;
    termScore: number;
    rateScore: number;
    featureScore: number;
    locationScore: number;
}
export interface MatchedProduct {
    product: Product;
    score: number;
    details: MatchScoreDetails;
}
export interface ProductMatchCriteria {
    amount: number;
    term: number;
    purpose?: string;
    location?: string;
    features?: string[];
}
export interface LocationMatchRule {
    type: 'exact' | 'city' | 'province';
    locations: string[];
    score: number;
}
export interface UserLocation {
    province: string;
    city: string;
    district: string;
}
export interface CacheManager {
    get<T>(key: string): Promise<T | null>;
    set<T>(key: string, value: T, ttl?: number): Promise<void>;
    del(key: string): Promise<void>;
}
export interface IGpuService {
    batchCalculate(productTensors: number[][], criteriaTensor: number[], weights: Record<string, number>): Promise<number[]>;
    prepareProductTensor(product: any): number[];
    prepareCriteriaTensor(criteria: any): number[];
}
