import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { Role } from '../enums/role.enum';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private jwtService: JwtService,
  ) {}

  async register(phone: string, password: string, email?: string): Promise<any> {
    // 检查手机号是否已存在
    const existingUser = await this.userRepository.findOne({ where: { phone } });
    if (existingUser) {
      throw new Error('手机号已注册');
    }

    // 生成盐值和密码哈希
    const salt = crypto.randomBytes(16).toString('hex');
    const passwordHash = await bcrypt.hash(password + salt, 10);

    // 创建用户
    const user = this.userRepository.create({
      phone,
      email,
      password_hash: passwordHash,
      salt,
      credit_score: 600, // 默认信用分
      role: Role.USER,
      name: email || phone, // Use email or phone as default name
      username: email || phone // Use email or phone as username
    });

    await this.userRepository.save(user);

    // 生成JWT令牌
    const payload = { userId: user.id, phone: user.phone };
    const token = this.jwtService.sign(payload);

    return {
      token,
      user: {
        id: user.id,
        phone: user.phone,
        email: user.email,
        creditScore: user.credit_score
      }
    };
  }

  async login(phone: string, password: string): Promise<any> {
    // 查找用户
    const user = await this.userRepository.findOne({ where: { phone } });
    if (!user) {
      throw new UnauthorizedException('手机号或密码错误');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password + user.salt, user.password_hash);
    if (!isPasswordValid) {
      throw new UnauthorizedException('手机号或密码错误');
    }

    // 更新最后登录时间
    user.last_login_at = new Date();
    await this.userRepository.save(user);

    // 生成JWT令牌
    const payload = { userId: user.id, phone: user.phone };
    const token = this.jwtService.sign(payload);

    return {
      token,
      user: {
        id: user.id,
        phone: user.phone,
        email: user.email,
        realName: user.real_name,
        creditScore: user.credit_score,
        riskLevel: user.risk_level
      }
    };
  }

  async getUserById(userId: number): Promise<User> {
    return this.userRepository.findOne({ where: { id: userId.toString() } });
  }

  async updateUserProfile(userId: number, updateData: Partial<User>): Promise<User> {
    await this.userRepository.update(userId, updateData);
    return this.getUserById(userId);
  }

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.userRepository.findOne({ where: { email } });
    if (user && await bcrypt.compare(password + user.salt, user.password_hash)) {
      return { id: user.id, email: user.email };
    }
    return null;
  }

  async changePassword(userId: string, oldPassword: string, newPassword: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new UnauthorizedException('用户不存在');
    }

    const isValidPassword = await bcrypt.compare(oldPassword + user.salt, user.password_hash);
    if (!isValidPassword) {
      throw new UnauthorizedException('原密码错误');
    }

    const newPasswordHash = await bcrypt.hash(newPassword + user.salt, 10);
    await this.userRepository.update(user.id, { password_hash: newPasswordHash });
  }

  async resetPassword(email: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      throw new Error('用户不存在');
    }
    // 发送重置密码邮件的逻辑
    // 这里可以集成邮件服务
  }
}
