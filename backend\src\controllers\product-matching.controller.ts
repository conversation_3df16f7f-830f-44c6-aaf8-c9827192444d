import { Controller, Post, Body, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsBoolean, IsEnum, ValidateNested, IsObject, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { ProductMatchingEngine, UserProfile, ProductMatchWeights } from '../services/interfaces/ProductMatchingEngine';
import { ProductService } from '../services/product.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { Product } from '../entities/product.entity';
import { LoanPurpose } from '../enums/loan-purpose.enum';

interface RiskAssessment {
  score: number;
  level: 'low' | 'medium' | 'high' | 'very_high';
  factors: Array<{
    factor: string;
    level: string;
    description: string;
  }>;
  approvalRate: number;
  timestamp: Date;
}

export enum ProductCategory {
  PERSONAL = 'personal',
  BUSINESS = 'business',
  MORTGAGE = 'mortgage',
  AUTO = 'auto',
  EDUCATION = 'education'
}

export interface ProductRequirements {
  minAge: number;
  maxAge: number;
  minIncome: number;
  minCreditScore: number;
  maxDebtToIncomeRatio: number;
  employmentTypes: string[];
  workExperienceYears: number;
  requiredDocuments: string[];
}

export class UserProfileDto implements UserProfile {
  @IsNumber()
  @Min(18)
  @Max(100)
  age: number;

  @IsNumber()
  @Min(0)
  monthlyIncome: number;

  @IsNumber()
  @Min(300)
  @Max(850)
  creditScore: number;

  @IsString()
  employmentType: string;

  @IsNumber()
  @Min(0)
  employmentDuration: number;

  @IsNumber()
  @Min(0)
  debtToIncomeRatio: number;

  @IsNumber()
  @Min(0)
  requestedAmount: number;

  @IsNumber()
  @Min(1)
  @Max(360)
  preferredTerm: number;

  @IsString()
  location: string;

  @IsEnum(LoanPurpose)
  purpose: string;
}

export class ProductFilterDto {
  @IsOptional()
  @IsEnum(ProductCategory)
  category?: ProductCategory;

  @IsOptional()
  @IsEnum(LoanPurpose)
  purpose?: LoanPurpose;

  @IsOptional()
  @IsNumber()
  @Min(0)
  minAmount?: number;

  @IsOptional()
  @IsNumber()
  @Max(1)
  maxInterestRate?: number;
}

export class ProductMatchWeightsDto implements ProductMatchWeights {
  @IsNumber()
  @Min(0)
  @Max(1)
  amount: number = 0.25;

  @IsNumber()
  @Min(0)
  @Max(1)
  term: number = 0.15;

  @IsNumber()
  @Min(0)
  @Max(1)
  interestRate: number = 0.3;

  @IsNumber()
  @Min(0)
  @Max(1)
  creditScore: number = 0.1;

  @IsNumber()
  @Min(0)
  @Max(1)
  income: number = 0.1;

  @IsNumber()
  @Min(0)
  @Max(1)
  purpose: number = 0.1;
}

export class ProductMatchingRequestDto {
  @ValidateNested()
  @Type(() => UserProfileDto)
  userProfile: UserProfileDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => ProductFilterDto)
  filters?: ProductFilterDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => ProductMatchWeightsDto)
  weights?: ProductMatchWeightsDto;
}

@ApiTags('智能产品匹配')
@Controller('product-matching')
export class ProductMatchingController {
  constructor(
    private readonly productMatchingEngine: ProductMatchingEngine,
    private readonly productService: ProductService
  ) {}

  @Post('match')
  @ApiOperation({ summary: '智能产品匹配' })
  @ApiResponse({ status: 200, description: '返回匹配的产品推荐列表' })
  async matchProducts(@Body() request: ProductMatchingRequestDto) {
    try {
      const matchedProducts = await this.productMatchingEngine.findBestMatches(
        request.userProfile,
        request.filters,
        request.weights
      );

      return {
        success: true,
        data: {
          recommendations: matchedProducts,
          totalMatched: matchedProducts.length,
          userProfile: request.userProfile
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  @Post('detailed-analysis')
  @ApiOperation({ summary: '详细产品分析' })
  @ApiResponse({ status: 200, description: '返回用户画像与产品的详细匹配分析' })
  async getDetailedAnalysis(@Body() request: ProductMatchingRequestDto) {
    try {
      const allProducts = await this.productService.findAll({});
      
      const detailedAnalysis = await Promise.all(
        allProducts.map(async (product) => {
          const matchScore = this.productMatchingEngine.calculateMatchScore(
            request.userProfile,
            product,
            request.weights
          );
          
          const approvalProbability = this.productMatchingEngine.calculateApprovalProbability(
            request.userProfile,
            product
          );
          
          const costAnalysis = this.productMatchingEngine.calculateTotalCost(
            request.userProfile.requestedAmount,
            product,
            request.userProfile.preferredTerm
          );

          return {
            product,
            matchScore,
            approvalProbability,
            costAnalysis,
            recommendations: this.generateProductRecommendations(request.userProfile, product)
          };
        })
      );

      // 按匹配分数排序
      detailedAnalysis.sort((a, b) => b.matchScore - a.matchScore);

      return {
        success: true,
        data: {
          analysis: detailedAnalysis,
          userProfile: request.userProfile,
          marketAnalysis: this.generateMarketAnalysis(allProducts)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  @Get('quick-match/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '基于用户ID的快速匹配' })
  @ApiResponse({ status: 200, description: '返回用户的快速产品推荐' })
  async quickMatch(@Param('userId') userId: string) {
    try {
      const userProfile = await this.getUserProfileFromDb(userId);
      if (!userProfile) {
        throw new Error('用户信息不存在');
      }

      const matchedProducts = await this.productMatchingEngine.findBestMatches(
        userProfile,
        {},
        this.getDefaultWeights()
      );

      return {
        success: true,
        data: {
          recommendations: matchedProducts,
          userProfile
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  @Post('compare-products')
  @ApiOperation({ summary: '对比多个产品' })
  @ApiResponse({ status: 200, description: '返回产品对比结果' })
  async compareProducts(
    @Body() request: { 
      userProfile: UserProfileDto; 
      productIds: string[] 
    }
  ) {
    try {
      const products = await Promise.all(
        request.productIds.map(id => this.productService.findById(id))
      );

      const comparison = products.map(product => {
        const matchScore = this.productMatchingEngine.calculateMatchScore(
          request.userProfile,
          product
        );
        
        const approvalProbability = this.productMatchingEngine.calculateApprovalProbability(
          request.userProfile,
          product
        );
        
        const costAnalysis = this.productMatchingEngine.calculateTotalCost(
          request.userProfile.requestedAmount,
          product,
          request.userProfile.preferredTerm
        );

        return {
          product,
          matchScore,
          approvalProbability,
          costAnalysis,
          recommendations: this.generateProductRecommendations(request.userProfile, product)
        };
      });

      return {
        success: true,
        data: {
          comparison,
          bestMatch: comparison.reduce((best, current) => 
            current.matchScore > best.matchScore ? current : best
          )
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        data: null
      };
    }  }

  private generateProductRecommendations(userProfile: UserProfileDto, product: Product): string[] {
    const recommendations: string[] = [];

    if (product.requirementsData) {
      if (userProfile.creditScore < product.minCreditScore) {
        recommendations.push('建议提高信用评分后再申请');
      }

      if (userProfile.monthlyIncome < product.minIncome) {
        recommendations.push('建议增加收入证明或考虑担保');
      }
    }

    if (product.interestRate > 0.15) {
      recommendations.push('利率较高，建议对比其他产品');
    }

    return recommendations;
  }

  private getDefaultWeights(): ProductMatchWeights {
    return {
      amount: 0.25,
      term: 0.15,
      interestRate: 0.3,
      creditScore: 0.1,
      income: 0.1,
      purpose: 0.1
    };
  }

  private generateMarketAnalysis(products: Product[]) {
    return {
      categoryDistribution: this.calculateCategoryDistribution(products),
      interestRateRange: this.calculateInterestRateRange(products),
      averageApprovalRate: this.calculateAverageApprovalRate(products),
      popularFeatures: this.analyzePopularFeatures(products),
      marketTrends: this.analyzeMarketTrends(products)
    };
  }

  private calculateCategoryDistribution(products: Product[]): Record<string, number> {
    const distribution = {};
    products.forEach(product => {
      const category = product.category || 'other';
      distribution[category] = (distribution[category] || 0) + 1;
    });
    return distribution;
  }

  private calculateInterestRateRange(products: Product[]) {
    const rates = products.map(p => p.interestRate);
    return {
      min: Math.min(...rates),
      max: Math.max(...rates),
      average: rates.reduce((sum, rate) => sum + rate, 0) / rates.length
    };
  }
  private calculateAverageApprovalRate(products: Product[]): number {
    const approvalRates = products
      .filter(p => p.metadata?.approvalRate !== undefined)
      .map(p => p.metadata.approvalRate);
    return approvalRates.length > 0 
      ? approvalRates.reduce((sum, rate) => sum + rate, 0) / approvalRates.length
      : 0;
  }

  private analyzePopularFeatures(products: Product[]): Array<{name: string; count: number; percentage: number}> {
    const featureCount: Record<string, number> = {};
    products.forEach(product => {
      if (product.features) {
        product.features.forEach(feature => {
          featureCount[feature.name] = (featureCount[feature.name] || 0) + 1;
        });
      }
    });
    
    return Object.entries(featureCount)
      .sort(([, countA], [, countB]) => Number(countB) - Number(countA))
      .slice(0, 5)
      .map(([name, count]) => ({
        name,
        count: Number(count),
        percentage: Number((Number(count) / products.length) * 100)
      }));
  }
  private analyzeMarketTrends(products: Product[]): {
    totalProducts: number;
    averageInterestRate: number;
    averageApprovalRate: number;
    mostPopularCategories: Array<{
      category: string;
      count: number;
      percentage: number;
    }>;
  } {
    // 此处可以添加更多市场趋势分析
    return {
      totalProducts: products.length,
      averageInterestRate: this.calculateInterestRateRange(products).average,
      averageApprovalRate: this.calculateAverageApprovalRate(products),
      mostPopularCategories: Object.entries(this.calculateCategoryDistribution(products))
        .sort(([, countA], [, countB]) => Number(countB) - Number(countA))
        .slice(0, 3)
        .map(([category, count]) => ({
          category,
          count: Number(count),
          percentage: Number((Number(count) / products.length) * 100)
        }))
    };
  }

  private async getUserProfileFromDb(userId: string): Promise<UserProfileDto> {
    // 实际实现中需要从数据库获取用户信息
    // 这里暂时返回一个示例用户画像
    return {
      age: 30,
      monthlyIncome: 10000,
      creditScore: 700,
      employmentType: 'employed',
      employmentDuration: 5,
      debtToIncomeRatio: 0.3,
      requestedAmount: 100000,
      preferredTerm: 12,
      location: '北京',
      purpose: LoanPurpose.PERSONAL
    };
  }
}
