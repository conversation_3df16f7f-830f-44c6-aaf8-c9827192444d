import { HttpException, HttpStatus, Logger } from '@nestjs/common';
import { QueryFailedError } from 'typeorm';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ErrorHandler {
  private readonly logger = new Logger(ErrorHandler.name);

  static handle(error: any) {
    // 处理错误的逻辑
    console.error('Error handled:', error);
    // 可以根据错误类型返回不同的响应
    return { error: 'An error occurred' };
  }

  handle(error: any): HttpException {
    this.logger.error('Error occurred:', error);

    // 处理 TypeORM 错误
    if (error instanceof QueryFailedError) {
      return this.handleDatabaseError(error);
    }

    // 处理 HTTP 异常
    if (error instanceof HttpException) {
      return error;
    }

    // 处理验证错误
    if (error.name === 'ValidationError') {
      return this.handleValidationError(error.message);
    }

    // 处理 JWT 错误
    if (error.name === 'JsonWebTokenError') {
      return this.handleAuthenticationError('无效的令牌，请重新登录');
    }

    // 处理令牌过期错误
    if (error.name === 'TokenExpiredError') {
      return this.handleAuthenticationError('令牌已过期，请重新登录');
    }

    // 处理文件上传错误
    if (error.name === 'MulterError') {
      return this.handleBusinessError(error.message, HttpStatus.BAD_REQUEST);
    }

    // 处理 Redis 错误
    if (error.name === 'RedisError') {
      return this.handleBusinessError('缓存服务错误，请稍后重试', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // 处理未知错误
    return this.handleBusinessError('服务器内部错误，请稍后重试', HttpStatus.INTERNAL_SERVER_ERROR);
  }

  private handleDatabaseError(error: QueryFailedError): HttpException {
    // 处理唯一约束违反错误
    if (error.message.includes('duplicate key')) {
      return this.handleBusinessError('该记录已存在，请勿重复创建', HttpStatus.CONFLICT);
    }

    // 处理外键约束违反错误
    if (error.message.includes('foreign key')) {
      return this.handleBusinessError('关联的数据不存在或已被删除', HttpStatus.BAD_REQUEST);
    }

    // 处理其他数据库错误
    return this.handleBusinessError('数据库错误，请稍后重试', HttpStatus.INTERNAL_SERVER_ERROR);
  }

  // 处理业务逻辑错误
  handleBusinessError(message: string, status: HttpStatus = HttpStatus.BAD_REQUEST): HttpException {
    return new HttpException({
      status,
      error: '业务逻辑错误',
      message
    }, status);
  }

  // 处理权限错误
  handlePermissionError(message: string = '没有权限执行此操作'): HttpException {
    return new HttpException({
      status: HttpStatus.FORBIDDEN,
      error: '权限错误',
      message
    }, HttpStatus.FORBIDDEN);
  }

  // 处理资源不存在错误
  handleNotFoundError(message: string = '请求的资源不存在'): HttpException {
    return new HttpException({
      status: HttpStatus.NOT_FOUND,
      error: '资源不存在',
      message
    }, HttpStatus.NOT_FOUND);
  }  // 处理参数错误
  handleValidationError(messageOrErrors: string | any[]): HttpException {
    if (typeof messageOrErrors === 'string') {
      return new HttpException({
        status: HttpStatus.BAD_REQUEST,
        error: '参数错误',
        message: messageOrErrors
      }, HttpStatus.BAD_REQUEST);
    } else {
      const messages = messageOrErrors.map(error => {
        const constraints = error.constraints;
        return Object.values(constraints).join(', ');
      });

      return new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: messages,
          error: 'Validation Error'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  // 处理认证错误
  handleAuthenticationError(message: string = '请先登录'): HttpException {
    return new HttpException({
      status: HttpStatus.UNAUTHORIZED,
      error: '认证错误',
      message
    }, HttpStatus.UNAUTHORIZED);
  }

  handleAuthError(error: any): HttpException {
    this.logger.error(`Authentication error: ${error.message}`, error.stack);

    if (error.name === 'JsonWebTokenError') {
      return new HttpException('无效的令牌', HttpStatus.UNAUTHORIZED);
    }

    if (error.name === 'TokenExpiredError') {
      return new HttpException('令牌已过期', HttpStatus.UNAUTHORIZED);
    }

    return new HttpException(
      '认证失败',
      HttpStatus.UNAUTHORIZED
    );
  }

  handleFileError(error: any): HttpException {
    this.logger.error(`File operation error: ${error.message}`, error.stack);

    if (error.code === 'ENOENT') {
      return new HttpException('文件不存在', HttpStatus.NOT_FOUND);
    }

    if (error.code === 'EACCES') {
      return new HttpException('没有文件访问权限', HttpStatus.FORBIDDEN);
    }

    return new HttpException(
      '文件操作失败',
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  static handleValidationError(errors: any[]): HttpException {
    const messages = errors.map(error => {
      const constraints = error.constraints;
      return Object.values(constraints).join(', ');
    });

    return new HttpException(
      {
        statusCode: HttpStatus.BAD_REQUEST,
        message: messages,
        error: 'Validation Error',
      },
      HttpStatus.BAD_REQUEST,
    );
  }

  static isOperational(error: any): boolean {
    if (error instanceof HttpException) {
      return true;
    }

    return false;
  }

  static async handleAsync<T>(promise: Promise<T>): Promise<T> {
    try {
      return await promise;
    } catch (error) {
      throw this.handle(error);
    }
  }
}