"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoanApplicationController = void 0;
var common_1 = require("@nestjs/common");
var platform_express_1 = require("@nestjs/platform-express");
var jwt_auth_guard_1 = require("../guards/jwt-auth.guard");
var roles_guard_1 = require("../guards/roles.guard");
var roles_decorator_1 = require("../decorators/roles.decorator");
var swagger_1 = require("@nestjs/swagger");
var loan_document_entity_1 = require("../entities/loan-document.entity");
var loan_application_dto_1 = require("../dto/loan-application.dto");
var role_enum_1 = require("../enums/role.enum");
var LoanApplicationController = function () {
    var _classDecorators = [(0, swagger_1.ApiTags)('贷款申请'), (0, common_1.Controller)('loan-applications'), (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard), (0, swagger_1.ApiBearerAuth)()];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _instanceExtraInitializers = [];
    var _create_decorators;
    var _findAll_decorators;
    var _findOne_decorators;
    var _updateApplication_decorators;
    var _cancelApplication_decorators;
    var _getUserApplications_decorators;
    var _getApplicationStatistics_decorators;
    var _getApplicationTrends_decorators;
    var _approveApplication_decorators;
    var _rejectApplication_decorators;
    var _uploadDocument_decorators;
    var _verifyDocument_decorators;
    var LoanApplicationController = _classThis = /** @class */ (function () {
        function LoanApplicationController_1(loanApplicationService) {
            this.loanApplicationService = (__runInitializers(this, _instanceExtraInitializers), loanApplicationService);
        }
        LoanApplicationController_1.prototype.create = function (req, createDto) {
            return __awaiter(this, void 0, void 0, function () {
                var application;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.loanApplicationService.create(__assign(__assign({}, createDto), { userId: req.user.id }))];
                        case 1:
                            application = _a.sent();
                            return [2 /*return*/, this.transformToDto(application)];
                    }
                });
            });
        };
        LoanApplicationController_1.prototype.findAll = function (req) {
            return __awaiter(this, void 0, void 0, function () {
                var applications;
                var _this = this;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.loanApplicationService.findAllByUser(req.user.id)];
                        case 1:
                            applications = _a.sent();
                            return [2 /*return*/, applications.map(function (app) { return _this.transformToDto(app); })];
                    }
                });
            });
        };
        LoanApplicationController_1.prototype.findOne = function (req, id) {
            return __awaiter(this, void 0, void 0, function () {
                var application;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.loanApplicationService.getApplication(id, req.user.id)];
                        case 1:
                            application = _a.sent();
                            return [2 /*return*/, this.transformToDto(application)];
                    }
                });
            });
        };
        LoanApplicationController_1.prototype.updateApplication = function (id, updateData, user) {
            return __awaiter(this, void 0, void 0, function () {
                var application;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.loanApplicationService.updateApplication(id, user, updateData)];
                        case 1:
                            application = _a.sent();
                            return [2 /*return*/, this.transformToDto(application)];
                    }
                });
            });
        };
        LoanApplicationController_1.prototype.cancelApplication = function (id, user) {
            return __awaiter(this, void 0, void 0, function () {
                var application;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.loanApplicationService.cancelApplication(id, user)];
                        case 1:
                            application = _a.sent();
                            return [2 /*return*/, this.transformToDto(application)];
                    }
                });
            });
        };
        LoanApplicationController_1.prototype.getUserApplications = function (userId, user) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    return [2 /*return*/, this.loanApplicationService.getUserApplications(userId, user)];
                });
            });
        };
        LoanApplicationController_1.prototype.getApplicationStatistics = function (user) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    return [2 /*return*/, this.loanApplicationService.getApplicationStatistics(user)];
                });
            });
        };
        LoanApplicationController_1.prototype.getApplicationTrends = function (user) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    return [2 /*return*/, this.loanApplicationService.getApplicationTrends(user)];
                });
            });
        };
        LoanApplicationController_1.prototype.approveApplication = function (id, user) {
            return __awaiter(this, void 0, void 0, function () {
                var application;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.loanApplicationService.approveApplication(id, user)];
                        case 1:
                            application = _a.sent();
                            return [2 /*return*/, this.transformToDto(application)];
                    }
                });
            });
        };
        LoanApplicationController_1.prototype.rejectApplication = function (id, reason, user) {
            return __awaiter(this, void 0, void 0, function () {
                var application;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.loanApplicationService.rejectApplication(id, reason, user)];
                        case 1:
                            application = _a.sent();
                            return [2 /*return*/, this.transformToDto(application)];
                    }
                });
            });
        };
        LoanApplicationController_1.prototype.uploadDocument = function (applicationId, file, type, user) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    return [2 /*return*/, this.loanApplicationService.uploadDocument(applicationId, file, type, user)];
                });
            });
        };
        LoanApplicationController_1.prototype.verifyDocument = function (applicationId, documentId, isVerified, notes, user) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    return [2 /*return*/, this.loanApplicationService.verifyDocument(applicationId, documentId, user, isVerified, notes)];
                });
            });
        };
        LoanApplicationController_1.prototype.transformToDto = function (application) {
            return {
                id: application.id,
                userId: application.userId,
                amount: application.amount,
                term: application.term,
                purpose: application.purpose,
                status: application.status,
                loanType: application.loanType,
                monthlyPayment: application.monthlyPayment,
                totalPayment: application.totalPayment,
                createdAt: application.createdAt,
                updatedAt: application.updatedAt,
            };
        };
        return LoanApplicationController_1;
    }());
    __setFunctionName(_classThis, "LoanApplicationController");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        _create_decorators = [(0, common_1.Post)(), (0, roles_decorator_1.Roles)(role_enum_1.Role.USER), (0, swagger_1.ApiOperation)({ summary: '创建贷款申请' }), (0, swagger_1.ApiResponse)({ status: 201, description: '贷款申请创建成功', type: loan_application_dto_1.LoanApplicationDto })];
        _findAll_decorators = [(0, common_1.Get)(), (0, swagger_1.ApiOperation)({ summary: '获取用户的贷款申请列表' }), (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [loan_application_dto_1.LoanApplicationDto] })];
        _findOne_decorators = [(0, common_1.Get)(':id'), (0, swagger_1.ApiOperation)({ summary: '获取贷款申请详情' }), (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: loan_application_dto_1.LoanApplicationDto })];
        _updateApplication_decorators = [(0, common_1.Post)(':id/update'), (0, roles_decorator_1.Roles)(role_enum_1.Role.USER, role_enum_1.Role.ADMIN), (0, swagger_1.ApiOperation)({ summary: '更新贷款申请' }), (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: loan_application_dto_1.LoanApplicationDto })];
        _cancelApplication_decorators = [(0, common_1.Post)(':id/cancel'), (0, roles_decorator_1.Roles)(role_enum_1.Role.USER, role_enum_1.Role.ADMIN), (0, swagger_1.ApiOperation)({ summary: '取消贷款申请' }), (0, swagger_1.ApiResponse)({ status: 200, description: '取消成功', type: loan_application_dto_1.LoanApplicationDto })];
        _getUserApplications_decorators = [(0, common_1.Get)('user/:userId'), (0, swagger_1.ApiOperation)({ summary: '获取用户的贷款申请列表' }), (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [loan_application_dto_1.LoanApplicationDto] })];
        _getApplicationStatistics_decorators = [(0, common_1.Get)('statistics'), (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.RISK_ANALYST), (0, swagger_1.ApiOperation)({ summary: '获取贷款申请统计数据' }), (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' })];
        _getApplicationTrends_decorators = [(0, common_1.Get)('trends'), (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.RISK_ANALYST), (0, swagger_1.ApiOperation)({ summary: '获取贷款申请趋势数据' }), (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' })];
        _approveApplication_decorators = [(0, common_1.Post)(':id/approve'), (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.LOAN_OFFICER), (0, swagger_1.ApiOperation)({ summary: '审批贷款申请' }), (0, swagger_1.ApiResponse)({ status: 200, description: '审批成功', type: loan_application_dto_1.LoanApplicationDto })];
        _rejectApplication_decorators = [(0, common_1.Post)(':id/reject'), (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.LOAN_OFFICER), (0, swagger_1.ApiOperation)({ summary: '拒绝贷款申请' }), (0, swagger_1.ApiResponse)({ status: 200, description: '拒绝成功', type: loan_application_dto_1.LoanApplicationDto })];
        _uploadDocument_decorators = [(0, common_1.Post)(':id/documents'), (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')), (0, swagger_1.ApiConsumes)('multipart/form-data'), (0, swagger_1.ApiBody)({
                schema: {
                    type: 'object',
                    properties: {
                        file: {
                            type: 'string',
                            format: 'binary',
                        },
                        type: {
                            type: 'string',
                            enum: Object.values(loan_document_entity_1.DocumentType),
                        },
                    },
                },
            }), (0, swagger_1.ApiOperation)({ summary: '上传贷款文档' }), (0, swagger_1.ApiResponse)({ status: 201, description: '上传成功' })];
        _verifyDocument_decorators = [(0, common_1.Post)(':id/documents/:documentId/verify'), (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.LOAN_OFFICER), (0, swagger_1.ApiOperation)({ summary: '验证贷款文档' }), (0, swagger_1.ApiResponse)({ status: 200, description: '验证成功' })];
        __esDecorate(_classThis, null, _create_decorators, { kind: "method", name: "create", static: false, private: false, access: { has: function (obj) { return "create" in obj; }, get: function (obj) { return obj.create; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findAll_decorators, { kind: "method", name: "findAll", static: false, private: false, access: { has: function (obj) { return "findAll" in obj; }, get: function (obj) { return obj.findAll; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findOne_decorators, { kind: "method", name: "findOne", static: false, private: false, access: { has: function (obj) { return "findOne" in obj; }, get: function (obj) { return obj.findOne; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _updateApplication_decorators, { kind: "method", name: "updateApplication", static: false, private: false, access: { has: function (obj) { return "updateApplication" in obj; }, get: function (obj) { return obj.updateApplication; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _cancelApplication_decorators, { kind: "method", name: "cancelApplication", static: false, private: false, access: { has: function (obj) { return "cancelApplication" in obj; }, get: function (obj) { return obj.cancelApplication; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getUserApplications_decorators, { kind: "method", name: "getUserApplications", static: false, private: false, access: { has: function (obj) { return "getUserApplications" in obj; }, get: function (obj) { return obj.getUserApplications; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getApplicationStatistics_decorators, { kind: "method", name: "getApplicationStatistics", static: false, private: false, access: { has: function (obj) { return "getApplicationStatistics" in obj; }, get: function (obj) { return obj.getApplicationStatistics; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getApplicationTrends_decorators, { kind: "method", name: "getApplicationTrends", static: false, private: false, access: { has: function (obj) { return "getApplicationTrends" in obj; }, get: function (obj) { return obj.getApplicationTrends; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _approveApplication_decorators, { kind: "method", name: "approveApplication", static: false, private: false, access: { has: function (obj) { return "approveApplication" in obj; }, get: function (obj) { return obj.approveApplication; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _rejectApplication_decorators, { kind: "method", name: "rejectApplication", static: false, private: false, access: { has: function (obj) { return "rejectApplication" in obj; }, get: function (obj) { return obj.rejectApplication; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _uploadDocument_decorators, { kind: "method", name: "uploadDocument", static: false, private: false, access: { has: function (obj) { return "uploadDocument" in obj; }, get: function (obj) { return obj.uploadDocument; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _verifyDocument_decorators, { kind: "method", name: "verifyDocument", static: false, private: false, access: { has: function (obj) { return "verifyDocument" in obj; }, get: function (obj) { return obj.verifyDocument; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        LoanApplicationController = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return LoanApplicationController = _classThis;
}();
exports.LoanApplicationController = LoanApplicationController;
