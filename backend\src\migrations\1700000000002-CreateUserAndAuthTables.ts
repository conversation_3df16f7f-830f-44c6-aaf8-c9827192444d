import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserAndAuthTables1700000000002 implements MigrationInterface {
  name = 'CreateUserAndAuthTables1700000000002';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建角色表
    await queryRunner.query(`
      CREATE TABLE "roles" (
        "id" SERIAL PRIMARY KEY,
        "name" VARCHAR NOT NULL UNIQUE,
        "description" VARCHAR,
        "permissions" JSONB NOT NULL DEFAULT '[]',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // 创建用户表
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" SERIAL PRIMARY KEY,
        "username" VARCHAR NOT NULL UNIQUE,
        "email" VARCHAR NOT NULL UNIQUE,
        "phone" VARCHAR,
        "password" VARCHAR NOT NULL,
        "salt" VARCHAR NOT NULL,
        "avatar" VARCHAR,
        "status" VARCHAR NOT NULL DEFAULT 'active',
        "last_login_at" TIMESTAMP,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // 创建用户角色关联表
    await queryRunner.query(`
      CREATE TABLE "user_roles" (
        "user_id" INTEGER NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
        "role_id" INTEGER NOT NULL REFERENCES "roles"("id") ON DELETE CASCADE,
        PRIMARY KEY ("user_id", "role_id")
      )
    `);

    // 创建用户认证令牌表
    await queryRunner.query(`
      CREATE TABLE "auth_tokens" (
        "id" SERIAL PRIMARY KEY,
        "user_id" INTEGER NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
        "token" VARCHAR NOT NULL UNIQUE,
        "type" VARCHAR NOT NULL,
        "expires_at" TIMESTAMP NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // 创建密码重置表
    await queryRunner.query(`
      CREATE TABLE "password_resets" (
        "id" SERIAL PRIMARY KEY,
        "user_id" INTEGER NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
        "token" VARCHAR NOT NULL UNIQUE,
        "expires_at" TIMESTAMP NOT NULL,
        "used_at" TIMESTAMP,
        "created_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // 插入基础角色
    await queryRunner.query(`
      INSERT INTO "roles" ("name", "description", "permissions") VALUES
      ('admin', '系统管理员', '["*"]'),
      ('user', '普通用户', '["read:own", "write:own"]'),
      ('auditor', '审核员', '["read:all", "write:audit"]')
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "password_resets"`);
    await queryRunner.query(`DROP TABLE "auth_tokens"`);
    await queryRunner.query(`DROP TABLE "user_roles"`);
    await queryRunner.query(`DROP TABLE "users"`);
    await queryRunner.query(`DROP TABLE "roles"`);
  }
} 