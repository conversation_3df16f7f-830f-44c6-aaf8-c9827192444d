import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, UploadedFile, UseInterceptors } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { ProductService } from '../services/product.service';
import { ProductRecommendationService } from '../services/product-recommendation.service';
import { ProductComparisonService } from '../services/product-comparison.service';
import { ProductSearchService, SearchResult } from '../services/product-search.service';
import { ProductImportExportService } from '../services/product-import-export.service';
import { CreateProductDto, UpdateProductDto, ProductFilterDto } from '../dto/product.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RolesGuard } from '../guards/roles.guard';
import { Roles } from '../decorators/roles.decorator';
import { Role } from '../enums/role.enum';

@ApiTags('产品管理')
@Controller('products')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ProductController {
  constructor(
    private readonly productService: ProductService,
    private readonly productRecommendationService: ProductRecommendationService,
    private readonly productComparisonService: ProductComparisonService,
    private readonly productSearchService: ProductSearchService,
    private readonly productImportExportService: ProductImportExportService
  ) {}
  @Post('ocr/recognize')
async recognizeDocument(@Body() data: any) {
  return {
    success: true,
    data: {
      documentType: data.documentType || 'identity_card',
      extractedData: { name: '张三', idNumber: '110101199001011234' },
      confidence: 0.95,
      processed: true
    }
  };
}

@Post('face/liveness')
async livenessDetection(@Body() data: any) {
  return {
    success: true,
    data: {
      isLive: true,
      confidence: 0.98,
      faceQuality: 'high'
    }
  };
}
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: '创建产品' })
  @ApiResponse({ status: 201, description: '产品创建成功' })
  async create(@Body() createProductDto: CreateProductDto) {
    return this.productService.create(createProductDto);
  }

  @Put(':id')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: '更新产品' })
  @ApiResponse({ status: 200, description: '产品更新成功' })
  async update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto) {
    return this.productService.update(id, updateProductDto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: '删除产品' })
  @ApiResponse({ status: 200, description: '产品删除成功' })
  async delete(@Param('id') id: string) {
    return this.productService.delete(id);
  }

  @Get()
  @ApiOperation({ summary: '获取产品列表' })
  @ApiResponse({ status: 200, description: '返回产品列表' })
  async findAll(@Query() filterDto: ProductFilterDto) {
    return this.productService.findAll(filterDto);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取产品详情' })
  @ApiResponse({ status: 200, description: '返回产品详情' })
  async findById(@Param('id') id: string) {
    return this.productService.findById(id);
  }

  @Get('code/:code')
  @ApiOperation({ summary: '根据代码获取产品' })
  @ApiResponse({ status: 200, description: '返回产品详情' })
  async findByCode(@Param('code') code: string) {
    return this.productService.findByCode(code);
  }

  @Get('recommendations/user/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取用户推荐产品' })  @ApiResponse({ status: 200, description: '返回用户推荐产品列表' })
  async getRecommendationsByUser(@Param('userId') userId: string) {
    const defaultFeatures = {
      preferredAmount: 100000,
      preferredTerm: 12,
      riskTolerance: 'medium',
      categoryPreferences: []
    };
    return this.productRecommendationService.recommendProductsByUserFeatures(userId, defaultFeatures);
  }

  @Get('popular')
  @ApiOperation({ summary: '获取热门产品' })
  @ApiResponse({ status: 200, description: '返回热门产品列表' })
  async getPopularProducts() {
    return this.productRecommendationService.getPopularProducts();
  }

  @Get('similar/:id')
  @ApiOperation({ summary: '获取相似产品' })
  @ApiResponse({ status: 200, description: '返回相似产品列表' })
  async getSimilarProducts(@Param('id') id: string) {
    return this.productRecommendationService.getSimilarProducts(id);
  }

  @Get('compare/:ids')
  @ApiOperation({ summary: '比较产品' })
  @ApiResponse({ status: 200, description: '返回产品比较结果' })
  async compareProducts(@Param('ids') ids: string) {
    const productIds = ids.split(',');
    return this.productComparisonService.compareProducts(productIds);
  }  @Get('search')
  @ApiOperation({ summary: '搜索产品' })
  @ApiResponse({ status: 200, description: '返回搜索结果' })
  async searchProducts(@Query('query') query: string): Promise<SearchResult> {
    const searchParams = { keyword: query };
    return this.productSearchService.searchProducts(searchParams);
  }
  @Post('import')
  @Roles(Role.ADMIN)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '从Excel导入产品数据' })
  @ApiResponse({ status: 200, description: '导入成功' })
  async importProducts(@UploadedFile() file: Express.Multer.File) {
    return this.productImportExportService.importProducts(file);
  }

  @Get('export')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: '导出产品数据到Excel' })
  @ApiResponse({ status: 200, description: '返回Excel文件' })
  async exportProducts() {
    return this.productImportExportService.exportProducts();
  }
} 